"""
DAG Airflow pour effectuer des appels CURL à intervalle régulier
"""

# Standard library imports
import logging
import os
from datetime import datetime, timedelta

# Third-party imports
import requests
from airflow import DAG
from airflow.models import Variable
from airflow.operators.bash import BashOperator
from airflow.operators.dummy import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.sensors.filesystem import FileSensor
from airflow.utils.dates import days_ago
from sqlalchemy import func

# Local/application imports
from query_biodata_jobs_lib import query_active_jobs

# Configuration du logger
logger = logging.getLogger(__name__)


# Définition des arguments par défaut
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email': ['<EMAIL>'],
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
    'execution_timeout': timedelta(minutes=30),
    'start_date': days_ago(1),
}

# Création du DAG
dag = DAG(
    'thema_job_status_biodata',
    default_args=default_args,
    description='DAG pour mettre à jour les status des jobs créés',
    # schedule_interval='0 */3 * * *',
    schedule_interval='* * * * *',
    catchup=False,
    tags=['api', 'jobs', 'status'],
    
)

# Tâche de démarrage
start_task = DummyOperator(
    task_id='start',
    dag=dag,
)

# Tâche de fin
end_task = DummyOperator(
    task_id='end',
    dag=dag,
)


def get_jobs():
    """
    Récupère les jobs avec le statut "SUBMITTED" ou "RUNNING" en utilisant
    la fonction query_active_jobs du module query_biodata_jobs
    """
    # Récupérez les jobs avec le statut "SUBMITTED" ou "RUNNING"
    jobs = query_active_jobs()
    
    # Maintenant, vous pouvez parcourir les jobs et traiter les résultats
    for job in jobs:
        logger.info(f"Job ID: {job.id}, UUID: {job.job_uuid}, Status: {job.status}")
        checkstatus(job.job_uuid);
    return jobs


# Tâche pour récupérer les jobs actifs
get_jobs_task = PythonOperator(
    task_id='get_active_jobs',
    python_callable=get_jobs,
    dag=dag,
)


def checkstatus(uuid = str()):
    """Vérifie le statut des jobs actifs"""
    try:
        
        thema_api_url = Variable.get("thema_api_url", default_var="http://biodata_api:8000/")   
        base_url = thema_api_url.rstrip('/')
        # Utilisation de l'URL depuis les variables
        url = f"{base_url}/jobs/check"
       
        
        # Données du formulaire
        data = {"job_id": uuid}
        
        # Effectuer la requête POST avec le formulaire
        response = requests.post(url, data=data)
        
        # Vérifier si la requête a réussi
        if response.status_code == 200:
            logger.info("L'API est disponible et prête à recevoir des requêtes.")
            logger.info(f"Réponse de l'API: {response.text}")
            return True
        else:
            raise Exception(f"L'API a répondu avec le code {response.status_code}")
    except Exception as e:
        logger.error(f"Erreur lors de la vérification de l'API: {str(e)}")
        raise


# Définition de l'ordre d'exécution
start_task >> get_jobs_task >> end_task

# Si le fichier est exécuté directement, afficher des informations sur le DAG
if __name__ == "__main__":
    dag.cli()
    print(f"DAG ID: {dag.dag_id}")
    print(f"Tâches: {dag.tasks}")
    print(f"Schedule: {dag.schedule_interval}")
