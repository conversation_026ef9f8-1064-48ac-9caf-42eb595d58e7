"""
Module to query jobs from the biodata PostgreSQL database using the biodata_models package

Example usage:
    from query_biodata_jobs import query_active_jobs
    
    # Get active jobs (SUBMITTED or RUNNING)
    active_jobs = query_active_jobs()
    
    # Process active jobs
    for job in active_jobs:
        print(f"Processing job {job.id}: {job.job_uuid} - {job.status}")
        # Do something with the job
"""

from sqlalchemy import create_engine, func
from sqlalchemy.orm import sessionmaker
from biodata_models import Base, Job

# Configure logging
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection URLs
# When running locally (outside Docker), use localhost
# When running inside Docker (e.g., in Airflow container), use the container name 'postgres'
# For connecting to the host machine from inside a container, use 'host.docker.internal' on macOS/Windows
# or the Docker bridge network gateway (often **********) on Linux
DATABASE_URL_LOCAL = "postgresql://biodata:biodata_password@localhost:5432/biodata"
DATABASE_URL_DOCKER = "***************************************************/biodata"
DATABASE_URL_HOST = "postgresql://biodata:<EMAIL>:5432/biodata"
DATABASE_URL_DOCKERHOST = "*****************************************************/biodata"

# Function to determine if we're running inside Docker
def is_running_in_docker():
    """Check if we're running inside a Docker container"""
    try:
        with open('/proc/self/cgroup', 'r') as f:
            return 'docker' in f.read()
    except:
        return False

# Choose the appropriate database URL
# DATABASE_URL = DATABASE_URL_DOCKER if is_running_in_docker() else DATABASE_URL_LOCAL
# DATABASE_URL = DATABASE_URL_LOCAL
# DATABASE_URL = DATABASE_URL_DOCKERHOST  # Try using dockerhost as suggested
DATABASE_URL = DATABASE_URL_HOST

def query_active_jobs():
    """Query jobs with status SUBMITTED or RUNNING from the biodata database"""
    try:
        # Create SQLAlchemy engine
        engine = create_engine(DATABASE_URL)
        logger.info("Database engine created successfully")
        
        # Create session factory
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        # Create a session
        session = SessionLocal()
        
        # Query jobs with status SUBMITTED or RUNNING
        active_jobs = session.query(Job).filter(
            func.lower(Job.status).in_([status.lower() for status in ["SUBMITTED", "RUNNING", "RUNNABLE", "STARTING"]])
        ).all()
        logger.info(f"Retrieved {len(active_jobs)} active jobs (SUBMITTED or RUNNING)")
        logger.info(f"Total jobs: {session.query(Job).count()}")

        # Print active job information
        for job in active_jobs:
            logger.info(f"Active Job ID: {job.id}, UUID: {job.job_uuid}, Status: {job.status}")
        
        # Close the session
        session.close()
        
        return active_jobs
    
    except Exception as e:
        logger.error(f"Error querying active jobs: {e}")
        return []

if __name__ == "__main__":
    logger.info("Starting job query script")
    active_jobs = query_active_jobs()
    logger.info(f"Active jobs: {len(active_jobs)}")
