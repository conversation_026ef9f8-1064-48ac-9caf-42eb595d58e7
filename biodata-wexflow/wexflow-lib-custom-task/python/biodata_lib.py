from datetime import datetime, timed<PERSON>ta
from logging import config
import os
import json
import requests
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.dummy import DummyOperator  # Pour créer des nœuds sans action
from airflow.utils.dates import days_ago
from airflow.sensors.filesystem import FileSensor
import logging
import base64
from sqlalchemy import func
from thema_hl7_lib import generate_hl7_attachment
from thema_hprim_lib import generate_hprim_attachment
from airflow.models import Variable
from airflow.sensors.external_task import ExternalTaskSensor
import yaml

# Configuration du logger
logger = logging.getLogger(__name__)

def get_static_config():
    """Charge la configuration statique depuis le fichier YAML"""
    config_path = "/opt/airflow/config_watcher/watcher_config.yaml"
    try:
        with open(config_path, 'r') as file:
            config = yaml.safe_load(file)['watcher']
        return config
    except Exception as e:
        logger.error(f"Erreur lors du chargement de la configuration statique: {str(e)}")
        # Retourner une configuration par défaut en cas d'erreur
        return {
            'base_path': '/opt/airflow/data',
            'output_path': '/opt/airflow/output',
            'scan_interval': '*/5 * * * *'  # Toutes les 5 minutes
        }


def get_config(**context):
    """Charge la configuration complète avec les valeurs dynamiques"""
    config = get_static_config()
    try:
        # base_path toujours depuis Variable Airflow
        config['base_path'] = Variable.get("base_path")
        
        # Log pour debug
        logger.info("Tentative de récupération des informations laboratoire...")
        
        # Récupération explicite depuis XCom (maintenant depuis notre propre tâche)
        lab_info = context['task_instance'].xcom_pull(
            task_ids='parse_lab_info',
            key='lab_info'
        )
        
        # logger.info(f"Données lab_info récupérées: {lab_info}")
        
        # if lab_info and 'repert_labo' in lab_info:
        #     config['output_path'] = lab_info['repert_labo']
        #     logger.info(f"repert_labo trouvé: {config['output_path']}")
        # else:
        #     logger.warning("repert_labo non trouvé, utilisation du chemin par défaut")
        #     config['output_path'] = os.path.join(config['base_path'], 'output')
        
        return config
        
    except Exception as e:
        logger.error(f"Erreur lors du chargement de la configuration: {str(e)}")
        config['output_path'] = os.path.join(config['base_path'], 'output')
        return config


def get_patient_info(patient_number, hmd_api_url=None, jwt_token=None, **context):
    """
    Récupère les informations d'un patient à partir de son numéro
    
    Args:
        patient_number (str): Le numéro du patient à rechercher
        hmd_api_url (str, optional): URL de l'API HMD
        jwt_token (str, optional): Token JWT pour l'authentification
        **context: Contexte Airflow pour accéder aux variables
    
    Returns:
        dict: Les informations du patient ou None en cas d'erreur
    """
    try:
        # Récupération des variables d'environnement depuis Airflow ou paramètres
        if not hmd_api_url:
            try:
                hmd_api_url = Variable.get("hmd_api_url")
            except:
                hmd_api_url = None
        if not jwt_token:
            try:
                jwt_token = Variable.get("hmd_jwt_token")
            except:
                jwt_token = None
            
        # Vérification que les variables sont disponibles
        if not hmd_api_url or not jwt_token:
            logger.warning(f"Variables manquantes - HMD_API_URL: {bool(hmd_api_url)}, JWT_TOKEN: {bool(jwt_token)}")
            return None
        
        # Préparation des données JSON pour la requête
        json_data = {
            "table": "PATIENT",
            "fields": [
                "NUMPAT",
                "NOM", 
                "PRENOM",
                "DAT_NAIS"
            ],
            "where": [
                f"NUMPAT = '{patient_number}'"
            ]
        }
        
        # Configuration des headers
        headers = {
            'Content-Type': 'application/json',
            'JWTToken': jwt_token,
            'User-Agent': 'airflow/biodata',
            'from': 'EMA_WS'
        }
        
        # URL complète pour la requête
        url = f"{hmd_api_url}/query/select"
        
        logger.info(f"Requête patient pour NUMPAT: {patient_number}")
        
        # Exécution de la requête POST
        response = requests.post(
            url=url,
            headers=headers,
            json=json_data,
            timeout=30
        )
        
        # Vérification du code de statut HTTP
        if response.status_code == 200:
            result = response.json()
            logger.info(f"Patient trouvé: {result}")
            return result
        else:
            logger.error(f"Erreur HTTP {response.status_code}: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        logger.error(f"Erreur de requête pour le patient {patient_number}: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Erreur générale lors de la récupération du patient {patient_number}: {str(e)}")
        return None
