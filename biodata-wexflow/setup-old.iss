; Script Inno Setup pour BioData OCR Solution
; Créé automatiquement pour déployer l'API BioData et Wexflow

[Setup]
AppName=BioData OCR Solution
AppVersion=1.0.0
AppPublisher=BioData Solutions
AppPublisherURL=https://biodata.hemadialyse.com
AppSupportURL=https://biodata.hemadialyse.com
AppUpdatesURL=https://biodata.hemadialyse.com
DefaultDirName={autopf}\BioData-Wexflow
DefaultGroupName=BioData-Wexflow Suite
AllowNoIcons=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir=Output
OutputBaseFilename=BioData-Install-Setup
SetupIconFile=
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "french"; MessagesFile: "compiler:Languages\French.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Types]
Name: "full"; Description: "Installation complète (API + Wexflow)"
Name: "apionly"; Description: "API BioData seulement"
Name: "wexflowonly"; Description: "Wexflow seulement"
Name: "custom"; Description: "Installation personnalisée"; Flags: iscustom

[Components]
Name: "api"; Description: "API BioData"; Types: full apionly; Flags: fixed
Name: "wexflow"; Description: "Wexflow Engine"; Types: full wexflowonly
Name: "scripts"; Description: "Scripts de lancement"; Types: full apionly wexflowonly

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1
Name: "startmenu"; Description: "Créer des raccourcis dans le menu démarrer"; GroupDescription: "{cm:AdditionalIcons}"; Flags: checkedonce
Name: "configpaths"; Description: "Configurer automatiquement les chemins Wexflow"; GroupDescription: "Configuration Wexflow"; Flags: checkedonce; Components: wexflow

[Files]
; API BioData Files
Source: "api\*"; DestDir: "{app}\API"; Components: api; Flags: ignoreversion recursesubdirs createallsubdirs
; Wexflow Files
Source: "wexflow\*"; DestDir: "{app}\Wexflow"; Components: wexflow; Flags: ignoreversion recursesubdirs createallsubdirs
; Scripts de lancement
Source: "scripts\*"; DestDir: "{app}\Scripts"; Components: scripts; Flags: ignoreversion

[Dirs]
; Dossiers Wexflow avec permissions complètes
Name: "{app}\Wexflow\Wexflow-netcore\Database"; Components: wexflow; Permissions: everyone-full
Name: "{app}\Wexflow\Wexflow-netcore\Records"; Components: wexflow; Permissions: everyone-full
Name: "{app}\Wexflow\Wexflow-netcore\Records\_HotFolder"; Components: wexflow; Permissions: everyone-full
Name: "{app}\Wexflow\Wexflow-netcore\Temp"; Components: wexflow; Permissions: everyone-full
Name: "{app}\Wexflow\Wexflow-netcore\Workflows"; Components: wexflow; Permissions: everyone-full
Name: "{app}\Wexflow\Wexflow-netcore\Tasks"; Components: wexflow; Permissions: everyone-full
Name: "{app}\Wexflow\Wexflow-netcore\Approval"; Components: wexflow; Permissions: everyone-full

[Icons]
Name: "{group}\Démarrer API BioData"; Filename: "{app}\Scripts\start-api.bat"; Components: api; Tasks: startmenu
Name: "{group}\Démarrer Wexflow"; Filename: "{app}\Scripts\start-wexflow.bat"; Components: wexflow; Tasks: startmenu
Name: "{group}\Démarrer Tout"; Filename: "{app}\Scripts\start-all.bat"; Components: api wexflow; Tasks: startmenu
Name: "{group}\Arrêter Tout"; Filename: "{app}\Scripts\stop-all.bat"; Components: api wexflow; Tasks: startmenu
Name: "{group}\{cm:UninstallProgram,BioData-Wexflow Suite}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\BioData-Wexflow Suite"; Filename: "{app}\Scripts\start-all.bat"; Tasks: desktopicon; Components: api wexflow
Name: "{autodesktop}\API BioData"; Filename: "{app}\Scripts\start-api.bat"; Tasks: desktopicon; Components: api; Check: not IsComponentSelected('wexflow')
Name: "{autodesktop}\Wexflow"; Filename: "{app}\Scripts\start-wexflow.bat"; Tasks: desktopicon; Components: wexflow; Check: not IsComponentSelected('api')

[Run]
Filename: "{app}\Scripts\start-all.bat"; Description: "{cm:LaunchProgram,BioData-Wexflow Suite}"; Flags: nowait postinstall skipifsilent; Components: api wexflow
Filename: "{app}\Scripts\start-api.bat"; Description: "{cm:LaunchProgram,API BioData}"; Flags: nowait postinstall skipifsilent; Components: api; Check: not IsComponentSelected('wexflow')
Filename: "{app}\Scripts\start-wexflow.bat"; Description: "{cm:LaunchProgram,Wexflow}"; Flags: nowait postinstall skipifsilent; Components: wexflow; Check: not IsComponentSelected('api')

[Registry]
; Variable d'environnement pour le dossier d'installation Wexflow
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; ValueType: string; ValueName: "WEXFLOW_INSTALL_DIR"; ValueData: "{app}\Wexflow"; Flags: preservestringtype; Components: wexflow

[UninstallRun]
Filename: "{app}\Scripts\stop-all.bat"; Flags: runhidden

[UninstallDelete]
; Sous-dossiers spécifiques
Type: filesandordirs; Name: "{app}\Wexflow\Wexflow-netcore\Database"
Type: filesandordirs; Name: "{app}\Wexflow\Wexflow-netcore\Records"
Type: filesandordirs; Name: "{app}\Wexflow\Wexflow-netcore\Temp"
Type: filesandordirs; Name: "{app}\Wexflow\Wexflow-netcore\Workflows"
Type: filesandordirs; Name: "{app}\Wexflow\Wexflow-netcore\Tasks"
Type: filesandordirs; Name: "{app}\Wexflow\Wexflow-netcore\Approval"
Type: filesandordirs; Name: "{app}\Wexflow\Logs"
Type: filesandordirs; Name: "{app}\API\logs"
Type: filesandordirs; Name: "{app}\API\test"

; Dossiers principaux
Type: filesandordirs; Name: "{app}\API"
Type: filesandordirs; Name: "{app}\Wexflow"
Type: filesandordirs; Name: "{app}\Wexflow\Wexflow-netcore"
Type: filesandordirs; Name: "{app}\Wexflow\WexflowTesting"

[Messages]
WelcomeLabel2=Cet assistant va installer [name/ver] sur votre ordinateur.%n%nCette suite comprend l'API BioData et le moteur de workflow Wexflow pour la gestion des données biomédicales.

[Code]
var
  ResultCode: Integer;

function IsDotNetInstalled(): Boolean;
begin
  Result := Exec('dotnet', '--version', '', SW_HIDE, ewWaitUntilTerminated, ResultCode) and (ResultCode = 0);
end;

function LoadTextFromFile(const FileName: String): String;
var
  Lines: TArrayOfString;
  I: Integer;
begin
  Result := '';
  if LoadStringsFromFile(FileName, Lines) then
  begin
    for I := 0 to GetArrayLength(Lines) - 1 do
    begin
      Result := Result + Lines[I];
      if I < GetArrayLength(Lines) - 1 then
        Result := Result + #13#10;
    end;
  end;
end;

function SaveTextToFile(const FileName, Content: String): Boolean;
var
  Lines: TArrayOfString;
  I, Start, LineCount: Integer;
  Line: String;
begin
  Result := False;
  SetArrayLength(Lines, 0);
  LineCount := 0;
  
  Start := 1;
  for I := 1 to Length(Content) do
  begin
    if (Content[I] = #13) and (I < Length(Content)) and (Content[I + 1] = #10) then
    begin
      Line := Copy(Content, Start, I - Start);
      Inc(LineCount);
      SetArrayLength(Lines, LineCount);
      Lines[LineCount - 1] := Line;
      Start := I + 2;
      I := I + 1; // Skip the #10
    end
    else if I = Length(Content) then
    begin
      Line := Copy(Content, Start, I - Start + 1);
      Inc(LineCount);
      SetArrayLength(Lines, LineCount);
      Lines[LineCount - 1] := Line;
    end;
  end;
  
  Result := SaveStringsToFile(FileName, Lines, False);
end;

function StringReplace(const S, OldPattern, NewPattern: String): String;
var
  SearchStr, TempStr: String;
  I, StartPos: Integer;
begin
  Result := '';
  TempStr := S;
  SearchStr := OldPattern;
  
  StartPos := Pos(SearchStr, TempStr);
  while StartPos > 0 do
  begin
    Result := Result + Copy(TempStr, 1, StartPos - 1) + NewPattern;
    TempStr := Copy(TempStr, StartPos + Length(SearchStr), Length(TempStr));
    StartPos := Pos(SearchStr, TempStr);
  end;
  
  Result := Result + TempStr;
end;

function EscapeJsonPath(const Path: String): String;
begin
  Result := StringReplace(Path, '\', '\\');
end;

procedure UpdateWexflowPaths(const InstallDir: String);
var
  WexflowXmlPath: String;
  Content: String;
  Lines: TArrayOfString;
  I: Integer;
begin
  WexflowXmlPath := ExpandConstant('{app}\Wexflow\Wexflow-netcore\Wexflow.xml');
  
  // Charger le contenu du fichier
  Content := LoadTextFromFile(WexflowXmlPath);
  if Content <> '' then
  begin
    Log('Mise à jour des chemins dans Wexflow.xml...');
    
    // Remplacer tous les "install_folder" par le chemin d'installation réel
    Content := StringReplace(Content, 'install_folder', ExpandConstant('{app}'));
    
    // Sauvegarder le fichier modifié
    if SaveTextToFile(WexflowXmlPath, Content) then
      Log('Wexflow.xml sauvegardé avec succès')
    else
      Log('Erreur lors de la sauvegarde de Wexflow.xml');
  end
  else
    Log('Erreur: Impossible de charger Wexflow.xml');
end;

procedure UpdateWexflowConfiguration();
var
  AppSettingsPath: String;
  AppSettingsContent: String;
  InstallDir: String;
  EscapedPath: String;
begin
  if not IsComponentSelected('wexflow') or not IsTaskSelected('configpaths') then
    Exit;
  
  InstallDir := ExpandConstant('{app}');
  Log('Configuration des chemins Wexflow pour : ' + InstallDir);
  
  // Mise à jour de Wexflow.xml
  UpdateWexflowPaths(InstallDir);
  
  // Mise à jour d'appsettings.json
  AppSettingsPath := ExpandConstant('{app}\Wexflow\Wexflow.Server\appsettings.json');
  AppSettingsContent := LoadTextFromFile(AppSettingsPath);
  if AppSettingsContent <> '' then
  begin
    Log('Mise à jour appsettings.json...');
    
    // Préparer le chemin pour JSON (doubler les backslashes)
    EscapedPath := EscapeJsonPath(InstallDir);
    
    // Remplacer le placeholder "install_folder" par le chemin d'installation réel
    AppSettingsContent := StringReplace(AppSettingsContent, 'install_folder', EscapedPath);
    
    // Sauvegarder le fichier modifié
    if SaveTextToFile(AppSettingsPath, AppSettingsContent) then
      Log('appsettings.json configuré avec les chemins absolus')
    else
      Log('Erreur: Impossible de sauvegarder appsettings.json');
  end
  else
    Log('Erreur: Impossible de charger appsettings.json');
  
  Log('Configuration Wexflow terminée');
end;

function InitializeSetup(): Boolean;
begin
  Result := True;
  if not IsDotNetInstalled() then
  begin
    if MsgBox('Microsoft .NET Runtime n''est pas installé sur ce système.' + #13#10 + 
              'Il est requis pour faire fonctionner cette application.' + #13#10 + #13#10 + 
              'Voulez-vous continuer l''installation ? Vous devrez installer .NET manuellement après.', 
              mbConfirmation, MB_YESNO) = IDNO then
      Result := False;
  end;
end;

procedure UpdateInstallBat(const InstallDir: String);
var
  InstallBatPath: String;
  Content: String;
begin
  InstallBatPath := ExpandConstant('{app}\Wexflow\install.bat');
  
  // Charger le contenu du fichier
  Content := LoadTextFromFile(InstallBatPath);
  if Content <> '' then
  begin
    Log('Mise à jour des chemins dans install.bat...');
    
    // Remplacer tous les "install_folder" par le chemin d'installation réel
    Content := StringReplace(Content, 'install_folder', ExpandConstant('{app}'));
    
    // Sauvegarder le fichier modifié
    if SaveTextToFile(InstallBatPath, Content) then
      Log('install.bat sauvegardé avec succès')
    else
      Log('Erreur lors de la sauvegarde de install.bat');
  end
  else
    Log('Erreur: Impossible de charger install.bat');
end;

function PrepareToInstall(var NeedsRestart: Boolean): String;
begin
  Result := '';
  
  // Configurer les fichiers avant l'exécution des commandes [Run]
  if IsComponentSelected('wexflow') then
  begin
    // S'assurer que la tâche de configuration des chemins est sélectionnée
    if not IsTaskSelected('configpaths') then
    begin
      Log('Activation forcée de la tâche de configuration des chemins Wexflow');
      // On ne peut pas activer la tâche programmatiquement, mais on peut avertir l'utilisateur
      Result := 'Pour une installation correcte, la tâche "Configurer automatiquement les chemins Wexflow" ' +
                'doit être sélectionnée. Veuillez revenir en arrière et la sélectionner.';
      Exit;
    end;
    
    // Configurer automatiquement les chemins Wexflow
    UpdateWexflowConfiguration();
    
    // Mettre à jour le fichier install.bat
    UpdateInstallBat('');  // Le chemin est maintenant défini directement dans la fonction
  end;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  // Actions post-installation
  if CurStep = ssPostInstall then
  begin
    // Initialiser la variable
    ResultCode := 0;
    // Créer les répertoires de logs si nécessaire
    if IsComponentSelected('api') then
    begin
      CreateDir(ExpandConstant('{app}\API\logs'));
      CreateDir(ExpandConstant('{app}\API\test'));
    end;
    
    if IsComponentSelected('wexflow') then
    begin
      CreateDir(ExpandConstant('{app}\Wexflow\Logs'));
      CreateDir(ExpandConstant('{app}\Wexflow\Temp'));
      
      // Vérifier une dernière fois si les remplacements ont été effectués
      if IsTaskSelected('configpaths') then
      begin
        // Vérifier Wexflow.xml
        if Pos('install_folder', LoadTextFromFile(ExpandConstant('{app}\Wexflow\Wexflow-netcore\Wexflow.xml'))) > 0 then
        begin
          // Réessayer le remplacement
          UpdateWexflowPaths(ExpandConstant('{app}'));
        end;
        
        // Vérifier appsettings.json
        if Pos('install_folder', LoadTextFromFile(ExpandConstant('{app}\Wexflow\Wexflow.Server\appsettings.json'))) > 0 then
        begin
          // Réessayer le remplacement
          UpdateWexflowConfiguration();
        end;
        
        // Vérifier install.bat
        if Pos('install_folder', LoadTextFromFile(ExpandConstant('{app}\Wexflow\install.bat'))) > 0 then
        begin
          // Réessayer le remplacement
          UpdateInstallBat('');
        end;
      end;
    end;
  end;
  
  // Exécuter install.bat juste avant la finalisation de l'installation
  if CurStep = ssDone then
  begin
    if IsComponentSelected('wexflow') then
    begin
      Log('Exécution de install.bat juste avant la finalisation...');
      
      // Vérifier une dernière fois si les remplacements ont été effectués
      if IsTaskSelected('configpaths') then
      begin
        // Vérifier et mettre à jour les fichiers si nécessaire
        if Pos('install_folder', LoadTextFromFile(ExpandConstant('{app}\Wexflow\Wexflow-netcore\Wexflow.xml'))) > 0 then
          UpdateWexflowPaths(ExpandConstant('{app}'));
          
        if Pos('install_folder', LoadTextFromFile(ExpandConstant('{app}\Wexflow\Wexflow.Server\appsettings.json'))) > 0 then
          UpdateWexflowConfiguration();
          
        if Pos('install_folder', LoadTextFromFile(ExpandConstant('{app}\Wexflow\install.bat'))) > 0 then
          UpdateInstallBat('');
      end;
      
      // Exécuter install.bat
      Log('Exécution de install.bat...');
      if Exec(ExpandConstant('{app}\Wexflow\install.bat'), '', ExpandConstant('{app}\Wexflow'), SW_SHOW, ewWaitUntilTerminated, ResultCode) then
        Log('install.bat exécuté avec succès. Code de retour: ' + IntToStr(ResultCode))
      else
        Log('Erreur lors de l''exécution de install.bat');
    end;
  end;
end;
