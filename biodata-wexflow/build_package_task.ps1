# Build et Deploy des Custom Tasks Wexflow
# Compatible macOS et Windows

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Build et Deploy des Custom Tasks" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Sauvegarde du répertoire courant
$OriginalDir = Get-Location

try {
    # Navigation vers le dossier des custom tasks
    $ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    $CustomTasksDir = Join-Path $ScriptDir "wexflow-lib-custom-task"
    
    Write-Host "Navigation vers: $CustomTasksDir" -ForegroundColor Yellow
    Set-Location $CustomTasksDir

    # Vérification que le fichier solution existe
    if (-not (Test-Path "WEXFLOW.sln")) {
        Write-Host "ERREUR: Fichier solution WEXFLOW.sln non trouvé !" -ForegroundColor Red
        Read-Host "Appuyez sur Entrée pour continuer"
        exit 1
    }

    # Clean et build de la solution
    Write-Host ""
    Write-Host "Nettoyage des anciens builds..." -ForegroundColor Yellow
    $cleanResult = & dotnet clean WEXFLOW.sln
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERREUR lors du nettoyage !" -ForegroundColor Red
        Read-Host "Appuyez sur Entrée pour continuer"
        exit 1
    }
    
    # Suppression manuelle des dossiers bin et obj pour forcer une reconstruction complète
    Write-Host ""
    Write-Host "Suppression manuelle des dossiers bin et obj..." -ForegroundColor Yellow
    Get-ChildItem -Path $CustomTasksDir -Include bin,obj -Directory -Recurse | ForEach-Object {
        Write-Host "Suppression de $($_.FullName)" -ForegroundColor Gray
        Remove-Item -Path $_.FullName -Recurse -Force
    }

    Write-Host ""
    Write-Host "Compilation en mode Release..." -ForegroundColor Yellow
    
    # Compilation individuelle de chaque projet pour s'assurer que tous sont reconstruits
    $Projects = @(
        "Wexflow.Tasks.BiodataFileWatcher",
        "Wexflow.Tasks.BiodataJobStatusUpdater",
        "Wexflow.Tasks.BiodataResultDownloader"
    )
    
    foreach ($Project in $Projects) {
        Write-Host "Compilation de $Project..." -ForegroundColor Yellow
        $buildResult = & dotnet build "$Project/$Project.csproj" --configuration Release --force
        if ($LASTEXITCODE -ne 0) {
            Write-Host "ERREUR lors de la compilation de $Project !" -ForegroundColor Red
            Read-Host "Appuyez sur Entrée pour continuer"
            exit 1
        }
    }

    # Création des dossiers de destination s'ils n'existent pas
    $DestDir = Join-Path $ScriptDir "wexflow-8.9-windows-netcore\Wexflow.Custom"
    $TasksDir = Join-Path $ScriptDir "wexflow-8.9-windows-netcore\Wexflow-netcore\Tasks"
    
    if (-not (Test-Path $DestDir)) {
        Write-Host "Création du dossier de destination Wexflow.Custom..." -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $DestDir -Force | Out-Null
    }
    
    if (-not (Test-Path $TasksDir)) {
        Write-Host "Création du dossier de destination Tasks..." -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $TasksDir -Force | Out-Null
    }

    # Copie des DLL compilées
    Write-Host ""
    Write-Host "Copie des tâches personnalisées..." -ForegroundColor Yellow

    $Projects = @(
        @{Name = "Wexflow.Tasks.BiodataFileWatcher"; DisplayName = "BiodataFileWatcher"},
        @{Name = "Wexflow.Tasks.BiodataJobStatusUpdater"; DisplayName = "BiodataJobStatusUpdater"},
        @{Name = "Wexflow.Tasks.BiodataResultDownloader"; DisplayName = "BiodataResultDownloader"}
    )

    $CopiedCount = 0
    foreach ($Project in $Projects) {
        $SourceDir = Join-Path $Project.Name "bin\Release\net9.0"

        if (Test-Path $SourceDir) {
            # Copier la DLL principale du projet
            $MainDll = Join-Path $SourceDir "$($Project.Name).dll"
            if (Test-Path $MainDll) {
                # Copie vers Wexflow.Custom
                Copy-Item $MainDll $DestDir -Force
                Write-Host "- $($Project.DisplayName) copiée vers Wexflow.Custom" -ForegroundColor Green

                # Copie vers Wexflow-netcore/Tasks
                Copy-Item $MainDll $TasksDir -Force
                Write-Host "- $($Project.DisplayName) copiée vers Tasks" -ForegroundColor Green

                # Copier les dépendances SQLite si le projet l'utilise
                if ($Project.Name -eq "Wexflow.Tasks.BiodataJobStatusUpdater") {
                    # Liste des DLLs SQLite à copier
                    $SqliteDependencies = @(
                        "System.Data.SQLite.dll",
                        "SQLite.Interop.dll"
                    )
                    
                    # Copier chaque dépendance
                    foreach ($dependency in $SqliteDependencies) {
                        $DllPath = Join-Path $SourceDir $dependency
                        if (Test-Path $DllPath) {
                            Copy-Item $DllPath $DestDir -Force
                            Copy-Item $DllPath $TasksDir -Force
                            Write-Host "  + $dependency copiée" -ForegroundColor Gray
                        } else {
                            Write-Host "  ATTENTION: $dependency non trouvée" -ForegroundColor Yellow
                        }
                    }
                }

                $CopiedCount++
            } else {
                Write-Host "ATTENTION: DLL principale non trouvée pour $($Project.Name)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "ATTENTION: Dossier de build non trouvé pour $($Project.Name)" -ForegroundColor Yellow
        }
    }

    # Copie des workflows personnalisés
    Write-Host ""
    Write-Host "Copie des workflows personnalisés..." -ForegroundColor Yellow
    $WorkflowsSourceDir = Join-Path $ScriptDir "wexflow-worfklows"
    $WorkflowsDestDir = Join-Path $ScriptDir "wexflow-8.9-windows-netcore\Wexflow-netcore\Workflows"

    $WorkflowsCopiedCount = 0
    if (Test-Path $WorkflowsSourceDir) {
        $WorkflowFiles = Get-ChildItem $WorkflowsSourceDir -Filter "*.xml"
        foreach ($WorkflowFile in $WorkflowFiles) {
            Copy-Item $WorkflowFile.FullName $WorkflowsDestDir -Force
            Write-Host "- $($WorkflowFile.Name) copié" -ForegroundColor Green
            $WorkflowsCopiedCount++
        }
    } else {
        Write-Host "ATTENTION: Dossier des workflows non trouvé: $WorkflowsSourceDir" -ForegroundColor Yellow
    }

    # Affichage du résumé
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "  Déploiement terminé !" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Résumé:" -ForegroundColor White
    Write-Host "- $CopiedCount projet(s) custom task déployé(s)" -ForegroundColor Green
    Write-Host "- $WorkflowsCopiedCount workflow(s) copié(s)" -ForegroundColor Green
    Write-Host "- Service Windows compilé" -ForegroundColor Green
    Write-Host ""
    Write-Host "Destinations:" -ForegroundColor White
    Write-Host "- Custom Tasks DLL 1: $DestDir" -ForegroundColor Gray
    Write-Host "- Custom Tasks DLL 2: $TasksDir" -ForegroundColor Gray
    Write-Host "- Workflows: $WorkflowsDestDir" -ForegroundColor Gray
    if (Test-Path $ServiceBuildDir) {
        Write-Host "- Service Windows: $ServiceBuildDir" -ForegroundColor Gray
    }
    Write-Host ""
    
    # Liste des fichiers copiés dans Wexflow.Custom
    $CopiedFiles = Get-ChildItem $DestDir -Filter "*.dll" | Sort-Object Name
    if ($CopiedFiles.Count -gt 0) {
        Write-Host "DLL déployées dans Wexflow.Custom:" -ForegroundColor White
        foreach ($File in $CopiedFiles) {
            Write-Host "  - $($File.Name)" -ForegroundColor Gray
        }
    }
    
    # Liste des fichiers copiés dans Tasks
    $TasksFiles = Get-ChildItem $TasksDir -Filter "*.dll" | Sort-Object Name
    if ($TasksFiles.Count -gt 0) {
        Write-Host "DLL déployées dans Tasks:" -ForegroundColor White
        foreach ($File in $TasksFiles) {
            Write-Host "  - $($File.Name)" -ForegroundColor Gray
        }
    }

    # Liste des workflows copiés
    if ($WorkflowsCopiedCount -gt 0) {
        $CopiedWorkflows = Get-ChildItem $WorkflowsDestDir -Filter "Workflow_Biodata*.xml" | Sort-Object Name
        if ($CopiedWorkflows.Count -gt 0) {
            Write-Host "Workflows copiés:" -ForegroundColor White
            foreach ($Workflow in $CopiedWorkflows) {
                Write-Host "  - $($Workflow.Name)" -ForegroundColor Gray
            }
        }
    }
    
    # Copie des workflows personnalisés
    Write-Host ""
    Write-Host "Copie des workflows personnalisés..." -ForegroundColor Yellow
    $WorkflowsSourceDir = Join-Path $ScriptDir "biodata-wexflow\wexflow-worfklows"
    $WorkflowsDestDir = Join-Path $ScriptDir "biodata-wexflow\wexflow-8.9-windows-netcore\Wexflow-netcore\Workflows"

    if (Test-Path $WorkflowsSourceDir) {
        $WorkflowFiles = Get-ChildItem $WorkflowsSourceDir -Filter "*.xml"
        foreach ($WorkflowFile in $WorkflowFiles) {
            Copy-Item $WorkflowFile.FullName $WorkflowsDestDir -Force
            Write-Host "- $($WorkflowFile.Name) copié" -ForegroundColor Green
        }
    }

    # ========================================
    # COMPILATION DU SERVICE WINDOWS WEXFLOW
    # ========================================
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "  Compilation du Service Windows" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan

    $ServiceProjectDir = Join-Path $ScriptDir "wexflow-8.9-windows-netcore\Wexflow.Server.WindowsService"
    $ServiceProjectFile = Join-Path $ServiceProjectDir "Wexflow.Server.WindowsService.csproj"
    $ServiceBuildDir = Join-Path $ServiceProjectDir "bin\Release\net9.0"

    if (Test-Path $ServiceProjectFile) {
        Write-Host "Compilation du service Windows Wexflow..." -ForegroundColor Yellow

        # Sauvegarde du répertoire courant pour le service
        $CurrentServiceDir = Get-Location

        try {
            # Navigation vers le dossier du service
            Set-Location $ServiceProjectDir

            # Nettoyage du service
            Write-Host "Nettoyage du service Windows..." -ForegroundColor Yellow
            & dotnet clean "Wexflow.Server.WindowsService.csproj" --configuration Release
            if ($LASTEXITCODE -ne 0) {
                Write-Host "ERREUR lors du nettoyage du service !" -ForegroundColor Red
                throw "Erreur de nettoyage du service"
            }

            # Restauration des packages NuGet pour le service
            Write-Host "Restauration des packages NuGet du service..." -ForegroundColor Yellow
            & dotnet restore "Wexflow.Server.WindowsService.csproj"
            if ($LASTEXITCODE -ne 0) {
                Write-Host "ERREUR lors de la restauration des packages du service !" -ForegroundColor Red
                throw "Erreur de restauration du service"
            }

            # Compilation du service
            Write-Host "Compilation du service Windows..." -ForegroundColor Yellow
            & dotnet build "Wexflow.Server.WindowsService.csproj" --configuration Release --no-restore
            if ($LASTEXITCODE -ne 0) {
                Write-Host "ERREUR lors de la compilation du service !" -ForegroundColor Red
                throw "Erreur de compilation du service"
            }

            Write-Host ""
            Write-Host "Service Windows compilé avec succès !" -ForegroundColor Green
            Write-Host "Emplacement: $ServiceBuildDir" -ForegroundColor White

        } finally {
            # Retour au répertoire des custom tasks
            Set-Location $CurrentServiceDir
        }
    } else {
        Write-Host "ATTENTION: Projet du service Windows non trouvé: $ServiceProjectFile" -ForegroundColor Yellow
        Write-Host "Le service Windows ne sera pas compilé." -ForegroundColor Yellow
    }

    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "  Instructions Finales" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "✅ Custom Tasks:" -ForegroundColor Green
    Write-Host "   Vos custom tasks sont prêtes à être utilisées dans Wexflow." -ForegroundColor White
    Write-Host ""
    Write-Host "✅ Service Windows:" -ForegroundColor Green
    Write-Host "   Le service Windows est compilé et prêt à être installé." -ForegroundColor White
    Write-Host ""
    Write-Host "Prochaines étapes pour le service Windows:" -ForegroundColor Yellow
    if (Test-Path $ServiceBuildDir) {
        Write-Host "1. Publier le service: .\wexflow-8.9-windows-netcore\Wexflow.Server.WindowsService\Scripts\Build-WexflowService.ps1" -ForegroundColor Gray
        Write-Host "2. Installer le service: Naviguer vers bin\Publish puis .\Scripts\Install-WexflowService.ps1" -ForegroundColor Gray
        Write-Host "3. Ou utiliser le service compilé directement depuis: $ServiceBuildDir" -ForegroundColor Gray
    } else {
        Write-Host "Le service Windows n'a pas pu être compilé." -ForegroundColor Red
    }
    Write-Host ""

} catch {
    Write-Host "ERREUR: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    # Retour au répertoire original
    Set-Location $OriginalDir
    Read-Host "Appuyez sur Entrée pour continuer"
}
