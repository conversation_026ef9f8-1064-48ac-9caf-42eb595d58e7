<Workflow xmlns="urn:wexflow-schema" id="146" name="Workflow_FilesLoaderEx_RemoveMinModifyDate" description="Workflow_FilesLoaderEx_RemoveMinModifyDate">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
  </Settings>
  <LocalVariables />
  <Tasks>
    <Task id="1" name="FilesLoaderEx" description="Loading files_RemoveMinModifyDate" enabled="true">
      <Setting name="folder" value="C:\WexflowTesting\FilesLoaderEx" />
      <Setting name="removeMinModifyDate" value="2" />
    </Task>
    <Task id="2" name="ListFiles" description="Listing files" enabled="true" />
  </Tasks>
</Workflow>