<Workflow xmlns="urn:wexflow-schema" id="4" name="Workflow_FilesMover" description="Workflow_FilesMover">
	<Settings>
		<Setting name="launchType" value="trigger" />
		<Setting name="enabled" value="true" />
	</Settings>
	<Tasks>
		<Task id="1" name="FilesLoader" description="Loading files" enabled="true">
			<Setting name="file" value="C:\WexflowTesting\file10.txt" />
		</Task>
		<Task id="2" name="FilesMover" description="Moving files to FilesMover folder" enabled="true">
			<Setting name="selectFiles" value="1" />
			<Setting name="destFolder" value="C:\WexflowTesting\FilesMover\" />
			<Setting name="overwrite" value="true" />
		</Task>
	</Tasks>
</Workflow>
