<Workflow xmlns="urn:wexflow-schema" id="12" name="Workflow_ffmpeg" description="Workflow_ffmpeg">
	<Settings>
		<Setting name="launchType" value="trigger" />
		<Setting name="enabled" value="true" />
	</Settings>
	<Tasks>
		<Task id="1" name="FilesLoader" description="Loading WAV files" enabled="true">
			<Setting name="folder" value="C:\WexflowTesting\WAV\" />
		</Task>
		<Task id="2" name="ProcessLauncher" description="WAV to MP3" enabled="true">
			<Setting name="selectFiles" value="1" />
			<Setting name="processPath" value="C:\Program Files\ffmpeg\bin\ffmpeg.exe" /> <!-- You need to install FFMPEG -->
			<Setting name="processCmd" value="-i {$filePath} -codec:a libmp3lame -qscale:a 2 {$output:$fileNameWithoutExtension.mp3}" /> <!-- variables: {$filePath},{$fileName},{$fileNameWithoutExtension}-->
			<Setting name="hideGui" value="true" /> <!-- true|false -->
			<Setting name="generatesFiles" value="true" /> <!-- true|false -->
		</Task>
		<Task id="3" name="FilesMover" description="Moving MP3 files from temp folder" enabled="true">
			<Setting name="selectFiles" value="2" />
			<Setting name="destFolder" value="C:\WexflowTesting\MP3\" />
			<Setting name="overwrite" value="true" />
		</Task>
	</Tasks>
</Workflow>
