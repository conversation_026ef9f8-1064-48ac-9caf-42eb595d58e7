<Workflow xmlns="urn:wexflow-schema" id="139" name="Workflow_FilesLoaderEx_AddMaxCreateDate" description="Workflow_FilesLoaderEx_AddMaxCreateDate">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
  </Settings>
  <LocalVariables />
  <Tasks>
    <Task id="1" name="FilesLoaderEx" description="Loading files_AddMaxCreateDate" enabled="true">
      <Setting name="folder" value="C:\WexflowTesting\FilesLoaderEx" />
      <Setting name="addMaxCreateDate" value="2" />
    </Task>
    <Task id="2" name="ListFiles" description="Listing files" enabled="true" />
  </Tasks>
</Workflow>