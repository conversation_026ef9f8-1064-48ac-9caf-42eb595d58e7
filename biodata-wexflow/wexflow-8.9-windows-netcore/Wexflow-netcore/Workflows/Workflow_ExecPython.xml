﻿<?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="175" name="Workflow_ExecPython" description="Workflow_ExecPython">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
  </Settings>
  <Tasks>
    <Task id="1" name="FilesLoader" description="Loading python scripts" enabled="true">
	    <Setting name="file" value="C:\WexflowTesting\ExecPython\script.py" />
    </Task>
    <Task id="2" name="ExecPython" description="Executing python scripts" enabled="true">
	    <Setting name="pythonPath" value="C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" />
      <Setting name="selectFiles" value="1" />
    </Task>
  </Tasks>
</Workflow>