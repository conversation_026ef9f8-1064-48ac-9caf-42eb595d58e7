<Workflow xmlns="urn:wexflow-schema" id="114" name="Workflow_FilesLoaderGlobalVariables" description="Workflow_FilesLoaderGlobalVariables">
	<Settings>
		<Setting name="launchType" value="trigger" /> <!-- startup|trigger|periodic -->
		<Setting name="enabled" value="true" /> <!-- true|false -->
	</Settings>
	<Tasks>
		<Task id="1" name="FilesLoader" description="Loading files" enabled="true">
			<Setting name="file" value="$file1" />
			<Setting name="file" value="$file2" />
			<Setting name="file" value="$file3" />
		</Task>
		<Task id="2" name="ListFiles" description="Listing files" enabled="true">
		</Task>
	</Tasks>
</Workflow>
