<Workflow xmlns="urn:wexflow-schema" id="136" name="Workflow_Vimeo" description="Workflow_Vimeo">
	<Settings>
		<Setting name="launchType" value="trigger" />
		<Setting name="enabled" value="true" />
	</Settings>
	<Tasks>
		<Task id="1" name="FilesLoader" description="Loading files" enabled="true">
			<Setting name="file" value="C:\WexflowTesting\Vimeo\Vimeo.xml" />
		</Task>
		<Task id="2" name="Vimeo" description="Uploading videos" enabled="true">
			<Setting name="selectFiles" value="1" />
			<Setting name="token" value="TOKEN" />
		</Task>
	</Tasks>
</Workflow>
