<Workflow xmlns="urn:wexflow-schema" id="46" name="Workflow_Sha1" description="Workflow_Sha1">
	<Settings>
		<Setting name="launchType" value="trigger" />
		<Setting name="enabled" value="true" />
	</Settings>
	<Tasks>
		<Task id="1" name="FilesLoader" description="Loading files" enabled="true">
			<Setting name="folder" value="C:\WexflowTesting\Watchfolder1\" />
		</Task>
		<Task id="2" name="Sha1" description="Generating Sha1 hashes" enabled="true">
			<Setting name="selectFiles" value="1" />
		</Task>
		<Task id="3" name="FilesMover" description="Moving Sha1 files from temp folder" enabled="true">
			<Setting name="selectFiles" value="2" />
			<Setting name="destFolder" value="C:\WexflowTesting\Sha1\" />
			<Setting name="overwrite" value="true" />
		</Task>
	</Tasks>
</Workflow>
