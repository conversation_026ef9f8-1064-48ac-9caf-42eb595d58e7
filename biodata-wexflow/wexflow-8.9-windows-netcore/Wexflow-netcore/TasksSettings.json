{"Approval": [], "ApprovalRecordsCreator": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "created<PERSON>y", "Required": true, "Type": "user", "List": [], "DefaultValue": ""}], "ApprovalWorkflowsCreator": [{"Name": "assignedTo", "Required": true, "Type": "user", "List": [], "DefaultValue": ""}, {"Name": "approver", "Required": true, "Type": "user", "List": [], "DefaultValue": ""}, {"Name": "deleteWorkflowOnApproval", "Required": false, "Type": "bool", "List": [], "DefaultValue": "true"}], "ApproveRecord": [{"Name": "record", "Required": true, "Type": "record", "List": [], "DefaultValue": ""}, {"Name": "assignedTo", "Required": true, "Type": "user", "List": [], "DefaultValue": ""}, {"Name": "reminderDelay", "Required": false, "Type": "string", "List": [], "DefaultValue": "3.00:00:00"}, {"Name": "onApproved", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "onRejected", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "onDueDateReached", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "onReminderDateReached", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "onDeleted", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "onStopped", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "deleteWorkflowOnApproval", "Required": false, "Type": "bool", "List": [], "DefaultValue": "false"}], "CsvToJson": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "separator", "Required": false, "Type": "string", "List": [], "DefaultValue": ";"}], "CsvToSql": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "tableName", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "separator", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "CsvToXml": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "CsvToYaml": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "separator", "Required": false, "Type": "string", "List": [], "DefaultValue": ";"}], "EnvironmentVariable": [{"Name": "name", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "ExecPython": [{"Name": "pythonPath", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "FileContentMatch": [{"Name": "file", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "folder", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "recursive", "Required": false, "Type": "bool", "List": [], "DefaultValue": "false"}, {"Name": "pattern", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "FileExists": [{"Name": "file", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "FileMatch": [{"Name": "dir", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "pattern", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "recursive", "Required": false, "Type": "bool", "List": [], "DefaultValue": "false"}], "FileNotExist": [{"Name": "file", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "FileNotMatch": [{"Name": "dir", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "pattern", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "recursive", "Required": false, "Type": "bool", "List": [], "DefaultValue": "false"}], "FilesConcat": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "FilesCopier": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "destFolder", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "overwrite", "Required": false, "Type": "bool", "List": [], "DefaultValue": "false"}, {"Name": "preserveFolderStructFrom", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "allowCreateDirectory", "Required": false, "Type": "bool", "List": [], "DefaultValue": "true"}], "FilesDecryptor": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "FilesDiff": [{"Name": "oldFile", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "newFile", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "FilesEncryptor": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "FilesEqual": [{"Name": "file1", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "file2", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "FilesExist": [{"Name": "file", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "folder", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}], "FilesInfo": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "FilesJoiner": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "destFolder", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "overwrite", "Required": false, "Type": "bool", "List": [], "DefaultValue": "false"}], "FilesLoader": [{"Name": "file", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "folder", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "regexPattern", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "recursive", "Required": false, "Type": "bool", "List": [], "DefaultValue": "false"}], "FilesLoaderEx": [{"Name": "file", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "folder", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "regexPattern", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "recursive", "Required": false, "Type": "bool", "List": [], "DefaultValue": "false"}, {"Name": "addMaxCreateDate", "Required": false, "Type": "int", "List": [], "DefaultValue": "0"}, {"Name": "addMinCreateDate", "Required": false, "Type": "int", "List": [], "DefaultValue": "0"}, {"Name": "addMaxModifyDate", "Required": false, "Type": "int", "List": [], "DefaultValue": "0"}, {"Name": "addMinModifyDate", "Required": false, "Type": "int", "List": [], "DefaultValue": "0"}, {"Name": "removeMaxCreateDate", "Required": false, "Type": "int", "List": [], "DefaultValue": "0"}, {"Name": "removeMinCreateDate", "Required": false, "Type": "int", "List": [], "DefaultValue": "0"}, {"Name": "removeMaxModifyDate", "Required": false, "Type": "int", "List": [], "DefaultValue": "0"}, {"Name": "removeMinModifyDate", "Required": false, "Type": "int", "List": [], "DefaultValue": "0"}], "FilesMover": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "destFolder", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "overwrite", "Required": false, "Type": "bool", "List": [], "DefaultValue": "false"}, {"Name": "preserveFolderStructFrom", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "allowCreateDirectory", "Required": false, "Type": "bool", "List": [], "DefaultValue": "true"}], "FilesRemover": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "FilesRenamer": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "overwrite", "Required": false, "Type": "bool", "List": [], "DefaultValue": "false"}], "FilesSplitter": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "chunkSize", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "FileSystemWatcher": [{"Name": "folderToWatch", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "filter", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "includeSubFolders", "Required": false, "Type": "bool", "List": [], "DefaultValue": "false"}, {"Name": "onFileFound", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "onFileCreated", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "onFileChanged", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "onFileDeleted", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "safeMode", "Required": false, "Type": "bool", "List": [], "DefaultValue": "true"}], "FolderExists": [{"Name": "folder", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "Ftp": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "command", "Required": true, "Type": "list", "List": ["list", "upload", "download", "delete"], "DefaultValue": ""}, {"Name": "protocol", "Required": true, "Type": "list", "List": ["ftp", "ftps", "sftp"], "DefaultValue": ""}, {"Name": "encryption", "Required": false, "Type": "list", "List": ["explicit", "implicit"], "DefaultValue": ""}, {"Name": "server", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "port", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "user", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "password", "Required": true, "Type": "password", "List": [], "DefaultValue": ""}, {"Name": "privateKeyPath", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "passphrase", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "path", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "retryCount", "Required": false, "Type": "int", "List": [], "DefaultValue": "3"}, {"Name": "retryTimeout", "Required": false, "Type": "int", "List": [], "DefaultValue": "1500"}, {"Name": "debugLogs", "Required": false, "Type": "bool", "List": [], "DefaultValue": "false"}], "Guid": [{"Name": "guid<PERSON>ount", "Required": false, "Type": "int", "List": [], "DefaultValue": "1"}], "Http": [{"Name": "url", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "HttpDelete": [{"Name": "url", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "authorizationScheme", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "authorizationParameter", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}], "HttpGet": [{"Name": "url", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "authorizationScheme", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "authorizationParameter", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}], "HttpPatch": [{"Name": "url", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "authorizationScheme", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "authorizationParameter", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "payload", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "type", "Required": false, "Type": "string", "List": [], "DefaultValue": "application/json"}], "HttpPost": [{"Name": "url", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "authorizationScheme", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "authorizationParameter", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "payload", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "type", "Required": false, "Type": "string", "List": [], "DefaultValue": "application/json"}], "HttpPut": [{"Name": "url", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "authorizationScheme", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "authorizationParameter", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "payload", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "type", "Required": false, "Type": "string", "List": [], "DefaultValue": "application/json"}], "ImagesConcat": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "ImagesCropper": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "width", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "height", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "x", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "y", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "ImagesOverlay": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "ImagesResizer": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "width", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "height", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "ImagesTransformer": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "outputFilePattern", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "outputFormat", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "InstagramUploadImage": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "username", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "password", "Required": true, "Type": "password", "List": [], "DefaultValue": ""}], "InstagramUploadVideo": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "username", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "password", "Required": true, "Type": "password", "List": [], "DefaultValue": ""}], "JsonToYaml": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "ListEntities": [], "MailsReceiver": [{"Name": "host", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "port", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "enableSsl", "Required": false, "Type": "bool", "List": [], "DefaultValue": "true"}, {"Name": "user", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "password", "Required": true, "Type": "password", "List": [], "DefaultValue": ""}, {"Name": "messageCount", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "deleteMessages", "Required": false, "Type": "bool", "List": [], "DefaultValue": ""}, {"Name": "protocol", "Required": false, "Type": "list", "List": ["pop3", "imap"], "DefaultValue": "pop3"}], "ListFiles": [], "MailsSender": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "selectAttachments", "Required": false, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "host", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "port", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "enableSsl", "Required": true, "Type": "bool", "List": [], "DefaultValue": ""}, {"Name": "user", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "password", "Required": true, "Type": "password", "List": [], "DefaultValue": ""}], "Md5": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "MessageCorrect": [{"Name": "checkString", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "Mkdir": [{"Name": "folder", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "Movedir": [{"Name": "folder", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "destinationFolder", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "overwrite", "Required": false, "Type": "bool", "List": [], "DefaultValue": ""}], "Now": [{"Name": "culture", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "format", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "Ping": [{"Name": "server", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "ProcessInfo": [{"Name": "processName", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "ProcessLauncher": [{"Name": "selectFiles", "Required": false, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "processPath", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "processCmd", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "<PERSON><PERSON><PERSON>", "Required": true, "Type": "bool", "List": [], "DefaultValue": ""}, {"Name": "generatesFiles", "Required": true, "Type": "bool", "List": [], "DefaultValue": ""}, {"Name": "ignoreExitCode", "Required": true, "Type": "bool", "List": [], "DefaultValue": "false"}], "Reddit": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "appId", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "refreshToken", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "RedditListComments": [{"Name": "appId", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "refreshToken", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "maxResults", "Required": false, "Type": "int", "List": [], "DefaultValue": ""}], "RedditListPosts": [{"Name": "appId", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "refreshToken", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "maxResults", "Required": false, "Type": "int", "List": [], "DefaultValue": ""}], "Rmdir": [{"Name": "folder", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "ScssToCss": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "Sha1": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "Sha256": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "Sha512": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "Slack": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "token", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "Sql": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "type", "Required": true, "Type": "list", "List": ["sqlserver", "access", "oracle", "mysql", "sqlite", "postgresql", "teradata", "odbc"], "DefaultValue": ""}, {"Name": "connectionString", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "sql", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}], "SqlToCsv": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "type", "Required": true, "Type": "list", "List": ["sqlserver", "access", "oracle", "mysql", "sqlite", "postgresql", "teradata", "odbc"], "DefaultValue": ""}, {"Name": "connectionString", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "sql", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "doNotGenerateFilesIfEmpty", "Required": false, "Type": "bool", "List": [], "DefaultValue": ""}], "SqlToXml": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "type", "Required": true, "Type": "list", "List": ["sqlserver", "access", "oracle", "mysql", "sqlite", "postgresql", "teradata", "odbc"], "DefaultValue": ""}, {"Name": "connectionString", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "sql", "Required": false, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "excludeEmptyValues", "Required": false, "Type": "bool", "List": [], "DefaultValue": ""}], "SshCmd": [{"Name": "host", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "port", "Required": false, "Type": "int", "List": [], "DefaultValue": "22"}, {"Name": "username", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "password", "Required": true, "Type": "password", "List": [], "DefaultValue": ""}, {"Name": "cmd", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "timeout", "Required": false, "Type": "int", "List": [], "DefaultValue": "60"}], "SubWorkflow": [{"Name": "id", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "mode", "Required": false, "Type": "list", "List": ["sync", "async"], "DefaultValue": "sync"}, {"Name": "action", "Required": false, "Type": "list", "List": ["start", "stop", "approve", "reject"], "DefaultValue": "start"}], "Tar": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "zipFileName", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "TextsDecryptor": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "TextsEncryptor": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "Tgz": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "tgzFileName", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "Torrent": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "saveFolder", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "Touch": [{"Name": "file", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "Twilio": [{"Name": "accountSid", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "authToken", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "from", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "to", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "message", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "Twitter": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "consumerKey", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "consumerSecret", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "accessToken", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "accessTokenSecret", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "UglifyCss": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "UglifyHtml": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "UglifyJs": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "Untar": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "destDir", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "Untgz": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "destDir", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "Unzip": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "destDir", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "password", "Required": false, "Type": "password", "List": [], "DefaultValue": ""}, {"Name": "overwrite", "Required": false, "Type": "bool", "List": [], "DefaultValue": "false"}, {"Name": "createSubDirectoryWithDateTime", "Required": false, "Type": "bool", "List": [], "DefaultValue": ""}], "Vimeo": [{"Name": "token", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "VimeoListUploads": [{"Name": "token", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "userId", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "Wait": [{"Name": "duration", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "XmlToCsv": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "Xslt": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "xsltPath", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "outputFormat", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "removeWexflowProcessingNodes", "Required": false, "Type": "bool", "List": [], "DefaultValue": "true"}], "YouTube": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "user", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "applicationName", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "clientSecrets", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "YouTubeListUploads": [{"Name": "user", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "applicationName", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "clientSecrets", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}], "YouTubeSearch": [{"Name": "user", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "applicationName", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "keyword", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}, {"Name": "maxResults", "Required": false, "Type": "int", "List": [], "DefaultValue": ""}], "YamlToJson": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}], "Zip": [{"Name": "selectFiles", "Required": true, "Type": "int", "List": [], "DefaultValue": ""}, {"Name": "zipFileName", "Required": true, "Type": "string", "List": [], "DefaultValue": ""}]}