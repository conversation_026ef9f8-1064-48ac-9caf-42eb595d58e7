using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using log4net;
using System.Reflection;
using System.Diagnostics;

namespace Wexflow.Server.WindowsService
{
    /// <summary>
    /// Wrapper pour le moteur Wexflow existant
    /// Cette classe encapsule la logique Wexflow existante pour l'adapter au service Windows
    /// </summary>
    public class WexflowEngineWrapper : IWexflowEngineWrapper
    {
        private readonly ILogger<WexflowEngineWrapper> _logger;
        private static readonly ILog _log4netLogger = LogManager.GetLogger(typeof(WexflowEngineWrapper));
        
        private WexflowConfiguration _config;
        private Process _wexflowProcess;
        private bool _isInitialized;
        private bool _isRunning;
        private readonly object _lockObject = new object();

        public bool IsRunning 
        { 
            get 
            { 
                lock (_lockObject)
                {
                    return _isRunning && _wexflowProcess != null && !_wexflowProcess.HasExited;
                }
            } 
        }

        public bool IsInitialized 
        { 
            get 
            { 
                lock (_lockObject)
                {
                    return _isInitialized;
                }
            } 
        }

        public WexflowEngineWrapper(ILogger<WexflowEngineWrapper> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Initialise le moteur Wexflow
        /// </summary>
        public async Task InitializeAsync(WexflowConfiguration config, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Initialisation du moteur Wexflow...");
                _log4netLogger.Info("Initialisation du moteur Wexflow...");

                _config = config ?? throw new ArgumentNullException(nameof(config));
                _config.Validate();

                // Vérification des fichiers requis
                await ValidateWexflowFilesAsync();

                lock (_lockObject)
                {
                    _isInitialized = true;
                }

                _logger.LogInformation("Moteur Wexflow initialisé avec succès");
                _log4netLogger.Info("Moteur Wexflow initialisé avec succès");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'initialisation du moteur Wexflow");
                _log4netLogger.Error("Erreur lors de l'initialisation du moteur Wexflow", ex);
                throw;
            }
        }

        /// <summary>
        /// Démarre le moteur Wexflow
        /// </summary>
        public async Task StartAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                if (!IsInitialized)
                    throw new InvalidOperationException("Le moteur doit être initialisé avant d'être démarré");

                if (IsRunning)
                {
                    _logger.LogWarning("Le moteur Wexflow est déjà en cours d'exécution");
                    return;
                }

                _logger.LogInformation("Démarrage du moteur Wexflow...");
                _log4netLogger.Info("Démarrage du moteur Wexflow...");

                // Démarrage du processus Wexflow.Server.dll
                await StartWexflowProcessAsync(cancellationToken);

                lock (_lockObject)
                {
                    _isRunning = true;
                }

                _logger.LogInformation("Moteur Wexflow démarré avec succès");
                _log4netLogger.Info("Moteur Wexflow démarré avec succès");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors du démarrage du moteur Wexflow");
                _log4netLogger.Error("Erreur lors du démarrage du moteur Wexflow", ex);
                throw;
            }
        }

        /// <summary>
        /// Arrête le moteur Wexflow
        /// </summary>
        public async Task StopAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                if (!IsRunning)
                {
                    _logger.LogWarning("Le moteur Wexflow n'est pas en cours d'exécution");
                    return;
                }

                _logger.LogInformation("Arrêt du moteur Wexflow...");
                _log4netLogger.Info("Arrêt du moteur Wexflow...");

                await StopWexflowProcessAsync(cancellationToken);

                lock (_lockObject)
                {
                    _isRunning = false;
                }

                _logger.LogInformation("Moteur Wexflow arrêté avec succès");
                _log4netLogger.Info("Moteur Wexflow arrêté avec succès");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'arrêt du moteur Wexflow");
                _log4netLogger.Error("Erreur lors de l'arrêt du moteur Wexflow", ex);
                throw;
            }
        }

        /// <summary>
        /// Vérifie l'état de santé du moteur
        /// </summary>
        public async Task HealthCheckAsync()
        {
            try
            {
                if (!IsInitialized)
                    throw new InvalidOperationException("Le moteur n'est pas initialisé");

                // Vérification que le processus est toujours actif
                if (_wexflowProcess != null && _wexflowProcess.HasExited)
                {
                    _logger.LogError($"Le processus Wexflow s'est arrêté de manière inattendue. Code de sortie: {_wexflowProcess.ExitCode}");
                    _log4netLogger.Error($"Le processus Wexflow s'est arrêté de manière inattendue. Code de sortie: {_wexflowProcess.ExitCode}");
                    
                    lock (_lockObject)
                    {
                        _isRunning = false;
                    }

                    // Tentative de redémarrage automatique
                    _logger.LogInformation("Tentative de redémarrage automatique du moteur Wexflow...");
                    await StartAsync();
                }

                // Vérification additionnelle via HTTP si nécessaire
                // TODO: Implémenter une vérification HTTP sur le port configuré
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la vérification de santé");
                _log4netLogger.Error("Erreur lors de la vérification de santé", ex);
                throw;
            }
        }

        /// <summary>
        /// Valide que tous les fichiers Wexflow requis sont présents
        /// </summary>
        private async Task ValidateWexflowFilesAsync()
        {
            var baseDirectory = AppContext.BaseDirectory;
            
            // Vérification du fichier principal Wexflow.Server.dll
            var wexflowServerDll = Path.Combine(baseDirectory, "Wexflow.Server.dll");
            if (!File.Exists(wexflowServerDll))
                throw new FileNotFoundException($"Fichier Wexflow.Server.dll non trouvé: {wexflowServerDll}");

            // Vérification du fichier de configuration
            var configPath = Path.IsPathRooted(_config.WexflowSettingsFile) 
                ? _config.WexflowSettingsFile 
                : Path.Combine(baseDirectory, "..", "Wexflow-netcore", _config.WexflowSettingsFile);
            
            if (!File.Exists(configPath))
                throw new FileNotFoundException($"Fichier de configuration Wexflow non trouvé: {configPath}");

            _logger.LogInformation($"Fichiers Wexflow validés - DLL: {wexflowServerDll}, Config: {configPath}");
            
            await Task.CompletedTask;
        }

        /// <summary>
        /// Démarre le processus Wexflow.Server.dll
        /// </summary>
        private async Task StartWexflowProcessAsync(CancellationToken cancellationToken)
        {
            var baseDirectory = AppContext.BaseDirectory;
            var wexflowServerDll = Path.Combine(baseDirectory, "Wexflow.Server.dll");

            var startInfo = new ProcessStartInfo
            {
                FileName = "dotnet",
                Arguments = $"\"{wexflowServerDll}\"",
                WorkingDirectory = baseDirectory,
                UseShellExecute = false,
                CreateNoWindow = true,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                RedirectStandardInput = true
            };

            _wexflowProcess = new Process { StartInfo = startInfo };
            
            // Gestion des sorties du processus
            _wexflowProcess.OutputDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    _logger.LogInformation($"Wexflow Output: {e.Data}");
                    _log4netLogger.Info($"Wexflow Output: {e.Data}");
                }
            };

            _wexflowProcess.ErrorDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    _logger.LogError($"Wexflow Error: {e.Data}");
                    _log4netLogger.Error($"Wexflow Error: {e.Data}");
                }
            };

            if (!_wexflowProcess.Start())
                throw new InvalidOperationException("Impossible de démarrer le processus Wexflow");

            _wexflowProcess.BeginOutputReadLine();
            _wexflowProcess.BeginErrorReadLine();

            // Attendre un peu pour s'assurer que le processus démarre correctement
            await Task.Delay(5000, cancellationToken);

            if (_wexflowProcess.HasExited)
                throw new InvalidOperationException($"Le processus Wexflow s'est arrêté immédiatement. Code de sortie: {_wexflowProcess.ExitCode}");
        }

        /// <summary>
        /// Arrête le processus Wexflow
        /// </summary>
        private async Task StopWexflowProcessAsync(CancellationToken cancellationToken)
        {
            if (_wexflowProcess == null)
                return;

            try
            {
                // Tentative d'arrêt propre
                if (!_wexflowProcess.HasExited)
                {
                    _wexflowProcess.CloseMainWindow();
                    
                    // Attendre l'arrêt gracieux
                    var timeout = TimeSpan.FromSeconds(_config.ShutdownTimeoutSeconds);
                    if (!_wexflowProcess.WaitForExit((int)timeout.TotalMilliseconds))
                    {
                        _logger.LogWarning("Arrêt forcé du processus Wexflow (timeout atteint)");
                        _wexflowProcess.Kill();
                        await Task.Delay(2000, cancellationToken);
                    }
                }
            }
            finally
            {
                _wexflowProcess?.Dispose();
                _wexflowProcess = null;
            }
        }

        /// <summary>
        /// Libération des ressources
        /// </summary>
        public void Dispose()
        {
            try
            {
                StopAsync().GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la libération des ressources");
            }
        }
    }
}
