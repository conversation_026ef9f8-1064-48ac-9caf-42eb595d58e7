{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Wexflow.Server.WindowsService": "Information"}, "EventLog": {"LogLevel": {"Default": "Information", "Microsoft": "Warning"}, "SourceName": "Wexflow Service", "LogName": "Application"}}, "Wexflow": {"WexflowSettingsFile": "Wexflow-netcore\\Wexflow.xml", "LogLevel": "Info", "WexflowServicePort": 8000, "SuperAdminUsername": "admin", "EnableWorkflowsHotFolder": true, "EnableRecordsHotFolder": true, "EnableEmailNotifications": false, "DateTimeFormat": "dd-MM-yyyy HH:mm:ss", "SmtpHost": "in-v3.mailjet.com", "SmtpPort": 587, "SmtpEnableSsl": true, "SmtpUser": "user", "SmtpPassword": "password", "SmtpFrom": "user", "HealthCheckIntervalSeconds": 30, "StartupTimeoutSeconds": 120, "ShutdownTimeoutSeconds": 60}, "AllowedHosts": "*"}