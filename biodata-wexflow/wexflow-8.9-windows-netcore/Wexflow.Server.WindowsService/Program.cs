using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System;
using System.IO;
using System.Threading.Tasks;

namespace Wexflow.Server.WindowsService
{
    /// <summary>
    /// Point d'entrée principal pour le service Windows Wexflow
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            try
            {
                // Configuration du répertoire de base
                var baseDirectory = AppContext.BaseDirectory;
                Directory.SetCurrentDirectory(baseDirectory);

                // Création du host avec support Windows Service
                var host = CreateHostBuilder(args).Build();

                // Démarrage du service
                await host.RunAsync();
            }
            catch (Exception ex)
            {
                // Log critique en cas d'erreur de démarrage
                Console.WriteLine($"Erreur critique lors du démarrage du service Wexflow: {ex}");
                
                // Écriture dans le journal d'événements Windows si possible
                try
                {
                    using var eventLog = new System.Diagnostics.EventLog("Application");
                    eventLog.Source = "Wexflow Service";
                    eventLog.WriteEntry($"Erreur critique: {ex.Message}", 
                        System.Diagnostics.EventLogEntryType.Error);
                }
                catch
                {
                    // Ignore les erreurs de log d'événements
                }

                Environment.Exit(1);
            }
        }

        /// <summary>
        /// Création et configuration du host builder
        /// </summary>
        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .UseWindowsService(options =>
                {
                    options.ServiceName = "WexflowService";
                })
                .ConfigureAppConfiguration((context, config) =>
                {
                    // Configuration des fichiers de configuration
                    var baseDirectory = AppContext.BaseDirectory;
                    
                    config.SetBasePath(baseDirectory)
                          .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                          .AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", 
                                     optional: true, reloadOnChange: true)
                          .AddEnvironmentVariables()
                          .AddCommandLine(args);
                })
                .ConfigureLogging((context, logging) =>
                {
                    // Configuration du logging
                    logging.ClearProviders();
                    
                    // Log vers la console (pour le mode debug)
                    if (context.HostingEnvironment.IsDevelopment())
                    {
                        logging.AddConsole();
                    }
                    
                    // Log vers le journal d'événements Windows
                    logging.AddEventLog(settings =>
                    {
                        settings.SourceName = "Wexflow Service";
                        settings.LogName = "Application";
                    });
                    
                    // Log vers fichier (via log4net sera configuré dans le service)
                    logging.SetMinimumLevel(LogLevel.Information);
                })
                .ConfigureServices((context, services) =>
                {
                    // Enregistrement du service principal
                    services.AddHostedService<WexflowWindowsService>();
                    
                    // Configuration Wexflow depuis appsettings.json
                    services.Configure<WexflowConfiguration>(
                        context.Configuration.GetSection("Wexflow"));
                    
                    // Services additionnels si nécessaire
                    services.AddSingleton<IWexflowEngineWrapper, WexflowEngineWrapper>();
                });
    }
}
