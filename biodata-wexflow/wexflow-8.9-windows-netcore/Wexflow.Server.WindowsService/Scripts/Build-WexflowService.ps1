<#
.SYNOPSIS
    Script de build et publication du service Windows Wexflow

.DESCRIPTION
    Ce script compile et publie le service Windows Wexflow avec toutes ses dépendances.

.PARAMETER Configuration
    Configuration de build (Debug ou Release)

.PARAMETER OutputPath
    Chemin de sortie pour la publication (optionnel)

.PARAMETER SelfContained
    Créer une publication autonome (inclut le runtime .NET)

.EXAMPLE
    .\Build-WexflowService.ps1
    
.EXAMPLE
    .\Build-WexflowService.ps1 -Configuration Release -SelfContained
#>

param(
    [ValidateSet("Debug", "Release")]
    [string]$Configuration = "Release",
    
    [string]$OutputPath = "",
    
    [switch]$SelfContained = $false
)

# Configuration
$ErrorActionPreference = "Stop"

function Write-ColoredOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Build-WexflowWindowsService {
    try {
        Write-ColoredOutput "========================================" "Cyan"
        Write-ColoredOutput "  Build du Service Windows Wexflow" "Cyan"
        Write-ColoredOutput "========================================" "Cyan"
        Write-Host ""

        # Détermination des chemins
        $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
        $projectDir = Split-Path -Parent $scriptDir
        $projectFile = Join-Path $projectDir "Wexflow.Server.WindowsService.csproj"
        
        if ([string]::IsNullOrEmpty($OutputPath)) {
            $OutputPath = Join-Path $projectDir "bin\Publish"
        }

        Write-ColoredOutput "Configuration:" "Yellow"
        Write-ColoredOutput "- Projet: $projectFile" "White"
        Write-ColoredOutput "- Configuration: $Configuration" "White"
        Write-ColoredOutput "- Sortie: $OutputPath" "White"
        Write-ColoredOutput "- Autonome: $SelfContained" "White"
        Write-Host ""

        # Vérification que le fichier projet existe
        if (-not (Test-Path $projectFile)) {
            throw "Fichier projet non trouvé: $projectFile"
        }

        # Nettoyage des builds précédents
        Write-ColoredOutput "Nettoyage des builds précédents..." "Yellow"
        & dotnet clean $projectFile --configuration $Configuration
        if ($LASTEXITCODE -ne 0) {
            throw "Erreur lors du nettoyage"
        }

        # Restauration des packages NuGet
        Write-ColoredOutput "Restauration des packages NuGet..." "Yellow"
        & dotnet restore $projectFile
        if ($LASTEXITCODE -ne 0) {
            throw "Erreur lors de la restauration des packages"
        }

        # Build du projet
        Write-ColoredOutput "Compilation du projet..." "Yellow"
        & dotnet build $projectFile --configuration $Configuration --no-restore
        if ($LASTEXITCODE -ne 0) {
            throw "Erreur lors de la compilation"
        }

        # Publication
        Write-ColoredOutput "Publication du service..." "Yellow"
        
        $publishArgs = @(
            "publish", $projectFile,
            "--configuration", $Configuration,
            "--output", $OutputPath,
            "--runtime", "win-x64",
            "--no-build"
        )
        
        if ($SelfContained) {
            $publishArgs += "--self-contained", "true"
            $publishArgs += "--property:PublishSingleFile=true"
        } else {
            $publishArgs += "--self-contained", "false"
        }
        
        & dotnet @publishArgs
        if ($LASTEXITCODE -ne 0) {
            throw "Erreur lors de la publication"
        }

        # Copie des fichiers Wexflow existants
        Write-ColoredOutput "Copie des fichiers Wexflow..." "Yellow"
        $wexflowServerDir = Join-Path (Split-Path -Parent $projectDir) "Wexflow.Server"
        
        if (Test-Path $wexflowServerDir) {
            # Copie des DLL et fichiers de configuration
            $filesToCopy = @("*.dll", "*.exe", "*.config", "*.json")
            foreach ($pattern in $filesToCopy) {
                $files = Get-ChildItem -Path $wexflowServerDir -Filter $pattern
                foreach ($file in $files) {
                    $destPath = Join-Path $OutputPath $file.Name
                    Copy-Item -Path $file.FullName -Destination $destPath -Force
                    Write-ColoredOutput "  Copié: $($file.Name)" "Gray"
                }
            }
        } else {
            Write-ColoredOutput "Attention: Dossier Wexflow.Server non trouvé: $wexflowServerDir" "Yellow"
        }

        # Copie du dossier Wexflow-netcore
        Write-ColoredOutput "Copie de la configuration Wexflow..." "Yellow"
        $wexflowConfigDir = Join-Path (Split-Path -Parent $projectDir) "Wexflow-netcore"
        $destConfigDir = Join-Path $OutputPath "Wexflow-netcore"
        
        if (Test-Path $wexflowConfigDir) {
            if (Test-Path $destConfigDir) {
                Remove-Item -Path $destConfigDir -Recurse -Force
            }
            Copy-Item -Path $wexflowConfigDir -Destination $destConfigDir -Recurse -Force
            Write-ColoredOutput "  Configuration Wexflow copiée" "Gray"
        } else {
            Write-ColoredOutput "Attention: Dossier de configuration Wexflow non trouvé: $wexflowConfigDir" "Yellow"
        }

        # Création du dossier de logs
        $logsDir = Join-Path $OutputPath "Logs"
        if (-not (Test-Path $logsDir)) {
            New-Item -ItemType Directory -Path $logsDir -Force | Out-Null
            Write-ColoredOutput "  Dossier de logs créé" "Gray"
        }

        # Copie des scripts de gestion
        Write-ColoredOutput "Copie des scripts de gestion..." "Yellow"
        $scriptsSourceDir = Join-Path $projectDir "Scripts"
        $scriptsDestDir = Join-Path $OutputPath "Scripts"
        
        if (Test-Path $scriptsSourceDir) {
            if (Test-Path $scriptsDestDir) {
                Remove-Item -Path $scriptsDestDir -Recurse -Force
            }
            Copy-Item -Path $scriptsSourceDir -Destination $scriptsDestDir -Recurse -Force
            Write-ColoredOutput "  Scripts de gestion copiés" "Gray"
        }

        # Génération d'informations sur le build
        $buildInfo = @{
            BuildDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            Configuration = $Configuration
            SelfContained = $SelfContained
            DotNetVersion = (& dotnet --version)
            OutputPath = $OutputPath
        }
        
        $buildInfoJson = $buildInfo | ConvertTo-Json -Depth 2
        $buildInfoPath = Join-Path $OutputPath "build-info.json"
        $buildInfoJson | Out-File -FilePath $buildInfoPath -Encoding UTF8
        
        Write-Host ""
        Write-ColoredOutput "========================================" "Cyan"
        Write-ColoredOutput "  Build terminé avec succès !" "Cyan"
        Write-ColoredOutput "========================================" "Cyan"
        Write-Host ""
        
        Write-ColoredOutput "Résumé du build:" "White"
        Write-ColoredOutput "- Configuration: $Configuration" "Green"
        Write-ColoredOutput "- Type: $(if ($SelfContained) { 'Autonome' } else { 'Framework-dépendant' })" "Green"
        Write-ColoredOutput "- Sortie: $OutputPath" "Green"
        Write-Host ""
        
        # Liste des fichiers générés
        $publishedFiles = Get-ChildItem -Path $OutputPath -File | Sort-Object Name
        Write-ColoredOutput "Fichiers générés ($($publishedFiles.Count)):" "White"
        foreach ($file in $publishedFiles | Select-Object -First 10) {
            $sizeKB = [math]::Round($file.Length / 1KB, 1)
            Write-ColoredOutput "  - $($file.Name) (${sizeKB} KB)" "Gray"
        }
        if ($publishedFiles.Count -gt 10) {
            Write-ColoredOutput "  ... et $($publishedFiles.Count - 10) autres fichiers" "Gray"
        }
        Write-Host ""
        
        Write-ColoredOutput "Prochaines étapes:" "White"
        Write-ColoredOutput "1. Tester le service: .\Wexflow.Server.WindowsService.exe" "Yellow"
        Write-ColoredOutput "2. Installer le service: .\Scripts\Install-WexflowService.ps1" "Yellow"
        Write-ColoredOutput "3. Gérer le service: .\Scripts\Manage-WexflowService.ps1 -Action Status" "Yellow"
        Write-Host ""

    }
    catch {
        Write-ColoredOutput "ERREUR: $($_.Exception.Message)" "Red"
        Write-Host ""
        Write-ColoredOutput "Le build a échoué. Vérifiez les points suivants:" "Yellow"
        Write-ColoredOutput "1. .NET 9.0 SDK est installé" "Yellow"
        Write-ColoredOutput "2. Tous les fichiers sources sont présents" "Yellow"
        Write-ColoredOutput "3. Aucun processus ne verrouille les fichiers de sortie" "Yellow"
        exit 1
    }
}

# Exécution du build
Build-WexflowWindowsService
