#Requires -RunAsAdministrator

<#
.SYNOPSIS
    Installe le service Windows Wexflow

.DESCRIPTION
    Ce script installe et configure le service Windows Wexflow.
    Il doit être exécuté avec des privilèges administrateur.

.PARAMETER ServicePath
    Chemin vers l'exécutable du service (optionnel, détecté automatiquement)

.PARAMETER ServiceName
    Nom du service (par défaut: WexflowService)

.PARAMETER DisplayName
    Nom d'affichage du service (par défaut: Wexflow Workflow Engine Service)

.PARAMETER StartupType
    Type de démarrage du service (Automatic, Manual, Disabled)

.EXAMPLE
    .\Install-WexflowService.ps1
    
.EXAMPLE
    .\Install-WexflowService.ps1 -StartupType Manual
#>

param(
    [string]$ServicePath = "",
    [string]$ServiceName = "WexflowService",
    [string]$DisplayName = "Wexflow Workflow Engine Service",
    [ValidateSet("Automatic", "Manual", "Disabled")]
    [string]$StartupType = "Automatic"
)

# Configuration
$ErrorActionPreference = "Stop"
$ServiceDescription = "Service de moteur de workflow Wexflow pour l'automatisation des processus métier"

function Write-ColoredOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Get-ServiceExecutablePath {
    $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    $parentDir = Split-Path -Parent $scriptDir
    $exePath = Join-Path $parentDir "Wexflow.Server.WindowsService.exe"
    
    if (-not (Test-Path $exePath)) {
        throw "Exécutable du service non trouvé: $exePath"
    }
    
    return $exePath
}

function Install-WexflowWindowsService {
    try {
        Write-ColoredOutput "========================================" "Cyan"
        Write-ColoredOutput "  Installation du Service Wexflow" "Cyan"
        Write-ColoredOutput "========================================" "Cyan"
        Write-Host ""

        # Vérification des privilèges administrateur
        if (-not (Test-Administrator)) {
            throw "Ce script doit être exécuté avec des privilèges administrateur"
        }

        # Détermination du chemin de l'exécutable
        if ([string]::IsNullOrEmpty($ServicePath)) {
            $ServicePath = Get-ServiceExecutablePath
        }

        Write-ColoredOutput "Configuration:" "Yellow"
        Write-ColoredOutput "- Nom du service: $ServiceName" "White"
        Write-ColoredOutput "- Nom d'affichage: $DisplayName" "White"
        Write-ColoredOutput "- Chemin: $ServicePath" "White"
        Write-ColoredOutput "- Type de démarrage: $StartupType" "White"
        Write-Host ""

        # Vérification si le service existe déjà
        $existingService = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if ($existingService) {
            Write-ColoredOutput "Le service '$ServiceName' existe déjà." "Yellow"
            
            # Arrêt du service s'il est en cours d'exécution
            if ($existingService.Status -eq "Running") {
                Write-ColoredOutput "Arrêt du service existant..." "Yellow"
                Stop-Service -Name $ServiceName -Force
                
                # Attendre l'arrêt complet
                $timeout = 30
                $elapsed = 0
                while ((Get-Service -Name $ServiceName).Status -ne "Stopped" -and $elapsed -lt $timeout) {
                    Start-Sleep -Seconds 1
                    $elapsed++
                }
                
                if ((Get-Service -Name $ServiceName).Status -ne "Stopped") {
                    throw "Impossible d'arrêter le service existant dans les temps impartis"
                }
            }

            # Suppression du service existant
            Write-ColoredOutput "Suppression du service existant..." "Yellow"
            & sc.exe delete $ServiceName
            if ($LASTEXITCODE -ne 0) {
                throw "Erreur lors de la suppression du service existant"
            }
            
            # Attendre que la suppression soit effective
            Start-Sleep -Seconds 2
        }

        # Création du service
        Write-ColoredOutput "Création du service..." "Yellow"
        
        $createResult = & sc.exe create $ServiceName binPath= "`"$ServicePath`"" DisplayName= "$DisplayName" start= auto
        if ($LASTEXITCODE -ne 0) {
            throw "Erreur lors de la création du service: $createResult"
        }

        # Configuration de la description
        Write-ColoredOutput "Configuration de la description..." "Yellow"
        & sc.exe description $ServiceName "$ServiceDescription"

        # Configuration du type de démarrage
        if ($StartupType -ne "Automatic") {
            Write-ColoredOutput "Configuration du type de démarrage..." "Yellow"
            $startType = switch ($StartupType) {
                "Manual" { "demand" }
                "Disabled" { "disabled" }
                default { "auto" }
            }
            & sc.exe config $ServiceName start= $startType
        }

        # Configuration de la récupération en cas d'échec
        Write-ColoredOutput "Configuration de la récupération automatique..." "Yellow"
        & sc.exe failure $ServiceName reset= 86400 actions= restart/5000/restart/10000/restart/30000

        # Création du dossier de logs
        $logsDir = Join-Path (Split-Path -Parent $ServicePath) "Logs"
        if (-not (Test-Path $logsDir)) {
            Write-ColoredOutput "Création du dossier de logs..." "Yellow"
            New-Item -ItemType Directory -Path $logsDir -Force | Out-Null
        }

        # Configuration des permissions sur le dossier de logs
        try {
            $acl = Get-Acl $logsDir
            $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("NETWORK SERVICE", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
            $acl.SetAccessRule($accessRule)
            Set-Acl -Path $logsDir -AclObject $acl
            Write-ColoredOutput "Permissions configurées sur le dossier de logs" "Green"
        }
        catch {
            Write-ColoredOutput "Attention: Impossible de configurer les permissions sur le dossier de logs: $($_.Exception.Message)" "Yellow"
        }

        # Démarrage du service si demandé
        if ($StartupType -eq "Automatic") {
            Write-ColoredOutput "Démarrage du service..." "Yellow"
            Start-Service -Name $ServiceName
            
            # Vérification du démarrage
            Start-Sleep -Seconds 3
            $service = Get-Service -Name $ServiceName
            if ($service.Status -eq "Running") {
                Write-ColoredOutput "Service démarré avec succès !" "Green"
            } else {
                Write-ColoredOutput "Attention: Le service n'a pas démarré automatiquement. Statut: $($service.Status)" "Yellow"
                Write-ColoredOutput "Vous pouvez le démarrer manuellement avec: Start-Service -Name $ServiceName" "Yellow"
            }
        }

        Write-Host ""
        Write-ColoredOutput "========================================" "Cyan"
        Write-ColoredOutput "  Installation terminée avec succès !" "Cyan"
        Write-ColoredOutput "========================================" "Cyan"
        Write-Host ""
        
        Write-ColoredOutput "Informations du service:" "White"
        $installedService = Get-Service -Name $ServiceName
        Write-ColoredOutput "- Nom: $($installedService.Name)" "Gray"
        Write-ColoredOutput "- Statut: $($installedService.Status)" "Gray"
        Write-ColoredOutput "- Type de démarrage: $StartupType" "Gray"
        Write-Host ""
        
        Write-ColoredOutput "Commandes utiles:" "White"
        Write-ColoredOutput "- Démarrer: Start-Service -Name $ServiceName" "Gray"
        Write-ColoredOutput "- Arrêter: Stop-Service -Name $ServiceName" "Gray"
        Write-ColoredOutput "- Statut: Get-Service -Name $ServiceName" "Gray"
        Write-ColoredOutput "- Logs: Get-EventLog -LogName Application -Source 'Wexflow Service' -Newest 10" "Gray"
        Write-Host ""

    }
    catch {
        Write-ColoredOutput "ERREUR: $($_.Exception.Message)" "Red"
        Write-Host ""
        Write-ColoredOutput "L'installation a échoué. Vérifiez les points suivants:" "Yellow"
        Write-ColoredOutput "1. Vous exécutez ce script en tant qu'administrateur" "Yellow"
        Write-ColoredOutput "2. L'exécutable du service existe: $ServicePath" "Yellow"
        Write-ColoredOutput "3. Aucun autre processus n'utilise le service" "Yellow"
        exit 1
    }
}

# Exécution de l'installation
Install-WexflowWindowsService
