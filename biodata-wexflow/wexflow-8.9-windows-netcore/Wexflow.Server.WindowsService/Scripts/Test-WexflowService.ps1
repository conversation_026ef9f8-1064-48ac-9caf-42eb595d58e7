<#
.SYNOPSIS
    Script de test et validation du service Windows Wexflow

.DESCRIPTION
    Ce script effectue une série de tests pour valider que le service Windows Wexflow
    fonctionne correctement et répond aux exigences.

.PARAMETER ServiceName
    Nom du service à tester (par défaut: WexflowService)

.PARAMETER SkipWebTest
    Ignore le test de connectivité web

.EXAMPLE
    .\Test-WexflowService.ps1
    
.EXAMPLE
    .\Test-WexflowService.ps1 -SkipWebTest
#>

param(
    [string]$ServiceName = "WexflowService",
    [switch]$SkipWebTest = $false
)

# Configuration
$ErrorActionPreference = "Continue"
$TestResults = @()

function Write-ColoredOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Add-TestResult {
    param(
        [string]$TestName,
        [bool]$Passed,
        [string]$Message = "",
        [string]$Details = ""
    )
    
    $script:TestResults += [PSCustomObject]@{
        TestName = $TestName
        Passed = $Passed
        Message = $Message
        Details = $Details
        Timestamp = Get-Date
    }
    
    $status = if ($Passed) { "✅ PASS" } else { "❌ FAIL" }
    $color = if ($Passed) { "Green" } else { "Red" }
    
    Write-ColoredOutput "[$status] $TestName" $color
    if ($Message) {
        Write-ColoredOutput "    $Message" "Gray"
    }
    if ($Details -and -not $Passed) {
        Write-ColoredOutput "    Détails: $Details" "Yellow"
    }
}

function Test-Prerequisites {
    Write-ColoredOutput "`n🔍 Test des prérequis..." "Cyan"
    
    # Test .NET Runtime
    try {
        $dotnetVersion = & dotnet --version 2>$null
        if ($dotnetVersion -and $dotnetVersion.StartsWith("9.")) {
            Add-TestResult "Runtime .NET 9.0" $true "Version: $dotnetVersion"
        } else {
            Add-TestResult "Runtime .NET 9.0" $false "Version trouvée: $dotnetVersion" "Requis: .NET 9.0"
        }
    }
    catch {
        Add-TestResult "Runtime .NET 9.0" $false "Non installé" $_.Exception.Message
    }
    
    # Test privilèges administrateur
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    $isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    
    Add-TestResult "Privilèges administrateur" $isAdmin $(if ($isAdmin) { "Disponibles" } else { "Requis pour certaines opérations" })
}

function Test-ServiceInstallation {
    Write-ColoredOutput "`n🔧 Test de l'installation du service..." "Cyan"
    
    # Test existence du service
    try {
        $service = Get-Service -Name $ServiceName -ErrorAction Stop
        Add-TestResult "Service installé" $true "Nom: $($service.DisplayName)"
        
        # Test configuration du service
        $serviceConfig = Get-WmiObject -Class Win32_Service -Filter "Name='$ServiceName'"
        if ($serviceConfig) {
            Add-TestResult "Configuration du service" $true "Type de démarrage: $($serviceConfig.StartMode)"
            
            # Test chemin de l'exécutable
            if (Test-Path $serviceConfig.PathName.Trim('"')) {
                Add-TestResult "Exécutable du service" $true "Chemin: $($serviceConfig.PathName)"
            } else {
                Add-TestResult "Exécutable du service" $false "Fichier non trouvé" $serviceConfig.PathName
            }
        } else {
            Add-TestResult "Configuration du service" $false "Impossible de récupérer la configuration WMI"
        }
        
        return $service
    }
    catch {
        Add-TestResult "Service installé" $false "Service non trouvé" $_.Exception.Message
        return $null
    }
}

function Test-ServiceOperation {
    param($Service)
    
    if (-not $Service) {
        Write-ColoredOutput "`n⚠️  Tests d'opération ignorés (service non installé)" "Yellow"
        return
    }
    
    Write-ColoredOutput "`n⚙️  Test des opérations du service..." "Cyan"
    
    $originalStatus = $Service.Status
    
    try {
        # Test démarrage du service
        if ($Service.Status -ne "Running") {
            Write-ColoredOutput "Tentative de démarrage du service..." "Yellow"
            Start-Service -Name $ServiceName -ErrorAction Stop
            
            # Attendre le démarrage
            $timeout = 30
            $elapsed = 0
            while ((Get-Service -Name $ServiceName).Status -ne "Running" -and $elapsed -lt $timeout) {
                Start-Sleep -Seconds 1
                $elapsed++
            }
            
            $newStatus = (Get-Service -Name $ServiceName).Status
            Add-TestResult "Démarrage du service" ($newStatus -eq "Running") "Statut: $newStatus"
        } else {
            Add-TestResult "Service en cours d'exécution" $true "Déjà démarré"
        }
        
        # Test processus associé
        Start-Sleep -Seconds 3
        $processes = Get-Process | Where-Object { $_.ProcessName -like "*Wexflow*" }
        if ($processes) {
            $processInfo = $processes | ForEach-Object { "$($_.ProcessName) (PID: $($_.Id))" }
            Add-TestResult "Processus Wexflow" $true "Processus trouvés: $($processInfo -join ', ')"
        } else {
            Add-TestResult "Processus Wexflow" $false "Aucun processus Wexflow trouvé"
        }
        
    }
    catch {
        Add-TestResult "Démarrage du service" $false "Erreur lors du démarrage" $_.Exception.Message
    }
}

function Test-WebConnectivity {
    if ($SkipWebTest) {
        Write-ColoredOutput "`n🌐 Tests de connectivité web ignorés" "Yellow"
        return
    }
    
    Write-ColoredOutput "`n🌐 Test de connectivité web..." "Cyan"
    
    # Test port 8000
    try {
        $connection = Test-NetConnection -ComputerName "localhost" -Port 8000 -WarningAction SilentlyContinue
        Add-TestResult "Port 8000 accessible" $connection.TcpTestSucceeded $(if ($connection.TcpTestSucceeded) { "Connecté" } else { "Non accessible" })
    }
    catch {
        Add-TestResult "Port 8000 accessible" $false "Erreur de test" $_.Exception.Message
    }
    
    # Test HTTP (si port accessible)
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000" -TimeoutSec 10 -ErrorAction Stop
        Add-TestResult "Service web HTTP" $true "Code de réponse: $($response.StatusCode)"
    }
    catch {
        if ($_.Exception.Message -like "*refused*" -or $_.Exception.Message -like "*timeout*") {
            Add-TestResult "Service web HTTP" $false "Service non accessible" "Vérifiez que Wexflow est démarré"
        } else {
            Add-TestResult "Service web HTTP" $false "Erreur HTTP" $_.Exception.Message
        }
    }
}

function Test-LoggingSystem {
    Write-ColoredOutput "`n📋 Test du système de logs..." "Cyan"
    
    # Test journal d'événements Windows
    try {
        $events = Get-EventLog -LogName Application -Source "Wexflow Service" -Newest 1 -ErrorAction Stop
        Add-TestResult "Journal d'événements Windows" $true "Dernière entrée: $($events[0].TimeGenerated)"
    }
    catch {
        Add-TestResult "Journal d'événements Windows" $false "Aucune entrée trouvée" "Le service n'a peut-être pas encore écrit de logs"
    }
    
    # Test fichiers de logs
    $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    $parentDir = Split-Path -Parent $scriptDir
    $logsDir = Join-Path $parentDir "Logs"
    
    if (Test-Path $logsDir) {
        $logFiles = Get-ChildItem -Path $logsDir -Filter "*.log"
        if ($logFiles) {
            $latestLog = $logFiles | Sort-Object LastWriteTime -Descending | Select-Object -First 1
            Add-TestResult "Fichiers de logs" $true "Dernier fichier: $($latestLog.Name) (modifié: $($latestLog.LastWriteTime))"
        } else {
            Add-TestResult "Fichiers de logs" $false "Aucun fichier de log trouvé" $logsDir
        }
    } else {
        Add-TestResult "Dossier de logs" $false "Dossier non trouvé" $logsDir
    }
}

function Test-Configuration {
    Write-ColoredOutput "`n⚙️  Test de la configuration..." "Cyan"
    
    $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    $parentDir = Split-Path -Parent $scriptDir
    
    # Test appsettings.json
    $appSettingsPath = Join-Path $parentDir "appsettings.json"
    if (Test-Path $appSettingsPath) {
        try {
            $config = Get-Content $appSettingsPath | ConvertFrom-Json
            Add-TestResult "Fichier appsettings.json" $true "Configuration chargée"
            
            # Test configuration Wexflow
            if ($config.Wexflow) {
                Add-TestResult "Section Wexflow" $true "Port: $($config.Wexflow.WexflowServicePort)"
            } else {
                Add-TestResult "Section Wexflow" $false "Section manquante dans appsettings.json"
            }
        }
        catch {
            Add-TestResult "Fichier appsettings.json" $false "Erreur de parsing JSON" $_.Exception.Message
        }
    } else {
        Add-TestResult "Fichier appsettings.json" $false "Fichier non trouvé" $appSettingsPath
    }
    
    # Test log4net.config
    $log4netPath = Join-Path $parentDir "log4net.config"
    Add-TestResult "Fichier log4net.config" (Test-Path $log4netPath) $(if (Test-Path $log4netPath) { "Présent" } else { "Non trouvé" })
    
    # Test configuration Wexflow
    $wexflowConfigPath = Join-Path $parentDir "..\Wexflow-netcore\Wexflow.xml"
    Add-TestResult "Configuration Wexflow.xml" (Test-Path $wexflowConfigPath) $(if (Test-Path $wexflowConfigPath) { "Présent" } else { "Non trouvé" })
}

function Show-TestSummary {
    Write-ColoredOutput "`n" "White"
    Write-ColoredOutput "========================================" "Cyan"
    Write-ColoredOutput "  RÉSUMÉ DES TESTS" "Cyan"
    Write-ColoredOutput "========================================" "Cyan"
    
    $totalTests = $TestResults.Count
    $passedTests = ($TestResults | Where-Object { $_.Passed }).Count
    $failedTests = $totalTests - $passedTests
    
    Write-ColoredOutput "`nStatistiques:" "White"
    Write-ColoredOutput "- Total des tests: $totalTests" "Gray"
    Write-ColoredOutput "- Tests réussis: $passedTests" "Green"
    Write-ColoredOutput "- Tests échoués: $failedTests" $(if ($failedTests -eq 0) { "Green" } else { "Red" })
    
    $successRate = [math]::Round(($passedTests / $totalTests) * 100, 1)
    Write-ColoredOutput "- Taux de réussite: $successRate%" $(if ($successRate -ge 90) { "Green" } elseif ($successRate -ge 70) { "Yellow" } else { "Red" })
    
    if ($failedTests -gt 0) {
        Write-ColoredOutput "`nTests échoués:" "Red"
        $TestResults | Where-Object { -not $_.Passed } | ForEach-Object {
            Write-ColoredOutput "❌ $($_.TestName): $($_.Message)" "Red"
            if ($_.Details) {
                Write-ColoredOutput "   $($_.Details)" "Yellow"
            }
        }
    }
    
    Write-ColoredOutput "`nRecommandations:" "White"
    if ($successRate -ge 90) {
        Write-ColoredOutput "✅ Le service Wexflow semble correctement configuré et fonctionnel !" "Green"
    } elseif ($successRate -ge 70) {
        Write-ColoredOutput "⚠️  Le service fonctionne mais quelques améliorations sont possibles" "Yellow"
    } else {
        Write-ColoredOutput "❌ Des problèmes importants ont été détectés, vérifiez la configuration" "Red"
    }
    
    Write-ColoredOutput "`nProchaines étapes:" "White"
    Write-ColoredOutput "1. Corriger les tests échoués si nécessaire" "Gray"
    Write-ColoredOutput "2. Vérifier les logs pour plus de détails" "Gray"
    Write-ColoredOutput "3. Tester les workflows Wexflow" "Gray"
    Write-ColoredOutput "4. Configurer la surveillance en production" "Gray"
}

# Fonction principale
function Test-WexflowService {
    Write-ColoredOutput "========================================" "Cyan"
    Write-ColoredOutput "  TESTS DU SERVICE WINDOWS WEXFLOW" "Cyan"
    Write-ColoredOutput "========================================" "Cyan"
    Write-ColoredOutput "Service testé: $ServiceName" "White"
    Write-ColoredOutput "Heure de début: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "White"
    
    # Exécution des tests
    Test-Prerequisites
    $service = Test-ServiceInstallation
    Test-ServiceOperation -Service $service
    Test-WebConnectivity
    Test-LoggingSystem
    Test-Configuration
    
    # Résumé
    Show-TestSummary
    
    # Sauvegarde des résultats
    $resultsPath = "test-results-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
    $TestResults | ConvertTo-Json -Depth 3 | Out-File -FilePath $resultsPath -Encoding UTF8
    Write-ColoredOutput "`nRésultats sauvegardés dans: $resultsPath" "Gray"
}

# Exécution des tests
Test-WexflowService
