# Wexflow Windows Service

Service Windows natif pour Wexflow - Moteur de workflow d'entreprise.

## 🎯 Objectif

Transformer Wexflow en service Windows natif pour :
- ✅ Démarrage automatique avec Windows
- ✅ Exécution en arrière-plan (sans fenêtre visible)
- ✅ Gestion centralisée via Services Windows
- ✅ Récupération automatique en cas de crash
- ✅ Logs intégrés au journal d'événements Windows

## 📋 Prérequis

- Windows 10/11 ou Windows Server 2016+
- .NET 9.0 Runtime ou SDK
- Privilèges administrateur pour l'installation
- Wexflow 8.9 (fichiers existants)

## 🚀 Installation Rapide

### 1. Build du Service

```powershell
# Compilation et publication
.\Scripts\Build-WexflowService.ps1

# Ou avec options avancées
.\Scripts\Build-WexflowService.ps1 -Configuration Release -SelfContained
```

### 2. Installation du Service

```powershell
# Installation avec démarrage automatique
.\Scripts\Install-WexflowService.ps1

# Installation avec démarrage manuel
.\Scripts\Install-WexflowService.ps1 -StartupType Manual
```

### 3. Gestion du Service

```powershell
# Vérifier le statut
.\Scripts\Manage-WexflowService.ps1 -Action Status

# Démarrer le service
.\Scripts\Manage-WexflowService.ps1 -Action Start

# Arrêter le service
.\Scripts\Manage-WexflowService.ps1 -Action Stop

# Redémarrer le service
.\Scripts\Manage-WexflowService.ps1 -Action Restart

# Voir les logs
.\Scripts\Manage-WexflowService.ps1 -Action Logs
```

## 📁 Structure du Projet

```
Wexflow.Server.WindowsService/
├── Program.cs                          # Point d'entrée principal
├── WexflowWindowsService.cs            # Service principal (IHostedService)
├── WexflowEngineWrapper.cs             # Wrapper du moteur Wexflow
├── WexflowConfiguration.cs             # Configuration du service
├── IWexflowEngineWrapper.cs            # Interface du wrapper
├── appsettings.json                    # Configuration principale
├── appsettings.Development.json        # Configuration développement
├── appsettings.Production.json         # Configuration production
├── log4net.config                      # Configuration des logs
├── Wexflow.Server.WindowsService.csproj # Fichier projet
├── Scripts/
│   ├── Build-WexflowService.ps1        # Script de build
│   ├── Install-WexflowService.ps1      # Script d'installation
│   ├── Uninstall-WexflowService.ps1    # Script de désinstallation
│   └── Manage-WexflowService.ps1       # Script de gestion
└── README.md                           # Cette documentation
```

## ⚙️ Configuration

### appsettings.json

```json
{
  "Wexflow": {
    "WexflowSettingsFile": "..\\Wexflow-netcore\\Wexflow.xml",
    "LogLevel": "Info",
    "WexflowServicePort": 8000,
    "SuperAdminUsername": "admin",
    "EnableWorkflowsHotFolder": true,
    "EnableRecordsHotFolder": true,
    "EnableEmailNotifications": false,
    "HealthCheckIntervalSeconds": 30,
    "StartupTimeoutSeconds": 120,
    "ShutdownTimeoutSeconds": 60
  }
}
```

### Paramètres Principaux

| Paramètre | Description | Valeur par défaut |
|-----------|-------------|-------------------|
| `WexflowSettingsFile` | Chemin vers Wexflow.xml | `..\\Wexflow-netcore\\Wexflow.xml` |
| `WexflowServicePort` | Port du service web | `8000` |
| `LogLevel` | Niveau de log | `Info` |
| `HealthCheckIntervalSeconds` | Intervalle de vérification | `30` |

## 📊 Logs et Monitoring

### Emplacements des Logs

1. **Journal d'événements Windows** : `Application` > `Wexflow Service`
2. **Fichiers de logs** : `.\Logs\WexflowService.log`
3. **Logs Wexflow** : Selon configuration Wexflow existante

### Consultation des Logs

```powershell
# Via script de gestion
.\Scripts\Manage-WexflowService.ps1 -Action Logs -LogCount 20

# Via PowerShell
Get-EventLog -LogName Application -Source "Wexflow Service" -Newest 10

# Via observateur d'événements
eventvwr.msc
```

## 🔧 Dépannage

### Problèmes Courants

#### Le service ne démarre pas

1. Vérifier les logs : `.\Scripts\Manage-WexflowService.ps1 -Action Logs`
2. Vérifier la configuration : `appsettings.json`
3. Vérifier les permissions sur les dossiers
4. Vérifier que le port 8000 n'est pas utilisé

#### Le service s'arrête de manière inattendue

1. Consulter le journal d'événements Windows
2. Vérifier les fichiers de logs
3. Vérifier l'espace disque disponible
4. Vérifier la configuration Wexflow.xml

#### Impossible d'installer le service

1. Exécuter PowerShell en tant qu'administrateur
2. Vérifier que le service n'existe pas déjà
3. Vérifier les permissions sur le dossier d'installation

### Commandes de Diagnostic

```powershell
# Statut détaillé du service
Get-Service WexflowService | Format-List *

# Processus Wexflow en cours
Get-Process | Where-Object { $_.ProcessName -like "*Wexflow*" }

# Test de connectivité web
Test-NetConnection -ComputerName localhost -Port 8000

# Logs récents
Get-EventLog -LogName Application -Source "Wexflow Service" -After (Get-Date).AddHours(-1)
```

## 🔄 Migration depuis l'Installation Existante

### Étapes de Migration

1. **Arrêter l'ancienne méthode** :
   ```powershell
   # Arrêter le processus manuel si en cours
   Stop-Process -Name "dotnet" -Force -ErrorAction SilentlyContinue
   ```

2. **Sauvegarder la configuration** :
   ```powershell
   Copy-Item ".\Wexflow-netcore\*" ".\Backup\" -Recurse
   ```

3. **Installer le nouveau service** :
   ```powershell
   .\Scripts\Build-WexflowService.ps1
   .\Scripts\Install-WexflowService.ps1
   ```

4. **Vérifier le fonctionnement** :
   ```powershell
   .\Scripts\Manage-WexflowService.ps1 -Action Status
   ```

### Retour en Arrière

Si nécessaire, vous pouvez revenir à l'ancienne méthode :

```powershell
# Désinstaller le service
.\Scripts\Uninstall-WexflowService.ps1

# Restaurer l'ancienne configuration
Copy-Item ".\Backup\*" ".\Wexflow-netcore\" -Recurse -Force

# Redémarrer manuellement
.\run.bat
```

## 📚 Références

- [Microsoft.Extensions.Hosting](https://docs.microsoft.com/en-us/dotnet/core/extensions/windows-service)
- [Windows Services](https://docs.microsoft.com/en-us/dotnet/framework/windows-services/)
- [log4net Configuration](https://logging.apache.org/log4net/release/manual/configuration.html)

## 🆘 Support

En cas de problème :

1. Consulter les logs détaillés
2. Vérifier la configuration
3. Tester en mode console : `.\Wexflow.Server.WindowsService.exe`
4. Consulter la documentation Wexflow officielle

---

**Note** : Ce service Windows encapsule l'installation Wexflow existante sans la modifier. Tous vos workflows et configurations restent inchangés.
