# Guide de Préparation du Setup Inno Setup

Ce guide explique comment préparer et utiliser le nouveau setup Inno Setup qui prend en compte le service Windows Wexflow.

## 🎯 **Nouveautés du Setup**

### ✅ **Nouveaux Types d'Installation**
- **Installation complète** : API + Service Windows Wexflow
- **API seulement** : Uniquement l'API BioData
- **Service Windows seulement** : Uniquement Wexflow en service Windows
- **Wexflow traditionnel** : Mode console (ancienne méthode)
- **Installation personnalisée** : Choix des composants

### ✅ **Nouvelles Fonctionnalités**
- Installation automatique du service Windows
- Démarrage automatique du service après installation
- Désinstallation propre du service
- Raccourcis pour gérer le service
- Configuration automatique des chemins

## 📁 **Structure Requise pour le Setup**

Avant de compiler le setup, vous devez organiser vos fichiers comme suit :

```
Setup-Source/
├── setup.iss                          # Script Inno Setup
├── api/                               # Dossier API BioData
│   └── [fichiers API]
├── wexflow/                           # Dossier Wexflow Legacy
│   └── [fichiers Wexflow traditionnel]
├── wexflow-service/                   # 🆕 Dossier Service Windows
│   ├── Wexflow.Server.WindowsService.exe
│   ├── appsettings.json
│   ├── log4net.config
│   ├── Wexflow-netcore/              # Configuration Wexflow
│   ├── Scripts/                      # Scripts de gestion
│   └── [autres fichiers du service]
└── scripts/                          # Scripts de lancement traditionnels
    ├── start-api.bat
    ├── start-wexflow.bat
    ├── start-all.bat
    └── stop-all.bat
```

## 🔧 **Préparation du Dossier wexflow-service**

### 1. **Compiler le Service Windows**
```powershell
# Depuis le dossier racine
.\build_package_task.ps1

# Puis publier le service complet
.\wexflow-8.9-windows-netcore\Wexflow.Server.WindowsService\Scripts\Build-WexflowService.ps1
```

### 2. **Copier les Fichiers Publiés**
```powershell
# Copier le contenu de bin\Publish vers le dossier setup
Copy-Item ".\wexflow-8.9-windows-netcore\Wexflow.Server.WindowsService\bin\Publish\*" `
          ".\Setup-Source\wexflow-service\" -Recurse -Force
```

### 3. **Vérifier la Structure**
Assurez-vous que `wexflow-service` contient :
- ✅ `Wexflow.Server.WindowsService.exe`
- ✅ `appsettings.json`
- ✅ `log4net.config`
- ✅ `Wexflow-netcore/` (dossier complet)
- ✅ `Scripts/` (scripts de gestion PowerShell)
- ✅ Toutes les DLL nécessaires

## 🚀 **Compilation du Setup**

### 1. **Ouvrir Inno Setup Compiler**
- Lancer Inno Setup Compiler
- Ouvrir le fichier `setup.iss`

### 2. **Vérifier les Chemins**
Dans le script, vérifiez que les chemins source correspondent à votre structure :
```pascal
Source: "wexflow-service\*"; DestDir: "{app}\WexflowService"
```

### 3. **Compiler**
- Menu `Build` > `Compile`
- Le setup sera généré dans le dossier `Output/`

## 🎛️ **Options d'Installation**

### **Installation Complète (Recommandée)**
- Installe API + Service Windows Wexflow
- Configure automatiquement les chemins
- Installe et démarre le service automatiquement

### **Service Windows Seulement**
- Installe uniquement le service Windows Wexflow
- Idéal pour les serveurs dédiés
- Démarrage automatique avec Windows

### **Wexflow Traditionnel**
- Mode console (ancienne méthode)
- Pour compatibilité ou tests
- Nécessite démarrage manuel

## 🔧 **Tâches d'Installation**

### ✅ **Tâches Automatiques**
- **Configurer les chemins** : Remplace `install_folder` par le chemin réel
- **Installer le service** : Installe automatiquement le service Windows
- **Démarrer le service** : Démarre le service après installation

### ✅ **Raccourcis Créés**
- **Menu Démarrer** :
  - Gérer Service Wexflow
  - Démarrer/Arrêter Service
  - Voir les Logs
- **Bureau** (optionnel) :
  - Gérer Service Wexflow

## 🛠️ **Post-Installation**

### **Vérification du Service**
Après installation, le service devrait être :
- ✅ Installé dans les Services Windows
- ✅ Configuré en démarrage automatique
- ✅ Démarré et fonctionnel

### **Accès Web**
- URL : `http://localhost:8000`
- Interface Wexflow accessible via navigateur

### **Gestion du Service**
Utilisez les raccourcis créés ou :
```powershell
# Depuis le dossier d'installation
.\WexflowService\Scripts\Manage-WexflowService.ps1 -Action Status
```

## 🔄 **Migration depuis l'Ancienne Version**

### **Désinstallation Propre**
1. Désinstaller l'ancienne version via "Programmes et fonctionnalités"
2. Le service sera automatiquement désinstallé
3. Les données utilisateur sont préservées

### **Nouvelle Installation**
1. Installer la nouvelle version
2. Choisir "Installation complète" ou "Service Windows seulement"
3. Le service démarre automatiquement

## 🆘 **Dépannage**

### **Le Service ne Démarre Pas**
1. Vérifier les logs : Menu Démarrer > "Logs Service Wexflow"
2. Vérifier la configuration : `appsettings.json`
3. Réinstaller le service manuellement

### **Erreur de Permissions**
1. Exécuter l'installation en tant qu'administrateur
2. Vérifier les permissions sur les dossiers Wexflow

### **Port 8000 Occupé**
1. Modifier le port dans `appsettings.json`
2. Redémarrer le service

## 📋 **Checklist de Préparation**

Avant de distribuer le setup :

- [ ] Service Windows compilé et testé
- [ ] Dossier `wexflow-service` correctement préparé
- [ ] Scripts PowerShell fonctionnels
- [ ] Configuration `appsettings.json` correcte
- [ ] Test d'installation sur machine propre
- [ ] Test de désinstallation
- [ ] Vérification du démarrage automatique

---

**Note** : Ce setup permet une coexistence entre l'ancienne méthode (Wexflow traditionnel) et la nouvelle (Service Windows). L'utilisateur peut choisir selon ses besoins.
