using System;

namespace Wexflow.Server.WindowsService
{
    /// <summary>
    /// Configuration pour le service Wexflow
    /// </summary>
    public class WexflowConfiguration
    {
        /// <summary>
        /// Chemin vers le fichier de configuration Wexflow.xml
        /// </summary>
        public string WexflowSettingsFile { get; set; } = "Wexflow.xml";

        /// <summary>
        /// Niveau de log (All, Debug, Info, Warn, Error, Fatal, Off)
        /// </summary>
        public string LogLevel { get; set; } = "Info";

        /// <summary>
        /// Port du service web Wexflow
        /// </summary>
        public int WexflowServicePort { get; set; } = 8000;

        /// <summary>
        /// Nom d'utilisateur super administrateur
        /// </summary>
        public string SuperAdminUsername { get; set; } = "admin";

        /// <summary>
        /// Activation du dossier de surveillance des workflows
        /// </summary>
        public bool EnableWorkflowsHotFolder { get; set; } = true;

        /// <summary>
        /// Activation du dossier de surveillance des enregistrements
        /// </summary>
        public bool EnableRecordsHotFolder { get; set; } = true;

        /// <summary>
        /// Activation des notifications par email
        /// </summary>
        public bool EnableEmailNotifications { get; set; } = false;

        /// <summary>
        /// Format de date et heure
        /// </summary>
        public string DateTimeFormat { get; set; } = "dd-MM-yyyy HH:mm:ss";

        /// <summary>
        /// Configuration SMTP - Hôte
        /// </summary>
        public string SmtpHost { get; set; } = "";

        /// <summary>
        /// Configuration SMTP - Port
        /// </summary>
        public int SmtpPort { get; set; } = 587;

        /// <summary>
        /// Configuration SMTP - SSL activé
        /// </summary>
        public bool SmtpEnableSsl { get; set; } = true;

        /// <summary>
        /// Configuration SMTP - Utilisateur
        /// </summary>
        public string SmtpUser { get; set; } = "";

        /// <summary>
        /// Configuration SMTP - Mot de passe
        /// </summary>
        public string SmtpPassword { get; set; } = "";

        /// <summary>
        /// Configuration SMTP - Adresse d'expéditeur
        /// </summary>
        public string SmtpFrom { get; set; } = "";

        /// <summary>
        /// Délai de vérification de santé en secondes
        /// </summary>
        public int HealthCheckIntervalSeconds { get; set; } = 30;

        /// <summary>
        /// Timeout de démarrage en secondes
        /// </summary>
        public int StartupTimeoutSeconds { get; set; } = 120;

        /// <summary>
        /// Timeout d'arrêt en secondes
        /// </summary>
        public int ShutdownTimeoutSeconds { get; set; } = 60;

        /// <summary>
        /// Validation de la configuration
        /// </summary>
        public void Validate()
        {
            if (string.IsNullOrWhiteSpace(WexflowSettingsFile))
                throw new ArgumentException("WexflowSettingsFile ne peut pas être vide");

            if (WexflowServicePort <= 0 || WexflowServicePort > 65535)
                throw new ArgumentException("WexflowServicePort doit être entre 1 et 65535");

            if (string.IsNullOrWhiteSpace(SuperAdminUsername))
                throw new ArgumentException("SuperAdminUsername ne peut pas être vide");

            if (HealthCheckIntervalSeconds <= 0)
                throw new ArgumentException("HealthCheckIntervalSeconds doit être positif");

            if (StartupTimeoutSeconds <= 0)
                throw new ArgumentException("StartupTimeoutSeconds doit être positif");

            if (ShutdownTimeoutSeconds <= 0)
                throw new ArgumentException("ShutdownTimeoutSeconds doit être positif");

            // Validation SMTP si les notifications sont activées
            if (EnableEmailNotifications)
            {
                if (string.IsNullOrWhiteSpace(SmtpHost))
                    throw new ArgumentException("SmtpHost requis si EnableEmailNotifications est activé");

                if (SmtpPort <= 0 || SmtpPort > 65535)
                    throw new ArgumentException("SmtpPort doit être entre 1 et 65535");

                if (string.IsNullOrWhiteSpace(SmtpFrom))
                    throw new ArgumentException("SmtpFrom requis si EnableEmailNotifications est activé");
            }
        }
    }
}
