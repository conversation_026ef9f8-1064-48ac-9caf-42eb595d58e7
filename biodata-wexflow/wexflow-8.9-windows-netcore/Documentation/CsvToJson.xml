﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    CsvToJson is a sequential task that converts CSV files to JSON files.
    
    The JSON files generated are loaded by this task so that other tasks can select them through the selectFiles option.
  -->
  <Task id="$int" name="CsvToJson" description="$string" enabled="true|false">
    <!-- 
      The CSV (.csv) files loaded by the task having as id $taskId will be converted to JSON files.
    -->
    <Setting name="selectFiles" value="$taskId" />
    <Setting name="selectFiles" value="$taskId" />
    <!-- You can add as many selecteFiles as you want.-->

    <!-- Optional and defaults to ';'. The separtor character in the CSV files.-->
    <Setting name="separator" value="$string" />
  </Task>
</Tasks>
