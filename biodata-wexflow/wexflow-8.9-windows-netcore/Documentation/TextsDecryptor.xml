﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    TextsDecryptor is a sequential task that decrypts a collection of files encrypted by the task TextsEncryptor.
    
    The decrypted files generated are loaded by this task so that other tasks can select them through the selectFiles option.
  -->
  <Task id="$int" name="TextsDecryptor" description="$string" enabled="true|false">
    <!--  The crypted files loaded by the task having as id $taskId will be decrypted. -->
    <Setting name="selectFiles" value="$taskId" />
    <Setting name="selectFiles" value="$taskId" />
    <!-- You can add as many selecteFiles as you want.-->
  </Task>
</Tasks>
