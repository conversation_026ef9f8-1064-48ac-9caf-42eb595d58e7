﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    ImagesOverlay is a sequential task that overlays a collection of image files.
    
    The image file generated is loaded by this task so that other tasks can select it through the selectFiles option.
  -->
  <Task id="$int" name="ImagesOverlay" description="$string" enabled="true|false">
    <!--  The image files loaded by the task having as id $taskId will be overlayed. -->
    <Setting name="selectFiles" value="$taskId" />
    <Setting name="selectFiles" value="$taskId" />
    <!-- You can add as many selecteFiles as you want.-->
  </Task>
</Tasks>
