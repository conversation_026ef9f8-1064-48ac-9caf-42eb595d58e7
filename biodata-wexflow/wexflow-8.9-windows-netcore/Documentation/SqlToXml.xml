﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    SqlToXml is a sequential task that executes a collection of SQL scripts (select queries) and outputs the results in XML files as follows:
    
    <Records>
      <Record>
        <Cell column="$string" value="$string" />
        <Cell column="$string" value="$string" />
        ...
      </Record>
      <Record>
        <Cell column="$string" value="$string" />
        <Cell column="$string" value="$string" />
        ...
      </Record>
      ...
    </Records>
    
    This task supports Microsoft SQL Server, Microsoft Access, Oracle, MySQL, SQLite, PostgreSQL, Teradata and Odbc.
    
    The XML files generated are loaded by this task so that other tasks can select them through the selectFiles setting.
  -->
  <Task id="$int" name="SqlToXml" description="$string" enabled="true|false">
    <!-- 
      The SQL script files loaded by the task having as id $taskId will be
      executed.
    -->
    <Setting name="selectFiles" value="$taskId" />
    <Setting name="selectFiles" value="$taskId" />
    <!-- You can add as many selecteFiles as you want.-->
    <!-- The database engine type. Possible options: sqlserver|access|oracle|mysql|sqlite|postgresql|teradata|odbc-->
    <Setting name="type" value="sqlserver|access|oracle|mysql|sqlite|postgresql|teradata|odbc" />
    <!--- The connection string.-->
    <Setting name="connectionString" value="$string" />
    <!-- Optional. It is possible to execute an SQL script through this option.-->
    <Setting name="sql" value="$string" />
    <!-- Optional and defaults to false. If set to true value i empty or zero, dont include node. If set to false, all nodes is included.-->
    <Setting name="excludeEmptyValues" value="$bool" />
  </Task>
</Tasks>
