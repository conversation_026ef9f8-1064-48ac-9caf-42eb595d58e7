﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    ImagesTransformer is a sequential task that transforms a collection of image files to a specified format.
    
    The image files generated will be loaded by this task so that other
    tasks can select them through the selectFiles option.
  -->
  <Task id="$int" name="ImagesTransformer" description="$string" enabled="true|false">
    <!-- 
      The image files loaded by the task having as id $taskId will be
      transformed.
    -->
    <Setting name="selectFiles" value="$taskId" />
    <Setting name="selectFiles" value="$taskId" />
    <!-- You can add as many selecteFiles as you want.-->
    <!-- Output file name pattern. Variables: $fileName|$fileNameWithoutExtension. Example: $fileNameWithoutExtension.png -->
    <Setting name="outputFilePattern" value="$string" />
    <!-- Output format. Possible values: Bmp|Gif|Icon|Jpeg|Png. Example: Png-->
    <Setting name="outputFormat" value="Bmp|Gif|Icon|Jpeg|Png" />
  </Task>
</Tasks>
