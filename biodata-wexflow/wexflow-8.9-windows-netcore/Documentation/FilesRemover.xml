﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    FilesRemover is a sequential task that deletes a collection of files.
    
    The files deleted will be removed from their tasks so that other
    tasks cannot select them through the selectFiles option.
  -->
  <Task id="$int" name="FilesRemover" description="$string" enabled="true|false">
    <!-- 
      The files loaded by the task having as id the $taskId will be deleted.
    -->
    <Setting name="selectFiles" value="$taskId" />
    <Setting name="selectFiles" value="$taskId" />
    <!-- You can add as many selecteFiles as you want.-->
  </Task>
</Tasks>