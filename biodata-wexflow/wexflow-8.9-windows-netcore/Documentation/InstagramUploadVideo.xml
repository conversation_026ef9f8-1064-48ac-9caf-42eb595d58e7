﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    InstagramUploadVideo is a sequential task that uploads videos to Instagram.
    
    This task takes as input XML files containing the images to be uploaded. The format of these files is as follows:
    
    <Videos>
      <Video>
	    <FilePath>C:\WexflowTesting\Videos\video1.mp4</FilePath>
	    <ThumbnailPath>C:\WexflowTesting\Images\image1.jpg</ThumbnailPath>
	    <Caption>My awesome video.</Caption>
      </Video>
      ...
    </Videos>
  -->
  <Task id="$int" name="InstagramUploadVideo" description="$string" enabled="true|false">
    <!-- 
      The XML files loaded by the task having as id the $taskId will be loaded in order to upload the videos to YouTube.
    -->
    <Setting name="selectFiles" value="$taskId" />
    <Setting name="selectFiles" value="$taskId" />
    <!-- You can add as many selecteFiles as you want.-->
    
    <!-- Instagram username.-->
    <Setting name="username" value="$string" />
    <!-- Instagram password.-->
    <Setting name="password" value="$string" />
  </Task>
</Tasks>