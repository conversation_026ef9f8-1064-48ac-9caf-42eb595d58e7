﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    JsonToYaml is a sequential task that converts JSON files to YAML files.
    
    The YAML files generated are loaded by this task so that other tasks can select them through the selectFiles option.
  -->
  <Task id="$int" name="JsonToYaml" description="$string" enabled="true|false">
    <!-- 
      The JSON (.json) files loaded by the task having as id $taskId will be converted to YAML files.
    -->
    <Setting name="selectFiles" value="$taskId" />
    <Setting name="selectFiles" value="$taskId" />
    <!-- You can add as many selecteFiles as you want.-->
  </Task>
</Tasks>
