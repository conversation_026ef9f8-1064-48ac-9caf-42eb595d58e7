﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    YamlToJson is a sequential task that converts YAML files to JSON files.
    
    The JSON files generated are loaded by this task so that other tasks can select them through the selectFiles option.
  -->
  <Task id="$int" name="YamlToJson" description="$string" enabled="true|false">
    <!-- 
      The YAML (.yml) files loaded by the task having as id $taskId will be converted to JSON files.
    -->
    <Setting name="selectFiles" value="$taskId" />
    <Setting name="selectFiles" value="$taskId" />
    <!-- You can add as many selecteFiles as you want.-->
  </Task>
</Tasks>
