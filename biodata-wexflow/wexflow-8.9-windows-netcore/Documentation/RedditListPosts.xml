﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    RedditListPosts is a sequential task that retrieves Reddit post history and writes it in an XML file. 
    
    The format of the output XML file is as follows:
    
    <Posts>
      <Post id="$string" subreddit="$string" title="$string" upvotes="$int" downvotes="$int" />
      ...
    </Posts>    
  -->
  <Task id="$int" name="RedditListPosts" description="$string" enabled="true|false">
    <!-- Application ID: You can obtain it from https://www.reddit.com/prefs/apps -->
    <Setting name="appId" value="$string" />
    <!-- Refresh token. You can obtain it from https://not-an-aardvark.github.io/reddit-oauth-helper/ -->
    <Setting name="refreshToken" value="$string" />
    <!-- Optional and defaults to 50. The maximum number of posts that should be returned in the result set. -->
    <Setting name="maxResults" value="$int" />
  </Task>
</Tasks>
