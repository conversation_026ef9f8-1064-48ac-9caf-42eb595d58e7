﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    ScssToCss is a sequential task that converts SCSS files to CSS files.
    
    The CSS files generated are loaded by this task so that other tasks can select them through the selectFiles option.
  -->
  <Task id="$int" name="ScssToCss" description="$string" enabled="true|false">
    <!-- 
      The SCSS (.scss) files loaded by the task having as id $taskId will be converted to CSS files.
    -->
    <Setting name="selectFiles" value="$taskId" />
    <Setting name="selectFiles" value="$taskId" />
    <!-- You can add as many selecteFiles as you want.-->
  </Task>
</Tasks>
