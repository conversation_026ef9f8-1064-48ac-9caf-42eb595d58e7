﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    Tar a sequential task that creates a .tar from a collection of files.
    
    The Tar file generated will be loaded by this task so that other
    tasks can select it through the selectFiles option.
  -->
  <Task id="$int" name="Tar" description="$string" enabled="true|false">
    <!-- 
      The files loaded by the task having as id $taskId will be
      added to the Tar file.
    -->
    <Setting name="selectFiles" value="$taskId" />
    <Setting name="selectFiles" value="$taskId" />
    <!-- You can add as many selecteFiles as you want.-->
    <!-- The Tar file name. Example: output.tar-->
    <Setting name="zipFileName" value="$string" />
  </Task>
</Tasks>
