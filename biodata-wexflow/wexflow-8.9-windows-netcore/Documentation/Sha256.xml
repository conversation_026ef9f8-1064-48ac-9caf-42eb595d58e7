﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    Sha1 is a sequential task that generates SHA-256 hashes of a collection of files. The results are written
    to an XML file as follows:
    <Files>
      <File path="$filePath" name="$fileName" sha256="ddd23773d8724b8a40749046d8e3414402507a21a1ccc522653197b0803d268b" />
      <File path="$filePath" name="$fileName" sha256="ddd23773d8724b8a40749046d8e3414402507a21a1ccc522653197b0803d268b" />
      <File path="$filePath" name="$fileName" sha256="ddd23773d8724b8a40749046d8e3414402507a21a1ccc522653197b0803d268b" />
      ...
   </Files>
   
   The XML file generated will be loaded by the Md5 task so that other tasks can select 
   it through the selectFiles option.
  -->
  <Task id="$int" name="Sha256" description="$string" enabled="true|false">
    <!-- 
      The files loaded by the task having as id $taskId will be
      used to calculate their SHA-256 hashes.
    -->
    <Setting name="selectFiles" value="$taskId" />
    <Setting name="selectFiles" value="$taskId" />
    <!-- You can add as many selecteFiles as you want.-->
  </Task>
</Tasks>