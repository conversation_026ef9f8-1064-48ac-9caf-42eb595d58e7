﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    SqlToCsv is a sequential task that executes a collection of SQL scripts (select queries) and outputs the results in CSV files as follows:
    
    columnName;columnName;columnName;...
    columnValue;columnValue;columnValue;...
    ...
    
    This task supports Microsoft SQL Server, Microsoft Access, Oracle, MySQL, SQLite, PostgreSQL, Teradata and Odbc.
    
    The CSV files generated are loaded by this task so that other tasks can select them through the selectFiles setting.
  -->
  <Task id="$int" name="SqlToCsv" description="$string" enabled="true|false">
    <!-- 
      The SQL script files loaded by the task having as id $taskId will be
      executed.
    -->
    <Setting name="selectFiles" value="$taskId" />
    <Setting name="selectFiles" value="$taskId" />
    <!-- You can add as many selecteFiles as you want.-->
    <!-- The database engine type. Possible options: sqlserver|access|oracle|mysql|sqlite|postgresql|teradata|odbc-->
    <Setting name="type" value="sqlserver|access|oracle|mysql|sqlite|postgresql|teradata|odbc" />
    <!--- The connection string.-->
    <Setting name="connectionString" value="$string" />
    <!-- Optional. It is possible to execute an SQL script through this option.-->
    <Setting name="sql" value="$string" />
    <!-- Optional and defaults to ';'. The separtor character in the CSV files.-->
    <Setting name="separator" value="$string" />
    <!-- Optional and defaults to empty string. The quote character for the values. example: ' or &quot;-->
    <Setting name="quote" value="$string" />
    <!-- Optional and defaults to true. True to include the headers, False otherwise.-->
    <Setting name="headers" value="$bool" />
    <!-- Optional and defaults to \r\n. End of line string.-->
    <Setting name="endline" value="$string" />
    <!-- Optional and defaults to false. If set to true and the result set is empty, no file will be generated. If set to false, a file will be generated even if the result set is empty.-->
    <Setting name="doNotGenerateFilesIfEmpty" value="$bool" />
  </Task>
</Tasks>
