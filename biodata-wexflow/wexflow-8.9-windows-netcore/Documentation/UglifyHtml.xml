﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    UglifyHtml is a sequential task that compresses and minifies HTML files.
    
    The minified files generated are loaded by this task so that other tasks can select them through the selectFiles option.
  -->
  <Task id="$int" name="UglifyHtml" description="$string" enabled="true|false">
    <!-- 
      The HTML (.html) files loaded by the task having as id $taskId will be compressed and minified.
    -->
    <Setting name="selectFiles" value="$taskId" />
    <Setting name="selectFiles" value="$taskId" />
    <!-- You can add as many selecteFiles as you want.-->
  </Task>
</Tasks>
