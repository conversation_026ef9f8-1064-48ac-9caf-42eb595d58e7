﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    FilesLoaderEx is a sequential task that loads a collection of files located in folders or through the file option.
    This task is inherited from FilesLoader task, but by default result is empty, you must configure file system attributes rules to populate result. -->
  -->
  <Task id="$int" name="FilesLoaderEx" description="$string" enabled="true|false">
    <!-- The files located the the folder option will be loaded. For example, if the option 
          value is C:\Watchfolder\ the files located in this folder will be loaded so that
          other tasks can work on them.
     -->
    <Setting name="folder" value="$string" />
    <!-- You can add as many folder options as you want.-->
    <!-- The file path of the file to load. For example, if the option 
          value is C:\MyFolder\MyFile.txt the file will be loaded so that
          other tasks can work on it.
     -->
    <Setting name="file" value="$string" />
    <Setting name="file" value="$string" />
    <!-- You can add as many file options as you want.-->
    <!-- Optional. The files loaded from the folders matching the regex pattern will be loaded. 
    Example: .*\.mp4 will load only mp4 files. The regex is case sensitive.-->
    <Setting name="regexPattern" value="$string" />
    <!-- Optional and by default false. If set to true the files in subfolders will be loaded recursively. -->
    <Setting name="recursive" value="true|false" />

    <!-- File System Rules -->

    <!-- AddRules: If greater than 0, it will be included in the result. -->
    <!-- Optional and defaults to 0. Only N files with the highest creation date will be loaded. -->
    <Setting name="addMaxCreateDate" value="$int" />
    <!-- Optional and defaults to 0. Only N files with the smallest creation date will be loaded. -->
    <Setting name="addMinCreateDate" value="$int" />
    <!-- Optional and defaults to 0. Only the N files with the most modified date will be loaded. -->
    <Setting name="addMaxModifyDate" value="$int" />
    <!-- Optional and defaults to 0. Only N files with the smallest modified date will be loaded. -->
    <Setting name="addMinModifyDate" value="$int" />
    <!-- End of AddRules -->

    <!-- RemoveRules: If greater than 0, the entire listing will be included in the result except these items.-->
    <!-- Optional and default to 0. The N files with the highest creation date will NOT be loaded. -->
    <Setting name="removeMaxCreateDate" value="$int" />
    <!-- Optional and default to 0. The N files with the smallest creation date will NOT be loaded. -->
    <Setting name="removeMinCreateDate" value="$int" />
    <!-- Optional and default to 0. The N files with the highest modified date will NOT be loaded. -->
    <Setting name="removeMaxModifyDate" value="$int" />
    <!-- Optional and default to 0. The N files with the smallest modified date will NOT be loaded. -->
    <Setting name="removeMinModifyDate" value="$int" />
    <!-- End of RemoveRules -->
  </Task>
</Tasks>