﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    FileNotMatch is a flowchart task that checks whether a file does not exist in a directory by using a regex pattern.
  -->
  <Task id="$int" name="FileNotMatch" description="$string" enabled="true|false">
    <!-- The path of the directory to check. Example: C:\WexflowTesting\ -->
    <Setting name="dir" value="$string" />
    <!-- The regex pattern. It is case sensitive! Ex: .*\.txt -->
    <Setting name="pattern" value=".*\.txt" />
    <!-- Optional and defaults to false. Indicates whether to check the sub-directories recursively. -->
    <Setting name="recursive" value="false" />
  </Task>
</Tasks>