﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    HttpDelete is a sequential task that executes a DELETE request.
    
    If the request outputs a response the result is written in a file that is loaded by the task so that other tasks can select it through selectFiles option.
  -->
  <Task id="$int" name="HttpDelete" description="$string" enabled="true|false">
    <!-- The URL. Ex: http://localhost:8000/wexflow/delete/999 -->
    <Setting name="url" value="$string" />
    <!-- Optional. Ex: Basic or Bearer.-->
    <Setting name="authorizationScheme" value="$string" />
    <!-- Optional. authorization parameter value.-->
    <Setting name="authorizationParameter" value="$string" />
  </Task>
</Tasks>
