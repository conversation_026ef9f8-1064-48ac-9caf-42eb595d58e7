﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    Sha1 is a sequential task that generates SHA-512 hashes of a collection of files. The results are written
    to an XML file as follows:
    <Files>
      <File path="$filePath" name="$fileName" sha512="37f1ee35382e6e7918ce5cedbca431e58073af561b4681704df8ea495e048ddd0c890995302893beaecc946890673d1b6d6d2428fabe7ace0564c57a5454fb2f" />
      <File path="$filePath" name="$fileName" sha512="37f1ee35382e6e7918ce5cedbca431e58073af561b4681704df8ea495e048ddd0c890995302893beaecc946890673d1b6d6d2428fabe7ace0564c57a5454fb2f" />
      <File path="$filePath" name="$fileName" sha512="37f1ee35382e6e7918ce5cedbca431e58073af561b4681704df8ea495e048ddd0c890995302893beaecc946890673d1b6d6d2428fabe7ace0564c57a5454fb2f" />
      ...
   </Files>
   
   The XML file generated will be loaded by the Md5 task so that other tasks can select 
   it through the selectFiles option.
  -->
  <Task id="$int" name="Sha512" description="$string" enabled="true|false">
    <!-- 
      The files loaded by the task having as id $taskId will be
      used to calculate their SHA-512 hashes.
    -->
    <Setting name="selectFiles" value="$taskId" />
    <Setting name="selectFiles" value="$taskId" />
    <!-- You can add as many selecteFiles as you want.-->
  </Task>
</Tasks>