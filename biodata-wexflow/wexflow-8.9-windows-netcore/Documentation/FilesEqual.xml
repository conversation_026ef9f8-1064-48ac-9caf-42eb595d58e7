﻿<?xml version="1.0" encoding="utf-8" ?>
<Tasks>
  <!--
    FilesEqual is a sequential task that checks whether two files are the same.
    
    The result is written in XML file in this format:
    
    <Root>
      <Files>
        <File path="C:\WexflowTesting\file1.txt" name="file1.txt" />
        <File path="C:\WexflowTesting\file2.txt" name="file2.txt" />
      </Files>
      <Result>false</Result>
    </Root>

    The XML file generated will be loaded by this task so that other
    tasks can select it through the selectFiles option.
  -->
  <Task id="$int" name="FilesEqual" description="$string" enabled="true|false">
    <!--  The file path of the first file to check.-->
    <Setting name="file1" value="$string" />
    <!--  The file path of the second file to check.-->
    <Setting name="file2" value="$string" />
  </Task>
</Tasks>
