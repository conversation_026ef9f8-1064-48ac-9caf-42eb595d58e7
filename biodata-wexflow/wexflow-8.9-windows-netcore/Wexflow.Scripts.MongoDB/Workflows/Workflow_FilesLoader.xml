<Workflow xmlns="urn:wexflow-schema" id="3" name="Workflow_FilesLoader" description="Workflow_FilesLoader">
	<Settings>
		<Setting name="launchType" value="trigger" /> <!-- startup|trigger|periodic -->
		<Setting name="enabled" value="true" /> <!-- true|false -->
	</Settings>
	<Tasks>
		<Task id="1" name="FilesLoader" description="Loading files" enabled="true">
			<Setting name="folder" value="C:\WexflowTesting\Watchfolder1\" />
			<Setting name="folder" value="C:\WexflowTesting\Watchfolder2\" />
			<Setting name="file" value="C:\WexflowTesting\file1.txt" />
			<Setting name="file" value="C:\WexflowTesting\file2.txt" />
			<Setting name="file" value="C:\WexflowTesting\file3.txt" />
			<Setting name="file" value="C:\WexflowTesting\file4.txt" />
			<Setting name="file" value="C:\WexflowTesting\file5.txt" />
			
			<Setting name="regexPattern" value=".*\.mp4" />
			<Setting name="recursive" value="true" />
		</Task>
		<Task id="2" name="ListFiles" description="Listing files" enabled="true">
		</Task>
	</Tasks>
</Workflow>
