﻿<?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="75" name="Workflow_Cron" description="Workflow_Cron">
  <Settings>
    <Setting name="launchType" value="cron" />
	<Setting name="cronExpression" value="0 0/1 * * * ?" /> <!-- Every one minute. -->
    <Setting name="enabled" value="false" />
  </Settings>
  <Tasks>
    <Task id="1" name="FilesLoader" description="Loading files" enabled="true">
      <Setting name="file" value="C:\WexflowTesting\file1.txt" />
    </Task>
	<Task id="2" name="Wait" description="Wait for 10 seconds..." enabled="true">
			<Setting name="duration" value="00.00:00:10" />
		</Task>
    <Task id="3" name="FilesCopier" description="Copying files" enabled="true">
      <Setting name="selectFiles" value="1" />
      <Setting name="destFolder" value="C:\WexflowTesting\Cron" />
      <Setting name="overwrite" value="true" />
    </Task>
  </Tasks>
</Workflow>