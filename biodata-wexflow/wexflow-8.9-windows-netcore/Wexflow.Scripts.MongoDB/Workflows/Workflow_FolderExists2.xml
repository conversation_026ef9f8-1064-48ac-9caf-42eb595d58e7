<Workflow xmlns="urn:wexflow-schema" id="125" name="Workflow_FolderExists2" description="Workflow_FolderExists2">
	<Settings>
		<Setting name="launchType" value="trigger" />
		<Setting name="enabled" value="true" />
	</Settings>
	<Tasks>
		<Task id="99" name="FolderExists" description="Checking folder..." enabled="true">
			<Setting name="folder" value="C:\DoesNotExist" />
		</Task>
		<Task id="1" name="Wait" description="Waiting for 1 seconds..." enabled="true">
			<Setting name="duration" value="00.00:00:01" />
		</Task>
		<Task id="2" name="Wait" description="Waiting for 2 seconds..." enabled="true">
			<Setting name="duration" value="00.00:00:02" />
		</Task>
	</Tasks>
	<ExecutionGraph>
		<If id="100" parent="-1" if="99">
			<Do>
				<Task id="1"><Parent id="-1" /></Task>
			</Do>
			<Else>
				<Task id="2"><Parent id="-1" /></Task>
			</Else>
		</If>
	</ExecutionGraph>
</Workflow>