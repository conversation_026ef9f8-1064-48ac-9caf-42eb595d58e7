﻿<?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="1" name="Workflow_CsvToXml" description="Workflow_CsvToXml">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
  </Settings>
  <Tasks>
    <Task id="1" name="FilesLoader" description="Loading CSVs" enabled="true">
      <Setting name="file" value="C:\WexflowTesting\CsvToXml\csv1.csv" />
      <Setting name="file" value="C:\WexflowTesting\CsvToXml\csv2.csv" />
    </Task>
    <Task id="2" name="CsvToXml" description="Generating XMl files from CSVs" enabled="true">
      <Setting name="selectFiles" value="1" />
    </Task>
    <Task id="3" name="FilesMover" description="Moving Xmls to CsvToXml folder" enabled="true">
      <Setting name="selectFiles" value="2" />
      <Setting name="destFolder" value="C:\WexflowTesting\CsvToXml\" />
	  <Setting name="overwrite" value="true" />
    </Task>
  </Tasks>
</Workflow>