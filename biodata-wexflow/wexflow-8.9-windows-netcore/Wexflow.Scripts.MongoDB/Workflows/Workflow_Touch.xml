<Workflow xmlns="urn:wexflow-schema" id="15" name="Workflow_Touch" description="Workflow_Touch">
	<Settings>
		<Setting name="launchType" value="trigger" />
		<Setting name="enabled" value="true" />
	</Settings>
	<Tasks>
		<Task id="1" name="Touch" description="Touching files" enabled="true">
			<Setting name="file" value="C:\WexflowTesting\trigger1.empty" />
			<Setting name="file" value="C:\WexflowTesting\trigger2.empty" />
		</Task>
		<Task id="2" name="FilesMover" description="Moving triggers to Triggers folder" enabled="true">
			<Setting name="selectFiles" value="1" />
			<Setting name="destFolder" value="C:\WexflowTesting\Triggers\" />
			<Setting name="overwrite" value="true" />
		</Task>
		<Task id="3" name="ListFiles" description="Listing files" enabled="true">
		</Task>
	</Tasks>
</Workflow>
