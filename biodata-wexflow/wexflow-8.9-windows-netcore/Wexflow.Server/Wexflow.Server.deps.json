{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {}, ".NETCoreApp,Version=v9.0/win-x64": {"Wexflow.Server/8.9.0": {"dependencies": {"Microsoft.AspNetCore.Owin": "8.0.8", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Newtonsoft.Json": "13.0.3", "ServiceStack.Kestrel": "8.4.0", "Wexflow.Core": "8.9.0", "Wexflow.Tasks.Approval": "8.9.0", "Wexflow.Tasks.ApprovalRecordsCreator": "8.9.0", "Wexflow.Tasks.ApprovalWorkflowsCreator": "8.9.0", "Wexflow.Tasks.ApproveRecord": "8.9.0", "Wexflow.Tasks.CsvToJson": "6.1.0", "Wexflow.Tasks.CsvToSql": "8.9.0", "Wexflow.Tasks.CsvToXml": "8.9.0", "Wexflow.Tasks.CsvToYaml": "8.9.0", "Wexflow.Tasks.EnvironmentVariable": "8.9.0", "Wexflow.Tasks.ExecPython": "8.9.0", "Wexflow.Tasks.FileContentMatch": "8.9.0", "Wexflow.Tasks.FileExists": "8.9.0", "Wexflow.Tasks.FileMatch": "8.9.0", "Wexflow.Tasks.FileNotExist": "8.9.0", "Wexflow.Tasks.FileNotMatch": "8.9.0", "Wexflow.Tasks.FileSystemWatcher": "8.9.0", "Wexflow.Tasks.FilesConcat": "8.9.0", "Wexflow.Tasks.FilesCopier": "8.9.0", "Wexflow.Tasks.FilesDecryptor": "8.9.0", "Wexflow.Tasks.FilesDiff": "8.9.0", "Wexflow.Tasks.FilesEncryptor": "8.9.0", "Wexflow.Tasks.FilesEqual": "8.9.0", "Wexflow.Tasks.FilesExist": "8.9.0", "Wexflow.Tasks.FilesInfo": "8.9.0", "Wexflow.Tasks.FilesJoiner": "8.9.0", "Wexflow.Tasks.FilesLoader": "8.9.0", "Wexflow.Tasks.FilesLoaderEx": "8.9.0", "Wexflow.Tasks.FilesMover": "8.9.0", "Wexflow.Tasks.FilesRemover": "8.9.0", "Wexflow.Tasks.FilesRenamer": "8.9.0", "Wexflow.Tasks.FilesSplitter": "8.9.0", "Wexflow.Tasks.FolderExists": "8.9.0", "Wexflow.Tasks.Ftp": "8.9.0", "Wexflow.Tasks.Guid": "8.9.0", "Wexflow.Tasks.Http": "8.9.0", "Wexflow.Tasks.HttpDelete": "8.9.0", "Wexflow.Tasks.HttpGet": "8.9.0", "Wexflow.Tasks.HttpPatch": "8.9.0", "Wexflow.Tasks.HttpPost": "8.9.0", "Wexflow.Tasks.HttpPut": "8.9.0", "Wexflow.Tasks.ImagesConcat": "8.9.0", "Wexflow.Tasks.ImagesCropper": "8.9.0", "Wexflow.Tasks.ImagesOverlay": "8.9.0", "Wexflow.Tasks.ImagesResizer": "8.9.0", "Wexflow.Tasks.ImagesTransformer": "8.9.0", "Wexflow.Tasks.InstagramUploadImage": "8.9.0", "Wexflow.Tasks.InstagramUploadVideo": "8.9.0", "Wexflow.Tasks.JsonToYaml": "6.1.0", "Wexflow.Tasks.ListEntities": "8.9.0", "Wexflow.Tasks.ListFiles": "8.9.0", "Wexflow.Tasks.MailsReceiver": "8.9.0", "Wexflow.Tasks.MailsSender": "8.9.0", "Wexflow.Tasks.Md5": "6.0.0", "Wexflow.Tasks.MessageCorrect": "8.9.0", "Wexflow.Tasks.Mkdir": "8.9.0", "Wexflow.Tasks.Movedir": "8.9.0", "Wexflow.Tasks.Now": "8.9.0", "Wexflow.Tasks.Ping": "8.9.0", "Wexflow.Tasks.ProcessInfo": "8.9.0", "Wexflow.Tasks.ProcessLauncher": "8.9.0", "Wexflow.Tasks.Reddit": "8.9.0", "Wexflow.Tasks.RedditListComments": "8.9.0", "Wexflow.Tasks.RedditListPosts": "8.9.0", "Wexflow.Tasks.Rmdir": "6.0.0", "Wexflow.Tasks.ScssToCss": "6.2.0", "Wexflow.Tasks.Sha1": "8.9.0", "Wexflow.Tasks.Sha256": "8.9.0", "Wexflow.Tasks.Sha512": "8.9.0", "Wexflow.Tasks.Slack": "8.9.0", "Wexflow.Tasks.Sql": "8.9.0", "Wexflow.Tasks.SqlToCsv": "8.9.0", "Wexflow.Tasks.SqlToXml": "8.9.0", "Wexflow.Tasks.SshCmd": "6.0.0", "Wexflow.Tasks.SubWorkflow": "8.9.0", "Wexflow.Tasks.Tar": "8.9.0", "Wexflow.Tasks.TextsDecryptor": "8.9.0", "Wexflow.Tasks.TextsEncryptor": "8.9.0", "Wexflow.Tasks.Tgz": "8.9.0", "Wexflow.Tasks.Torrent": "8.9.0", "Wexflow.Tasks.Touch": "8.9.0", "Wexflow.Tasks.Twilio": "8.9.0", "Wexflow.Tasks.Twitter": "8.9.0", "Wexflow.Tasks.UglifyCss": "6.2.0", "Wexflow.Tasks.UglifyHtml": "8.9.0", "Wexflow.Tasks.UglifyJs": "6.1.0", "Wexflow.Tasks.Untar": "8.9.0", "Wexflow.Tasks.Untgz": "8.9.0", "Wexflow.Tasks.Unzip": "8.9.0", "Wexflow.Tasks.Vimeo": "8.9.0", "Wexflow.Tasks.VimeoListUploads": "8.9.0", "Wexflow.Tasks.Wait": "8.9.0", "Wexflow.Tasks.XmlToCsv": "7.0.0", "Wexflow.Tasks.Xslt": "8.9.0", "Wexflow.Tasks.YamlToJson": "6.1.0", "Wexflow.Tasks.YouTube": "8.9.0", "Wexflow.Tasks.YouTubeListUploads": "8.9.0", "Wexflow.Tasks.YouTubeSearch": "8.9.0", "Wexflow.Tasks.Zip": "8.9.0"}, "runtime": {"Wexflow.Server.dll": {}}}, "Autofac/4.9.4": {"runtime": {"lib/netstandard2.0/Autofac.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AWSSDK.Core/**********": {"runtime": {"lib/netcoreapp3.1/AWSSDK.Core.dll": {"assemblyVersion": "*******", "fileVersion": "**********"}}}, "AWSSDK.SecurityToken/**********": {"dependencies": {"AWSSDK.Core": "**********"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.SecurityToken.dll": {"assemblyVersion": "*******", "fileVersion": "**********"}}}, "BouncyCastle.Cryptography/2.4.0": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "2.4.0.33771"}}}, "DiffPlex/1.7.2": {"runtime": {"lib/net6.0/DiffPlex.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/net5.0/DnsClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FirebirdSql.Data.FirebirdClient/10.3.1": {"runtime": {"lib/net8.0/FirebirdSql.Data.FirebirdClient.dll": {"assemblyVersion": "10.3.1.0", "fileVersion": "10.3.1.0"}}}, "FluentFTP/51.1.0": {"runtime": {"lib/net6.0/FluentFTP.dll": {"assemblyVersion": "51.1.0.0", "fileVersion": "51.1.0.0"}}}, "Google.Apis/1.68.0": {"dependencies": {"Google.Apis.Core": "1.68.0"}, "runtime": {"lib/net6.0/Google.Apis.dll": {"assemblyVersion": "1.68.0.0", "fileVersion": "1.68.0.0"}}}, "Google.Apis.Auth/1.68.0": {"dependencies": {"Google.Apis": "1.68.0", "Google.Apis.Core": "1.68.0", "System.Management": "7.0.2"}, "runtime": {"lib/net6.0/Google.Apis.Auth.dll": {"assemblyVersion": "1.68.0.0", "fileVersion": "1.68.0.0"}}}, "Google.Apis.Core/1.68.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/Google.Apis.Core.dll": {"assemblyVersion": "1.68.0.0", "fileVersion": "1.68.0.0"}}}, "Google.Apis.YouTube.v3/1.68.0.3539": {"dependencies": {"Google.Apis": "1.68.0", "Google.Apis.Auth": "1.68.0"}, "runtime": {"lib/net6.0/Google.Apis.YouTube.v3.dll": {"assemblyVersion": "1.68.0.3539", "fileVersion": "1.68.0.3539"}}}, "IdentityModel/5.2.0": {"runtime": {"lib/net5.0/IdentityModel.dll": {"assemblyVersion": "5.2.0.0", "fileVersion": "5.2.0.0"}}}, "IdentityModel.OidcClient/5.0.0": {"dependencies": {"IdentityModel": "5.2.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/netstandard2.0/IdentityModel.OidcClient.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "InstagramApiSharp/1.7.4": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3", "Portable.BouncyCastle": "*******"}, "runtime": {"lib/netstandard2.0/InstagramApiSharp.dll": {"assemblyVersion": "1.7.4.0", "fileVersion": "1.7.4.0"}}}, "JetBrains.Annotations/2023.2.0": {"runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"assemblyVersion": "4242.42.42.42", "fileVersion": "2023.2.0.0"}}}, "Lambda2Js.Signed/3.1.4": {"runtime": {"lib/netstandard2.0/Lambda2Js.Signed.dll": {"assemblyVersion": "3.1.4.0", "fileVersion": "3.1.4.0"}}}, "LiteDB/5.0.21": {"dependencies": {"System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/LiteDB.dll": {"assemblyVersion": "5.0.21.0", "fileVersion": "5.0.21.0"}}}, "log4net/3.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "runtime": {"lib/netstandard2.0/log4net.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.0"}}}, "MailKit/4.7.1.1": {"dependencies": {"MimeKit": "4.7.1", "System.Formats.Asn1": "8.0.1"}, "runtime": {"lib/net8.0/MailKit.dll": {"assemblyVersion": "*******", "fileVersion": "4.7.1.1"}}}, "Microsoft.AspNetCore.JsonPatch/8.0.8": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "8.0.8.0", "fileVersion": "8.0.824.36908"}}}, "Microsoft.AspNetCore.Owin/8.0.8": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.Owin.dll": {"assemblyVersion": "8.0.8.0", "fileVersion": "8.0.824.36908"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {}, "Microsoft.Extensions.Http/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.IdentityModel.Abstractions/7.4.0": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.4.0.50226"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.4.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.4.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.4.0.50226"}}}, "Microsoft.IdentityModel.Logging/7.4.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.4.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "7.4.0.50226"}}}, "Microsoft.IdentityModel.Tokens/7.4.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.4.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.4.0.50226"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.Microsoft.Win32.Primitives": "4.3.0"}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "MimeKit/4.7.1": {"dependencies": {"BouncyCastle.Cryptography": "2.4.0", "System.Formats.Asn1": "8.0.1", "System.Security.Cryptography.Pkcs": "8.0.0"}, "runtime": {"lib/net8.0/MimeKit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MongoDB.Bson/2.29.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.1/MongoDB.Bson.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver/2.29.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "MongoDB.Bson": "2.29.0", "MongoDB.Driver.Core": "2.29.0", "MongoDB.Libmongocrypt": "1.12.0"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver.Core/2.29.0": {"dependencies": {"AWSSDK.SecurityToken": "**********", "DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "MongoDB.Bson": "2.29.0", "MongoDB.Libmongocrypt": "1.12.0", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.7.3"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Libmongocrypt/1.12.0": {"runtime": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {"assemblyVersion": "1.12.0.0", "fileVersion": "1.12.0.0"}}, "native": {"runtimes/win/native/mongocrypt.dll": {"fileVersion": "0.0.0.0"}}}, "Mono.Nat/3.0.0": {"runtime": {"lib/netstandard2.1/Mono.Nat.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.0"}}}, "MonoTorrent/3.0.2": {"dependencies": {"Mono.Nat": "3.0.0", "ReusableTasks": "4.0.0"}, "runtime": {"lib/net6.0/MonoTorrent.BEncoding.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.2.0"}, "lib/net6.0/MonoTorrent.Client.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.2.0"}, "lib/net6.0/MonoTorrent.Connections.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.2.0"}, "lib/net6.0/MonoTorrent.Dht.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.2.0"}, "lib/net6.0/MonoTorrent.Factories.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.2.0"}, "lib/net6.0/MonoTorrent.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.2.0"}, "lib/net6.0/MonoTorrent.PiecePicking.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.2.0"}, "lib/net6.0/MonoTorrent.PieceWriter.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.2.0"}, "lib/net6.0/MonoTorrent.PortForwarding.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.2.0"}, "lib/net6.0/MonoTorrent.Trackers.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.2.0"}, "lib/net6.0/MonoTorrent.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.2.0"}}}, "MySqlConnector/2.3.7": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "2.3.7.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "Nito.AsyncEx.Coordination/5.1.2": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.2", "Nito.Collections.Deque": "1.1.1"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}}, "Nito.AsyncEx.Tasks/5.1.2": {"dependencies": {"Nito.Disposables": "2.2.1"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}}, "Nito.Collections.Deque/1.1.1": {"runtime": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"assemblyVersion": "1.1.1.0", "fileVersion": "1.1.1.0"}}}, "Nito.Disposables/2.2.1": {"dependencies": {"System.Collections.Immutable": "1.7.1"}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"assemblyVersion": "2.2.1.0", "fileVersion": "2.2.1.0"}}}, "Npgsql/8.0.3": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "8.0.3.0", "fileVersion": "8.0.3.0"}}}, "NUglify/1.21.9": {"runtime": {"lib/net5.0/NUglify.dll": {"assemblyVersion": "1.21.9.0", "fileVersion": "1.21.9.0"}}}, "Oracle.ManagedDataAccess.Core/23.5.1": {"dependencies": {"System.Diagnostics.PerformanceCounter": "8.0.0", "System.DirectoryServices.Protocols": "8.0.0", "System.Formats.Asn1": "8.0.1", "System.Security.Cryptography.Pkcs": "8.0.0"}, "runtime": {"lib/netstandard2.1/Oracle.ManagedDataAccess.dll": {"assemblyVersion": "23.1.0.0", "fileVersion": "23.1.0.0"}}}, "Portable.BouncyCastle/*******": {"runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "1.8.6.0", "fileVersion": "*******"}}}, "Quartz/3.13.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/Quartz.dll": {"assemblyVersion": "3.13.0.0", "fileVersion": "3.13.0.0"}}}, "Quartz.Serialization.Json/3.13.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Quartz": "3.13.0"}, "runtime": {"lib/netstandard2.0/Quartz.Serialization.Json.dll": {"assemblyVersion": "3.13.0.0", "fileVersion": "3.13.0.0"}}}, "RavenDB.Client/6.2.0": {"dependencies": {"Lambda2Js.Signed": "3.1.4", "Microsoft.AspNetCore.JsonPatch": "8.0.8", "Newtonsoft.Json": "13.0.3", "Nito.AsyncEx.Coordination": "5.1.2"}, "runtime": {"lib/net8.0/Raven.Client.dll": {"assemblyVersion": "*******", "fileVersion": "6.2.0.62001"}, "lib/net8.0/Sparrow.dll": {"assemblyVersion": "*******", "fileVersion": "6.2.0.62001"}}, "native": {"runtimes/win-x64/native/libzstd.win.x64.dll": {"fileVersion": "0.0.0.0"}}}, "Reddit/1.5.2": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3", "RestSharp": "106.6.9"}, "runtime": {"lib/netstandard2.0/Reddit.NET.dll": {"assemblyVersion": "1.5.2.0", "fileVersion": "1.5.2.0"}}}, "RestSharp/106.6.9": {"runtime": {"lib/netstandard2.0/RestSharp.dll": {"assemblyVersion": "106.6.9.0", "fileVersion": "106.6.9.0"}}}, "ReusableTasks/4.0.0": {"runtime": {"lib/net5.0/ReusableTasks.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.0.0"}}}, "runtime.any.System.Collections/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "runtime.any.System.Diagnostics.Tracing/4.3.0": {}, "runtime.any.System.Globalization/4.3.0": {}, "runtime.any.System.Globalization.Calendars/4.3.0": {}, "runtime.any.System.IO/4.3.0": {}, "runtime.any.System.Reflection/4.3.0": {}, "runtime.any.System.Reflection.Primitives/4.3.0": {}, "runtime.any.System.Resources.ResourceManager/4.3.0": {}, "runtime.any.System.Runtime/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "runtime.any.System.Runtime.Handles/4.3.0": {}, "runtime.any.System.Runtime.InteropServices/4.3.0": {}, "runtime.any.System.Text.Encoding/4.3.0": {}, "runtime.any.System.Text.Encoding.Extensions/4.3.0": {}, "runtime.any.System.Threading.Tasks/4.3.0": {}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"native": {"runtimes/win-x64/native/sni.dll": {"fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "runtime.win.Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "runtime.win.System.Diagnostics.Debug/4.3.0": {}, "runtime.win.System.IO.FileSystem/4.3.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Overlapped": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "runtime.win.System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.Win32.Primitives": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0"}}, "runtime.win.System.Runtime.Extensions/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "ServiceStack/8.4.0": {"dependencies": {"ServiceStack.Client": "8.4.0", "ServiceStack.Common": "8.4.0", "ServiceStack.Interfaces": "8.4.0", "ServiceStack.Text": "8.4.0"}, "runtime": {"lib/net8.0/ServiceStack.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ServiceStack.Client/8.4.0": {"dependencies": {"Microsoft.Extensions.Http": "8.0.0", "ServiceStack.Interfaces": "8.4.0", "ServiceStack.Text": "8.4.0"}, "runtime": {"lib/net8.0/ServiceStack.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ServiceStack.Common/8.4.0": {"dependencies": {"ServiceStack.Interfaces": "8.4.0", "ServiceStack.Text": "8.4.0", "System.Memory": "4.5.5"}, "runtime": {"lib/net8.0/ServiceStack.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ServiceStack.Interfaces/8.4.0": {"runtime": {"lib/net8.0/ServiceStack.Interfaces.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ServiceStack.Kestrel/8.4.0": {"dependencies": {"ServiceStack": "8.4.0", "ServiceStack.Client": "8.4.0", "ServiceStack.Common": "8.4.0", "ServiceStack.Interfaces": "8.4.0", "ServiceStack.Text": "8.4.0", "System.Memory": "4.5.5"}, "runtime": {"lib/net8.0/ServiceStack.Kestrel.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ServiceStack.Text/8.4.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Memory": "4.5.5"}, "runtime": {"lib/net8.0/ServiceStack.Text.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpCompress/0.30.1": {"runtime": {"lib/net5.0/SharpCompress.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SharpScss/2.0.0": {"runtime": {"lib/netstandard2.0/SharpScss.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "native": {"runtimes/win-x64/native/libsass.dll": {"fileVersion": "0.0.0.0"}}}, "SharpZipLib/1.4.2": {"runtime": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SkiaSharp/2.88.8": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.8", "SkiaSharp.NativeAssets.macOS": "2.88.8"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.8": {"dependencies": {"SkiaSharp": "2.88.8"}}, "SkiaSharp.NativeAssets.macOS/2.88.8": {}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"native": {"runtimes/win-x64/native/libSkiaSharp.dll": {"fileVersion": "0.0.0.0"}}}, "SlackAPI/1.1.14": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.Net.Http": "4.3.4"}, "runtime": {"lib/netstandard2.0/SlackAPI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Snappier/1.0.0": {"runtime": {"lib/net5.0/Snappier.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SSH.NET/2024.1.0": {"runtime": {"lib/net8.0/Renci.SshNet.dll": {"assemblyVersion": "2024.1.0.0", "fileVersion": "2024.1.0.0"}}}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.118": {"runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"assemblyVersion": "1.0.118.0", "fileVersion": "1.0.118.0"}}, "native": {"runtimes/win-x64/native/SQLite.Interop.dll": {"fileVersion": "1.0.118.0"}}}, "System.Buffers/4.5.1": {}, "System.CodeDom/7.0.0": {"runtime": {"lib/net7.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Collections": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/1.7.1": {}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Configuration.ConfigurationManager/8.0.0": {"dependencies": {"System.Diagnostics.EventLog": "8.0.0", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Data.Odbc/8.0.0": {"dependencies": {"System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"runtimes/win/lib/net8.0/System.Data.Odbc.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Data.OleDb/8.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.0", "System.Diagnostics.PerformanceCounter": "8.0.0"}, "runtime": {"runtimes/win/lib/net8.0/System.Data.OleDb.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Data.SqlClient/4.8.6": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}}, "System.Data.SQLite.Core/1.0.118": {"dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "1.0.118"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Diagnostics.Debug": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/8.0.0": {}, "System.Diagnostics.EventLog/8.0.0": {}, "System.Diagnostics.PerformanceCounter/8.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "runtime": {"runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Diagnostics.Tracing": "4.3.0"}}, "System.DirectoryServices.Protocols/8.0.0": {"runtime": {"runtimes/win/lib/net8.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Formats.Asn1/8.0.1": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Globalization": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Globalization.Calendars": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.34.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.4.0", "Microsoft.IdentityModel.Tokens": "7.4.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.34.0.0", "fileVersion": "6.34.0.41017"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.any.System.IO": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.win.System.IO.FileSystem": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Management/7.0.2": {"dependencies": {"System.CodeDom": "7.0.0"}, "runtime": {"runtimes/win/lib/net7.0/System.Management.dll": {"assemblyVersion": "7.0.0.2", "fileVersion": "7.0.723.27404"}}}, "System.Memory/4.5.5": {}, "System.Net.Http/4.3.4": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "runtime.win.System.Net.Primitives": "4.3.0"}}, "System.Private.Uri/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Primitives": "4.3.0"}}, "System.Reflection.TypeExtensions/4.5.1": {}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Resources.ResourceManager": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.any.System.Runtime": "4.3.0"}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "runtime.any.System.Runtime.InteropServices": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Pkcs/8.0.0": {"dependencies": {"System.Formats.Asn1": "8.0.1"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Text.Encoding": "4.3.0"}}, "System.Text.Encoding.CodePages/8.0.0": {}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.any.System.Text.Encoding.Extensions": "4.3.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Overlapped/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Tasks": "4.3.0"}}, "Teradata.Client.Provider/20.0.3": {"dependencies": {"IdentityModel.OidcClient": "5.0.0", "Microsoft.IdentityModel.JsonWebTokens": "7.4.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/net6.0/Teradata.Client.Provider.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net6.0/Teradata.Net.Security.Tdgss.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "resources": {"lib/net6.0/ja/Teradata.Client.Provider.resources.dll": {"locale": "ja"}, "lib/net6.0/ja/Teradata.Net.Security.Tdgss.resources.dll": {"locale": "ja"}}}, "TweetinviAPI/5.0.4": {"dependencies": {"Autofac": "4.9.4", "Newtonsoft.Json": "13.0.3", "System.Collections": "4.3.0", "System.Net.Http": "4.3.4", "System.Reflection.TypeExtensions": "4.5.1"}, "runtime": {"lib/netstandard2.0/Tweetinvi.Controllers.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.0/Tweetinvi.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.0/Tweetinvi.Credentials.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.0/Tweetinvi.Logic.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.0/Tweetinvi.Security.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.0/Tweetinvi.Streams.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.0/Tweetinvi.WebLogic.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.0/Tweetinvi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Twilio/7.3.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.4.0", "Newtonsoft.Json": "13.0.3", "System.Collections.Specialized": "4.3.0", "System.IdentityModel.Tokens.Jwt": "6.34.0"}, "runtime": {"lib/net6.0/Twilio.dll": {"assemblyVersion": "7.3.1.0", "fileVersion": "7.3.1.0"}}}, "VimeoDotNet/3.2.4": {"dependencies": {"JetBrains.Annotations": "2023.2.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/VimeoDotNet.dll": {"assemblyVersion": "2.2020.522.0", "fileVersion": "2.2020.522.0"}}}, "YamlDotNet/16.1.2": {"runtime": {"lib/net8.0/YamlDotNet.dll": {"assemblyVersion": "1*******", "fileVersion": "16.1.2.0"}}}, "ZstdSharp.Port/0.7.3": {"runtime": {"lib/net7.0/ZstdSharp.dll": {"assemblyVersion": "0.7.3.0", "fileVersion": "0.7.3.0"}}}, "Wexflow.Core/8.9.0": {"dependencies": {"Quartz": "3.13.0", "Quartz.Serialization.Json": "3.13.0", "Wexflow.Core.Db": "8.9.0", "Wexflow.Core.Db.Firebird": "8.9.0", "Wexflow.Core.Db.LiteDB": "8.9.0", "Wexflow.Core.Db.MariaDB": "8.9.0", "Wexflow.Core.Db.MongoDB": "8.9.0", "Wexflow.Core.Db.MySQL": "8.9.0", "Wexflow.Core.Db.Oracle": "8.9.0", "Wexflow.Core.Db.PostgreSQL": "8.9.0", "Wexflow.Core.Db.RavenDB": "8.9.0", "Wexflow.Core.Db.SQLServer": "8.9.0", "Wexflow.Core.Db.SQLite": "8.9.0", "log4net": "3.0.0"}, "runtime": {"Wexflow.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Core.Db/8.9.0": {"runtime": {"Wexflow.Core.Db.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Core.Db.Firebird/8.9.0": {"dependencies": {"FirebirdSql.Data.FirebirdClient": "10.3.1", "Wexflow.Core.Db": "8.9.0"}, "runtime": {"Wexflow.Core.Db.Firebird.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Core.Db.LiteDB/8.9.0": {"dependencies": {"LiteDB": "5.0.21", "Wexflow.Core.Db": "8.9.0"}, "runtime": {"Wexflow.Core.Db.LiteDB.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Core.Db.MariaDB/8.9.0": {"dependencies": {"MySqlConnector": "2.3.7", "Wexflow.Core.Db": "8.9.0"}, "runtime": {"Wexflow.Core.Db.MariaDB.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Core.Db.MongoDB/8.9.0": {"dependencies": {"MongoDB.Driver": "2.29.0", "Wexflow.Core.Db": "8.9.0"}, "runtime": {"Wexflow.Core.Db.MongoDB.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Core.Db.MySQL/8.9.0": {"dependencies": {"MySqlConnector": "2.3.7", "Wexflow.Core.Db": "8.9.0"}, "runtime": {"Wexflow.Core.Db.MySQL.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Core.Db.Oracle/8.9.0": {"dependencies": {"Oracle.ManagedDataAccess.Core": "23.5.1", "Wexflow.Core.Db": "8.9.0"}, "runtime": {"Wexflow.Core.Db.Oracle.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Core.Db.PostgreSQL/8.9.0": {"dependencies": {"Npgsql": "8.0.3", "Wexflow.Core.Db": "8.9.0"}, "runtime": {"Wexflow.Core.Db.PostgreSQL.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Core.Db.RavenDB/8.9.0": {"dependencies": {"RavenDB.Client": "6.2.0", "Wexflow.Core.Db": "8.9.0"}, "runtime": {"Wexflow.Core.Db.RavenDB.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Core.Db.SQLite/8.9.0": {"dependencies": {"System.Data.SQLite.Core": "1.0.118", "Wexflow.Core.Db": "8.9.0"}, "runtime": {"Wexflow.Core.Db.SQLite.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Core.Db.SQLServer/8.9.0": {"dependencies": {"System.Data.SqlClient": "4.8.6", "Wexflow.Core.Db": "8.9.0"}, "runtime": {"Wexflow.Core.Db.SQLServer.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Approval/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Approval.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.ApprovalRecordsCreator/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.ApprovalRecordsCreator.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.ApprovalWorkflowsCreator/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0", "Wexflow.Core.Db": "8.9.0"}, "runtime": {"Wexflow.Tasks.ApprovalWorkflowsCreator.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.ApproveRecord/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0", "Wexflow.Core.Db": "8.9.0"}, "runtime": {"Wexflow.Tasks.ApproveRecord.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.CsvToJson/6.1.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.CsvToJson.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.CsvToSql/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.CsvToSql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.CsvToXml/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.CsvToXml.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.CsvToYaml/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0", "YamlDotNet": "16.1.2"}, "runtime": {"Wexflow.Tasks.CsvToYaml.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.EnvironmentVariable/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.EnvironmentVariable.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.ExecPython/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.ExecPython.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FileContentMatch/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FileContentMatch.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FileExists/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FileExists.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FileMatch/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FileMatch.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FileNotExist/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FileNotExist.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FileNotMatch/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FileNotMatch.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FilesConcat/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FilesConcat.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FilesCopier/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FilesCopier.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FilesDecryptor/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FilesDecryptor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FilesDiff/8.9.0": {"dependencies": {"DiffPlex": "1.7.2", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FilesDiff.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FilesEncryptor/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FilesEncryptor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FilesEqual/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FilesEqual.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FilesExist/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FilesExist.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FilesInfo/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FilesInfo.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FilesJoiner/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FilesJoiner.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FilesLoader/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FilesLoader.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FilesLoaderEx/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FilesLoaderEx.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FilesMover/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FilesMover.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FilesRemover/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FilesRemover.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FilesRenamer/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FilesRenamer.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FilesSplitter/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FilesSplitter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FileSystemWatcher/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FileSystemWatcher.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.FolderExists/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.FolderExists.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Ftp/8.9.0": {"dependencies": {"FluentFTP": "51.1.0", "SSH.NET": "2024.1.0", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Ftp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Guid/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Guid.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Http/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.HttpDelete/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.HttpDelete.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.HttpGet/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.HttpGet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.HttpPatch/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.HttpPatch.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.HttpPost/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.HttpPost.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.HttpPut/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.HttpPut.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.ImagesConcat/8.9.0": {"dependencies": {"SkiaSharp.NativeAssets.Linux.NoDependencies": "2.88.8", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.ImagesConcat.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.ImagesCropper/8.9.0": {"dependencies": {"SkiaSharp.NativeAssets.Linux.NoDependencies": "2.88.8", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.ImagesCropper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.ImagesOverlay/8.9.0": {"dependencies": {"SkiaSharp.NativeAssets.Linux.NoDependencies": "2.88.8", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.ImagesOverlay.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.ImagesResizer/8.9.0": {"dependencies": {"SkiaSharp.NativeAssets.Linux.NoDependencies": "2.88.8", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.ImagesResizer.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.ImagesTransformer/8.9.0": {"dependencies": {"SkiaSharp.NativeAssets.Linux.NoDependencies": "2.88.8", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.ImagesTransformer.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.InstagramUploadImage/8.9.0": {"dependencies": {"InstagramApiSharp": "1.7.4", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.InstagramUploadImage.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.InstagramUploadVideo/8.9.0": {"dependencies": {"InstagramApiSharp": "1.7.4", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.InstagramUploadVideo.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.JsonToYaml/6.1.0": {"dependencies": {"Wexflow.Core": "8.9.0", "YamlDotNet": "16.1.2"}, "runtime": {"Wexflow.Tasks.JsonToYaml.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.ListEntities/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.ListEntities.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.ListFiles/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.ListFiles.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.MailsReceiver/8.9.0": {"dependencies": {"MailKit": "4.7.1.1", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.MailsReceiver.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.MailsSender/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.MailsSender.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Md5/6.0.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Md5.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.MessageCorrect/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.MessageCorrect.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Mkdir/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Mkdir.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Movedir/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Movedir.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Now/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Now.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Ping/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.ProcessInfo/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.ProcessInfo.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.ProcessLauncher/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.ProcessLauncher.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Reddit/8.9.0": {"dependencies": {"Reddit": "1.5.2", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Reddit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.RedditListComments/8.9.0": {"dependencies": {"Reddit": "1.5.2", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.RedditListComments.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.RedditListPosts/8.9.0": {"dependencies": {"Reddit": "1.5.2", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.RedditListPosts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Rmdir/6.0.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Rmdir.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.ScssToCss/6.2.0": {"dependencies": {"SharpScss": "2.0.0", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.ScssToCss.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Sha1/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Sha1.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Sha256/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Sha256.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Sha512/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Sha512.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Slack/8.9.0": {"dependencies": {"SlackAPI": "1.1.14", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Slack.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Sql/8.9.0": {"dependencies": {"MySqlConnector": "2.3.7", "Npgsql": "8.0.3", "Oracle.ManagedDataAccess.Core": "23.5.1", "System.Data.Odbc": "8.0.0", "System.Data.OleDb": "8.0.0", "System.Data.SQLite.Core": "1.0.118", "Teradata.Client.Provider": "20.0.3", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Sql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.SqlToCsv/8.9.0": {"dependencies": {"MySqlConnector": "2.3.7", "Npgsql": "8.0.3", "Oracle.ManagedDataAccess.Core": "23.5.1", "System.Data.Odbc": "8.0.0", "System.Data.OleDb": "8.0.0", "System.Data.SQLite.Core": "1.0.118", "Teradata.Client.Provider": "20.0.3", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.SqlToCsv.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.SqlToXml/8.9.0": {"dependencies": {"MySqlConnector": "2.3.7", "Npgsql": "8.0.3", "Oracle.ManagedDataAccess.Core": "23.5.1", "System.Data.Odbc": "8.0.0", "System.Data.OleDb": "8.0.0", "System.Data.SQLite.Core": "1.0.118", "Teradata.Client.Provider": "20.0.3", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.SqlToXml.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.SshCmd/6.0.0": {"dependencies": {"SSH.NET": "2024.1.0", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.SshCmd.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.SubWorkflow/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.SubWorkflow.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Tar/8.9.0": {"dependencies": {"SharpZipLib": "1.4.2", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.TextsDecryptor/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.TextsDecryptor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.TextsEncryptor/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.TextsEncryptor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Tgz/8.9.0": {"dependencies": {"SharpZipLib": "1.4.2", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Tgz.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Torrent/8.9.0": {"dependencies": {"MonoTorrent": "3.0.2", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Torrent.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Touch/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Touch.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Twilio/8.9.0": {"dependencies": {"Twilio": "7.3.1", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Twilio.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Twitter/8.9.0": {"dependencies": {"TweetinviAPI": "5.0.4", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Twitter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.UglifyCss/6.2.0": {"dependencies": {"NUglify": "1.21.9", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.UglifyCss.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.UglifyHtml/8.9.0": {"dependencies": {"NUglify": "1.21.9", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.UglifyHtml.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.UglifyJs/6.1.0": {"dependencies": {"NUglify": "1.21.9", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.UglifyJs.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Untar/8.9.0": {"dependencies": {"SharpZipLib": "1.4.2", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Untar.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Untgz/8.9.0": {"dependencies": {"SharpZipLib": "1.4.2", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Untgz.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Unzip/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Unzip.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Vimeo/8.9.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "VimeoDotNet": "3.2.4", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Vimeo.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.VimeoListUploads/8.9.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "VimeoDotNet": "3.2.4", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.VimeoListUploads.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Wait/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Wait.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.XmlToCsv/7.0.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.XmlToCsv.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Xslt/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Xslt.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.YamlToJson/6.1.0": {"dependencies": {"Wexflow.Core": "8.9.0", "YamlDotNet": "16.1.2"}, "runtime": {"Wexflow.Tasks.YamlToJson.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.YouTube/8.9.0": {"dependencies": {"Google.Apis.YouTube.v3": "1.68.0.3539", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.YouTube.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.YouTubeListUploads/8.9.0": {"dependencies": {"Google.Apis.YouTube.v3": "1.68.0.3539", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.YouTubeListUploads.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.YouTubeSearch/8.9.0": {"dependencies": {"Google.Apis.YouTube.v3": "1.68.0.3539", "Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.YouTubeSearch.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Wexflow.Tasks.Zip/8.9.0": {"dependencies": {"Wexflow.Core": "8.9.0"}, "runtime": {"Wexflow.Tasks.Zip.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Wexflow.Server/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Autofac/4.9.4": {"type": "package", "serviceable": true, "sha512": "sha512-dYiDG4yoV7GDCoOc66d1sqMmg2gwDyomHKgATuMfhFzYxVpWEltToHVHXI3I9vOEvIXE55S4OZPY0ldiZmZtpA==", "path": "autofac/4.9.4", "hashPath": "autofac.4.9.4.nupkg.sha512"}, "AWSSDK.Core/**********": {"type": "package", "serviceable": true, "sha512": "sha512-gnEgxBlk4PFEfdPE8Lkf4+D16MZFYSaW7/o6Wwe5e035QWUkTJX0Dn4LfTCdV5QSEL/fOFxu+yCAm55eIIBgog==", "path": "awssdk.core/**********", "hashPath": "awssdk.core.**********.nupkg.sha512"}, "AWSSDK.SecurityToken/**********": {"type": "package", "serviceable": true, "sha512": "sha512-dGCVuVo0CFUKWW85W8YENO+aREf8sCBDjvGbnNvxJuNW4Ss+brEU9ltHhq2KfZze2VUNK1/wygbPG1bmbpyXEw==", "path": "awssdk.securitytoken/**********", "hashPath": "awssdk.securitytoken.**********.nupkg.sha512"}, "BouncyCastle.Cryptography/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-SwXsAV3sMvAU/Nn31pbjhWurYSjJ+/giI/0n6tCrYoupEK34iIHCuk3STAd9fx8yudM85KkLSVdn951vTng/vQ==", "path": "bouncycastle.cryptography/2.4.0", "hashPath": "bouncycastle.cryptography.2.4.0.nupkg.sha512"}, "DiffPlex/1.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-qJEjdxEDBWSFZGB8paBB9HDeJXHGlHlOXeGX3kbTuXWuOsgv2iSAEOOzo5V1/B39Vcxr9IVVrNKewRcX+rsn4g==", "path": "diffplex/1.7.2", "hashPath": "diffplex.1.7.2.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "FirebirdSql.Data.FirebirdClient/10.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-LDKlTIH3Tivi8sMkwrolVcIZWJDTwxBCugE+eEZRY6pFnK75gmJnV0u4FkC73M6ERbCKUvY7m5ox8VkicJZubg==", "path": "firebirdsql.data.firebirdclient/10.3.1", "hashPath": "firebirdsql.data.firebirdclient.10.3.1.nupkg.sha512"}, "FluentFTP/51.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vm3eGvHACns0zsKfCMCZdZSFKYu2ajV/GeRk4vfgy6d5AR+WqSUjaSWbbhkmS01A8OMjZHFLduPH7nxwGvlcpQ==", "path": "fluentftp/51.1.0", "hashPath": "fluentftp.51.1.0.nupkg.sha512"}, "Google.Apis/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-s2MymhdpH+ybZNBeZ2J5uFgFHApBp+QXf9FjZSdM1lk/vx5VqIknJwnaWiuAzXxPrLEkesX0Q+UsiWn39yZ9zw==", "path": "google.apis/1.68.0", "hashPath": "google.apis.1.68.0.nupkg.sha512"}, "Google.Apis.Auth/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-hFx8Qz5bZ4w0hpnn4tSmZaaFpjAMsgVElZ+ZgVLUZ2r9i+AKcoVgwiNfv1pruNS5cCvpXqhKECbruBCfRezPHA==", "path": "google.apis.auth/1.68.0", "hashPath": "google.apis.auth.1.68.0.nupkg.sha512"}, "Google.Apis.Core/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-pAqwa6pfu53UXCR2b7A/PAPXeuVg6L1OFw38WckN27NU2+mf+KTjoEg2YGv/f0UyKxzz7DxF1urOTKg/6dTP9g==", "path": "google.apis.core/1.68.0", "hashPath": "google.apis.core.1.68.0.nupkg.sha512"}, "Google.Apis.YouTube.v3/1.68.0.3539": {"type": "package", "serviceable": true, "sha512": "sha512-nQDz5hjS8LESncWXHXqMgKWjksf4JyWFwWLB7YpR+iQ6mSd18kG6gzLjCTqPkNfga5HF3cbXCUv3bA2Cp7qVtg==", "path": "google.apis.youtube.v3/1.68.0.3539", "hashPath": "google.apis.youtube.v3.1.68.0.3539.nupkg.sha512"}, "IdentityModel/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-nuhkbaDH9l5QzNJp2MtP3qio57MPtiRneUN8Ocr7od0JvSYaIe3gBj/vxllr11S/Qvu1AG4GZXoyv5469ewYDA==", "path": "identitymodel/5.2.0", "hashPath": "identitymodel.5.2.0.nupkg.sha512"}, "IdentityModel.OidcClient/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4KTG0+M3UBxr0UraGo8MfqEGT9MeMDtaBvPyZccmwW+JH+UG3psW5IVITKgobpgWmK+OKoE2FQQ4XBlaLfyCyw==", "path": "identitymodel.oidcclient/5.0.0", "hashPath": "identitymodel.oidcclient.5.0.0.nupkg.sha512"}, "InstagramApiSharp/1.7.4": {"type": "package", "serviceable": true, "sha512": "sha512-a9R2pxWdKsWFwAOZg0oWArpT34F/UJp04h9mYG3F9tJVw7/qucztkqqyyyqjHWrrsVq1gGKrkAOYOJqZQ+YjZA==", "path": "instagramapisharp/1.7.4", "hashPath": "instagramapisharp.1.7.4.nupkg.sha512"}, "JetBrains.Annotations/2023.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-dvO//8aLmLRsCVVgoc/7qBqi2/y4BTyRcg20LCBWtK4n6E9Um06Zp7jF1n0hOE+yqBHwcrDzAjWvCaM3qH8flg==", "path": "jetbrains.annotations/2023.2.0", "hashPath": "jetbrains.annotations.2023.2.0.nupkg.sha512"}, "Lambda2Js.Signed/3.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-Ryzu7T7ZzjQxTGxCSInAe0ZO3gbskW09qizdnrTUbZeNOyJzbdcQ39ylj4mJEYFeOUO1DQ1RqtBhQdiaMTSC6g==", "path": "lambda2js.signed/3.1.4", "hashPath": "lambda2js.signed.3.1.4.nupkg.sha512"}, "LiteDB/5.0.21": {"type": "package", "serviceable": true, "sha512": "sha512-ykJ7ffFl7P9YQKR/PLci6zupiLrsSCNkOTiw6OtzntH7d2kCYp5L1+3a/pksKgTEHcJBoPXFtg7VZSGVBseN9w==", "path": "litedb/5.0.21", "hashPath": "litedb.5.0.21.nupkg.sha512"}, "log4net/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qMdZQuo3u7aurPbcyO55xe6GykvhVOxIFqzdA1uMPQb4IldIMez8z4jA71PBSuZqTljzYxtjIGcIhiKjt5F1cw==", "path": "log4net/3.0.0", "hashPath": "log4net.3.0.0.nupkg.sha512"}, "MailKit/4.7.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Y3okmIxu8g/ZcoJiE2i+dCeKgnNyddsXmcJslZnCPGVPP0aRyeVINHV1h97V+OVMdqjQI6O12J2p8Duwq5UEqQ==", "path": "mailkit/4.7.1.1", "hashPath": "mailkit.4.7.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-IGhuO/SsjHIIvFP4O/5pn/WcPJor+A+BERBhIkMYrlYcRXnZmbBBNSyqoNI9wFq0oxtsrnYMnzXAIi+0MKVdSA==", "path": "microsoft.aspnetcore.jsonpatch/8.0.8", "hashPath": "microsoft.aspnetcore.jsonpatch.8.0.8.nupkg.sha512"}, "Microsoft.AspNetCore.Owin/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-oY9p5sCP2WA5V2VsxzSY5aXgYXlEGMd3JQuxEgv88IeMuyoYnJjn8ivbj23//2F6weGuTrNVpQJwb7KFtyZO2w==", "path": "microsoft.aspnetcore.owin/8.0.8", "hashPath": "microsoft.aspnetcore.owin.8.0.8.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cWz4caHwvx0emoYe7NkHPxII/KkTI8R/LC9qdqJqnKv2poTJ4e2qqPGQqvRoQ5kaSA4FU5IV3qFAuLuOhoqULQ==", "path": "microsoft.extensions.http/8.0.0", "hashPath": "microsoft.extensions.http.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y5wvSoKT6G9gi94FmTwyMCRDjMBVwCnDTXT9yyy95jS3lq3IRXJ/Fh+tqmsluA181RzAtXgK5SsvGGkcWm2n0g==", "path": "microsoft.identitymodel.abstractions/7.4.0", "hashPath": "microsoft.identitymodel.abstractions.7.4.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-8Gxy/KSjzEXg4hEyMvR0EsuLpaYLERoX1grIxechhoS8ugUyVvX9zxhMUg0EC/Oi3slhQ+rWrvN6M/M26RnRLQ==", "path": "microsoft.identitymodel.jsonwebtokens/7.4.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.4.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-TuCWnlQprih0PieviKAdvHtjuVLUjEBymEUlBqAe8BUBWlMjB7eHaZ1pi5mkPR5bbrVWLYu8/qiMVc0+neE6dQ==", "path": "microsoft.identitymodel.logging/7.4.0", "hashPath": "microsoft.identitymodel.logging.7.4.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2GXnFtFNv51QaIFw7pC3+OSRG+wDxj53MY6o6DRFFp/wAsTUTeasN+7Gkc6zSuI4cbrU9KXSVzs2cnW6EQ5szg==", "path": "microsoft.identitymodel.tokens/7.4.0", "hashPath": "microsoft.identitymodel.tokens.7.4.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "MimeKit/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-Qoj4aVvhX14A1FNvaJ33hzOP4VZI2j+Mr38I9wSGcjMq4BYDtWLJG89aJ9nRW2cNfH6Czjwyp7+Mh++xv3AZvg==", "path": "mimekit/4.7.1", "hashPath": "mimekit.4.7.1.nupkg.sha512"}, "MongoDB.Bson/2.29.0": {"type": "package", "serviceable": true, "sha512": "sha512-wz8UtxdjnknHRJuMatTRr2QMlh7k9457BRfaDQxl+OiKV01vMzm1K0/jTvQJqORV5eba6HrCAzMJzj7nnt5cag==", "path": "mongodb.bson/2.29.0", "hashPath": "mongodb.bson.2.29.0.nupkg.sha512"}, "MongoDB.Driver/2.29.0": {"type": "package", "serviceable": true, "sha512": "sha512-1IfTnHX8G2SsXIHDz80OVSmcdL95vzzWXGGucR45k7+TUOcWVhiqYcc13ckRXl4NVxn4UTRrrKl+92xvjwWrRA==", "path": "mongodb.driver/2.29.0", "hashPath": "mongodb.driver.2.29.0.nupkg.sha512"}, "MongoDB.Driver.Core/2.29.0": {"type": "package", "serviceable": true, "sha512": "sha512-3M7gdmuLEay4Wc/q4zm/oA5KT4E62SsRBiq3prpT3tGEPkfstr3N3fYRbzYNu8TJgeyS0q5OPe4zI4yi6/GemQ==", "path": "mongodb.driver.core/2.29.0", "hashPath": "mongodb.driver.core.2.29.0.nupkg.sha512"}, "MongoDB.Libmongocrypt/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-B1X51jrtNacKvxKoaqWeknYeJfQS5aWf6BmVLT5JZerz3AUXFzv8edPskJYqBc3kLy1J2PWzMqqsnyb9g8FtcA==", "path": "mongodb.libmongocrypt/1.12.0", "hashPath": "mongodb.libmongocrypt.1.12.0.nupkg.sha512"}, "Mono.Nat/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LgRC8/8plJfvr1nCPmyHNIwP4fs346doMA/UVDZvFN0RfGJdyD2uS7Dl1AydxjVLrIvad8ghozI0vYFWcfPeYQ==", "path": "mono.nat/3.0.0", "hashPath": "mono.nat.3.0.0.nupkg.sha512"}, "MonoTorrent/3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-kWy0mLj2MKX9uUGe3lnNYfyQhthjUK4IAMNJO8llXP9yB3ZYfGaAy9ag20wMcjTz2YfhKOW/uZ5FR//BYZGeYA==", "path": "monotorrent/3.0.2", "hashPath": "monotorrent.3.0.2.nupkg.sha512"}, "MySqlConnector/2.3.7": {"type": "package", "serviceable": true, "sha512": "sha512-YiVOxvJ+vAYW8NT9gHv8RxKCDFCSXAObF3z0Ou/8WRv8Lsn2QsxaPW5xEwPE+xCcAq6BGkrI8GTOC09Xg09blQ==", "path": "mysqlconnector/2.3.7", "hashPath": "mysqlconnector.2.3.7.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Nito.AsyncEx.Coordination/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-QMyUfsaxov//0ZMbOHWr9hJaBFteZd66DV1ay4J5wRODDb8+K/uHC7+3VsOflo6SVw/29mu8OWZp8vMDSuzc0w==", "path": "nito.asyncex.coordination/5.1.2", "hashPath": "nito.asyncex.coordination.5.1.2.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-jEkCfR2/M26OK/U4G7SEN063EU/F4LiVA06TtpZILMdX/quIHCg+wn31Zerl2LC+u1cyFancjTY3cNAr2/89PA==", "path": "nito.asyncex.tasks/5.1.2", "hashPath": "nito.asyncex.tasks.5.1.2.nupkg.sha512"}, "Nito.Collections.Deque/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-CU0/Iuv5VDynK8I8pDLwkgF0rZhbQoZahtodfL0M3x2gFkpBRApKs8RyMyNlAi1mwExE4gsmqQXk4aFVvW9a4Q==", "path": "nito.collections.deque/1.1.1", "hashPath": "nito.collections.deque.1.1.1.nupkg.sha512"}, "Nito.Disposables/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-6sZ5uynQeAE9dPWBQGKebNmxbY4xsvcc5VplB5WkYEESUS7oy4AwnFp0FhqxTSKm/PaFrFqLrYr696CYN8cugg==", "path": "nito.disposables/2.2.1", "hashPath": "nito.disposables.2.2.1.nupkg.sha512"}, "Npgsql/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-6WEmzsQJCZAlUG1pThKg/RmeF6V+I0DmBBBE/8YzpRtEzhyZzKcK7ulMANDm5CkxrALBEC8H+5plxHWtIL7xnA==", "path": "npgsql/8.0.3", "hashPath": "npgsql.8.0.3.nupkg.sha512"}, "NUglify/1.21.9": {"type": "package", "serviceable": true, "sha512": "sha512-ULyI/scrIRAo2In6cnaCc/QkWUt+wB6pBVt5lrVddOKyamsCAm1XgArkk4px9tVn6SipjhbTt4M38QPlpoET+g==", "path": "nuglify/1.21.9", "hashPath": "nuglify.1.21.9.nupkg.sha512"}, "Oracle.ManagedDataAccess.Core/23.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-OYcTrQeUh3CEUYuUA0rSzdnqjVDvbQRByfeIxQub4WJShlbeY/o2NdKUuP87rU8TlGADf2ZPKsxqUdke90KNNg==", "path": "oracle.manageddataaccess.core/23.5.1", "hashPath": "oracle.manageddataaccess.core.23.5.1.nupkg.sha512"}, "Portable.BouncyCastle/*******": {"type": "package", "serviceable": true, "sha512": "sha512-RBCAkzkBkur4w1YXFpwVdYvdm4ez7PUNyl5DsqPKDe0QSoH9xt6Mwzy1NSFSrrw4T7bCkxi06nSHCuBEuMEgfw==", "path": "portable.bouncycastle/*******", "hashPath": "portable.bouncycastle.*******.nupkg.sha512"}, "Quartz/3.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-ddD1CPjWcDMbKdAhzuna3Ng23Sdv1YtuHbFMdAAFw0eBEj9jnadYkDWiKGo/c7BMnbI/ruuyd370sKVgdMCkDA==", "path": "quartz/3.13.0", "hashPath": "quartz.3.13.0.nupkg.sha512"}, "Quartz.Serialization.Json/3.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nm8klDppHsttLLfbchWOAOb2bB5mgwbuALpNY+a8Yd+C4kx6RiTvmwbM2l/8qifMOcJxyFvfWTWyoB69ubwTWw==", "path": "quartz.serialization.json/3.13.0", "hashPath": "quartz.serialization.json.3.13.0.nupkg.sha512"}, "RavenDB.Client/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-tPeCd1oDxqSM0lt4SSt0uhXblH0DVRkFRzQegx/F1id5pr1bwNgkyRFjYV/YMPranP9PMm3/WMJqsgA8eolJhA==", "path": "ravendb.client/6.2.0", "hashPath": "ravendb.client.6.2.0.nupkg.sha512"}, "Reddit/1.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-a8fLFxuSBG/NANlcdj6tmeAS1gnZNV+0HesL3bEvnVBimiE7+MQGi9AMcIq8AFAGY2Kv21p369NZgadD2HEMKA==", "path": "reddit/1.5.2", "hashPath": "reddit.1.5.2.nupkg.sha512"}, "RestSharp/106.6.9": {"type": "package", "serviceable": true, "sha512": "sha512-/IAQTwlnlQcjlIyvelf+rhQcCRPy/r/0MftpNbff/D7PZVnmVXE4AnHk/F4NJJJEZbI4M+QTglB+xFkAyzr+9A==", "path": "restsharp/106.6.9", "hashPath": "restsharp.106.6.9.nupkg.sha512"}, "ReusableTasks/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6EKzscjq+Q+uxD0N5GWUBO2N34cuDiJ08ZxRs1lqS0Hwvnjp6AgimUIX/8dqIxRZbPOG4gcikTMo+PM4D9CfaQ==", "path": "reusabletasks/4.0.0", "hashPath": "reusabletasks.4.0.0.nupkg.sha512"}, "runtime.any.System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-23g6rqftKmovn2cLeGsuHUYm0FD7pdutb0uQMJpZ3qTvq+zHkgmt6J65VtRry4WDGYlmkMa4xDACtaQ94alNag==", "path": "runtime.any.system.collections/4.3.0", "hashPath": "runtime.any.system.collections.4.3.0.nupkg.sha512"}, "runtime.any.System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1lpifymjGDzoYIaam6/Hyqf8GhBI3xXYLK2TgEvTtuZMorG3Kb9QnMTIKhLjJYXIiu1JvxjngHvtVFQQlpQ3HQ==", "path": "runtime.any.system.diagnostics.tracing/4.3.0", "hashPath": "runtime.any.system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "runtime.any.System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sMDBnad4rp4t7GY442Jux0MCUuKL4otn5BK6Ni0ARTXTSpRNBzZ7hpMfKSvnVSED5kYJm96YOWsqV0JH0d2uuw==", "path": "runtime.any.system.globalization/4.3.0", "hashPath": "runtime.any.system.globalization.4.3.0.nupkg.sha512"}, "runtime.any.System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-M1r+760j1CNA6M/ZaW6KX8gOS8nxPRqloqDcJYVidRG566Ykwcs29AweZs2JF+nMOCgWDiMfPSTMfvwOI9F77w==", "path": "runtime.any.system.globalization.calendars/4.3.0", "hashPath": "runtime.any.system.globalization.calendars.4.3.0.nupkg.sha512"}, "runtime.any.System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SDZ5AD1DtyRoxYtEcqQ3HDlcrorMYXZeCt7ZhG9US9I5Vva+gpIWDGMkcwa5XiKL0ceQKRZIX2x0XEjLX7PDzQ==", "path": "runtime.any.system.io/4.3.0", "hashPath": "runtime.any.system.io.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hLC3A3rI8jipR5d9k7+f0MgRCW6texsAp0MWkN/ci18FMtQ9KH7E2vDn/DH2LkxsszlpJpOn9qy6Z6/69rH6eQ==", "path": "runtime.any.system.reflection/4.3.0", "hashPath": "runtime.any.system.reflection.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nrm1p3armp6TTf2xuvaa+jGTTmncALWFq22CpmwRvhDf6dE9ZmH40EbOswD4GnFLrMRS0Ki6Kx5aUPmKK/hZBg==", "path": "runtime.any.system.reflection.primitives/4.3.0", "hashPath": "runtime.any.system.reflection.primitives.4.3.0.nupkg.sha512"}, "runtime.any.System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lxb89SMvf8w9p9+keBLyL6H6x/TEmc6QVsIIA0T36IuyOY3kNvIdyGddA2qt35cRamzxF8K5p0Opq4G4HjNbhQ==", "path": "runtime.any.system.resources.resourcemanager/4.3.0", "hashPath": "runtime.any.system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fRS7zJgaG9NkifaAxGGclDDoRn9HC7hXACl52Or06a/fxdzDajWb5wov3c6a+gVSlekRoexfjwQSK9sh5um5LQ==", "path": "runtime.any.system.runtime/4.3.0", "hashPath": "runtime.any.system.runtime.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GG84X6vufoEzqx8PbeBKheE4srOhimv+yLtGb/JkR3Y2FmoqmueLNFU4Xx8Y67plFpltQSdK74x0qlEhIpv/CQ==", "path": "runtime.any.system.runtime.handles/4.3.0", "hashPath": "runtime.any.system.runtime.handles.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lBoFeQfxe/4eqjPi46E0LU/YaCMdNkQ8B4MZu/mkzdIAZh8RQ1NYZSj0egrQKdgdvlPFtP4STtob40r4o2DBAw==", "path": "runtime.any.system.runtime.interopservices/4.3.0", "hashPath": "runtime.any.system.runtime.interopservices.4.3.0.nupkg.sha512"}, "runtime.any.System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+ihI5VaXFCMVPJNstG4O4eo1CfbrByLxRrQQTqOTp1ttK0kUKDqOdBSTaCB2IBk/QtjDrs6+x4xuezyMXdm0HQ==", "path": "runtime.any.system.text.encoding/4.3.0", "hashPath": "runtime.any.system.text.encoding.4.3.0.nupkg.sha512"}, "runtime.any.System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NLrxmLsfRrOuVqPWG+2lrQZnE53MLVeo+w9c54EV+TUo4c8rILpsDXfY8pPiOy9kHpUHHP07ugKmtsU3vVW5Jg==", "path": "runtime.any.system.text.encoding.extensions/4.3.0", "hashPath": "runtime.any.system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "runtime.any.System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OhBAVBQG5kFj1S+hCEQ3TUHBAEtZ3fbEMgZMRNdN8A0Pj4x+5nTELEqL59DU0TjKVE6II3dqKw4Dklb3szT65w==", "path": "runtime.any.system.threading.tasks/4.3.0", "hashPath": "runtime.any.system.threading.tasks.4.3.0.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win.Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NU51SEt/ZaD2MF48sJ17BIqx7rjeNNLXUevfMOjqQIetdndXwYjZfZsT6jD+rSWp/FYxjesdK4xUSl4OTEI0jw==", "path": "runtime.win.microsoft.win32.primitives/4.3.0", "hashPath": "runtime.win.microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "runtime.win.System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hHHP0WCStene2jjeYcuDkETozUYF/3sHVRHAEOgS3L15hlip24ssqCTnJC28Z03Wpo078oMcJd0H4egD2aJI8g==", "path": "runtime.win.system.diagnostics.debug/4.3.0", "hashPath": "runtime.win.system.diagnostics.debug.4.3.0.nupkg.sha512"}, "runtime.win.System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z37zcSCpXuGCYtFbqYO0TwOVXxS2d+BXgSoDFZmRg8BC4Cuy54edjyIvhhcfCrDQA9nl+EPFTgHN54dRAK7mNA==", "path": "runtime.win.system.io.filesystem/4.3.0", "hashPath": "runtime.win.system.io.filesystem.4.3.0.nupkg.sha512"}, "runtime.win.System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lkXXykakvXUU+Zq2j0pC6EO20lEhijjqMc01XXpp1CJN+DeCwl3nsj4t5Xbpz3kA7yQyTqw6d9SyIzsyLsV3zA==", "path": "runtime.win.system.net.primitives/4.3.0", "hashPath": "runtime.win.system.net.primitives.4.3.0.nupkg.sha512"}, "runtime.win.System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RkgHVhUPvzZxuUubiZe8yr/6CypRVXj0VBzaR8hsqQ8f+rUo7e4PWrHTLOCjd8fBMGWCrY//fi7Ku3qXD7oHRw==", "path": "runtime.win.system.runtime.extensions/4.3.0", "hashPath": "runtime.win.system.runtime.extensions.4.3.0.nupkg.sha512"}, "ServiceStack/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-DuAL8hJ2ouhHoDbi4p7/nLnrF2wbc1j7i8lAsIGU/7LzGYczVOKvTeBihuwdCUJMurO2hzO0Cv9s/S1KURNV6g==", "path": "servicestack/8.4.0", "hashPath": "servicestack.8.4.0.nupkg.sha512"}, "ServiceStack.Client/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-p+IgeW8e089mSJm4Wdil20XMRd2z3HDXqseWRhbGyVRSmGEm2n4eMAL8vgxA6JO2KCduh2cm3NxOVKiDNee0Tw==", "path": "servicestack.client/8.4.0", "hashPath": "servicestack.client.8.4.0.nupkg.sha512"}, "ServiceStack.Common/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-SynptUlyMGz++l4u3t63GpdZoqRIh5XBJJDdY6MsvvybNhWuoeA0vI/gsjQ/ArvG0JOVYEDFY4tRnJ32R3soOA==", "path": "servicestack.common/8.4.0", "hashPath": "servicestack.common.8.4.0.nupkg.sha512"}, "ServiceStack.Interfaces/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-Dh9i5B6Xwb3BN47KP+4TG8j/BcBUI9FcWSiM1tJWfDTIcRLYTW91+gS74kURyu6nI9Oa4uDrfDwjVbJc6MzR/w==", "path": "servicestack.interfaces/8.4.0", "hashPath": "servicestack.interfaces.8.4.0.nupkg.sha512"}, "ServiceStack.Kestrel/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-TBgtjCh6UZ3PkUNhdEasSGeCMgI4cuwt8JtQNR23Rx4s+NsK1RCswDYSaJe95GsHWJcAKCeVxdiKfTdESgw4ow==", "path": "servicestack.kestrel/8.4.0", "hashPath": "servicestack.kestrel.8.4.0.nupkg.sha512"}, "ServiceStack.Text/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-sm/djcCmuoo6jrMxxjEMkTyMcAWQhJndZTTyd/vqY3IjC8jzqpLvmdgSTaBQFmBT3hdhIqbX/8cBQz0+xH4w2A==", "path": "servicestack.text/8.4.0", "hashPath": "servicestack.text.8.4.0.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "SharpScss/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+5jnxm6neBhRcNaQB5S4mqlo4iB4Bvmho2UVTKEKp3uBuouJTq9WfAQor0UmIDaVIVBhXbBFey55Kk4izQ5RBQ==", "path": "sharpscss/2.0.0", "hashPath": "sharpscss.2.0.0.nupkg.sha512"}, "SharpZipLib/1.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-yjj+3zgz8zgXpiiC3ZdF/iyTBbz2fFvMxZFEBPUcwZjIvXOf37Ylm+K58hqMfIBt5JgU/Z2uoUS67JmTLe973A==", "path": "sharpziplib/1.4.2", "hashPath": "sharpziplib.1.4.2.nupkg.sha512"}, "SkiaSharp/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-bRkp3uKp5ZI8gXYQT57uKwil1uobb2p8c69n7v5evlB/2JNcMAXVcw9DZAP5Ig3WSvgzGm2YSn27UVeOi05NlA==", "path": "skiasharp/2.88.8", "hashPath": "skiasharp.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-/DoKtdyvRgCC5GR/SH+ps3ZiOjmf0BYpAyrhWQELFOO1hdcqddrDVJjDNCOJ41vV+NlS5b3kcDoZZ7jLhFjyXg==", "path": "skiasharp.nativeassets.linux.nodependencies/2.88.8", "hashPath": "skiasharp.nativeassets.linux.nodependencies.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-6Kn5TSkKlfyS6azWHF3Jk2sW5C4jCE5uSshM/5AbfFrR+5n6qM5XEnz9h4VaVl7LTxBvHvMkuPb/3bpbq0vxTw==", "path": "skiasharp.nativeassets.macos/2.88.8", "hashPath": "skiasharp.nativeassets.macos.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-O9QXoWEXA+6cweR4h3BOnwMz+pO9vL9mXdjLrpDd0w1QzCgWmLQBxa1VgySDITiH7nQndrDG1h6937zm9pLj1Q==", "path": "skiasharp.nativeassets.win32/2.88.8", "hashPath": "skiasharp.nativeassets.win32.2.88.8.nupkg.sha512"}, "SlackAPI/1.1.14": {"type": "package", "serviceable": true, "sha512": "sha512-1mqKq+faHsWZfyWTXKpSLZH9g/KbpcHhTQx0q+h9BeAEXm0unlr1rGcVDIgEku44/X09dJM60Y9qz4pnMTQGrg==", "path": "slackapi/1.1.14", "hashPath": "slackapi.1.1.14.nupkg.sha512"}, "Snappier/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "path": "snappier/1.0.0", "hashPath": "snappier.1.0.0.nupkg.sha512"}, "SSH.NET/2024.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-pyOea9czgC+OwMoetuWtMuwRebOyskKkqTZtODgyEZ5K6JwV+Hh/GFiyYubl93YnEvmvNZeuyWyUDsb3LvBDYA==", "path": "ssh.net/2024.1.0", "hashPath": "ssh.net.2024.1.0.nupkg.sha512"}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.118": {"type": "package", "serviceable": true, "sha512": "sha512-4TS8IZvDj0ud6utxfXI6zv9Ditk4U9Kt9KqLyAIQGcU3GXp5oGBHgGZifq+APcqRCayuN/MSE8t9ZZmygtU28A==", "path": "stub.system.data.sqlite.core.netstandard/1.0.118", "hashPath": "stub.system.data.sqlite.core.netstandard.1.0.118.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.CodeDom/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "path": "system.codedom/7.0.0", "hashPath": "system.codedom.7.0.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/1.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-B43<PERSON>sz5EfMwyEbnObwRxW5u85fzJma3lrDeGcSAV1qkhSRTNY5uXAByTn9h9ddNdhM+4/YoLc/CI43umjwIl9Q==", "path": "system.collections.immutable/1.7.1", "hashPath": "system.collections.immutable.1.7.1.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "path": "system.configuration.configurationmanager/8.0.0", "hashPath": "system.configuration.configurationmanager.8.0.0.nupkg.sha512"}, "System.Data.Odbc/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c+GfnZt2/HyU+voKw2fctLZClcNjPZPWS+mnIhGvDknRMqL/fwWlREWPgA4csbp9ZkQIgB4qkufgdh/oh5Ubow==", "path": "system.data.odbc/8.0.0", "hashPath": "system.data.odbc.8.0.0.nupkg.sha512"}, "System.Data.OleDb/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FpUTcQ0E8mFvcYp8UZA3NX8wgmhmsCue56g1zfkr1xdOnT5FrYYmC5DWQ9xCw8o8zuxVBKLZvliqEGgmeoalaQ==", "path": "system.data.oledb/8.0.0", "hashPath": "system.data.oledb.8.0.0.nupkg.sha512"}, "System.Data.SqlClient/4.8.6": {"type": "package", "serviceable": true, "sha512": "sha512-2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "path": "system.data.sqlclient/4.8.6", "hashPath": "system.data.sqlclient.4.8.6.nupkg.sha512"}, "System.Data.SQLite.Core/1.0.118": {"type": "package", "serviceable": true, "sha512": "sha512-2V1PsfBeqWlZxF/VtB8lQKPfDBayCU8zD5Xc3Mq7cILOa2ZqpPDSwMP0fTfk1gtGSStSk//DxKiGy6zwCQs7Uw==", "path": "system.data.sqlite.core/1.0.118", "hashPath": "system.data.sqlite.core.1.0.118.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "path": "system.diagnostics.eventlog/8.0.0", "hashPath": "system.diagnostics.eventlog.8.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lX6DXxtJqVGWw7N/QmVoiCyVQ+Q/Xp+jVXPr3gLK1jJExSn1qmAjJQeb8gnOYeeBTG3E3PmG1nu92eYj/TEjpg==", "path": "system.diagnostics.performancecounter/8.0.0", "hashPath": "system.diagnostics.performancecounter.8.0.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.DirectoryServices.Protocols/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-puwJxURHDrYLGTQdsHyeMS72ClTqYa4lDYz6LHSbkZEk5hq8H8JfsO4MyYhB5BMMxg93jsQzLUwrnCumj11UIg==", "path": "system.directoryservices.protocols/8.0.0", "hashPath": "system.directoryservices.protocols.8.0.0.nupkg.sha512"}, "System.Formats.Asn1/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqKba7Mm/koKSjKMfW82olQdmfbI5yqeoLV/tidRp7fbh5rmHAQ5raDI/7SU0swTzv+jgqtUGkzmFxuUg0it1A==", "path": "system.formats.asn1/8.0.1", "hashPath": "system.formats.asn1.8.0.1.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.34.0": {"type": "package", "serviceable": true, "sha512": "sha512-c0misfmFT3QxKY+a16PGlj+DtiUzoPaf26m2avyPZaLRc9vlIdLtmovfRY5MqN+y/SEoBSRXrgVaeZGPgFQQ6w==", "path": "system.identitymodel.tokens.jwt/6.34.0", "hashPath": "system.identitymodel.tokens.jwt.6.34.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Management/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-/qEUN91mP/MUQmJnM5y5BdT7ZoPuVrtxnFlbJ8a3kBJGhe2wCzBfnPFtK2wTtEEcf3DMGR9J00GZZfg6HRI6yA==", "path": "system.management/7.0.2", "hashPath": "system.management.7.0.2.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Net.Http/4.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "path": "system.net.http/4.3.4", "hashPath": "system.net.http.4.3.4.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Private.Uri/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I4SwANiUGho1esj4V4oSlPllXjzCZDE+5XXso2P03LW2vOda2Enzh8DWOxwN6hnrJyp314c7KuVu31QYhRzOGg==", "path": "system.private.uri/4.3.0", "hashPath": "system.private.uri.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-fO8GMEkgoKioJ7cglZbhcnBgkCWWn9poS3G2jevS+fuwW9xJXMx7/1kr7dkwOJfo0pWqxLFWVcxlOr+WeK5ipA==", "path": "system.reflection.typeextensions/4.5.1", "hashPath": "system.reflection.typeextensions.4.5.1.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "path": "system.security.cryptography.cng/4.3.0", "hashPath": "system.security.cryptography.cng.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ULmp3xoOwNYjOYp4JZ2NK/6NdTgiN1GQXzVVN1njQ7LOZ0d0B9vyMnhyqbIi9Qw4JXj1JgCsitkTShboHRx7Eg==", "path": "system.security.cryptography.pkcs/8.0.0", "hashPath": "system.security.cryptography.pkcs.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "path": "system.security.cryptography.protecteddata/8.0.0", "hashPath": "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OZIsVplFGaVY90G2SbpgU7EnCoOO5pw1t4ic21dBF3/1omrJFpAGoNAVpPyMVOC90/hvgkGG3VFqR13YgZMQfg==", "path": "system.text.encoding.codepages/8.0.0", "hashPath": "system.text.encoding.codepages.8.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>drZ<PERSON>2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "path": "system.text.json/8.0.0", "hashPath": "system.text.json.8.0.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Overlapped/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m3HQ2dPiX/DSTpf+yJt8B0c+SRvzfqAJKx+QDWi+VLhz8svLT23MVjEOHPF/KiSLeArKU/iHescrbLd3yVgyNg==", "path": "system.threading.overlapped/4.3.0", "hashPath": "system.threading.overlapped.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "Teradata.Client.Provider/20.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-9MXUeVolGkfdfvrKnci69v9VcCtBAKtHM2c6OkXQWNsIvxJOqhNQCQEegBOxtnoXcy1v5e3z866g94ad0sTS1A==", "path": "teradata.client.provider/20.0.3", "hashPath": "teradata.client.provider.20.0.3.nupkg.sha512"}, "TweetinviAPI/5.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-YMKsU1+lt88yICPPFiYpyDDTyNXdaJ5IPugs2eqtZCJnuxZ3Al/6Hs+0Tui93FXsQXA5vDS0S/dNgqNJ56QJUA==", "path": "tweetinviapi/5.0.4", "hashPath": "tweetinviapi.5.0.4.nupkg.sha512"}, "Twilio/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-pSbaX86w7G3uzv13tDBixiaoh2ttRsyJL5HtVzQLDAK086sudbAx4GMbX1KBgRw9AGhBiKliDaob6209ybkyXQ==", "path": "twilio/7.3.1", "hashPath": "twilio.7.3.1.nupkg.sha512"}, "VimeoDotNet/3.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-zHsjfHoGDOYX7r7dMSHv44NHczjGiCqoiCkgsZGfQohYHvehyVBqj8+PeCKEyjctfwDDf0kunquO07HNd06jXQ==", "path": "vimeodotnet/3.2.4", "hashPath": "vimeodotnet.3.2.4.nupkg.sha512"}, "YamlDotNet/16.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-VHBamNzHIKqkdWgYrGk48mEos+lVDFHzJyk0JiFYydoViSiCNIdoDTL3kaqAh+exQ2m2ZgZQphBgaOngR1fYjg==", "path": "yamldotnet/16.1.2", "hashPath": "yamldotnet.16.1.2.nupkg.sha512"}, "ZstdSharp.Port/0.7.3": {"type": "package", "serviceable": true, "sha512": "sha512-U9Ix4l4cl58Kzz1rJzj5hoVTjmbx1qGMwzAcbv1j/d3NzrFaESIurQyg+ow4mivCgkE3S413y+U9k4WdnEIkRA==", "path": "zstdsharp.port/0.7.3", "hashPath": "zstdsharp.port.0.7.3.nupkg.sha512"}, "Wexflow.Core/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Core.Db/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Core.Db.Firebird/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Core.Db.LiteDB/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Core.Db.MariaDB/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Core.Db.MongoDB/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Core.Db.MySQL/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Core.Db.Oracle/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Core.Db.PostgreSQL/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Core.Db.RavenDB/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Core.Db.SQLite/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Core.Db.SQLServer/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Approval/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.ApprovalRecordsCreator/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.ApprovalWorkflowsCreator/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.ApproveRecord/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.CsvToJson/6.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.CsvToSql/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.CsvToXml/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.CsvToYaml/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.EnvironmentVariable/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.ExecPython/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FileContentMatch/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FileExists/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FileMatch/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FileNotExist/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FileNotMatch/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FilesConcat/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FilesCopier/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FilesDecryptor/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FilesDiff/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FilesEncryptor/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FilesEqual/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FilesExist/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FilesInfo/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FilesJoiner/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FilesLoader/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FilesLoaderEx/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FilesMover/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FilesRemover/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FilesRenamer/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FilesSplitter/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FileSystemWatcher/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.FolderExists/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Ftp/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Guid/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Http/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.HttpDelete/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.HttpGet/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.HttpPatch/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.HttpPost/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.HttpPut/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.ImagesConcat/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.ImagesCropper/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.ImagesOverlay/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.ImagesResizer/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.ImagesTransformer/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.InstagramUploadImage/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.InstagramUploadVideo/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.JsonToYaml/6.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.ListEntities/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.ListFiles/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.MailsReceiver/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.MailsSender/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Md5/6.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.MessageCorrect/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Mkdir/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Movedir/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Now/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Ping/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.ProcessInfo/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.ProcessLauncher/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Reddit/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.RedditListComments/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.RedditListPosts/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Rmdir/6.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.ScssToCss/6.2.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Sha1/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Sha256/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Sha512/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Slack/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Sql/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.SqlToCsv/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.SqlToXml/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.SshCmd/6.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.SubWorkflow/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Tar/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.TextsDecryptor/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.TextsEncryptor/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Tgz/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Torrent/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Touch/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Twilio/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Twitter/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.UglifyCss/6.2.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.UglifyHtml/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.UglifyJs/6.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Untar/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Untgz/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Unzip/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Vimeo/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.VimeoListUploads/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Wait/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.XmlToCsv/7.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Xslt/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.YamlToJson/6.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.YouTube/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.YouTubeListUploads/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.YouTubeSearch/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}, "Wexflow.Tasks.Zip/8.9.0": {"type": "project", "serviceable": false, "sha512": ""}}}