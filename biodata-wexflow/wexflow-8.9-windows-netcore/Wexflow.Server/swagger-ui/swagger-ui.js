!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.SwaggerUICore=t():e.SwaggerUICore=t()}(this,(function(){return(()=>{var e={6024:(e,t,r)=>{"use strict";r.d(t,{Z:()=>E});var n=r(4250),s=r.n(n),a=r(1093),o=r.n(a),l=r(8493),i=r.n(l),c=r(3942),p=r.n(c),u=r(6689),d=r.n(u);const m=require("react-immutable-pure-component");var h=r.n(m),g=r(8082),f=r.n(g),y=r(580),v=r.n(y);class E extends(h()){constructor(){super(...arguments),o()(this,"getModelName",(e=>-1!==i()(e).call(e,"#/definitions/")?e.replace(/^.*#\/definitions\//,""):-1!==i()(e).call(e,"#/components/schemas/")?e.replace(/^.*#\/components\/schemas\//,""):void 0)),o()(this,"getRefSchema",(e=>{let{specSelectors:t}=this.props;return t.findDefinition(e)}))}render(){let{getComponent:e,getConfigs:t,specSelectors:n,schema:a,required:o,name:l,isRef:i,specPath:c,displayName:p,includeReadOnly:u,includeWriteOnly:m}=this.props;const h=e("ObjectModel"),g=e("ArrayModel"),f=e("PrimitiveModel");let y="object",v=a&&a.get("$$ref");if(!l&&v&&(l=this.getModelName(v)),!a&&v&&(a=this.getRefSchema(l)),!a)return d().createElement("span",{className:"model model-title"},d().createElement("span",{className:"model-title__text"},p||l),d().createElement("img",{src:r(2517),height:"20px",width:"20px"}));const E=n.isOAS3()&&a.get("deprecated");switch(i=void 0!==i?i:!!v,y=a&&a.get("type")||y,y){case"object":return d().createElement(h,s()({className:"object"},this.props,{specPath:c,getConfigs:t,schema:a,name:l,deprecated:E,isRef:i,includeReadOnly:u,includeWriteOnly:m}));case"array":return d().createElement(g,s()({className:"array"},this.props,{getConfigs:t,schema:a,name:l,deprecated:E,required:o,includeReadOnly:u,includeWriteOnly:m}));default:return d().createElement(f,s()({},this.props,{getComponent:e,getConfigs:t,schema:a,name:l,deprecated:E,required:o}))}}}o()(E,"propTypes",{schema:p()(f()).isRequired,getComponent:v().func.isRequired,getConfigs:v().func.isRequired,specSelectors:v().object.isRequired,name:v().string,displayName:v().string,isRef:v().bool,required:v().bool,expandDepth:v().number,depth:v().number,specPath:f().list.isRequired,includeReadOnly:v().bool,includeWriteOnly:v().bool})},5623:(e,t,r)=>{"use strict";r.d(t,{Z:()=>m});var n=r(1093),s=r.n(n),a=r(7252),o=r.n(a),l=r(6689),i=r.n(l),c=r(3883),p=r.n(c),u=(r(580),r(1890)),d=r(7504);class m extends i().Component{constructor(e,t){super(e,t),s()(this,"getDefinitionUrl",(()=>{let{specSelectors:e}=this.props;return new(p())(e.url(),d.Z.location).toString()}));let{getConfigs:r}=e,{validatorUrl:n}=r();this.state={url:this.getDefinitionUrl(),validatorUrl:void 0===n?"https://validator.swagger.io/validator":n}}UNSAFE_componentWillReceiveProps(e){let{getConfigs:t}=e,{validatorUrl:r}=t();this.setState({url:this.getDefinitionUrl(),validatorUrl:void 0===r?"https://validator.swagger.io/validator":r})}render(){let{getConfigs:e}=this.props,{spec:t}=e(),r=(0,u.Nm)(this.state.validatorUrl);return"object"==typeof t&&o()(t).length?null:this.state.url&&(0,u.hW)(this.state.validatorUrl)&&(0,u.hW)(this.state.url)?i().createElement("span",{className:"float-right"},i().createElement("a",{target:"_blank",rel:"noopener noreferrer",href:`${r}/debug?url=${encodeURIComponent(this.state.url)}`},i().createElement(h,{src:`${r}?url=${encodeURIComponent(this.state.url)}`,alt:"Online validator badge"}))):null}}class h extends i().Component{constructor(e){super(e),this.state={loaded:!1,error:!1}}componentDidMount(){const e=new Image;e.onload=()=>{this.setState({loaded:!0})},e.onerror=()=>{this.setState({error:!0})},e.src=this.props.src}UNSAFE_componentWillReceiveProps(e){if(e.src!==this.props.src){const t=new Image;t.onload=()=>{this.setState({loaded:!0})},t.onerror=()=>{this.setState({error:!0})},t.src=e.src}}render(){return this.state.error?i().createElement("img",{alt:"Error"}):this.state.loaded?i().createElement("img",{src:this.props.src,alt:this.props.alt}):null}}},2552:(e,t,r)=>{"use strict";r.d(t,{Z:()=>d,s:()=>m});var n=r(6689),s=r.n(n),a=(r(580),r(963));const o=require("remarkable/linkify"),l=require("dompurify");var i=r.n(l),c=r(9003),p=r.n(c);function u(e){let{source:t,className:r="",getConfigs:n}=e;if("string"!=typeof t)return null;const l=new a.Remarkable({html:!0,typographer:!0,breaks:!0,linkTarget:"_blank"}).use(o.linkify);l.core.ruler.disable(["replacements","smartquotes"]);const{useUnsafeMarkdown:i}=n(),c=l.render(t),u=m(c,{useUnsafeMarkdown:i});return t&&c&&u?s().createElement("div",{className:p()(r,"markdown"),dangerouslySetInnerHTML:{__html:u}}):null}i().addHook&&i().addHook("beforeSanitizeElements",(function(e){return e.href&&e.setAttribute("rel","noopener noreferrer"),e})),u.defaultProps={getConfigs:()=>({useUnsafeMarkdown:!1})};const d=u;function m(e){let{useUnsafeMarkdown:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=t,n=t?[]:["style","class"];return t&&!m.hasWarnedAboutDeprecation&&(console.warn("useUnsafeMarkdown display configuration parameter is deprecated since >3.26.0 and will be removed in v4.0.0."),m.hasWarnedAboutDeprecation=!0),i().sanitize(e,{ADD_ATTR:["target"],FORBID_TAGS:["style","form"],ALLOW_DATA_ATTR:r,FORBID_ATTR:n})}m.hasWarnedAboutDeprecation=!1},5308:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var n,s=r(4235),a=r.n(s),o=r(874),l=r.n(o),i=r(1890),c=r(9595);const p=r(5102),u={},d=u;a()(n=l()(p).call(p)).call(n,(function(e){if("./index.js"===e)return;let t=p(e);u[(0,i.Zl)(e)]=t.default?t.default:t})),u.SafeRender=c.default},5812:(e,t,r)=>{"use strict";r.r(t),r.d(t,{SHOW_AUTH_POPUP:()=>u,AUTHORIZE:()=>d,LOGOUT:()=>m,PRE_AUTHORIZE_OAUTH2:()=>h,AUTHORIZE_OAUTH2:()=>g,VALIDATE:()=>f,CONFIGURE_AUTH:()=>y,RESTORE_AUTHORIZATION:()=>v,showDefinitions:()=>E,authorize:()=>S,authorizeWithPersistOption:()=>C,logout:()=>b,logoutWithPersistOption:()=>x,preAuthorizeImplicit:()=>w,authorizeOauth2:()=>_,authorizeOauth2WithPersistOption:()=>A,authorizePassword:()=>I,authorizeApplication:()=>N,authorizeAccessCodeWithFormParams:()=>q,authorizeAccessCodeWithBasicAuthentication:()=>R,authorizeRequest:()=>T,configureAuth:()=>P,restoreAuthorization:()=>k,persistAuthorizationIfNeeded:()=>O,authPopup:()=>M});var n=r(8344),s=r.n(n),a=r(4994),o=r.n(a),l=r(3883),i=r.n(l),c=r(7504),p=r(1890);const u="show_popup",d="authorize",m="logout",h="pre_authorize_oauth2",g="authorize_oauth2",f="validate",y="configure_auth",v="restore_authorization";function E(e){return{type:u,payload:e}}function S(e){return{type:d,payload:e}}const C=e=>t=>{let{authActions:r}=t;r.authorize(e),r.persistAuthorizationIfNeeded()};function b(e){return{type:m,payload:e}}const x=e=>t=>{let{authActions:r}=t;r.logout(e),r.persistAuthorizationIfNeeded()},w=e=>t=>{let{authActions:r,errActions:n}=t,{auth:a,token:o,isValid:l}=e,{schema:i,name:p}=a,u=i.get("flow");delete c.Z.swaggerUIRedirectOauth2,"accessCode"===u||l||n.newAuthErr({authId:p,source:"auth",level:"warning",message:"Authorization may be unsafe, passed state was changed in server Passed state wasn't returned from auth server"}),o.error?n.newAuthErr({authId:p,source:"auth",level:"error",message:s()(o)}):r.authorizeOauth2WithPersistOption({auth:a,token:o})};function _(e){return{type:g,payload:e}}const A=e=>t=>{let{authActions:r}=t;r.authorizeOauth2(e),r.persistAuthorizationIfNeeded()},I=e=>t=>{let{authActions:r}=t,{schema:n,name:s,username:a,password:l,passwordType:i,clientId:c,clientSecret:u}=e,d={grant_type:"password",scope:e.scopes.join(" "),username:a,password:l},m={};switch(i){case"request-body":!function(e,t,r){t&&o()(e,{client_id:t});r&&o()(e,{client_secret:r})}(d,c,u);break;case"basic":m.Authorization="Basic "+(0,p.r3)(c+":"+u);break;default:console.warn(`Warning: invalid passwordType ${i} was passed, not including client id and secret`)}return r.authorizeRequest({body:(0,p.GZ)(d),url:n.get("tokenUrl"),name:s,headers:m,query:{},auth:e})};const N=e=>t=>{let{authActions:r}=t,{schema:n,scopes:s,name:a,clientId:o,clientSecret:l}=e,i={Authorization:"Basic "+(0,p.r3)(o+":"+l)},c={grant_type:"client_credentials",scope:s.join(" ")};return r.authorizeRequest({body:(0,p.GZ)(c),name:a,url:n.get("tokenUrl"),auth:e,headers:i})},q=e=>{let{auth:t,redirectUrl:r}=e;return e=>{let{authActions:n}=e,{schema:s,name:a,clientId:o,clientSecret:l,codeVerifier:i}=t,c={grant_type:"authorization_code",code:t.code,client_id:o,client_secret:l,redirect_uri:r,code_verifier:i};return n.authorizeRequest({body:(0,p.GZ)(c),name:a,url:s.get("tokenUrl"),auth:t})}},R=e=>{let{auth:t,redirectUrl:r}=e;return e=>{let{authActions:n}=e,{schema:s,name:a,clientId:o,clientSecret:l,codeVerifier:i}=t,c={Authorization:"Basic "+(0,p.r3)(o+":"+l)},u={grant_type:"authorization_code",code:t.code,client_id:o,redirect_uri:r,code_verifier:i};return n.authorizeRequest({body:(0,p.GZ)(u),name:a,url:s.get("tokenUrl"),auth:t,headers:c})}},T=e=>t=>{let r,{fn:n,getConfigs:a,authActions:l,errActions:c,oas3Selectors:p,specSelectors:u,authSelectors:d}=t,{body:m,query:h={},headers:g={},name:f,url:y,auth:v}=e,{additionalQueryStringParams:E}=d.getConfigs()||{};if(u.isOAS3()){let e=p.serverEffectiveValue(p.selectedServer());r=i()(y,e,!0)}else r=i()(y,u.url(),!0);"object"==typeof E&&(r.query=o()({},r.query,E));const S=r.toString();let C=o()({Accept:"application/json, text/plain, */*","Content-Type":"application/x-www-form-urlencoded","X-Requested-With":"XMLHttpRequest"},g);n.fetch({url:S,method:"post",headers:C,query:h,body:m,requestInterceptor:a().requestInterceptor,responseInterceptor:a().responseInterceptor}).then((function(e){let t=JSON.parse(e.data),r=t&&(t.error||""),n=t&&(t.parseError||"");e.ok?r||n?c.newAuthErr({authId:f,level:"error",source:"auth",message:s()(t)}):l.authorizeOauth2WithPersistOption({auth:v,token:t}):c.newAuthErr({authId:f,level:"error",source:"auth",message:e.statusText})})).catch((e=>{let t=new Error(e).message;if(e.response&&e.response.data){const r=e.response.data;try{const e="string"==typeof r?JSON.parse(r):r;e.error&&(t+=`, error: ${e.error}`),e.error_description&&(t+=`, description: ${e.error_description}`)}catch(e){}}c.newAuthErr({authId:f,level:"error",source:"auth",message:t})}))};function P(e){return{type:y,payload:e}}function k(e){return{type:v,payload:e}}const O=()=>e=>{let{authSelectors:t,getConfigs:r}=e;if(r().persistAuthorization){const e=t.authorized();localStorage.setItem("authorized",s()(e.toJS()))}},M=(e,t)=>()=>{c.Z.swaggerUIRedirectOauth2=t,c.Z.open(e)}},3705:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,preauthorizeBasic:()=>p,preauthorizeApiKey:()=>u});var n=r(593),s=r.n(n),a=r(3962),o=r(5812),l=r(35),i=r(8302);function c(){return{afterLoad(e){this.rootInjects=this.rootInjects||{},this.rootInjects.initOAuth=e.authActions.configureAuth,this.rootInjects.preauthorizeApiKey=s()(u).call(u,null,e),this.rootInjects.preauthorizeBasic=s()(p).call(p,null,e)},statePlugins:{auth:{reducers:a.default,actions:o,selectors:l},spec:{wrapActions:i}}}}function p(e,t,r,n){const{authActions:{authorize:s},specSelectors:{specJson:a,isOAS3:o}}=e,l=o()?["components","securitySchemes"]:["securityDefinitions"],i=a().getIn([...l,t]);return i?s({[t]:{value:{username:r,password:n},schema:i.toJS()}}):null}function u(e,t,r){const{authActions:{authorize:n},specSelectors:{specJson:s,isOAS3:a}}=e,o=a()?["components","securitySchemes"]:["securityDefinitions"],l=s().getIn([...o,t]);return l?n({[t]:{value:r,schema:l.toJS()}}):null}},3962:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var n=r(4235),s=r.n(n),a=r(4994),o=r.n(a),l=r(5572),i=r(1890),c=r(5812);const p={[c.SHOW_AUTH_POPUP]:(e,t)=>{let{payload:r}=t;return e.set("showDefinitions",r)},[c.AUTHORIZE]:(e,t)=>{var r;let{payload:n}=t,a=(0,l.fromJS)(n),o=e.get("authorized")||(0,l.Map)();return s()(r=a.entrySeq()).call(r,(t=>{let[r,n]=t;if(!(0,i.Wl)(n.getIn))return e.set("authorized",o);let s=n.getIn(["schema","type"]);if("apiKey"===s||"http"===s)o=o.set(r,n);else if("basic"===s){let e=n.getIn(["value","username"]),t=n.getIn(["value","password"]);o=o.setIn([r,"value"],{username:e,header:"Basic "+(0,i.r3)(e+":"+t)}),o=o.setIn([r,"schema"],n.get("schema"))}})),e.set("authorized",o)},[c.AUTHORIZE_OAUTH2]:(e,t)=>{let r,{payload:n}=t,{auth:s,token:a}=n;s.token=o()({},a),r=(0,l.fromJS)(s);let i=e.get("authorized")||(0,l.Map)();return i=i.set(r.get("name"),r),e.set("authorized",i)},[c.LOGOUT]:(e,t)=>{let{payload:r}=t,n=e.get("authorized").withMutations((e=>{s()(r).call(r,(t=>{e.delete(t)}))}));return e.set("authorized",n)},[c.CONFIGURE_AUTH]:(e,t)=>{let{payload:r}=t;return e.set("configs",r)},[c.RESTORE_AUTHORIZATION]:(e,t)=>{let{payload:r}=t;return e.set("authorized",(0,l.fromJS)(r.authorized))}}},35:(e,t,r)=>{"use strict";r.r(t),r.d(t,{shownDefinitions:()=>v,definitionsToAuthorize:()=>E,getDefinitionsByNames:()=>S,definitionsForRequirements:()=>C,authorized:()=>b,isAuthorized:()=>x,getConfigs:()=>w});var n=r(4235),s=r.n(n),a=r(3580),o=r.n(a),l=r(9998),i=r.n(l),c=r(8493),p=r.n(c),u=r(3942),d=r.n(u),m=r(7252),h=r.n(m),g=r(6814),f=r(5572);const y=e=>e,v=(0,g.createSelector)(y,(e=>e.get("showDefinitions"))),E=(0,g.createSelector)(y,(()=>e=>{var t;let{specSelectors:r}=e,n=r.securityDefinitions()||(0,f.Map)({}),a=(0,f.List)();return s()(t=n.entrySeq()).call(t,(e=>{let[t,r]=e,n=(0,f.Map)();n=n.set(t,r),a=a.push(n)})),a})),S=(e,t)=>e=>{var r;let{specSelectors:n}=e;console.warn("WARNING: getDefinitionsByNames is deprecated and will be removed in the next major version.");let a=n.securityDefinitions(),o=(0,f.List)();return s()(r=t.valueSeq()).call(r,(e=>{var t;let r=(0,f.Map)();s()(t=e.entrySeq()).call(t,(e=>{let t,[n,o]=e,l=a.get(n);var i;"oauth2"===l.get("type")&&o.size&&(t=l.get("scopes"),s()(i=t.keySeq()).call(i,(e=>{o.contains(e)||(t=t.delete(e))})),l=l.set("allowedScopes",t));r=r.set(n,l)})),o=o.push(r)})),o},C=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(0,f.List)();return e=>{let{authSelectors:r}=e;const n=r.definitionsToAuthorize()||(0,f.List)();let a=(0,f.List)();return s()(n).call(n,(e=>{let r=o()(t).call(t,(t=>t.get(e.keySeq().first())));r&&(s()(e).call(e,((t,n)=>{if("oauth2"===t.get("type")){const o=r.get(n);let l=t.get("scopes");var a;if(f.List.isList(o)&&f.Map.isMap(l))s()(a=l.keySeq()).call(a,(e=>{o.contains(e)||(l=l.delete(e))})),e=e.set(n,t.set("scopes",l))}})),a=a.push(e))})),a}},b=(0,g.createSelector)(y,(e=>e.get("authorized")||(0,f.Map)())),x=(e,t)=>e=>{var r;let{authSelectors:n}=e,s=n.authorized();return f.List.isList(t)?!!i()(r=t.toJS()).call(r,(e=>{var t,r;return-1===p()(t=d()(r=h()(e)).call(r,(e=>!!s.get(e)))).call(t,!1)})).length:null},w=(0,g.createSelector)(y,(e=>e.get("configs")))},8302:(e,t,r)=>{"use strict";r.r(t),r.d(t,{execute:()=>n});const n=(e,t)=>{let{authSelectors:r,specSelectors:n}=t;return t=>{let{path:s,method:a,operation:o,extras:l}=t,i={authorized:r.authorized()&&r.authorized().toJS(),definitions:n.securityDefinitions()&&n.securityDefinitions().toJS(),specSecurity:n.security()&&n.security().toJS()};return e({path:s,method:a,operation:o,securities:i,...l})}}},714:(e,t,r)=>{"use strict";r.r(t),r.d(t,{UPDATE_CONFIGS:()=>n,TOGGLE_CONFIGS:()=>s,update:()=>a,toggle:()=>o,loaded:()=>l});const n="configs_update",s="configs_toggle";function a(e,t){return{type:n,payload:{[e]:t}}}function o(e){return{type:s,payload:e}}const l=()=>e=>{let{getConfigs:t,authActions:r}=e;if(t().persistAuthorization){const e=localStorage.getItem("authorized");e&&r.restoreAuthorization({authorized:JSON.parse(e)})}}},2256:(e,t,r)=>{"use strict";r.r(t),r.d(t,{parseYamlConfig:()=>a});var n=r(9793),s=r.n(n);const a=(e,t)=>{try{return s().load(e)}catch(e){return t&&t.errActions.newThrownErr(new Error(e)),{}}}},1661:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var n=r(5163),s=r(2256),a=r(714),o=r(2698),l=r(9018),i=r(7743);const c={getLocalConfig:()=>(0,s.parseYamlConfig)(n)};function p(){return{statePlugins:{spec:{actions:o,selectors:c},configs:{reducers:i.default,actions:a,selectors:l}}}}},7743:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(5572),s=r(714);const a={[s.UPDATE_CONFIGS]:(e,t)=>e.merge((0,n.fromJS)(t.payload)),[s.TOGGLE_CONFIGS]:(e,t)=>{const r=t.payload,n=e.get(r);return e.set(r,!n)}}},9018:(e,t,r)=>{"use strict";r.r(t),r.d(t,{get:()=>a});var n=r(7104),s=r.n(n);const a=(e,t)=>e.getIn(s()(t)?t:[t])},2698:(e,t,r)=>{"use strict";r.r(t),r.d(t,{downloadConfig:()=>s,getConfigByUrl:()=>a});var n=r(2256);const s=e=>t=>{const{fn:{fetch:r}}=t;return r(e)},a=(e,t)=>r=>{let{specActions:s}=r;if(e)return s.downloadConfig(e).then(a,a);function a(r){r instanceof Error||r.status>=400?(s.updateLoadingStatus("failedConfig"),s.updateLoadingStatus("failedConfig"),s.updateUrl(""),console.error(r.statusText+" "+e.url),t(null)):t((0,n.parseYamlConfig)(r.text))}}},1970:(e,t,r)=>{"use strict";r.r(t),r.d(t,{setHash:()=>n});const n=e=>e?history.pushState(null,null,`#${e}`):window.location.hash=""},4980:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(2179),s=r(877),a=r(4584);function o(){return[n.default,{statePlugins:{configs:{wrapActions:{loaded:(e,t)=>function(){e(...arguments);const r=decodeURIComponent(window.location.hash);t.layoutActions.parseDeepLinkHash(r)}}}},wrapComponents:{operation:s.default,OperationTag:a.default}}]}},2179:(e,t,r)=>{"use strict";r.r(t),r.d(t,{clearScrollTo:()=>w,default:()=>_,parseDeepLinkHash:()=>C,readyToScroll:()=>b,scrollTo:()=>S,scrollToElement:()=>x,show:()=>E});var n=r(7104),s=r.n(n),a=r(600),o=r.n(a),l=r(3942),i=r.n(l),c=r(8493),p=r.n(c),u=r(1970);const d=require("zenscroll");var m=r.n(d),h=r(1890),g=r(5572),f=r.n(g);const y="layout_scroll_to",v="layout_clear_scroll",E=(e,t)=>{let{getConfigs:r,layoutSelectors:n}=t;return function(){for(var t=arguments.length,a=new Array(t),o=0;o<t;o++)a[o]=arguments[o];if(e(...a),r().deepLinking)try{let[e,t]=a;e=s()(e)?e:[e];const r=n.urlHashArrayFromIsShownKey(e);if(!r.length)return;const[o,l]=r;if(!t)return(0,u.setHash)("/");2===r.length?(0,u.setHash)((0,h.oJ)(`/${encodeURIComponent(o)}/${encodeURIComponent(l)}`)):1===r.length&&(0,u.setHash)((0,h.oJ)(`/${encodeURIComponent(o)}`))}catch(e){console.error(e)}}},S=e=>({type:y,payload:s()(e)?e:[e]}),C=e=>t=>{let{layoutActions:r,layoutSelectors:n,getConfigs:s}=t;if(s().deepLinking&&e){var a;let t=o()(e).call(e,1);"!"===t[0]&&(t=o()(t).call(t,1)),"/"===t[0]&&(t=o()(t).call(t,1));const s=i()(a=t.split("/")).call(a,(e=>e||"")),l=n.isShownKeyFromUrlHashArray(s),[c,u="",d=""]=l;if("operations"===c){const e=n.isShownKeyFromUrlHashArray([u]);p()(u).call(u,"_")>-1&&(console.warn("Warning: escaping deep link whitespace with `_` will be unsupported in v4.0, use `%20` instead."),r.show(i()(e).call(e,(e=>e.replace(/_/g," "))),!0)),r.show(e,!0)}(p()(u).call(u,"_")>-1||p()(d).call(d,"_")>-1)&&(console.warn("Warning: escaping deep link whitespace with `_` will be unsupported in v4.0, use `%20` instead."),r.show(i()(l).call(l,(e=>e.replace(/_/g," "))),!0)),r.show(l,!0),r.scrollTo(l)}},b=(e,t)=>r=>{const n=r.layoutSelectors.getScrollToKey();f().is(n,(0,g.fromJS)(e))&&(r.layoutActions.scrollToElement(t),r.layoutActions.clearScrollTo())},x=(e,t)=>r=>{try{t=t||r.fn.getScrollParent(e),m().createScroller(t).to(e)}catch(e){console.error(e)}},w=()=>({type:v});const _={fn:{getScrollParent:function(e,t){const r=document.documentElement;let n=getComputedStyle(e);const s="absolute"===n.position,a=t?/(auto|scroll|hidden)/:/(auto|scroll)/;if("fixed"===n.position)return r;for(let t=e;t=t.parentElement;)if(n=getComputedStyle(t),(!s||"static"!==n.position)&&a.test(n.overflow+n.overflowY+n.overflowX))return t;return r}},statePlugins:{layout:{actions:{scrollToElement:x,scrollTo:S,clearScrollTo:w,readyToScroll:b,parseDeepLinkHash:C},selectors:{getScrollToKey:e=>e.get("scrollToKey"),isShownKeyFromUrlHashArray(e,t){const[r,n]=t;return n?["operations",r,n]:r?["operations-tag",r]:[]},urlHashArrayFromIsShownKey(e,t){let[r,n,s]=t;return"operations"==r?[n,s]:"operations-tag"==r?[n]:[]}},reducers:{[y]:(e,t)=>e.set("scrollToKey",f().fromJS(t.payload)),[v]:e=>e.delete("scrollToKey")},wrapActions:{show:E}}}}},4584:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(1093),s=r.n(n),a=r(6689),o=r.n(a);r(580);const l=(e,t)=>class extends o().Component{constructor(){super(...arguments),s()(this,"onLoad",(e=>{const{tag:r}=this.props,n=["operations-tag",r];t.layoutActions.readyToScroll(n,e)}))}render(){return o().createElement("span",{ref:this.onLoad},o().createElement(e,this.props))}}},877:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(1093),s=r.n(n),a=r(6689),o=r.n(a);r(8082);const l=(e,t)=>class extends o().Component{constructor(){super(...arguments),s()(this,"onLoad",(e=>{const{operation:r}=this.props,{tag:n,operationId:s}=r.toObject();let{isShownKey:a}=r.toObject();a=a||["operations",n,s],t.layoutActions.readyToScroll(a,e)}))}render(){return o().createElement("span",{ref:this.onLoad},o().createElement(e,this.props))}}},8011:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var n=r(4994),s=r.n(n),a=r(9478),o=r.n(a),l=r(8493),i=r.n(l),c=r(8344),p=r.n(c),u=r(6814),d=r(5572),m=r(7504);function h(e){let{fn:t}=e;return{statePlugins:{spec:{actions:{download:e=>r=>{let{errActions:n,specSelectors:a,specActions:l,getConfigs:i}=r,{fetch:c}=t;const p=i();function u(t){if(t instanceof Error||t.status>=400)return l.updateLoadingStatus("failed"),n.newThrownErr(s()(new Error((t.message||t.statusText)+" "+e),{source:"fetch"})),void(!t.status&&t instanceof Error&&function(){try{let t;if("URL"in m.Z?t=new(o())(e):(t=document.createElement("a"),t.href=e),"https:"!==t.protocol&&"https:"===m.Z.location.protocol){const e=s()(new Error(`Possible mixed-content issue? The page was loaded over https:// but a ${t.protocol}// URL was specified. Check that you are not attempting to load mixed content.`),{source:"fetch"});return void n.newThrownErr(e)}if(t.origin!==m.Z.location.origin){const e=s()(new Error(`Possible cross-origin (CORS) issue? The URL origin (${t.origin}) does not match the page (${m.Z.location.origin}). Check the server returns the correct 'Access-Control-Allow-*' headers.`),{source:"fetch"});n.newThrownErr(e)}}catch(e){return}}());l.updateLoadingStatus("success"),l.updateSpec(t.text),a.url()!==e&&l.updateUrl(e)}e=e||a.url(),l.updateLoadingStatus("loading"),n.clear({source:"fetch"}),c({url:e,loadSpec:!0,requestInterceptor:p.requestInterceptor||(e=>e),responseInterceptor:p.responseInterceptor||(e=>e),credentials:"same-origin",headers:{Accept:"application/json,*/*"}}).then(u,u)},updateLoadingStatus:e=>{let t=[null,"loading","failed","success","failedConfig"];return-1===i()(t).call(t,e)&&console.error(`Error: ${e} is not one of ${p()(t)}`),{type:"spec_update_loading_status",payload:e}}},reducers:{spec_update_loading_status:(e,t)=>"string"==typeof t.payload?e.set("loadingStatus",t.payload):e},selectors:{loadingStatus:(0,u.createSelector)((e=>e||(0,d.Map)()),(e=>e.get("loadingStatus")||null))}}}}}},4966:(e,t,r)=>{"use strict";r.r(t),r.d(t,{NEW_THROWN_ERR:()=>s,NEW_THROWN_ERR_BATCH:()=>a,NEW_SPEC_ERR:()=>o,NEW_SPEC_ERR_BATCH:()=>l,NEW_AUTH_ERR:()=>i,CLEAR:()=>c,CLEAR_BY:()=>p,newThrownErr:()=>u,newThrownErrBatch:()=>d,newSpecErr:()=>m,newSpecErrBatch:()=>h,newAuthErr:()=>g,clear:()=>f,clearBy:()=>y});var n=r(41);const s="err_new_thrown_err",a="err_new_thrown_err_batch",o="err_new_spec_err",l="err_new_spec_err_batch",i="err_new_auth_err",c="err_clear",p="err_clear_by";function u(e){return{type:s,payload:(0,n.serializeError)(e)}}function d(e){return{type:a,payload:e}}function m(e){return{type:o,payload:e}}function h(e){return{type:l,payload:e}}function g(e){return{type:i,payload:e}}function f(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{type:c,payload:e}}function y(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>!0;return{type:p,payload:e}}},2860:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var n=r(9998),s=r.n(n),a=r(3942),o=r.n(a);const l=require("lodash/reduce");var i=r.n(l);const c=[r(2392),r(1835)];function p(e){var t;let r={jsSpec:{}},n=i()(c,((e,t)=>{try{let n=t.transform(e,r);return s()(n).call(n,(e=>!!e))}catch(t){return console.error("Transformer error:",t),e}}),e);return o()(t=s()(n).call(n,(e=>!!e))).call(t,(e=>(!e.get("line")&&e.get("path"),e)))}},2392:(e,t,r)=>{"use strict";r.r(t),r.d(t,{transform:()=>u});var n=r(3942),s=r.n(n),a=r(8493),o=r.n(a),l=r(600),i=r.n(l),c=r(66),p=r.n(c);function u(e){return s()(e).call(e,(e=>{var t;let r="is not of a type(s)",n=o()(t=e.get("message")).call(t,r);if(n>-1){var s,a;let t=i()(s=e.get("message")).call(s,n+r.length).split(",");return e.set("message",i()(a=e.get("message")).call(a,0,n)+function(e){return p()(e).call(e,((e,t,r,n)=>r===n.length-1&&n.length>1?e+"or "+t:n[r+1]&&n.length>2?e+t+", ":n[r+1]?e+t+" ":e+t),"should be a")}(t))}return e}))}},1835:(e,t,r)=>{"use strict";r.r(t),r.d(t,{transform:()=>n});r(3942),r(8493),r(1712),r(5572);function n(e,t){let{jsSpec:r}=t;return e}},7793:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(3527),s=r(4966),a=r(7667);function o(e){return{statePlugins:{err:{reducers:(0,n.default)(e),actions:s,selectors:a}}}}},3527:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var n=r(4994),s=r.n(n),a=r(3942),o=r.n(a),l=r(4883),i=r.n(l),c=r(9998),p=r.n(c),u=r(7834),d=r.n(u),m=r(4966),h=r(5572),g=r(2860);let f={line:0,level:"error",message:"Unknown error"};function y(){return{[m.NEW_THROWN_ERR]:(e,t)=>{let{payload:r}=t,n=s()(f,r,{type:"thrown"});return e.update("errors",(e=>(e||(0,h.List)()).push((0,h.fromJS)(n)))).update("errors",(e=>(0,g.default)(e)))},[m.NEW_THROWN_ERR_BATCH]:(e,t)=>{let{payload:r}=t;return r=o()(r).call(r,(e=>(0,h.fromJS)(s()(f,e,{type:"thrown"})))),e.update("errors",(e=>{var t;return i()(t=e||(0,h.List)()).call(t,(0,h.fromJS)(r))})).update("errors",(e=>(0,g.default)(e)))},[m.NEW_SPEC_ERR]:(e,t)=>{let{payload:r}=t,n=(0,h.fromJS)(r);return n=n.set("type","spec"),e.update("errors",(e=>(e||(0,h.List)()).push((0,h.fromJS)(n)).sortBy((e=>e.get("line"))))).update("errors",(e=>(0,g.default)(e)))},[m.NEW_SPEC_ERR_BATCH]:(e,t)=>{let{payload:r}=t;return r=o()(r).call(r,(e=>(0,h.fromJS)(s()(f,e,{type:"spec"})))),e.update("errors",(e=>{var t;return i()(t=e||(0,h.List)()).call(t,(0,h.fromJS)(r))})).update("errors",(e=>(0,g.default)(e)))},[m.NEW_AUTH_ERR]:(e,t)=>{let{payload:r}=t,n=(0,h.fromJS)(s()({},r));return n=n.set("type","auth"),e.update("errors",(e=>(e||(0,h.List)()).push((0,h.fromJS)(n)))).update("errors",(e=>(0,g.default)(e)))},[m.CLEAR]:(e,t)=>{var r;let{payload:n}=t;if(!n||!e.get("errors"))return e;let s=p()(r=e.get("errors")).call(r,(e=>{var t;return d()(t=e.keySeq()).call(t,(t=>{const r=e.get(t),s=n[t];return!s||r!==s}))}));return e.merge({errors:s})},[m.CLEAR_BY]:(e,t)=>{var r;let{payload:n}=t;if(!n||"function"!=typeof n)return e;let s=p()(r=e.get("errors")).call(r,(e=>n(e)));return e.merge({errors:s})}}}},7667:(e,t,r)=>{"use strict";r.r(t),r.d(t,{allErrors:()=>a,lastError:()=>o});var n=r(5572),s=r(6814);const a=(0,s.createSelector)((e=>e),(e=>e.get("errors",(0,n.List)()))),o=(0,s.createSelector)(a,(e=>e.last()))},9978:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(4309);function s(){return{fn:{opsFilter:n.default}}}},4309:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(9998),s=r.n(n),a=r(8493),o=r.n(a);function l(e,t){return s()(e).call(e,((e,r)=>-1!==o()(r).call(r,t)))}},5474:(e,t,r)=>{"use strict";r.r(t),r.d(t,{UPDATE_LAYOUT:()=>s,UPDATE_FILTER:()=>a,UPDATE_MODE:()=>o,SHOW:()=>l,updateLayout:()=>i,updateFilter:()=>c,show:()=>p,changeMode:()=>u});var n=r(1890);const s="layout_update_layout",a="layout_update_filter",o="layout_update_mode",l="layout_show";function i(e){return{type:s,payload:e}}function c(e){return{type:a,payload:e}}function p(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return e=(0,n.AF)(e),{type:l,payload:{thing:e,shown:t}}}function u(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e=(0,n.AF)(e),{type:o,payload:{thing:e,mode:t}}}},6821:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(5672),s=r(5474),a=r(4400),o=r(8989);function l(){return{statePlugins:{layout:{reducers:n.default,actions:s,selectors:a},spec:{wrapSelectors:o}}}}},5672:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(4883),s=r.n(n),a=r(5572),o=r(5474);const l={[o.UPDATE_LAYOUT]:(e,t)=>e.set("layout",t.payload),[o.UPDATE_FILTER]:(e,t)=>e.set("filter",t.payload),[o.SHOW]:(e,t)=>{const r=t.payload.shown,n=(0,a.fromJS)(t.payload.thing);return e.update("shown",(0,a.fromJS)({}),(e=>e.set(n,r)))},[o.UPDATE_MODE]:(e,t)=>{var r;let n=t.payload.thing,a=t.payload.mode;return e.setIn(s()(r=["modes"]).call(r,n),(a||"")+"")}}},4400:(e,t,r)=>{"use strict";r.r(t),r.d(t,{current:()=>o,currentFilter:()=>l,isShown:()=>i,whatMode:()=>c,showSummary:()=>p});var n=r(6814),s=r(1890),a=r(5572);const o=e=>e.get("layout"),l=e=>e.get("filter"),i=(e,t,r)=>(t=(0,s.AF)(t),e.get("shown",(0,a.fromJS)({})).get((0,a.fromJS)(t),r)),c=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return t=(0,s.AF)(t),e.getIn(["modes",...t],r)},p=(0,n.createSelector)((e=>e),(e=>!i(e,"editor")))},8989:(e,t,r)=>{"use strict";r.r(t),r.d(t,{taggedOperations:()=>a});var n=r(600),s=r.n(n);const a=(e,t)=>function(r){for(var n=arguments.length,a=new Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];let l=e(r,...a);const{fn:i,layoutSelectors:c,getConfigs:p}=t.getSystem(),u=p(),{maxDisplayedTags:d}=u;let m=c.currentFilter();return m&&!0!==m&&"true"!==m&&"false"!==m&&(l=i.opsFilter(l,m)),d&&!isNaN(d)&&d>=0&&(l=s()(l).call(l,0,d)),l}},9150:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(593),s=r.n(n);function a(e){let{configs:t}=e;const r={debug:0,info:1,log:2,warn:3,error:4},n=e=>r[e]||-1;let{logLevel:a}=t,o=n(a);function l(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),s=1;s<t;s++)r[s-1]=arguments[s];n(e)>=o&&console[e](...r)}return l.warn=s()(l).call(l,null,"warn"),l.error=s()(l).call(l,null,"error"),l.info=s()(l).call(l,null,"info"),l.debug=s()(l).call(l,null,"debug"),{rootInjects:{log:l}}}},7002:(e,t,r)=>{"use strict";r.r(t),r.d(t,{UPDATE_SELECTED_SERVER:()=>n,UPDATE_REQUEST_BODY_VALUE:()=>s,UPDATE_REQUEST_BODY_VALUE_RETAIN_FLAG:()=>a,UPDATE_REQUEST_BODY_INCLUSION:()=>o,UPDATE_ACTIVE_EXAMPLES_MEMBER:()=>l,UPDATE_REQUEST_CONTENT_TYPE:()=>i,UPDATE_RESPONSE_CONTENT_TYPE:()=>c,UPDATE_SERVER_VARIABLE_VALUE:()=>p,SET_REQUEST_BODY_VALIDATE_ERROR:()=>u,CLEAR_REQUEST_BODY_VALIDATE_ERROR:()=>d,CLEAR_REQUEST_BODY_VALUE:()=>m,setSelectedServer:()=>h,setRequestBodyValue:()=>g,setRetainRequestBodyValueFlag:()=>f,setRequestBodyInclusion:()=>y,setActiveExamplesMember:()=>v,setRequestContentType:()=>E,setResponseContentType:()=>S,setServerVariableValue:()=>C,setRequestBodyValidateError:()=>b,clearRequestBodyValidateError:()=>x,initRequestBodyValidateError:()=>w,clearRequestBodyValue:()=>_});const n="oas3_set_servers",s="oas3_set_request_body_value",a="oas3_set_request_body_retain_flag",o="oas3_set_request_body_inclusion",l="oas3_set_active_examples_member",i="oas3_set_request_content_type",c="oas3_set_response_content_type",p="oas3_set_server_variable_value",u="oas3_set_request_body_validate_error",d="oas3_clear_request_body_validate_error",m="oas3_clear_request_body_value";function h(e,t){return{type:n,payload:{selectedServerUrl:e,namespace:t}}}function g(e){let{value:t,pathMethod:r}=e;return{type:s,payload:{value:t,pathMethod:r}}}const f=e=>{let{value:t,pathMethod:r}=e;return{type:a,payload:{value:t,pathMethod:r}}};function y(e){let{value:t,pathMethod:r,name:n}=e;return{type:o,payload:{value:t,pathMethod:r,name:n}}}function v(e){let{name:t,pathMethod:r,contextType:n,contextName:s}=e;return{type:l,payload:{name:t,pathMethod:r,contextType:n,contextName:s}}}function E(e){let{value:t,pathMethod:r}=e;return{type:i,payload:{value:t,pathMethod:r}}}function S(e){let{value:t,path:r,method:n}=e;return{type:c,payload:{value:t,path:r,method:n}}}function C(e){let{server:t,namespace:r,key:n,val:s}=e;return{type:p,payload:{server:t,namespace:r,key:n,val:s}}}const b=e=>{let{path:t,method:r,validationErrors:n}=e;return{type:u,payload:{path:t,method:r,validationErrors:n}}},x=e=>{let{path:t,method:r}=e;return{type:d,payload:{path:t,method:r}}},w=e=>{let{pathMethod:t}=e;return{type:d,payload:{path:t[0],method:t[1]}}},_=e=>{let{pathMethod:t}=e;return{type:m,payload:{pathMethod:t}}}},3723:(e,t,r)=>{"use strict";r.r(t),r.d(t,{definitionsToAuthorize:()=>d});var n=r(4235),s=r.n(n),a=r(9998),o=r.n(a),l=r(66),i=r.n(l),c=r(6814),p=r(5572),u=r(7779);const d=(m=(0,c.createSelector)((e=>e),(e=>{let{specSelectors:t}=e;return t.securityDefinitions()}),((e,t)=>{var r;let n=(0,p.List)();return t?(s()(r=t.entrySeq()).call(r,(e=>{let[t,r]=e;const a=r.get("type");var l;if("oauth2"===a&&s()(l=r.get("flows").entrySeq()).call(l,(e=>{let[s,a]=e,l=(0,p.fromJS)({flow:s,authorizationUrl:a.get("authorizationUrl"),tokenUrl:a.get("tokenUrl"),scopes:a.get("scopes"),type:r.get("type"),description:r.get("description")});n=n.push(new p.Map({[t]:o()(l).call(l,(e=>void 0!==e))}))})),"http"!==a&&"apiKey"!==a||(n=n.push(new p.Map({[t]:r}))),"openIdConnect"===a&&r.get("openIdConnectData")){let e=r.get("openIdConnectData"),a=e.get("grant_types_supported")||["authorization_code","implicit"];s()(a).call(a,(s=>{var a;let l=e.get("scopes_supported")&&i()(a=e.get("scopes_supported")).call(a,((e,t)=>e.set(t,"")),new p.Map),c=(0,p.fromJS)({flow:s,authorizationUrl:e.get("authorization_endpoint"),tokenUrl:e.get("token_endpoint"),scopes:l,type:"oauth2",openIdConnectUrl:r.get("openIdConnectUrl")});n=n.push(new p.Map({[t]:o()(c).call(c,(e=>void 0!==e))}))}))}})),n):n})),(e,t)=>function(){const r=t.getSystem().specSelectors.specJson();for(var n=arguments.length,s=new Array(n),a=0;a<n;a++)s[a]=arguments[a];if((0,u.isOAS3)(r)){let e=t.getState().getIn(["spec","resolvedSubtrees","components","securitySchemes"]);return m(t,e,...s)}return e(...s)});var m},3427:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var n=r(4250),s=r.n(n),a=r(3942),o=r.n(a),l=r(6689),i=r.n(l),c=(r(580),r(8082),r(5572));const p=e=>{var t;let{callbacks:r,getComponent:n,specPath:a}=e;const l=n("OperationContainer",!0);if(!r)return i().createElement("span",null,"No callbacks");let p=o()(t=r.entrySeq()).call(t,(t=>{var r;let[n,p]=t;return i().createElement("div",{key:n},i().createElement("h2",null,n),o()(r=p.entrySeq()).call(r,(t=>{var r;let[p,u]=t;return"$$ref"===p?null:i().createElement("div",{key:p},o()(r=u.entrySeq()).call(r,(t=>{let[r,o]=t;if("$$ref"===r)return null;let u=(0,c.fromJS)({operation:o});return i().createElement(l,s()({},e,{op:u,key:r,tag:"",method:r,path:p,specPath:a.push(n,p,r),allowTryItOut:!1}))})))})))}));return i().createElement("div",null,p)}},6775:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var n=r(1093),s=r.n(n),a=r(4994),o=r.n(a),l=r(9998),i=r.n(l),c=r(3942),p=r.n(c),u=r(6689),d=r.n(u);r(580);class m extends d().Component{constructor(e,t){super(e,t),s()(this,"onChange",(e=>{let{onChange:t}=this.props,{value:r,name:n}=e.target,s=o()({},this.state.value);n?s[n]=r:s=r,this.setState({value:s},(()=>t(this.state)))}));let{name:r,schema:n}=this.props,a=this.getValue();this.state={name:r,schema:n,value:a}}getValue(){let{name:e,authorized:t}=this.props;return t&&t.getIn([e,"value"])}render(){var e;let{schema:t,getComponent:r,errSelectors:n,name:s}=this.props;const a=r("Input"),o=r("Row"),l=r("Col"),c=r("authError"),u=r("Markdown",!0),m=r("JumpToPath",!0),h=(t.get("scheme")||"").toLowerCase();let g=this.getValue(),f=i()(e=n.allErrors()).call(e,(e=>e.get("authId")===s));if("basic"===h){var y;let e=g?g.get("username"):null;return d().createElement("div",null,d().createElement("h4",null,d().createElement("code",null,s||t.get("name")),"  (http, Basic)",d().createElement(m,{path:["securityDefinitions",s]})),e&&d().createElement("h6",null,"Authorized"),d().createElement(o,null,d().createElement(u,{source:t.get("description")})),d().createElement(o,null,d().createElement("label",null,"Username:"),e?d().createElement("code",null," ",e," "):d().createElement(l,null,d().createElement(a,{type:"text",required:"required",name:"username","aria-label":"auth-basic-username",onChange:this.onChange,autoFocus:!0}))),d().createElement(o,null,d().createElement("label",null,"Password:"),e?d().createElement("code",null," ****** "):d().createElement(l,null,d().createElement(a,{autoComplete:"new-password",name:"password",type:"password","aria-label":"auth-basic-password",onChange:this.onChange}))),p()(y=f.valueSeq()).call(y,((e,t)=>d().createElement(c,{error:e,key:t}))))}var v;return"bearer"===h?d().createElement("div",null,d().createElement("h4",null,d().createElement("code",null,s||t.get("name")),"  (http, Bearer)",d().createElement(m,{path:["securityDefinitions",s]})),g&&d().createElement("h6",null,"Authorized"),d().createElement(o,null,d().createElement(u,{source:t.get("description")})),d().createElement(o,null,d().createElement("label",null,"Value:"),g?d().createElement("code",null," ****** "):d().createElement(l,null,d().createElement(a,{type:"text","aria-label":"auth-bearer-value",onChange:this.onChange,autoFocus:!0}))),p()(v=f.valueSeq()).call(v,((e,t)=>d().createElement(c,{error:e,key:t})))):d().createElement("div",null,d().createElement("em",null,d().createElement("b",null,s)," HTTP authentication: unsupported scheme ",`'${h}'`))}}},6467:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(3427),s=r(2458),a=r(5757),o=r(6617),l=r(9928),i=r(5327),c=r(6775),p=r(6796);const u={Callbacks:n.default,HttpAuth:c.default,RequestBody:s.default,Servers:o.default,ServersContainer:l.default,RequestBodyEditor:i.default,OperationServers:p.default,operationLink:a.default}},5757:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var n=r(8344),s=r.n(n),a=r(3942),o=r.n(a),l=r(6689),i=r.n(l);r(580),r(8082);class c extends l.Component{render(){const{link:e,name:t,getComponent:r}=this.props,n=r("Markdown",!0);let a=e.get("operationId")||e.get("operationRef"),l=e.get("parameters")&&e.get("parameters").toJS(),c=e.get("description");return i().createElement("div",{className:"operation-link"},i().createElement("div",{className:"description"},i().createElement("b",null,i().createElement("code",null,t)),c?i().createElement(n,{source:c}):null),i().createElement("pre",null,"Operation `",a,"`",i().createElement("br",null),i().createElement("br",null),"Parameters ",function(e,t){var r;if("string"!=typeof t)return"";return o()(r=t.split("\n")).call(r,((t,r)=>r>0?Array(e+1).join(" ")+t:t)).join("\n")}(0,s()(l,null,2))||"{}",i().createElement("br",null)))}}const p=c},6796:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(1093),s=r.n(n),a=r(6689),o=r.n(a);r(580),r(8082);class l extends o().Component{constructor(){super(...arguments),s()(this,"setSelectedServer",(e=>{const{path:t,method:r}=this.props;return this.forceUpdate(),this.props.setSelectedServer(e,`${t}:${r}`)})),s()(this,"setServerVariableValue",(e=>{const{path:t,method:r}=this.props;return this.forceUpdate(),this.props.setServerVariableValue({...e,namespace:`${t}:${r}`})})),s()(this,"getSelectedServer",(()=>{const{path:e,method:t}=this.props;return this.props.getSelectedServer(`${e}:${t}`)})),s()(this,"getServerVariable",((e,t)=>{const{path:r,method:n}=this.props;return this.props.getServerVariable({namespace:`${r}:${n}`,server:e},t)})),s()(this,"getEffectiveServerValue",(e=>{const{path:t,method:r}=this.props;return this.props.getEffectiveServerValue({server:e,namespace:`${t}:${r}`})}))}render(){const{operationServers:e,pathServers:t,getComponent:r}=this.props;if(!e&&!t)return null;const n=r("Servers"),s=e||t,a=e?"operation":"path";return o().createElement("div",{className:"opblock-section operation-servers"},o().createElement("div",{className:"opblock-section-header"},o().createElement("div",{className:"tab-header"},o().createElement("h4",{className:"opblock-title"},"Servers"))),o().createElement("div",{className:"opblock-description-wrapper"},o().createElement("h4",{className:"message"},"These ",a,"-level options override the global server options."),o().createElement(n,{servers:s,currentServer:this.getSelectedServer(),setSelectedServer:this.setSelectedServer,setServerVariableValue:this.setServerVariableValue,getServerVariable:this.getServerVariable,getEffectiveServerValue:this.getEffectiveServerValue})))}}},5327:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(1093),s=r.n(n),a=r(6689),o=r.n(a),l=(r(580),r(9003)),i=r.n(l),c=r(1890);const p=Function.prototype;class u extends a.PureComponent{constructor(e,t){super(e,t),s()(this,"applyDefaultValue",(e=>{const{onChange:t,defaultValue:r}=e||this.props;return this.setState({value:r}),t(r)})),s()(this,"onChange",(e=>{this.props.onChange((0,c.Pz)(e))})),s()(this,"onDomChange",(e=>{const t=e.target.value;this.setState({value:t},(()=>this.onChange(t)))})),this.state={value:(0,c.Pz)(e.value)||e.defaultValue},e.onChange(e.value)}UNSAFE_componentWillReceiveProps(e){this.props.value!==e.value&&e.value!==this.state.value&&this.setState({value:(0,c.Pz)(e.value)}),!e.value&&e.defaultValue&&this.state.value&&this.applyDefaultValue(e)}render(){let{getComponent:e,errors:t}=this.props,{value:r}=this.state,n=t.size>0;const s=e("TextArea");return o().createElement("div",{className:"body-param"},o().createElement(s,{className:i()("body-param__text",{invalid:n}),title:t.size?t.join(", "):"",value:r,onChange:this.onDomChange}))}}s()(u,"defaultProps",{onChange:p,userHasEditedBody:!1})},2458:(e,t,r)=>{"use strict";r.r(t),r.d(t,{getDefaultRequestBodyValue:()=>f,default:()=>y});var n=r(3942),s=r.n(n),a=r(8493),o=r.n(a),l=r(2605),i=r.n(l),c=r(7104),p=r.n(c),u=r(6689),d=r.n(u),m=(r(580),r(8082),r(5572)),h=r(1890),g=r(2518);const f=(e,t,r)=>{const n=e.getIn(["content",t]),s=n.get("schema").toJS(),a=void 0!==n.get("examples"),o=n.get("example"),l=a?n.getIn(["examples",r,"value"]):o,i=(0,h.xi)(s,t,{includeWriteOnly:!0},l);return(0,h.Pz)(i)},y=e=>{let{userHasEditedBody:t,requestBody:r,requestBodyValue:n,requestBodyInclusionSetting:a,requestBodyErrors:l,getComponent:c,getConfigs:u,specSelectors:y,fn:v,contentType:E,isExecute:S,specPath:C,onChange:b,onChangeIncludeEmpty:x,activeExamplesKey:w,updateActiveExamplesKey:_,setRetainRequestBodyValueFlag:A}=e;const I=e=>{b(e.target.files[0])},N=e=>{let t={key:e,shouldDispatchInit:!1,defaultValue:!0};return"no value"===a.get(e,"no value")&&(t.shouldDispatchInit=!0),t},q=c("Markdown",!0),R=c("modelExample"),T=c("RequestBodyEditor"),P=c("highlightCode"),k=c("ExamplesSelectValueRetainer"),O=c("Example"),M=c("ParameterIncludeEmpty"),{showCommonExtensions:j}=u(),V=r&&r.get("description")||null,D=r&&r.get("content")||new m.OrderedMap;E=E||D.keySeq().first()||"";const L=D.get(E,(0,m.OrderedMap)()),U=L.get("schema",(0,m.OrderedMap)()),z=L.get("examples",null),B=null==z?void 0:s()(z).call(z,((e,t)=>{var n;const s=null===(n=e)||void 0===n?void 0:n.get("value",null);return s&&(e=e.set("value",f(r,E,t),s)),e}));if(l=m.List.isList(l)?l:(0,m.List)(),!L.size)return null;const $="object"===L.getIn(["schema","type"]),J="binary"===L.getIn(["schema","format"]),F="base64"===L.getIn(["schema","format"]);if("application/octet-stream"===E||0===o()(E).call(E,"image/")||0===o()(E).call(E,"audio/")||0===o()(E).call(E,"video/")||J||F){const e=c("Input");return S?d().createElement(e,{type:"file",onChange:I}):d().createElement("i",null,"Example values are not available for ",d().createElement("code",null,E)," media types.")}if($&&("application/x-www-form-urlencoded"===E||0===o()(E).call(E,"multipart/"))&&U.get("properties",(0,m.OrderedMap)()).size>0){var W;const e=c("JsonSchemaForm"),t=c("ParameterExt"),r=U.get("properties",(0,m.OrderedMap)());return n=m.Map.isMap(n)?n:(0,m.OrderedMap)(),d().createElement("div",{className:"table-container"},V&&d().createElement(q,{source:V}),d().createElement("table",null,d().createElement("tbody",null,m.Map.isMap(r)&&s()(W=r.entrySeq()).call(W,(r=>{var o,u;let[g,f]=r;if(f.get("readOnly"))return;let y=j?(0,h.po)(f):null;const E=i()(o=U.get("required",(0,m.List)())).call(o,g),C=f.get("type"),w=f.get("format"),_=f.get("description"),A=n.getIn([g,"value"]),I=n.getIn([g,"errors"])||l,R=a.get(g)||!1,T=f.has("default")||f.has("example")||f.hasIn(["items","example"])||f.hasIn(["items","default"]),P=f.has("enum")&&(1===f.get("enum").size||E),k=T||P;let O="";"array"!==C||k||(O=[]),("object"===C||k)&&(O=(0,h.xi)(f,!1,{includeWriteOnly:!0})),"string"!=typeof O&&"object"===C&&(O=(0,h.Pz)(O)),"string"==typeof O&&"array"===C&&(O=JSON.parse(O));const V="string"===C&&("binary"===w||"base64"===w);return d().createElement("tr",{key:g,className:"parameters","data-property-name":g},d().createElement("td",{className:"parameters-col_name"},d().createElement("div",{className:E?"parameter__name required":"parameter__name"},g,E?d().createElement("span",null," *"):null),d().createElement("div",{className:"parameter__type"},C,w&&d().createElement("span",{className:"prop-format"},"($",w,")"),j&&y.size?s()(u=y.entrySeq()).call(u,(e=>{let[r,n]=e;return d().createElement(t,{key:`${r}-${n}`,xKey:r,xVal:n})})):null),d().createElement("div",{className:"parameter__deprecated"},f.get("deprecated")?"deprecated":null)),d().createElement("td",{className:"parameters-col_description"},d().createElement(q,{source:_}),S?d().createElement("div",null,d().createElement(e,{fn:v,dispatchInitialValue:!V,schema:f,description:g,getComponent:c,value:void 0===A?O:A,required:E,errors:I,onChange:e=>{b(e,[g])}}),E?null:d().createElement(M,{onChange:e=>x(g,e),isIncluded:R,isIncludedOptions:N(g),isDisabled:p()(A)?0!==A.length:!(0,h.O2)(A)})):null))})))))}const H=f(r,E,w);let K=null;return(0,g.O)(H)&&(K="json"),d().createElement("div",null,V&&d().createElement(q,{source:V}),B?d().createElement(k,{userHasEditedBody:t,examples:B,currentKey:w,currentUserInputValue:n,onSelect:e=>{_(e)},updateValue:b,defaultToFirstExample:!0,getComponent:c,setRetainRequestBodyValueFlag:A}):null,S?d().createElement("div",null,d().createElement(T,{value:n,errors:l,defaultValue:H,onChange:b,getComponent:c})):d().createElement(R,{getComponent:c,getConfigs:u,specSelectors:y,expandDepth:1,isExecute:S,schema:L.get("schema"),specPath:C.push("content",E),example:d().createElement(P,{className:"body-param__example",getConfigs:u,language:K,value:(0,h.Pz)(n)||H}),includeWriteOnly:!0}),B?d().createElement(O,{example:B.get(w),getComponent:c,getConfigs:u}):null)}},9928:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(6689),s=r.n(n);r(580);class a extends s().Component{render(){const{specSelectors:e,oas3Selectors:t,oas3Actions:r,getComponent:n}=this.props,a=e.servers(),o=n("Servers");return a&&a.size?s().createElement("div",null,s().createElement("span",{className:"servers-title"},"Servers"),s().createElement(o,{servers:a,currentServer:t.selectedServer(),setSelectedServer:r.setSelectedServer,setServerVariableValue:r.setServerVariableValue,getServerVariable:t.serverVariableValue,getEffectiveServerValue:t.serverEffectiveValue})):null}}},6617:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var n=r(1093),s=r.n(n),a=r(3580),o=r.n(a),l=r(3942),i=r.n(l),c=r(6689),p=r.n(c),u=r(5572);r(580),r(8082);class d extends p().Component{constructor(){super(...arguments),s()(this,"onServerChange",(e=>{this.setServer(e.target.value)})),s()(this,"onServerVariableValueChange",(e=>{let{setServerVariableValue:t,currentServer:r}=this.props,n=e.target.getAttribute("data-variable"),s=e.target.value;"function"==typeof t&&t({server:r,key:n,val:s})})),s()(this,"setServer",(e=>{let{setSelectedServer:t}=this.props;t(e)}))}componentDidMount(){var e;let{servers:t,currentServer:r}=this.props;r||this.setServer(null===(e=t.first())||void 0===e?void 0:e.get("url"))}UNSAFE_componentWillReceiveProps(e){let{servers:t,setServerVariableValue:r,getServerVariable:n}=e;if(this.props.currentServer!==e.currentServer||this.props.servers!==e.servers){var s;let a=o()(t).call(t,(t=>t.get("url")===e.currentServer)),l=o()(s=this.props.servers).call(s,(e=>e.get("url")===this.props.currentServer))||(0,u.OrderedMap)();if(!a)return this.setServer(t.first().get("url"));let c=l.get("variables")||(0,u.OrderedMap)(),p=(o()(c).call(c,(e=>e.get("default")))||(0,u.OrderedMap)()).get("default"),d=a.get("variables")||(0,u.OrderedMap)(),m=(o()(d).call(d,(e=>e.get("default")))||(0,u.OrderedMap)()).get("default");i()(d).call(d,((t,s)=>{n(e.currentServer,s)&&p===m||r({server:e.currentServer,key:s,val:t.get("default")||""})}))}}render(){var e,t;let{servers:r,currentServer:n,getServerVariable:s,getEffectiveServerValue:a}=this.props,l=(o()(r).call(r,(e=>e.get("url")===n))||(0,u.OrderedMap)()).get("variables")||(0,u.OrderedMap)(),c=0!==l.size;return p().createElement("div",{className:"servers"},p().createElement("label",{htmlFor:"servers"},p().createElement("select",{onChange:this.onServerChange,value:n},i()(e=r.valueSeq()).call(e,(e=>p().createElement("option",{value:e.get("url"),key:e.get("url")},e.get("url"),e.get("description")&&` - ${e.get("description")}`))).toArray())),c?p().createElement("div",null,p().createElement("div",{className:"computed-url"},"Computed URL:",p().createElement("code",null,a(n))),p().createElement("h4",null,"Server variables"),p().createElement("table",null,p().createElement("tbody",null,i()(t=l.entrySeq()).call(t,(e=>{var t;let[r,a]=e;return p().createElement("tr",{key:r},p().createElement("td",null,r),p().createElement("td",null,a.get("enum")?p().createElement("select",{"data-variable":r,onChange:this.onServerVariableValueChange},i()(t=a.get("enum")).call(t,(e=>p().createElement("option",{selected:e===s(n,r),key:e,value:e},e)))):p().createElement("input",{type:"text",value:s(n,r)||"",onChange:this.onServerVariableValueChange,"data-variable":r})))}))))):null)}}},7779:(e,t,r)=>{"use strict";r.r(t),r.d(t,{isOAS3:()=>c,isSwagger2:()=>p,OAS3ComponentWrapFactory:()=>u});var n=r(4250),s=r.n(n),a=r(3262),o=r.n(a),l=r(6689),i=r.n(l);function c(e){const t=e.get("openapi");return"string"==typeof t&&(o()(t).call(t,"3.0.")&&t.length>4)}function p(e){const t=e.get("swagger");return"string"==typeof t&&o()(t).call(t,"2.0")}function u(e){return(t,r)=>n=>{if(r&&r.specSelectors&&r.specSelectors.specJson){return c(r.specSelectors.specJson())?i().createElement(e,s()({},n,r,{Ori:t})):i().createElement(t,n)}return console.warn("OAS3 wrapper: couldn't get spec"),null}}},7451:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(2044),s=r(3723),a=r(1741),o=r(6467),l=r(7761),i=r(7002),c=r(5065),p=r(2109);function u(){return{components:o.default,wrapComponents:l.default,statePlugins:{spec:{wrapSelectors:n,selectors:a},auth:{wrapSelectors:s},oas3:{actions:i,reducers:p.default,selectors:c}}}}},2109:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(874),s=r.n(n),a=r(4235),o=r.n(a),l=r(66),i=r.n(l),c=r(5572),p=r(7002);const u={[p.UPDATE_SELECTED_SERVER]:(e,t)=>{let{payload:{selectedServerUrl:r,namespace:n}}=t;const s=n?[n,"selectedServer"]:["selectedServer"];return e.setIn(s,r)},[p.UPDATE_REQUEST_BODY_VALUE]:(e,t)=>{let{payload:{value:r,pathMethod:n}}=t,[a,l]=n;if(!c.Map.isMap(r))return e.setIn(["requestData",a,l,"bodyValue"],r);let i,p=e.getIn(["requestData",a,l,"bodyValue"])||(0,c.Map)();c.Map.isMap(p)||(p=(0,c.Map)());const[...u]=s()(r).call(r);return o()(u).call(u,(e=>{let t=r.getIn([e]);p.has(e)&&c.Map.isMap(t)||(i=p.setIn([e,"value"],t))})),e.setIn(["requestData",a,l,"bodyValue"],i)},[p.UPDATE_REQUEST_BODY_VALUE_RETAIN_FLAG]:(e,t)=>{let{payload:{value:r,pathMethod:n}}=t,[s,a]=n;return e.setIn(["requestData",s,a,"retainBodyValue"],r)},[p.UPDATE_REQUEST_BODY_INCLUSION]:(e,t)=>{let{payload:{value:r,pathMethod:n,name:s}}=t,[a,o]=n;return e.setIn(["requestData",a,o,"bodyInclusion",s],r)},[p.UPDATE_ACTIVE_EXAMPLES_MEMBER]:(e,t)=>{let{payload:{name:r,pathMethod:n,contextType:s,contextName:a}}=t,[o,l]=n;return e.setIn(["examples",o,l,s,a,"activeExample"],r)},[p.UPDATE_REQUEST_CONTENT_TYPE]:(e,t)=>{let{payload:{value:r,pathMethod:n}}=t,[s,a]=n;return e.setIn(["requestData",s,a,"requestContentType"],r)},[p.UPDATE_RESPONSE_CONTENT_TYPE]:(e,t)=>{let{payload:{value:r,path:n,method:s}}=t;return e.setIn(["requestData",n,s,"responseContentType"],r)},[p.UPDATE_SERVER_VARIABLE_VALUE]:(e,t)=>{let{payload:{server:r,namespace:n,key:s,val:a}}=t;const o=n?[n,"serverVariableValues",r,s]:["serverVariableValues",r,s];return e.setIn(o,a)},[p.SET_REQUEST_BODY_VALIDATE_ERROR]:(e,t)=>{let{payload:{path:r,method:n,validationErrors:s}}=t,a=[];if(a.push("Required field is not provided"),s.missingBodyValue)return e.setIn(["requestData",r,n,"errors"],(0,c.fromJS)(a));if(s.missingRequiredKeys&&s.missingRequiredKeys.length>0){const{missingRequiredKeys:t}=s;return e.updateIn(["requestData",r,n,"bodyValue"],(0,c.fromJS)({}),(e=>i()(t).call(t,((e,t)=>e.setIn([t,"errors"],(0,c.fromJS)(a))),e)))}return console.warn("unexpected result: SET_REQUEST_BODY_VALIDATE_ERROR"),e},[p.CLEAR_REQUEST_BODY_VALIDATE_ERROR]:(e,t)=>{let{payload:{path:r,method:n}}=t;const a=e.getIn(["requestData",r,n,"bodyValue"]);if(!c.Map.isMap(a))return e.setIn(["requestData",r,n,"errors"],(0,c.fromJS)([]));const[...o]=s()(a).call(a);return o?e.updateIn(["requestData",r,n,"bodyValue"],(0,c.fromJS)({}),(e=>i()(o).call(o,((e,t)=>e.setIn([t,"errors"],(0,c.fromJS)([]))),e))):e},[p.CLEAR_REQUEST_BODY_VALUE]:(e,t)=>{let{payload:{pathMethod:r}}=t,[n,s]=r;const a=e.getIn(["requestData",n,s,"bodyValue"]);return a?c.Map.isMap(a)?e.setIn(["requestData",n,s,"bodyValue"],(0,c.Map)()):e.setIn(["requestData",n,s,"bodyValue"],""):e}}},5065:(e,t,r)=>{"use strict";r.r(t),r.d(t,{selectedServer:()=>f,requestBodyValue:()=>y,shouldRetainRequestBodyValue:()=>v,selectDefaultRequestBodyValue:()=>E,hasUserEditedBody:()=>S,requestBodyInclusionSetting:()=>C,requestBodyErrors:()=>b,activeExamplesMember:()=>x,requestContentType:()=>w,responseContentType:()=>_,serverVariableValue:()=>A,serverVariables:()=>I,serverEffectiveValue:()=>N,validateBeforeExecute:()=>q,validateShallowRequired:()=>T});var n=r(3942),s=r.n(n),a=r(4235),o=r.n(a),l=r(7252),i=r.n(l),c=r(8493),p=r.n(c),u=r(5572),d=r(7779),m=r(2458),h=r(1890);function g(e){return function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return t=>{const n=t.getSystem().specSelectors.specJson();return(0,d.isOAS3)(n)?e(...r):null}}}const f=g(((e,t)=>{const r=t?[t,"selectedServer"]:["selectedServer"];return e.getIn(r)||""})),y=g(((e,t,r)=>e.getIn(["requestData",t,r,"bodyValue"])||null)),v=g(((e,t,r)=>e.getIn(["requestData",t,r,"retainBodyValue"])||!1)),E=(e,t,r)=>e=>{const{oas3Selectors:n,specSelectors:s}=e.getSystem(),a=s.specJson();if((0,d.isOAS3)(a)){const e=n.requestContentType(t,r);if(e)return(0,m.getDefaultRequestBodyValue)(s.specResolvedSubtree(["paths",t,r,"requestBody"]),e,n.activeExamplesMember(t,r,"requestBody","requestBody"))}return null},S=(e,t,r)=>e=>{const{oas3Selectors:n,specSelectors:s}=e.getSystem(),a=s.specJson();if((0,d.isOAS3)(a)){let e=!1;const a=n.requestContentType(t,r);let o=n.requestBodyValue(t,r);if(u.Map.isMap(o)&&(o=(0,h.Pz)(o.mapEntries((e=>u.Map.isMap(e[1])?[e[0],e[1].get("value")]:e)).toJS())),u.List.isList(o)&&(o=(0,h.Pz)(o)),a){const l=(0,m.getDefaultRequestBodyValue)(s.specResolvedSubtree(["paths",t,r,"requestBody"]),a,n.activeExamplesMember(t,r,"requestBody","requestBody"));e=!!o&&o!==l}return e}return null},C=g(((e,t,r)=>e.getIn(["requestData",t,r,"bodyInclusion"])||(0,u.Map)())),b=g(((e,t,r)=>e.getIn(["requestData",t,r,"errors"])||null)),x=g(((e,t,r,n,s)=>e.getIn(["examples",t,r,n,s,"activeExample"])||null)),w=g(((e,t,r)=>e.getIn(["requestData",t,r,"requestContentType"])||null)),_=g(((e,t,r)=>e.getIn(["requestData",t,r,"responseContentType"])||null)),A=g(((e,t,r)=>{let n;if("string"!=typeof t){const{server:e,namespace:s}=t;n=s?[s,"serverVariableValues",e,r]:["serverVariableValues",e,r]}else{n=["serverVariableValues",t,r]}return e.getIn(n)||null})),I=g(((e,t)=>{let r;if("string"!=typeof t){const{server:e,namespace:n}=t;r=n?[n,"serverVariableValues",e]:["serverVariableValues",e]}else{r=["serverVariableValues",t]}return e.getIn(r)||(0,u.OrderedMap)()})),N=g(((e,t)=>{var r,n;if("string"!=typeof t){const{server:s,namespace:a}=t;n=s,r=a?e.getIn([a,"serverVariableValues",n]):e.getIn(["serverVariableValues",n])}else n=t,r=e.getIn(["serverVariableValues",n]);r=r||(0,u.OrderedMap)();let a=n;return s()(r).call(r,((e,t)=>{a=a.replace(new RegExp(`{${t}}`,"g"),e)})),a})),q=(R=(e,t)=>((e,t)=>(t=t||[],!!e.getIn(["requestData",...t,"bodyValue"])))(e,t),function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return e=>{const r=e.getSystem().specSelectors.specJson();let n=[...t][1]||[];return!r.getIn(["paths",...n,"requestBody","required"])||R(...t)}});var R;const T=(e,t)=>{var r;let{oas3RequiredRequestBodyContentType:n,oas3RequestContentType:s,oas3RequestBodyValue:a}=t,l=[];if(!u.Map.isMap(a))return l;let c=[];return o()(r=i()(n.requestContentType)).call(r,(e=>{if(e===s){let t=n.requestContentType[e];o()(t).call(t,(e=>{p()(c).call(c,e)<0&&c.push(e)}))}})),o()(c).call(c,(e=>{a.getIn([e,"value"])||l.push(e)})),l}},1741:(e,t,r)=>{"use strict";r.r(t),r.d(t,{servers:()=>c,isSwagger2:()=>u});var n=r(6814),s=r(5572),a=r(7779);const o=e=>e||(0,s.Map)(),l=(0,n.createSelector)(o,(e=>e.get("json",(0,s.Map)()))),i=(0,n.createSelector)(o,(e=>e.get("resolved",(0,s.Map)()))),c=(p=(0,n.createSelector)((e=>{let t=i(e);return t.count()<1&&(t=l(e)),t}),(e=>e.getIn(["servers"])||(0,s.Map)())),()=>function(e){const t=e.getSystem().specSelectors.specJson();if((0,a.isOAS3)(t)){for(var r=arguments.length,n=new Array(r>1?r-1:0),s=1;s<r;s++)n[s-1]=arguments[s];return p(...n)}return null});var p;const u=(e,t)=>()=>{const e=t.getSystem().specSelectors.specJson();return(0,a.isSwagger2)(e)}},2044:(e,t,r)=>{"use strict";r.r(t),r.d(t,{definitions:()=>m,hasHost:()=>h,securityDefinitions:()=>g,host:()=>f,basePath:()=>y,consumes:()=>v,produces:()=>E,schemes:()=>S,servers:()=>C,isOAS3:()=>b,isSwagger2:()=>x});var n=r(6814),s=r(3881),a=r(5572),o=r(7779);function l(e){return(t,r)=>function(){const n=r.getSystem().specSelectors.specJson();return(0,o.isOAS3)(n)?e(...arguments):t(...arguments)}}const i=e=>e||(0,a.Map)(),c=l((0,n.createSelector)((()=>null))),p=(0,n.createSelector)(i,(e=>e.get("json",(0,a.Map)()))),u=(0,n.createSelector)(i,(e=>e.get("resolved",(0,a.Map)()))),d=e=>{let t=u(e);return t.count()<1&&(t=p(e)),t},m=l((0,n.createSelector)(d,(e=>{const t=e.getIn(["components","schemas"]);return a.Map.isMap(t)?t:(0,a.Map)()}))),h=l((e=>d(e).hasIn(["servers",0]))),g=l((0,n.createSelector)(s.specJsonWithResolvedSubtrees,(e=>e.getIn(["components","securitySchemes"])||null))),f=c,y=c,v=c,E=c,S=c,C=l((0,n.createSelector)(d,(e=>e.getIn(["servers"])||(0,a.Map)()))),b=(e,t)=>()=>{const e=t.getSystem().specSelectors.specJson();return(0,o.isOAS3)(a.Map.isMap(e)?e:(0,a.Map)())},x=(e,t)=>()=>{const e=t.getSystem().specSelectors.specJson();return(0,o.isSwagger2)(a.Map.isMap(e)?e:(0,a.Map)())}},356:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(6689),s=r.n(n);const a=(0,r(7779).OAS3ComponentWrapFactory)((e=>{let{Ori:t,...r}=e;const{schema:n,getComponent:a,errSelectors:o,authorized:l,onAuthChange:i,name:c}=r,p=a("HttpAuth");return"http"===n.get("type")?s().createElement(p,{key:c,schema:n,name:c,errSelectors:o,authorized:l,getComponent:a,onChange:i}):s().createElement(t,r)}))},7761:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var n=r(2460),s=r(356),a=r(9487),o=r(58),l=r(3499),i=r(287);const c={Markdown:n.default,AuthItem:s.default,JsonSchema_string:i.default,VersionStamp:a.default,model:l.default,onlineValidatorBadge:o.default}},287:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(6689),s=r.n(n);const a=(0,r(7779).OAS3ComponentWrapFactory)((e=>{let{Ori:t,...r}=e;const{schema:n,getComponent:a,errors:o,onChange:l}=r,i=n&&n.get?n.get("format"):null,c=n&&n.get?n.get("type"):null,p=a("Input");return c&&"string"===c&&i&&("binary"===i||"base64"===i)?s().createElement(p,{type:"file",className:o.length?"invalid":"",title:o.length?o:"",onChange:e=>{l(e.target.files[0])},disabled:t.isDisabled}):s().createElement(t,r)}))},2460:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Markdown:()=>m,default:()=>h});var n=r(7390),s=r.n(n),a=r(6689),o=r.n(a),l=(r(580),r(9003)),i=r.n(l),c=r(963),p=r(7779),u=r(2552);const d=new c.Remarkable("commonmark");d.block.ruler.enable(["table"]),d.set({linkTarget:"_blank"});const m=e=>{let{source:t,className:r="",getConfigs:n}=e;if("string"!=typeof t)return null;if(t){const{useUnsafeMarkdown:e}=n(),a=d.render(t),l=(0,u.s)(a,{useUnsafeMarkdown:e});let c;return"string"==typeof l&&(c=s()(l).call(l)),o().createElement("div",{dangerouslySetInnerHTML:{__html:c},className:i()(r,"renderedMarkdown")})}return null};m.defaultProps={getConfigs:()=>({useUnsafeMarkdown:!1})};const h=(0,p.OAS3ComponentWrapFactory)(m)},3499:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var n=r(4250),s=r.n(n),a=r(6689),o=r.n(a),l=(r(580),r(7779)),i=r(6024);class c extends a.Component{render(){let{getConfigs:e,schema:t}=this.props,r=["model-box"],n=null;return!0===t.get("deprecated")&&(r.push("deprecated"),n=o().createElement("span",{className:"model-deprecated-warning"},"Deprecated:")),o().createElement("div",{className:r.join(" ")},n,o().createElement(i.Z,s()({},this.props,{getConfigs:e,depth:1,expandDepth:this.props.expandDepth||0})))}}const p=(0,l.OAS3ComponentWrapFactory)(c)},58:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(7779),s=r(5623);const a=(0,n.OAS3ComponentWrapFactory)(s.Z)},9487:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(6689),s=r.n(n);const a=(0,r(7779).OAS3ComponentWrapFactory)((e=>{const{Ori:t}=e;return s().createElement("span",null,s().createElement(t,e),s().createElement("small",{className:"version-stamp"},s().createElement("pre",{className:"version"},"OAS3")))}))},8560:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(9300),s=r.n(n);let a=!1;function o(){return{statePlugins:{spec:{wrapActions:{updateSpec:e=>function(){return a=!0,e(...arguments)},updateJsonSpec:(e,t)=>function(){const r=t.getConfigs().onComplete;return a&&"function"==typeof r&&(s()(r,0),a=!1),e(...arguments)}}}}}}},8223:(e,t,r)=>{"use strict";r.r(t),r.d(t,{requestSnippetGenerator_curl_bash:()=>A,requestSnippetGenerator_curl_cmd:()=>I,requestSnippetGenerator_curl_powershell:()=>_});var n=r(8493),s=r.n(n),a=r(7390),o=r.n(a),l=r(8344),i=r.n(l),c=r(3942),p=r.n(c);const u=require("@babel/runtime-corejs3/core-js-stable/instance/repeat");var d=r.n(u),m=r(7862),h=r.n(m),g=r(2605),f=r.n(g),y=r(7504),v=r(5572);const E=e=>{var t;const r="_**[]";return s()(e).call(e,r)<0?e:o()(t=e.split(r)[0]).call(t)},S=e=>"-d "===e||/^[_\/-]/g.test(e)?e:"'"+e.replace(/'/g,"'\\''")+"'",C=e=>"-d "===(e=e.replace(/\^/g,"^^").replace(/\\"/g,'\\\\"').replace(/"/g,'""').replace(/\n/g,"^\n"))?e.replace(/-d /g,"-d ^\n"):/^[_\/-]/g.test(e)?e:'"'+e+'"',b=e=>"-d "===e?e:/\n/.test(e)?'@"\n'+e.replace(/"/g,'\\"').replace(/`/g,"``").replace(/\$/,"`$")+'\n"@':/^[_\/-]/g.test(e)?e:"'"+e.replace(/"/g,'""').replace(/'/g,"''")+"'";function x(e){let t=[];for(let[r,n]of e.get("body").entrySeq()){let e=E(r);n instanceof y.Z.File?t.push(`  "${e}": {\n    "name": "${n.name}"${n.type?`,\n    "type": "${n.type}"`:""}\n  }`):t.push(`  "${e}": ${i()(n,null,2).replace(/(\r\n|\r|\n)/g,"\n  ")}`)}return`{\n${t.join(",\n")}\n}`}const w=function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",s=!1,a="";const o=function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return a+=" "+p()(r).call(r,t).join(" ")},l=function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return a+=p()(r).call(r,t).join(" ")},c=()=>a+=` ${r}`,u=function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return a+=d()(e="  ").call(e,t)};let m=e.get("headers");if(a+="curl"+n,e.has("curlOptions")&&o(...e.get("curlOptions")),o("-X",e.get("method")),c(),u(),l(`${e.get("url")}`),m&&m.size)for(let t of h()(g=e.get("headers")).call(g)){var g;c(),u();let[e,r]=t;l("-H",`${e}: ${r}`),s=s||/^content-type$/i.test(e)&&/^multipart\/form-data$/i.test(r)}const S=e.get("body");var C;if(S)if(s&&f()(C=["POST","PUT","PATCH"]).call(C,e.get("method")))for(let[e,t]of S.entrySeq()){let r=E(e);c(),u(),l("-F"),t instanceof y.Z.File?o(`${r}=@${t.name}${t.type?`;type=${t.type}`:""}`):o(`${r}=${t}`)}else if(S instanceof y.Z.File)c(),u(),l(`--data-binary '@${S.name}'`);else{c(),u(),l("-d ");let t=S;v.Map.isMap(t)?l(x(e)):("string"!=typeof t&&(t=i()(t)),l(t))}else S||"POST"!==e.get("method")||(c(),u(),l("-d ''"));return a},_=e=>w(e,b,"`\n",".exe"),A=e=>w(e,S,"\\\n"),I=e=>w(e,C,"^\n")},6575:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(8223),s=r(4669),a=r(4206);const o=()=>({components:{RequestSnippets:a.default},fn:n,statePlugins:{requestSnippets:{selectors:s}}})},4206:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var n=r(9998),s=r.n(n),a=r(1733),o=r.n(a),l=r(4235),i=r.n(l),c=r(3942),p=r.n(c),u=r(6689),d=r.n(u),m=(r(580),r(1712)),h=r.n(m),g=r(5716),f=r.n(g),y=r(2807),v=r(6068);const E={cursor:"pointer",lineHeight:1,display:"inline-flex",backgroundColor:"rgb(250, 250, 250)",paddingBottom:"0",paddingTop:"0",border:"1px solid rgb(51, 51, 51)",borderRadius:"4px 4px 0 0",boxShadow:"none",borderBottom:"none"},S={cursor:"pointer",lineHeight:1,display:"inline-flex",backgroundColor:"rgb(51, 51, 51)",boxShadow:"none",border:"1px solid rgb(51, 51, 51)",paddingBottom:"0",paddingTop:"0",borderRadius:"4px 4px 0 0",marginTop:"-5px",marginRight:"-5px",marginLeft:"-5px",zIndex:"9999",borderBottom:"none"},C=e=>{var t,r;let{request:n,requestSnippetsSelectors:a,getConfigs:l}=e;const c=f()(l)?l():null,m=!1!==h()(c,"syntaxHighlight")&&h()(c,"syntaxHighlight.activated",!0),g=(0,u.useRef)(null),[C,b]=(0,u.useState)(null===(t=a.getSnippetGenerators())||void 0===t?void 0:t.keySeq().first()),[x,w]=(0,u.useState)(null==a?void 0:a.getDefaultExpanded());(0,u.useEffect)((()=>{}),[]),(0,u.useEffect)((()=>{var e;const t=s()(e=o()(g.current.childNodes)).call(e,(e=>{var t;return!!e.nodeType&&(null===(t=e.classList)||void 0===t?void 0:t.contains("curl-command"))}));return i()(t).call(t,(e=>e.addEventListener("mousewheel",R,{passive:!1}))),()=>{i()(t).call(t,(e=>e.removeEventListener("mousewheel",R)))}}),[n]);const _=a.getSnippetGenerators(),A=_.get(C),I=A.get("fn")(n),N=()=>{w(!x)},q=e=>e===C?S:E,R=e=>{const{target:t,deltaY:r}=e,{scrollHeight:n,offsetHeight:s,scrollTop:a}=t;n>s&&(0===a&&r<0||s+a>=n&&r>0)&&e.preventDefault()},T=m?d().createElement(v.d3,{language:A.get("syntax"),className:"curl microlight",style:(0,v.C2)(h()(c,"syntaxHighlight.theme"))},I):d().createElement("textarea",{readOnly:!0,className:"curl",value:I});return d().createElement("div",{className:"request-snippets",ref:g},d().createElement("div",{style:{width:"100%",display:"flex",justifyContent:"flex-start",alignItems:"center",marginBottom:"15px"}},d().createElement("h4",{onClick:()=>N(),style:{cursor:"pointer"}},"Snippets"),d().createElement("button",{onClick:()=>N(),style:{border:"none",background:"none"},title:x?"Collapse operation":"Expand operation"},d().createElement("svg",{className:"arrow",width:"10",height:"10"},d().createElement("use",{href:x?"#large-arrow-down":"#large-arrow",xlinkHref:x?"#large-arrow-down":"#large-arrow"})))),x&&d().createElement("div",{className:"curl-command"},d().createElement("div",{style:{paddingLeft:"15px",paddingRight:"10px",width:"100%",display:"flex"}},p()(r=_.entrySeq()).call(r,(e=>{let[t,r]=e;return d().createElement("div",{style:q(t),className:"btn",key:t,onClick:()=>(e=>{C!==e&&b(e)})(t)},d().createElement("h4",{style:t===C?{color:"white"}:{}},r.get("title")))}))),d().createElement("div",{className:"copy-to-clipboard"},d().createElement(y.CopyToClipboard,{text:I},d().createElement("button",null))),d().createElement("div",null,T)))}},4669:(e,t,r)=>{"use strict";r.r(t),r.d(t,{getGenerators:()=>d,getSnippetGenerators:()=>m,getActiveLanguage:()=>h,getDefaultExpanded:()=>g});var n=r(9998),s=r.n(n),a=r(2605),o=r.n(a),l=r(3942),i=r.n(l),c=r(6814),p=r(5572);const u=e=>e||(0,p.Map)(),d=(0,c.createSelector)(u,(e=>{const t=e.get("languages"),r=e.get("generators",(0,p.Map)());return!t||t.isEmpty()?r:s()(r).call(r,((e,r)=>o()(t).call(t,r)))})),m=e=>t=>{var r,n;let{fn:a}=t;return s()(r=i()(n=d(e)).call(n,((e,t)=>{const r=(e=>a[`requestSnippetGenerator_${e}`])(t);return"function"!=typeof r?null:e.set("fn",r)}))).call(r,(e=>e))},h=(0,c.createSelector)(u,(e=>e.get("activeLanguage"))),g=(0,c.createSelector)(u,(e=>e.get("defaultExpanded")))},6195:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ErrorBoundary:()=>l,default:()=>i});r(580);var n=r(6689),s=r.n(n),a=r(6189),o=r(9403);class l extends n.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}constructor(){super(...arguments),this.state={hasError:!1,error:null}}componentDidCatch(e,t){this.props.fn.componentDidCatch(e,t)}render(){const{getComponent:e,targetName:t,children:r}=this.props;if(this.state.hasError){const r=e("Fallback");return s().createElement(r,{name:t})}return r}}l.defaultProps={targetName:"this component",getComponent:()=>o.default,fn:{componentDidCatch:a.componentDidCatch},children:null};const i=l},9403:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(6689),s=r.n(n);r(580);const a=e=>{let{name:t}=e;return s().createElement("div",{className:"fallback"},"😱 ",s().createElement("i",null,"Could not render ","t"===t?"this component":t,", see the console."))}},6189:(e,t,r)=>{"use strict";r.r(t),r.d(t,{componentDidCatch:()=>l,withErrorBoundary:()=>i});var n=r(4250),s=r.n(n),a=r(6689),o=r.n(a);const l=console.error,i=e=>t=>{const{getComponent:r,fn:n}=e(),l=r("ErrorBoundary"),i=n.getDisplayName(t);class c extends a.Component{render(){return o().createElement(l,{targetName:i,getComponent:r,fn:n},o().createElement(t,s()({},this.props,this.context)))}}var p;return c.displayName=`WithErrorBoundary(${i})`,(p=t).prototype&&p.prototype.isReactComponent&&(c.prototype.mapStateToProps=t.prototype.mapStateToProps),c}},9595:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});const n=require("@babel/runtime-corejs3/core-js-stable/instance/fill");var s=r.n(n);const a=require("lodash/zipObject");var o=r.n(a),l=r(6195),i=r(9403),c=r(6189);const p=function(){let{componentList:e=[],fullOverride:t=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return r=>{var n;let{getSystem:a}=r;const p=t?e:["App","BaseLayout","VersionPragmaFilter","InfoContainer","ServersContainer","SchemesContainer","AuthorizeBtnContainer","FilterContainer","Operations","OperationContainer","parameters","responses","OperationServers","Models","ModelWrapper",...e],u=o()(p,s()(n=Array(p.length)).call(n,((e,t)=>{let{fn:r}=t;return r.withErrorBoundary(e)})));return{fn:{componentDidCatch:c.componentDidCatch,withErrorBoundary:(0,c.withErrorBoundary)(a)},components:{ErrorBoundary:l.default,Fallback:i.default},wrapComponents:u}}}},4128:(e,t,r)=>{"use strict";r.r(t),r.d(t,{createXMLExample:()=>z,inferSchema:()=>U,memoizedCreateXMLExample:()=>J,memoizedSampleFromSchema:()=>F,sampleFromSchema:()=>B,sampleFromSchemaGeneric:()=>L});var n=r(8493),s=r.n(n),a=r(4235),o=r.n(a),l=r(7104),i=r.n(l),c=r(2605),p=r.n(c),u=r(5626),d=r.n(u),m=r(600),h=r.n(m),g=r(3580),f=r.n(g),y=r(4883),v=r.n(y),E=r(3942),S=r.n(E),C=r(8344),b=r.n(C);const x=require("xml");var w=r.n(x);const _=require("randexp");var A=r.n(_);const I=require("lodash/isEmpty");var N=r.n(I),q=r(1890),R=r(7481);const T={string:e=>e.pattern?(e=>{try{return new(A())(e).gen()}catch(e){return"string"}})(e.pattern):"string",string_email:()=>"<EMAIL>","string_date-time":()=>(new Date).toISOString(),string_date:()=>(new Date).toISOString().substring(0,10),string_uuid:()=>"3fa85f64-5717-4562-b3fc-2c963f66afa6",string_hostname:()=>"example.com",string_ipv4:()=>"*************",string_ipv6:()=>"2001:0db8:5b96:0000:0000:426f:8e17:642a",number:()=>0,number_float:()=>0,integer:()=>0,boolean:e=>"boolean"!=typeof e.default||e.default},P=e=>{e=(0,q.mz)(e);let{type:t,format:r}=e,n=T[`${t}_${r}`]||T[t];return(0,q.Wl)(n)?n(e):"Unknown Type: "+e.type},k=e=>(0,q.XV)(e,"$$ref",(e=>"string"==typeof e&&s()(e).call(e,"#")>-1)),O=["maxProperties","minProperties"],M=["minItems","maxItems"],j=["minimum","maximum","exclusiveMinimum","exclusiveMaximum"],V=["minLength","maxLength"],D=function(e,t){var r;let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const a=r=>{void 0===t[r]&&void 0!==e[r]&&(t[r]=e[r])};var l;(o()(r=["example","default","enum","xml","type",...O,...M,...j,...V]).call(r,(e=>a(e))),void 0!==e.required&&i()(e.required))&&(void 0!==t.required&&t.required.length||(t.required=[]),o()(l=e.required).call(l,(e=>{var r;p()(r=t.required).call(r,e)||t.required.push(e)})));if(e.properties){t.properties||(t.properties={});let r=(0,q.mz)(e.properties);for(let a in r){var c;if(Object.prototype.hasOwnProperty.call(r,a))if(!r[a]||!r[a].deprecated)if(!r[a]||!r[a].readOnly||n.includeReadOnly)if(!r[a]||!r[a].writeOnly||n.includeWriteOnly)if(!t.properties[a])t.properties[a]=r[a],!e.required&&i()(e.required)&&-1!==s()(c=e.required).call(c,a)&&(t.required?t.required.push(a):t.required=[a])}}return e.items&&(t.items||(t.items={}),t.items=D(e.items,t.items,n)),t},L=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];e&&(0,q.Wl)(e.toJS)&&(e=e.toJS());let a=void 0!==r||e&&void 0!==e.example||e&&void 0!==e.default;const l=!a&&e&&e.oneOf&&e.oneOf.length>0,c=!a&&e&&e.anyOf&&e.anyOf.length>0;if(!a&&(l||c)){const r=(0,q.mz)(l?e.oneOf[0]:e.anyOf[0]);if(D(r,e,t),!e.xml&&r.xml&&(e.xml=r.xml),void 0!==e.example&&void 0!==r.example)a=!0;else if(r.properties){e.properties||(e.properties={});let n=(0,q.mz)(r.properties);for(let a in n){var u;if(Object.prototype.hasOwnProperty.call(n,a))if(!n[a]||!n[a].deprecated)if(!n[a]||!n[a].readOnly||t.includeReadOnly)if(!n[a]||!n[a].writeOnly||t.includeWriteOnly)if(!e.properties[a])e.properties[a]=n[a],!r.required&&i()(r.required)&&-1!==s()(u=r.required).call(u,a)&&(e.required?e.required.push(a):e.required=[a])}}}const m={};let{xml:g,type:y,example:E,properties:C,additionalProperties:b,items:x}=e||{},{includeReadOnly:w,includeWriteOnly:_}=t;g=g||{};let A,{name:I,prefix:R,namespace:T}=g,V={};if(n&&(I=I||"notagname",A=(R?R+":":"")+I,T)){m[R?"xmlns:"+R:"xmlns"]=T}n&&(V[A]=[]);const U=t=>d()(t).call(t,(t=>Object.prototype.hasOwnProperty.call(e,t)));e&&!y&&(C||b||U(O)?y="object":x||U(M)?y="array":U(j)?(y="number",e.type="number"):a||e.enum||(y="string",e.type="string"));const z=t=>{var r,n,s,a,o;null!==(null===(r=e)||void 0===r?void 0:r.maxItems)&&void 0!==(null===(n=e)||void 0===n?void 0:n.maxItems)&&(t=h()(t).call(t,0,null===(o=e)||void 0===o?void 0:o.maxItems));if(null!==(null===(s=e)||void 0===s?void 0:s.minItems)&&void 0!==(null===(a=e)||void 0===a?void 0:a.minItems)){let r=0;for(;t.length<(null===(l=e)||void 0===l?void 0:l.minItems);){var l;t.push(t[r++%t.length])}}return t},B=(0,q.mz)(C);let $,J=0;const F=()=>e&&null!==e.maxProperties&&void 0!==e.maxProperties&&J>=e.maxProperties,W=()=>{if(!e||!e.required)return 0;let t=0;var r,s;n?o()(r=e.required).call(r,(e=>t+=void 0===V[e]?0:1)):o()(s=e.required).call(s,(e=>{var r;return t+=void 0===(null===(r=V[A])||void 0===r?void 0:f()(r).call(r,(t=>void 0!==t[e])))?0:1}));return e.required.length-t},H=t=>{var r;return!(e&&e.required&&e.required.length)||!p()(r=e.required).call(r,t)},K=t=>!e||null===e.maxProperties||void 0===e.maxProperties||!F()&&(!H(t)||e.maxProperties-J-W()>0);if($=n?function(r){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;if(e&&B[r]){if(B[r].xml=B[r].xml||{},B[r].xml.attribute){const e=i()(B[r].enum)?B[r].enum[0]:void 0,t=B[r].example,n=B[r].default;return void(m[B[r].xml.name||r]=void 0!==t?t:void 0!==n?n:void 0!==e?e:P(B[r]))}B[r].xml.name=B[r].xml.name||r}else B[r]||!1===b||(B[r]={xml:{name:r}});let a=L(e&&B[r]||void 0,t,s,n);var o;K(r)&&(J++,i()(a)?V[A]=v()(o=V[A]).call(o,a):V[A].push(a))}:(r,s)=>{if(K(r)){if(Object.prototype.hasOwnProperty.call(e,"discriminator")&&e.discriminator&&Object.prototype.hasOwnProperty.call(e.discriminator,"mapping")&&e.discriminator.mapping&&Object.prototype.hasOwnProperty.call(e,"$$ref")&&e.$$ref&&e.discriminator.propertyName===r){for(let t in e.discriminator.mapping)if(-1!==e.$$ref.search(e.discriminator.mapping[t])){V[r]=t;break}}else V[r]=L(B[r],t,s,n);J++}},a){let s;if(s=k(void 0!==r?r:void 0!==E?E:e.default),!n){if("number"==typeof s&&"string"===y)return`${s}`;if("string"!=typeof s||"string"===y)return s;try{return JSON.parse(s)}catch(e){return s}}if(e||(y=i()(s)?"array":typeof s),"array"===y){if(!i()(s)){if("string"==typeof s)return s;s=[s]}const r=e?e.items:void 0;r&&(r.xml=r.xml||g||{},r.xml.name=r.xml.name||g.name);let a=S()(s).call(s,(e=>L(r,t,e,n)));return a=z(a),g.wrapped?(V[A]=a,N()(m)||V[A].push({_attr:m})):V=a,V}if("object"===y){if("string"==typeof s)return s;for(let t in s)Object.prototype.hasOwnProperty.call(s,t)&&(e&&B[t]&&B[t].readOnly&&!w||e&&B[t]&&B[t].writeOnly&&!_||(e&&B[t]&&B[t].xml&&B[t].xml.attribute?m[B[t].xml.name||t]=s[t]:$(t,s[t])));return N()(m)||V[A].push({_attr:m}),V}return V[A]=N()(m)?s:[{_attr:m},s],V}if("object"===y){for(let e in B)Object.prototype.hasOwnProperty.call(B,e)&&(B[e]&&B[e].deprecated||B[e]&&B[e].readOnly&&!w||B[e]&&B[e].writeOnly&&!_||$(e));if(n&&m&&V[A].push({_attr:m}),F())return V;if(!0===b)n?V[A].push({additionalProp:"Anything can be here"}):V.additionalProp1={},J++;else if(b){const r=(0,q.mz)(b),s=L(r,t,void 0,n);if(n&&r.xml&&r.xml.name&&"notagname"!==r.xml.name)V[A].push(s);else{const t=null!==e.minProperties&&void 0!==e.minProperties&&J<e.minProperties?e.minProperties-J:3;for(let e=1;e<=t;e++){if(F())return V;if(n){const t={};t["additionalProp"+e]=s.notagname,V[A].push(t)}else V["additionalProp"+e]=s;J++}}}return V}if("array"===y){if(!x)return;let r;var Z,G;if(n)x.xml=x.xml||(null===(Z=e)||void 0===Z?void 0:Z.xml)||{},x.xml.name=x.xml.name||g.name;if(i()(x.anyOf))r=S()(G=x.anyOf).call(G,(e=>L(D(x,e,t),t,void 0,n)));else if(i()(x.oneOf)){var Y;r=S()(Y=x.oneOf).call(Y,(e=>L(D(x,e,t),t,void 0,n)))}else{if(!(!n||n&&g.wrapped))return L(x,t,void 0,n);r=[L(x,t,void 0,n)]}return r=z(r),n&&g.wrapped?(V[A]=r,N()(m)||V[A].push({_attr:m}),V):r}let X;if(e&&i()(e.enum))X=(0,q.AF)(e.enum)[0];else{if(!e)return;if(X=P(e),"number"==typeof X){let t=e.minimum;null!=t&&(e.exclusiveMinimum&&t++,X=t);let r=e.maximum;null!=r&&(e.exclusiveMaximum&&r--,X=r)}if("string"==typeof X&&(null!==e.maxLength&&void 0!==e.maxLength&&(X=h()(X).call(X,0,e.maxLength)),null!==e.minLength&&void 0!==e.minLength)){let t=0;for(;X.length<e.minLength;)X+=X[t++%X.length]}}if("file"!==y)return n?(V[A]=N()(m)?X:[{_attr:m},X],V):X},U=e=>(e.schema&&(e=e.schema),e.properties&&(e.type="object"),e),z=(e,t,r)=>{const n=L(e,t,r,!0);if(n)return"string"==typeof n?n:w()(n,{declaration:!0,indent:"\t"})},B=(e,t,r)=>L(e,t,r,!1),$=(e,t,r)=>[e,b()(t),b()(r)],J=(0,R.Z)(z,$),F=(0,R.Z)(B,$)},8883:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(4128);function s(){return{fn:n}}},9381:(e,t,r)=>{"use strict";r.r(t),r.d(t,{CLEAR_REQUEST:()=>Q,CLEAR_RESPONSE:()=>X,CLEAR_VALIDATE_PARAMS:()=>ee,LOG_REQUEST:()=>Y,SET_MUTATED_REQUEST:()=>G,SET_REQUEST:()=>Z,SET_RESPONSE:()=>K,SET_SCHEME:()=>se,UPDATE_EMPTY_PARAM_INCLUSION:()=>W,UPDATE_JSON:()=>J,UPDATE_OPERATION_META_VALUE:()=>te,UPDATE_PARAM:()=>F,UPDATE_RESOLVED:()=>re,UPDATE_RESOLVED_SUBTREE:()=>ne,UPDATE_SPEC:()=>B,UPDATE_URL:()=>$,VALIDATE_PARAMS:()=>H,changeConsumesValue:()=>be,changeParam:()=>ge,changeParamByIdentity:()=>fe,changeProducesValue:()=>xe,clearRequest:()=>Te,clearResponse:()=>Re,clearValidateParams:()=>Ce,execute:()=>qe,executeRequest:()=>Ne,invalidateResolvedSubtreeCache:()=>ve,logRequest:()=>Ie,parseToJson:()=>ce,requestResolvedSubtree:()=>he,resolveSpec:()=>ue,setMutatedRequest:()=>Ae,setRequest:()=>_e,setResponse:()=>we,setScheme:()=>Pe,updateEmptyParamInclusion:()=>Se,updateJsonSpec:()=>ie,updateResolved:()=>oe,updateResolvedSubtree:()=>ye,updateSpec:()=>ae,updateUrl:()=>le,validateParams:()=>Ee});var n=r(7104),s=r.n(n),a=r(3942),o=r.n(a);const l=require("@babel/runtime-corejs3/core-js-stable/object/define-property");var i=r.n(l),c=r(66),p=r.n(c),u=r(7834),d=r.n(u);const m=require("@babel/runtime-corejs3/core-js-stable/promise");var h=r.n(m),g=r(9998),f=r.n(g),y=r(9968),v=r.n(y),E=r(8493),S=r.n(E),C=r(4235),b=r.n(C),x=r(7252),w=r.n(x),_=r(4994),A=r.n(_);const I=require("@babel/runtime-corejs3/core-js-stable/date/now");var N=r.n(I),q=r(9793),R=r.n(q),T=r(5572),P=r(3883),k=r.n(P),O=r(41);const M=require("lodash/isString");var j=r.n(M);const V=require("lodash/debounce");var D=r.n(V);const L=require("lodash/set");var U=r.n(L),z=r(1890);const B="spec_update_spec",$="spec_update_url",J="spec_update_json",F="spec_update_param",W="spec_update_empty_param_inclusion",H="spec_validate_param",K="spec_set_response",Z="spec_set_request",G="spec_set_mutated_request",Y="spec_log_request",X="spec_clear_response",Q="spec_clear_request",ee="spec_clear_validate_param",te="spec_update_operation_meta_value",re="spec_update_resolved",ne="spec_update_resolved_subtree",se="set_scheme";function ae(e){const t=(r=e,j()(r)?r:"").replace(/\t/g,"  ");var r;if("string"==typeof e)return{type:B,payload:t}}function oe(e){return{type:re,payload:e}}function le(e){return{type:$,payload:e}}function ie(e){return{type:J,payload:e}}const ce=e=>t=>{let{specActions:r,specSelectors:n,errActions:s}=t,{specStr:a}=n,o=null;try{e=e||a(),s.clear({source:"parser"}),o=R().load(e,{schema:q.JSON_SCHEMA})}catch(e){return console.error(e),s.newSpecErr({source:"parser",level:"error",message:e.reason,line:e.mark&&e.mark.line?e.mark.line+1:void 0})}return o&&"object"==typeof o?r.updateJsonSpec(o):{}};let pe=!1;const ue=(e,t)=>r=>{let{specActions:n,specSelectors:a,errActions:l,fn:{fetch:c,resolve:p,AST:u={}},getConfigs:d}=r;pe||(console.warn("specActions.resolveSpec is deprecated since v3.10.0 and will be removed in v4.0.0; use requestResolvedSubtree instead!"),pe=!0);const{modelPropertyMacro:m,parameterMacro:h,requestInterceptor:g,responseInterceptor:f}=d();void 0===e&&(e=a.specJson()),void 0===t&&(t=a.url());let y=u.getLineNumberForPath?u.getLineNumberForPath:()=>{},v=a.specStr();return p({fetch:c,spec:e,baseDoc:t,modelPropertyMacro:m,parameterMacro:h,requestInterceptor:g,responseInterceptor:f}).then((e=>{let{spec:t,errors:r}=e;if(l.clear({type:"thrown"}),s()(r)&&r.length>0){let e=o()(r).call(r,(e=>(console.error(e),e.line=e.fullPath?y(v,e.fullPath):null,e.path=e.fullPath?e.fullPath.join("."):null,e.level="error",e.type="thrown",e.source="resolver",i()(e,"message",{enumerable:!0,value:e.message}),e)));l.newThrownErrBatch(e)}return n.updateResolved(t)}))};let de=[];const me=D()((async()=>{const e=de.system;if(!e)return void console.error("debResolveSubtrees: don't have a system to operate on, aborting.");const{errActions:t,errSelectors:r,fn:{resolveSubtree:n,fetch:a,AST:l={}},specSelectors:c,specActions:u}=e;if(!n)return void console.error("Error: Swagger-Client did not provide a `resolveSubtree` method, doing nothing.");let m=l.getLineNumberForPath?l.getLineNumberForPath:()=>{};const g=c.specStr(),{modelPropertyMacro:y,parameterMacro:E,requestInterceptor:S,responseInterceptor:C}=e.getConfigs();try{var b=await p()(de).call(de,(async(e,l)=>{const{resultMap:p,specWithCurrentSubtrees:u}=await e,{errors:b,spec:x}=await n(u,l,{baseDoc:c.url(),modelPropertyMacro:y,parameterMacro:E,requestInterceptor:S,responseInterceptor:C});if(r.allErrors().size&&t.clearBy((e=>{var t;return"thrown"!==e.get("type")||"resolver"!==e.get("source")||!d()(t=e.get("fullPath")).call(t,((e,t)=>e===l[t]||void 0===l[t]))})),s()(b)&&b.length>0){let e=o()(b).call(b,(e=>(e.line=e.fullPath?m(g,e.fullPath):null,e.path=e.fullPath?e.fullPath.join("."):null,e.level="error",e.type="thrown",e.source="resolver",i()(e,"message",{enumerable:!0,value:e.message}),e)));t.newThrownErrBatch(e)}var w,_;x&&c.isOAS3()&&"components"===l[0]&&"securitySchemes"===l[1]&&await h().all(o()(w=f()(_=v()(x)).call(_,(e=>"openIdConnect"===e.type))).call(w,(async e=>{const t={url:e.openIdConnectUrl,requestInterceptor:S,responseInterceptor:C};try{const r=await a(t);r instanceof Error||r.status>=400?console.error(r.statusText+" "+t.url):e.openIdConnectData=JSON.parse(r.text)}catch(e){console.error(e)}})));return U()(p,l,x),U()(u,l,x),{resultMap:p,specWithCurrentSubtrees:u}}),h().resolve({resultMap:(c.specResolvedSubtree([])||(0,T.Map)()).toJS(),specWithCurrentSubtrees:c.specJson().toJS()}));delete de.system,de=[]}catch(e){console.error(e)}u.updateResolvedSubtree([],b.resultMap)}),35),he=e=>t=>{var r;S()(r=o()(de).call(de,(e=>e.join("@@")))).call(r,e.join("@@"))>-1||(de.push(e),de.system=t,me())};function ge(e,t,r,n,s){return{type:F,payload:{path:e,value:n,paramName:t,paramIn:r,isXml:s}}}function fe(e,t,r,n){return{type:F,payload:{path:e,param:t,value:r,isXml:n}}}const ye=(e,t)=>({type:ne,payload:{path:e,value:t}}),ve=()=>({type:ne,payload:{path:[],value:(0,T.Map)()}}),Ee=(e,t)=>({type:H,payload:{pathMethod:e,isOAS3:t}}),Se=(e,t,r,n)=>({type:W,payload:{pathMethod:e,paramName:t,paramIn:r,includeEmptyValue:n}});function Ce(e){return{type:ee,payload:{pathMethod:e}}}function be(e,t){return{type:te,payload:{path:e,value:t,key:"consumes_value"}}}function xe(e,t){return{type:te,payload:{path:e,value:t,key:"produces_value"}}}const we=(e,t,r)=>({payload:{path:e,method:t,res:r},type:K}),_e=(e,t,r)=>({payload:{path:e,method:t,req:r},type:Z}),Ae=(e,t,r)=>({payload:{path:e,method:t,req:r},type:G}),Ie=e=>({payload:e,type:Y}),Ne=e=>t=>{let{fn:r,specActions:n,specSelectors:a,getConfigs:l,oas3Selectors:i}=t,{pathName:c,method:p,operation:u}=e,{requestInterceptor:d,responseInterceptor:m}=l(),h=u.toJS();var g,y;u&&u.get("parameters")&&b()(g=f()(y=u.get("parameters")).call(y,(e=>e&&!0===e.get("allowEmptyValue")))).call(g,(t=>{if(a.parameterInclusionSettingFor([c,p],t.get("name"),t.get("in"))){e.parameters=e.parameters||{};const r=(0,z.cz)(t,e.parameters);(!r||r&&0===r.size)&&(e.parameters[t.get("name")]="")}}));if(e.contextUrl=k()(a.url()).toString(),h&&h.operationId?e.operationId=h.operationId:h&&c&&p&&(e.operationId=r.opId(h,c,p)),a.isOAS3()){const t=`${c}:${p}`;e.server=i.selectedServer(t)||i.selectedServer();const r=i.serverVariables({server:e.server,namespace:t}).toJS(),n=i.serverVariables({server:e.server}).toJS();e.serverVariables=w()(r).length?r:n,e.requestContentType=i.requestContentType(c,p),e.responseContentType=i.responseContentType(c,p)||"*/*";const a=i.requestBodyValue(c,p),l=i.requestBodyInclusionSetting(c,p);var v;if(a&&a.toJS)e.requestBody=f()(v=o()(a).call(a,(e=>T.Map.isMap(e)?e.get("value"):e))).call(v,((e,t)=>(s()(e)?0!==e.length:!(0,z.O2)(e))||l.get(t))).toJS();else e.requestBody=a}let E=A()({},e);E=r.buildRequest(E),n.setRequest(e.pathName,e.method,E);e.requestInterceptor=async t=>{let r=await d.apply(void 0,[t]),s=A()({},r);return n.setMutatedRequest(e.pathName,e.method,s),r},e.responseInterceptor=m;const S=N()();return r.execute(e).then((t=>{t.duration=N()()-S,n.setResponse(e.pathName,e.method,t)})).catch((t=>{"Failed to fetch"===t.message&&(t.name="",t.message='**Failed to fetch.**  \n**Possible Reasons:** \n  - CORS \n  - Network Failure \n  - URL scheme must be "http" or "https" for CORS request.'),n.setResponse(e.pathName,e.method,{error:!0,err:(0,O.serializeError)(t)})}))},qe=function(){let{path:e,method:t,...r}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n=>{let{fn:{fetch:s},specSelectors:a,specActions:o}=n,l=a.specJsonWithResolvedSubtrees().toJS(),i=a.operationScheme(e,t),{requestContentType:c,responseContentType:p}=a.contentTypeValues([e,t]).toJS(),u=/xml/i.test(c),d=a.parameterValues([e,t],u).toJS();return o.executeRequest({...r,fetch:s,spec:l,pathName:e,method:t,parameters:d,requestContentType:c,scheme:i,responseContentType:p})}};function Re(e,t){return{type:X,payload:{path:e,method:t}}}function Te(e,t){return{type:Q,payload:{path:e,method:t}}}function Pe(e,t,r){return{type:se,payload:{scheme:e,path:t,method:r}}}},7038:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(32),s=r(9381),a=r(3881),o=r(7508);function l(){return{statePlugins:{spec:{wrapActions:o,reducers:n.default,actions:s,selectors:a}}}}},32:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var n=r(66),s=r.n(n),a=r(3942),o=r.n(a),l=r(4994),i=r.n(l),c=r(5572),p=r(1890),u=r(7504),d=r(3881),m=r(9381);const h={[m.UPDATE_SPEC]:(e,t)=>"string"==typeof t.payload?e.set("spec",t.payload):e,[m.UPDATE_URL]:(e,t)=>e.set("url",t.payload+""),[m.UPDATE_JSON]:(e,t)=>e.set("json",(0,p.oG)(t.payload)),[m.UPDATE_RESOLVED]:(e,t)=>e.setIn(["resolved"],(0,p.oG)(t.payload)),[m.UPDATE_RESOLVED_SUBTREE]:(e,t)=>{const{value:r,path:n}=t.payload;return e.setIn(["resolvedSubtrees",...n],(0,p.oG)(r))},[m.UPDATE_PARAM]:(e,t)=>{let{payload:r}=t,{path:n,paramName:s,paramIn:a,param:o,value:l,isXml:i}=r,c=o?(0,p.V9)(o):`${a}.${s}`;const u=i?"value_xml":"value";return e.setIn(["meta","paths",...n,"parameters",c,u],l)},[m.UPDATE_EMPTY_PARAM_INCLUSION]:(e,t)=>{let{payload:r}=t,{pathMethod:n,paramName:s,paramIn:a,includeEmptyValue:o}=r;if(!s||!a)return console.warn("Warning: UPDATE_EMPTY_PARAM_INCLUSION could not generate a paramKey."),e;const l=`${a}.${s}`;return e.setIn(["meta","paths",...n,"parameter_inclusions",l],o)},[m.VALIDATE_PARAMS]:(e,t)=>{let{payload:{pathMethod:r,isOAS3:n}}=t;const a=(0,d.specJsonWithResolvedSubtrees)(e).getIn(["paths",...r]),o=(0,d.parameterValues)(e,r).toJS();return e.updateIn(["meta","paths",...r,"parameters"],(0,c.fromJS)({}),(t=>{var l;return s()(l=a.get("parameters",(0,c.List)())).call(l,((t,s)=>{const a=(0,p.cz)(s,o),l=(0,d.parameterInclusionSettingFor)(e,r,s.get("name"),s.get("in")),i=(0,p.Ik)(s,a,{bypassRequiredCheck:l,isOAS3:n});return t.setIn([(0,p.V9)(s),"errors"],(0,c.fromJS)(i))}),t)}))},[m.CLEAR_VALIDATE_PARAMS]:(e,t)=>{let{payload:{pathMethod:r}}=t;return e.updateIn(["meta","paths",...r,"parameters"],(0,c.fromJS)([]),(e=>o()(e).call(e,(e=>e.set("errors",(0,c.fromJS)([]))))))},[m.SET_RESPONSE]:(e,t)=>{let r,{payload:{res:n,path:s,method:a}}=t;r=n.error?i()({error:!0,name:n.err.name,message:n.err.message,statusCode:n.err.statusCode},n.err.response):n,r.headers=r.headers||{};let o=e.setIn(["responses",s,a],(0,p.oG)(r));return u.Z.Blob&&n.data instanceof u.Z.Blob&&(o=o.setIn(["responses",s,a,"text"],n.data)),o},[m.SET_REQUEST]:(e,t)=>{let{payload:{req:r,path:n,method:s}}=t;return e.setIn(["requests",n,s],(0,p.oG)(r))},[m.SET_MUTATED_REQUEST]:(e,t)=>{let{payload:{req:r,path:n,method:s}}=t;return e.setIn(["mutatedRequests",n,s],(0,p.oG)(r))},[m.UPDATE_OPERATION_META_VALUE]:(e,t)=>{let{payload:{path:r,value:n,key:s}}=t,a=["paths",...r],o=["meta","paths",...r];return e.getIn(["json",...a])||e.getIn(["resolved",...a])||e.getIn(["resolvedSubtrees",...a])?e.setIn([...o,s],(0,c.fromJS)(n)):e},[m.CLEAR_RESPONSE]:(e,t)=>{let{payload:{path:r,method:n}}=t;return e.deleteIn(["responses",r,n])},[m.CLEAR_REQUEST]:(e,t)=>{let{payload:{path:r,method:n}}=t;return e.deleteIn(["requests",r,n])},[m.SET_SCHEME]:(e,t)=>{let{payload:{scheme:r,path:n,method:s}}=t;return n&&s?e.setIn(["scheme",n,s],r):n||s?void 0:e.setIn(["scheme","_defaultScheme"],r)}}},3881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{lastError:()=>N,url:()=>q,specStr:()=>R,specSource:()=>T,specJson:()=>P,specResolved:()=>k,specResolvedSubtree:()=>O,specJsonWithResolvedSubtrees:()=>j,spec:()=>V,isOAS3:()=>D,info:()=>L,externalDocs:()=>U,version:()=>z,semver:()=>B,paths:()=>$,operations:()=>J,consumes:()=>F,produces:()=>W,security:()=>H,securityDefinitions:()=>K,findDefinition:()=>Z,definitions:()=>G,basePath:()=>Y,host:()=>X,schemes:()=>Q,operationsWithRootInherited:()=>ee,tags:()=>te,tagDetails:()=>re,operationsWithTags:()=>ne,taggedOperations:()=>se,responses:()=>ae,requests:()=>oe,mutatedRequests:()=>le,responseFor:()=>ie,requestFor:()=>ce,mutatedRequestFor:()=>pe,allowTryItOutFor:()=>ue,parameterWithMetaByIdentity:()=>de,parameterInclusionSettingFor:()=>me,parameterWithMeta:()=>he,operationWithMeta:()=>ge,getParameter:()=>fe,hasHost:()=>ye,parameterValues:()=>ve,parametersIncludeIn:()=>Ee,parametersIncludeType:()=>Se,contentTypeValues:()=>Ce,currentProducesFor:()=>be,producesOptionsFor:()=>xe,consumesOptionsFor:()=>we,operationScheme:()=>_e,canExecuteScheme:()=>Ae,validationErrors:()=>Ie,validateBeforeExecute:()=>Ne,getOAS3RequiredRequestBodyContentType:()=>qe,isMediaTypeSchemaPropertiesEqual:()=>Re});var n=r(600),s=r.n(n),a=r(4235),o=r.n(a),l=r(8493),i=r.n(l),c=r(3942),p=r.n(c),u=r(9998),d=r.n(u),m=r(3580),h=r.n(m),g=r(66),f=r.n(g),y=r(9247),v=r.n(y),E=r(5626),S=r.n(E),C=r(7104),b=r.n(C),x=r(6814),w=r(1890),_=r(5572);const A=["get","put","post","delete","options","head","patch","trace"],I=e=>e||(0,_.Map)(),N=(0,x.createSelector)(I,(e=>e.get("lastError"))),q=(0,x.createSelector)(I,(e=>e.get("url"))),R=(0,x.createSelector)(I,(e=>e.get("spec")||"")),T=(0,x.createSelector)(I,(e=>e.get("specSource")||"not-editor")),P=(0,x.createSelector)(I,(e=>e.get("json",(0,_.Map)()))),k=(0,x.createSelector)(I,(e=>e.get("resolved",(0,_.Map)()))),O=(e,t)=>e.getIn(["resolvedSubtrees",...t],void 0),M=(e,t)=>_.Map.isMap(e)&&_.Map.isMap(t)?t.get("$$ref")?t:(0,_.OrderedMap)().mergeWith(M,e,t):t,j=(0,x.createSelector)(I,(e=>(0,_.OrderedMap)().mergeWith(M,e.get("json"),e.get("resolvedSubtrees")))),V=e=>P(e),D=(0,x.createSelector)(V,(()=>!1)),L=(0,x.createSelector)(V,(e=>Te(e&&e.get("info")))),U=(0,x.createSelector)(V,(e=>Te(e&&e.get("externalDocs")))),z=(0,x.createSelector)(L,(e=>e&&e.get("version"))),B=(0,x.createSelector)(z,(e=>{var t;return s()(t=/v?([0-9]*)\.([0-9]*)\.([0-9]*)/i.exec(e)).call(t,1)})),$=(0,x.createSelector)(j,(e=>e.get("paths"))),J=(0,x.createSelector)($,(e=>{if(!e||e.size<1)return(0,_.List)();let t=(0,_.List)();return e&&o()(e)?(o()(e).call(e,((e,r)=>{if(!e||!o()(e))return{};o()(e).call(e,((e,n)=>{i()(A).call(A,n)<0||(t=t.push((0,_.fromJS)({path:r,method:n,operation:e,id:`${n}-${r}`})))}))})),t):(0,_.List)()})),F=(0,x.createSelector)(V,(e=>(0,_.Set)(e.get("consumes")))),W=(0,x.createSelector)(V,(e=>(0,_.Set)(e.get("produces")))),H=(0,x.createSelector)(V,(e=>e.get("security",(0,_.List)()))),K=(0,x.createSelector)(V,(e=>e.get("securityDefinitions"))),Z=(e,t)=>{const r=e.getIn(["resolvedSubtrees","definitions",t],null),n=e.getIn(["json","definitions",t],null);return r||n||null},G=(0,x.createSelector)(V,(e=>{const t=e.get("definitions");return _.Map.isMap(t)?t:(0,_.Map)()})),Y=(0,x.createSelector)(V,(e=>e.get("basePath"))),X=(0,x.createSelector)(V,(e=>e.get("host"))),Q=(0,x.createSelector)(V,(e=>e.get("schemes",(0,_.Map)()))),ee=(0,x.createSelector)(J,F,W,((e,t,r)=>p()(e).call(e,(e=>e.update("operation",(e=>{if(e){if(!_.Map.isMap(e))return;return e.withMutations((e=>(e.get("consumes")||e.update("consumes",(e=>(0,_.Set)(e).merge(t))),e.get("produces")||e.update("produces",(e=>(0,_.Set)(e).merge(r))),e)))}return(0,_.Map)()})))))),te=(0,x.createSelector)(V,(e=>{const t=e.get("tags",(0,_.List)());return _.List.isList(t)?d()(t).call(t,(e=>_.Map.isMap(e))):(0,_.List)()})),re=(e,t)=>{var r;let n=te(e)||(0,_.List)();return h()(r=d()(n).call(n,_.Map.isMap)).call(r,(e=>e.get("name")===t),(0,_.Map)())},ne=(0,x.createSelector)(ee,te,((e,t)=>f()(e).call(e,((e,t)=>{let r=(0,_.Set)(t.getIn(["operation","tags"]));return r.count()<1?e.update("default",(0,_.List)(),(e=>e.push(t))):f()(r).call(r,((e,r)=>e.update(r,(0,_.List)(),(e=>e.push(t)))),e)}),f()(t).call(t,((e,t)=>e.set(t.get("name"),(0,_.List)())),(0,_.OrderedMap)())))),se=e=>t=>{var r;let{getConfigs:n}=t,{tagsSorter:s,operationsSorter:a}=n();return p()(r=ne(e).sortBy(((e,t)=>t),((e,t)=>{let r="function"==typeof s?s:w.wh.tagsSorter[s];return r?r(e,t):null}))).call(r,((t,r)=>{let n="function"==typeof a?a:w.wh.operationsSorter[a],s=n?v()(t).call(t,n):t;return(0,_.Map)({tagDetails:re(e,r),operations:s})}))},ae=(0,x.createSelector)(I,(e=>e.get("responses",(0,_.Map)()))),oe=(0,x.createSelector)(I,(e=>e.get("requests",(0,_.Map)()))),le=(0,x.createSelector)(I,(e=>e.get("mutatedRequests",(0,_.Map)()))),ie=(e,t,r)=>ae(e).getIn([t,r],null),ce=(e,t,r)=>oe(e).getIn([t,r],null),pe=(e,t,r)=>le(e).getIn([t,r],null),ue=()=>!0,de=(e,t,r)=>{const n=j(e).getIn(["paths",...t,"parameters"],(0,_.OrderedMap)()),s=e.getIn(["meta","paths",...t,"parameters"],(0,_.OrderedMap)()),a=p()(n).call(n,(e=>{const t=s.get(`${r.get("in")}.${r.get("name")}`),n=s.get(`${r.get("in")}.${r.get("name")}.hash-${r.hashCode()}`);return(0,_.OrderedMap)().merge(e,t,n)}));return h()(a).call(a,(e=>e.get("in")===r.get("in")&&e.get("name")===r.get("name")),(0,_.OrderedMap)())},me=(e,t,r,n)=>{const s=`${n}.${r}`;return e.getIn(["meta","paths",...t,"parameter_inclusions",s],!1)},he=(e,t,r,n)=>{const s=j(e).getIn(["paths",...t,"parameters"],(0,_.OrderedMap)()),a=h()(s).call(s,(e=>e.get("in")===n&&e.get("name")===r),(0,_.OrderedMap)());return de(e,t,a)},ge=(e,t,r)=>{var n;const s=j(e).getIn(["paths",t,r],(0,_.OrderedMap)()),a=e.getIn(["meta","paths",t,r],(0,_.OrderedMap)()),o=p()(n=s.get("parameters",(0,_.List)())).call(n,(n=>de(e,[t,r],n)));return(0,_.OrderedMap)().merge(s,a).set("parameters",o)};function fe(e,t,r,n){t=t||[];let s=e.getIn(["meta","paths",...t,"parameters"],(0,_.fromJS)([]));return h()(s).call(s,(e=>_.Map.isMap(e)&&e.get("name")===r&&e.get("in")===n))||(0,_.Map)()}const ye=(0,x.createSelector)(V,(e=>{const t=e.get("host");return"string"==typeof t&&t.length>0&&"/"!==t[0]}));function ve(e,t,r){t=t||[];let n=ge(e,...t).get("parameters",(0,_.List)());return f()(n).call(n,((e,t)=>{let n=r&&"body"===t.get("in")?t.get("value_xml"):t.get("value");return e.set((0,w.V9)(t,{allowHashes:!1}),n)}),(0,_.fromJS)({}))}function Ee(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(_.List.isList(e))return S()(e).call(e,(e=>_.Map.isMap(e)&&e.get("in")===t))}function Se(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(_.List.isList(e))return S()(e).call(e,(e=>_.Map.isMap(e)&&e.get("type")===t))}function Ce(e,t){t=t||[];let r=j(e).getIn(["paths",...t],(0,_.fromJS)({})),n=e.getIn(["meta","paths",...t],(0,_.fromJS)({})),s=be(e,t);const a=r.get("parameters")||new _.List,o=n.get("consumes_value")?n.get("consumes_value"):Se(a,"file")?"multipart/form-data":Se(a,"formData")?"application/x-www-form-urlencoded":void 0;return(0,_.fromJS)({requestContentType:o,responseContentType:s})}function be(e,t){t=t||[];const r=j(e).getIn(["paths",...t],null);if(null===r)return;const n=e.getIn(["meta","paths",...t,"produces_value"],null),s=r.getIn(["produces",0],null);return n||s||"application/json"}function xe(e,t){t=t||[];const r=j(e),n=r.getIn(["paths",...t],null);if(null===n)return;const[s]=t,a=n.get("produces",null),o=r.getIn(["paths",s,"produces"],null),l=r.getIn(["produces"],null);return a||o||l}function we(e,t){t=t||[];const r=j(e),n=r.getIn(["paths",...t],null);if(null===n)return;const[s]=t,a=n.get("consumes",null),o=r.getIn(["paths",s,"consumes"],null),l=r.getIn(["consumes"],null);return a||o||l}const _e=(e,t,r)=>{let n=e.get("url").match(/^([a-z][a-z0-9+\-.]*):/),s=b()(n)?n[1]:null;return e.getIn(["scheme",t,r])||e.getIn(["scheme","_defaultScheme"])||s||""},Ae=(e,t,r)=>{var n;return i()(n=["http","https"]).call(n,_e(e,t,r))>-1},Ie=(e,t)=>{t=t||[];let r=e.getIn(["meta","paths",...t,"parameters"],(0,_.fromJS)([]));const n=[];return o()(r).call(r,(e=>{let t=e.get("errors");t&&t.count()&&o()(t).call(t,(e=>n.push(e)))})),n},Ne=(e,t)=>0===Ie(e,t).length,qe=(e,t)=>{var r;let n={requestBody:!1,requestContentType:{}},s=e.getIn(["resolvedSubtrees","paths",...t,"requestBody"],(0,_.fromJS)([]));return s.size<1||(s.getIn(["required"])&&(n.requestBody=s.getIn(["required"])),o()(r=s.getIn(["content"]).entrySeq()).call(r,(e=>{const t=e[0];if(e[1].getIn(["schema","required"])){const r=e[1].getIn(["schema","required"]).toJS();n.requestContentType[t]=r}}))),n},Re=(e,t,r,n)=>{if((r||n)&&r===n)return!0;let s=e.getIn(["resolvedSubtrees","paths",...t,"requestBody","content"],(0,_.fromJS)([]));if(s.size<2||!r||!n)return!1;let a=s.getIn([r,"schema","properties"],(0,_.fromJS)([])),o=s.getIn([n,"schema","properties"],(0,_.fromJS)([]));return!!a.equals(o)};function Te(e){return _.Map.isMap(e)?e:new _.Map}},7508:(e,t,r)=>{"use strict";r.r(t),r.d(t,{updateSpec:()=>c,updateJsonSpec:()=>p,executeRequest:()=>u,validateParams:()=>d});var n=r(7252),s=r.n(n),a=r(4235),o=r.n(a),l=r(1712),i=r.n(l);const c=(e,t)=>{let{specActions:r}=t;return function(){e(...arguments),r.parseToJson(...arguments)}},p=(e,t)=>{let{specActions:r}=t;return function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e(...n),r.invalidateResolvedSubtreeCache();const[l]=n,c=i()(l,["paths"])||{},p=s()(c);o()(p).call(p,(e=>{i()(c,[e]).$ref&&r.requestResolvedSubtree(["paths",e])})),r.requestResolvedSubtree(["components","securitySchemes"])}},u=(e,t)=>{let{specActions:r}=t;return t=>(r.logRequest(t),e(t))},d=(e,t)=>{let{specSelectors:r}=t;return t=>e(t,r.isOAS3())}},4852:(e,t,r)=>{"use strict";r.r(t),r.d(t,{loaded:()=>n});const n=(e,t)=>function(){e(...arguments);const r=t.getConfigs().withCredentials;void 0!==r&&(t.fn.fetch.withCredentials="string"==typeof r?"true"===r:!!r)}},8901:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});const n=require("swagger-client/es/resolver");var s=r.n(n);const a=require("swagger-client/es/execute"),o=require("swagger-client/es/http");var l=r.n(o);const i=require("swagger-client/es/subtree-resolver");var c=r.n(i),p=r(6765),u=r(4852);function d(e){let{configs:t,getConfigs:r}=e;return{fn:{fetch:(0,o.makeHttp)(l(),t.preFetch,t.postFetch),buildRequest:a.buildRequest,execute:a.execute,resolve:s(),resolveSubtree:function(e,t,n){if(void 0===n){const e=r();n={modelPropertyMacro:e.modelPropertyMacro,parameterMacro:e.parameterMacro,requestInterceptor:e.requestInterceptor,responseInterceptor:e.responseInterceptor}}for(var s=arguments.length,a=new Array(s>3?s-3:0),o=3;o<s;o++)a[o-3]=arguments[o];return c()(e,t,n,...a)},serializeRes:o.serializeRes,opId:p.opId},statePlugins:{configs:{wrapActions:{loaded:u.loaded}}}}}},8525:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(1890);function s(){return{fn:{shallowEqualKeys:n.be}}}},8347:(e,t,r)=>{"use strict";r.r(t),r.d(t,{getDisplayName:()=>n});const n=e=>e.displayName||e.name||"Component"},3420:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var n=r(8344),s=r.n(n),a=r(1890),o=r(290),l=r(8347),i=r(7481);const c=e=>{let{getComponents:t,getStore:r,getSystem:n}=e;const c=(p=(0,o.getComponent)(n,r,t),(0,a.HP)(p,(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return s()(t)})));var p;const u=(e=>(0,i.Z)(e,(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return t})))((0,o.withMappedContainer)(n,r,c));return{rootInjects:{getComponent:c,makeMappedContainer:u,render:(0,o.render)(n,r,o.getComponent,t)},fn:{getDisplayName:l.getDisplayName}}}},290:(e,t,r)=>{"use strict";r.r(t),r.d(t,{getComponent:()=>x,render:()=>b,withMappedContainer:()=>C});var n=r(4250),s=r.n(n),a=r(7252),o=r.n(a),l=r(6689),i=r.n(l);const c=require("react-dom");var p=r.n(c),u=r(6695);const d=require("react-redux"),m=require("lodash/omit");var h=r.n(m);const g=require("lodash/identity");var f=r.n(g);const y=e=>t=>{const{fn:r}=e();class n extends l.Component{render(){return i().createElement(t,s()({},e(),this.props,this.context))}}return n.displayName=`WithSystem(${r.getDisplayName(t)})`,n},v=(e,t)=>r=>{const{fn:n}=e();class a extends l.Component{render(){return i().createElement(d.Provider,{store:t},i().createElement(r,s()({},this.props,this.context)))}}return a.displayName=`WithRoot(${n.getDisplayName(r)})`,a},E=(e,t,r)=>(0,u.compose)(r?v(e,r):f(),(0,d.connect)(((r,n)=>{var s;const a={...n,...e()},o=(null===(s=t.prototype)||void 0===s?void 0:s.mapStateToProps)||(e=>({state:e}));return o(r,a)})),y(e))(t),S=(e,t,r,n)=>{for(const s in t){const a=t[s];"function"==typeof a&&a(r[s],n[s],e())}},C=(e,t,r)=>(t,n)=>{const{fn:s}=e(),a=r(t,"root");class c extends l.Component{constructor(t,r){super(t,r),S(e,n,t,{})}UNSAFE_componentWillReceiveProps(t){S(e,n,t,this.props)}render(){const e=h()(this.props,n?o()(n):[]);return i().createElement(a,e)}}return c.displayName=`WithMappedContainer(${s.getDisplayName(a)})`,c},b=(e,t,r,n)=>s=>{const a=r(e,t,n)("App","root");p().render(i().createElement(a,null),s)},x=(e,t,r)=>function(n,s){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"!=typeof n)throw new TypeError("Need a string, to fetch a component. Was given a "+typeof n);const o=r(n);return o?s?"root"===s?E(e,o,t()):E(e,o):o:(a.failSilently||e().log.warn("Could not find component:",n),null)}},6068:(e,t,r)=>{"use strict";r.d(t,{d3:()=>i(),C2:()=>V});var n=r(7252),s=r.n(n),a=r(2605),o=r.n(a);const l=require("react-syntax-highlighter/dist/esm/light");var i=r.n(l);const c=require("react-syntax-highlighter/dist/esm/languages/hljs/javascript");var p=r.n(c);const u=require("react-syntax-highlighter/dist/esm/languages/hljs/json");var d=r.n(u);const m=require("react-syntax-highlighter/dist/esm/languages/hljs/xml");var h=r.n(m);const g=require("react-syntax-highlighter/dist/esm/languages/hljs/bash");var f=r.n(g);const y=require("react-syntax-highlighter/dist/esm/languages/hljs/yaml");var v=r.n(y);const E=require("react-syntax-highlighter/dist/esm/languages/hljs/http");var S=r.n(E);const C=require("react-syntax-highlighter/dist/esm/languages/hljs/powershell");var b=r.n(C);const x=require("react-syntax-highlighter/dist/esm/styles/hljs/agate");var w=r.n(x);const _=require("react-syntax-highlighter/dist/esm/styles/hljs/arta");var A=r.n(_);const I=require("react-syntax-highlighter/dist/esm/styles/hljs/monokai");var N=r.n(I);const q=require("react-syntax-highlighter/dist/esm/styles/hljs/nord");var R=r.n(q);const T=require("react-syntax-highlighter/dist/esm/styles/hljs/obsidian");var P=r.n(T);const k=require("react-syntax-highlighter/dist/esm/styles/hljs/tomorrow-night");var O=r.n(k);i().registerLanguage("json",d()),i().registerLanguage("js",p()),i().registerLanguage("xml",h()),i().registerLanguage("yaml",v()),i().registerLanguage("http",S()),i().registerLanguage("bash",f()),i().registerLanguage("powershell",b()),i().registerLanguage("javascript",p());const M={agate:w(),arta:A(),monokai:N(),nord:R(),obsidian:P(),"tomorrow-night":O()},j=s()(M),V=e=>o()(j).call(j,e)?M[e]:(console.warn(`Request style '${e}' is not available, returning default instead`),w())},1890:(e,t,r)=>{"use strict";r.d(t,{r3:()=>je,GZ:()=>De,Xb:()=>Qe,oJ:()=>$e,XV:()=>He,iQ:()=>xe,J6:()=>Je,DR:()=>_e,oG:()=>me,Uj:()=>Xe,QG:()=>Be,po:()=>We,nX:()=>Fe,gp:()=>we,xi:()=>Oe,kJ:()=>ve,O2:()=>tt,LQ:()=>ge,Wl:()=>ye,Kn:()=>fe,HP:()=>Ee,AF:()=>he,D$:()=>Ze,Ay:()=>Se,Q2:()=>Ce,mz:()=>de,V9:()=>Ge,cz:()=>Ye,UG:()=>Me,Zl:()=>Ae,hW:()=>ze,Nm:()=>Ue,be:()=>Le,wh:()=>Ve,Pz:()=>Ke,_5:()=>be,Ik:()=>Ne});var n=r(7104),s=r.n(n),a=r(3942),o=r.n(a),l=r(7862),i=r.n(l),c=r(4235),p=r.n(c),u=r(9998),d=r.n(u),m=r(7252),h=r.n(m),g=(r(593),r(66)),f=r.n(g),y=r(4994),v=r.n(y),E=r(9247),S=r.n(E),C=r(600),b=r.n(C),x=(r(4883),r(5626)),w=r.n(x),_=(r(2605),r(8344)),A=r.n(_),I=r(8493),N=r.n(I),q=r(3580),R=r.n(q),T=r(3262),P=r.n(T),k=r(7390),O=r.n(k),M=r(5572),j=r.n(M);const V=require("@braintree/sanitize-url"),D=require("lodash/camelCase");var L=r.n(D);const U=require("lodash/upperFirst");var z=r.n(U),B=r(541),$=r.n(B);const J=require("lodash/find");var F=r.n(J);const W=require("lodash/some");var H=r.n(W);const K=require("lodash/eq");var Z=r.n(K),G=r(5716),Y=r.n(G),X=r(4128),Q=r(7504);const ee=require("css.escape");var te=r.n(ee),re=r(9069),ne=r(185),se=r.n(ne);const ae=require("sha.js");var oe=r.n(ae),le=r(9793),ie=r.n(le),ce=r(871).Buffer;const pe="default",ue=e=>j().Iterable.isIterable(e);function de(e){return fe(e)?ue(e)?e.toJS():e:{}}function me(e){var t,r;if(ue(e))return e;if(e instanceof Q.Z.File)return e;if(!fe(e))return e;if(s()(e))return o()(r=j().Seq(e)).call(r,me).toList();if(Y()(i()(e))){var n;const t=function(e){if(!Y()(i()(e)))return e;const t={},r="_**[]",n={};for(let s of i()(e).call(e))if(t[s[0]]||n[s[0]]&&n[s[0]].containsMultiple){if(!n[s[0]]){n[s[0]]={containsMultiple:!0,length:1},t[`${s[0]}${r}${n[s[0]].length}`]=t[s[0]],delete t[s[0]]}n[s[0]].length+=1,t[`${s[0]}${r}${n[s[0]].length}`]=s[1]}else t[s[0]]=s[1];return t}(e);return o()(n=j().OrderedMap(t)).call(n,me)}return o()(t=j().OrderedMap(e)).call(t,me)}function he(e){return s()(e)?e:[e]}function ge(e){return"function"==typeof e}function fe(e){return!!e&&"object"==typeof e}function ye(e){return"function"==typeof e}function ve(e){return s()(e)}const Ee=$();function Se(e,t){var r;return f()(r=h()(e)).call(r,((r,n)=>(r[n]=t(e[n],n),r)),{})}function Ce(e,t){var r;return f()(r=h()(e)).call(r,((r,n)=>{let s=t(e[n],n);return s&&"object"==typeof s&&v()(r,s),r}),{})}function be(e){return t=>{let{dispatch:r,getState:n}=t;return t=>r=>"function"==typeof r?r(e()):t(r)}}function xe(e){var t;let r=e.keySeq();return r.contains(pe)?pe:S()(t=d()(r).call(r,(e=>"2"===(e+"")[0]))).call(t).first()}function we(e,t){if(!j().Iterable.isIterable(e))return j().List();let r=e.getIn(s()(t)?t:[t]);return j().List.isList(r)?r:j().List()}function _e(e){let t,r=[/filename\*=[^']+'\w*'"([^"]+)";?/i,/filename\*=[^']+'\w*'([^;]+);?/i,/filename="([^;]*);?"/i,/filename=([^;]*);?/i];if(w()(r).call(r,(r=>(t=r.exec(e),null!==t))),null!==t&&t.length>1)try{return decodeURIComponent(t[1])}catch(e){console.error(e)}return null}function Ae(e){return t=e.replace(/\.[^./]*$/,""),z()(L()(t));var t}function Ie(e,t,r,n,a){if(!t)return[];let l=[],i=t.get("nullable"),c=t.get("required"),u=t.get("maximum"),m=t.get("minimum"),h=t.get("type"),g=t.get("format"),f=t.get("maxLength"),y=t.get("minLength"),v=t.get("uniqueItems"),E=t.get("maxItems"),S=t.get("minItems"),C=t.get("pattern");const b=r||!0===c,x=null!=e;if(i&&null===e||!h||!(b||x&&"array"===h||!(!b&&!x)))return[];let _="string"===h&&e,A="array"===h&&s()(e)&&e.length,I="array"===h&&j().List.isList(e)&&e.count();const N=[_,A,I,"array"===h&&"string"==typeof e&&e,"file"===h&&e instanceof Q.Z.File,"boolean"===h&&(e||!1===e),"number"===h&&(e||0===e),"integer"===h&&(e||0===e),"object"===h&&"object"==typeof e&&null!==e,"object"===h&&"string"==typeof e&&e],q=w()(N).call(N,(e=>!!e));if(b&&!q&&!n)return l.push("Required field is not provided"),l;if("object"===h&&(null===a||"application/json"===a)){let r=e;if("string"==typeof e)try{r=JSON.parse(e)}catch(e){return l.push("Parameter string value must be valid JSON"),l}var R;if(t&&t.has("required")&&ye(c.isList)&&c.isList()&&p()(c).call(c,(e=>{void 0===r[e]&&l.push({propKey:e,error:"Required property not found"})})),t&&t.has("properties"))p()(R=t.get("properties")).call(R,((e,t)=>{const s=Ie(r[t],e,!1,n,a);l.push(...o()(s).call(s,(e=>({propKey:t,error:e}))))}))}if(C){let t=((e,t)=>{if(!new RegExp(t).test(e))return"Value must follow pattern "+t})(e,C);t&&l.push(t)}if(S&&"array"===h){let t=((e,t)=>{if(!e&&t>=1||e&&e.length<t)return`Array must contain at least ${t} item${1===t?"":"s"}`})(e,S);t&&l.push(t)}if(E&&"array"===h){let t=((e,t)=>{if(e&&e.length>t)return`Array must not contain more then ${t} item${1===t?"":"s"}`})(e,E);t&&l.push({needRemove:!0,error:t})}if(v&&"array"===h){let t=((e,t)=>{if(e&&("true"===t||!0===t)){const t=(0,M.fromJS)(e),r=t.toSet();if(e.length>r.size){let e=(0,M.Set)();if(p()(t).call(t,((r,n)=>{d()(t).call(t,(e=>ye(e.equals)?e.equals(r):e===r)).size>1&&(e=e.add(n))})),0!==e.size)return o()(e).call(e,(e=>({index:e,error:"No duplicates allowed."}))).toArray()}}})(e,v);t&&l.push(...t)}if(f||0===f){let t=((e,t)=>{if(e.length>t)return`Value must be no longer than ${t} character${1!==t?"s":""}`})(e,f);t&&l.push(t)}if(y){let t=((e,t)=>{if(e.length<t)return`Value must be at least ${t} character${1!==t?"s":""}`})(e,y);t&&l.push(t)}if(u||0===u){let t=((e,t)=>{if(e>t)return`Value must be less than ${t}`})(e,u);t&&l.push(t)}if(m||0===m){let t=((e,t)=>{if(e<t)return`Value must be greater than ${t}`})(e,m);t&&l.push(t)}if("string"===h){let t;if(t="date-time"===g?(e=>{if(isNaN(Date.parse(e)))return"Value must be a DateTime"})(e):"uuid"===g?(e=>{if(e=e.toString().toLowerCase(),!/^[{(]?[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}[)}]?$/.test(e))return"Value must be a Guid"})(e):(e=>{if(e&&"string"!=typeof e)return"Value must be a string"})(e),!t)return l;l.push(t)}else if("boolean"===h){let t=(e=>{if("true"!==e&&"false"!==e&&!0!==e&&!1!==e)return"Value must be a boolean"})(e);if(!t)return l;l.push(t)}else if("number"===h){let t=(e=>{if(!/^-?\d+(\.?\d+)?$/.test(e))return"Value must be a number"})(e);if(!t)return l;l.push(t)}else if("integer"===h){let t=(e=>{if(!/^-?\d+$/.test(e))return"Value must be an integer"})(e);if(!t)return l;l.push(t)}else if("array"===h){if(!A&&!I)return l;e&&p()(e).call(e,((e,r)=>{const s=Ie(e,t.get("items"),!1,n,a);l.push(...o()(s).call(s,(e=>({index:r,error:e}))))}))}else if("file"===h){let t=(e=>{if(e&&!(e instanceof Q.Z.File))return"Value must be a file"})(e);if(!t)return l;l.push(t)}return l}const Ne=function(e,t){let{isOAS3:r=!1,bypassRequiredCheck:n=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=e.get("required"),{schema:a,parameterContentMediaType:o}=(0,re.Z)(e,{isOAS3:r});return Ie(t,a,s,n,o)},qe=(e,t,r)=>{if(e&&!e.xml&&(e.xml={}),e&&!e.xml.name){if(!e.$$ref&&(e.type||e.items||e.properties||e.additionalProperties))return'<?xml version="1.0" encoding="UTF-8"?>\n\x3c!-- XML example cannot be generated; root element name is undefined --\x3e';if(e.$$ref){let t=e.$$ref.match(/\S*\/(\S+)$/);e.xml.name=t[1]}}return(0,X.memoizedCreateXMLExample)(e,t,r)},Re=[{when:/json/,shouldStringifyTypes:["string"]}],Te=["object"],Pe=(e,t,r,n)=>{const s=(0,X.memoizedSampleFromSchema)(e,t,n),a=typeof s,o=f()(Re).call(Re,((e,t)=>t.when.test(r)?[...e,...t.shouldStringifyTypes]:e),Te);return H()(o,(e=>e===a))?A()(s,null,2):s},ke=(e,t,r,n)=>{const s=Pe(e,t,r,n);let a;try{a=ie().dump(ie().load(s),{lineWidth:-1},{schema:le.JSON_SCHEMA}),"\n"===a[a.length-1]&&(a=b()(a).call(a,0,a.length-1))}catch(e){return console.error(e),"error: could not generate yaml example"}return a.replace(/\t/g,"  ")},Oe=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;return e&&ye(e.toJS)&&(e=e.toJS()),n&&ye(n.toJS)&&(n=n.toJS()),/xml/.test(t)?qe(e,r,n):/(yaml|yml)/.test(t)?ke(e,r,t,n):Pe(e,r,t,n)},Me=()=>{let e={},t=Q.Z.location.search;if(!t)return{};if(""!=t){let r=t.substr(1).split("&");for(let t in r)Object.prototype.hasOwnProperty.call(r,t)&&(t=r[t].split("="),e[decodeURIComponent(t[0])]=t[1]&&decodeURIComponent(t[1])||"")}return e},je=e=>{let t;return t=e instanceof ce?e:ce.from(e.toString(),"utf-8"),t.toString("base64")},Ve={operationsSorter:{alpha:(e,t)=>e.get("path").localeCompare(t.get("path")),method:(e,t)=>e.get("method").localeCompare(t.get("method"))},tagsSorter:{alpha:(e,t)=>e.localeCompare(t)}},De=e=>{let t=[];for(let r in e){let n=e[r];void 0!==n&&""!==n&&t.push([r,"=",encodeURIComponent(n).replace(/%20/g,"+")].join(""))}return t.join("&")},Le=(e,t,r)=>!!F()(r,(r=>Z()(e[r],t[r])));function Ue(e){return"string"!=typeof e||""===e?"":(0,V.sanitizeUrl)(e)}function ze(e){return!(!e||N()(e).call(e,"localhost")>=0||N()(e).call(e,"127.0.0.1")>=0||"none"===e)}function Be(e){if(!j().OrderedMap.isOrderedMap(e))return null;if(!e.size)return null;const t=R()(e).call(e,((e,t)=>P()(t).call(t,"2")&&h()(e.get("content")||{}).length>0)),r=e.get("default")||j().OrderedMap(),n=(r.get("content")||j().OrderedMap()).keySeq().toJS().length?r:null;return t||n}const $e=e=>"string"==typeof e||e instanceof String?O()(e).call(e).replace(/\s/g,"%20"):"",Je=e=>te()($e(e).replace(/%20/g,"_")),Fe=e=>d()(e).call(e,((e,t)=>/^x-/.test(t))),We=e=>d()(e).call(e,((e,t)=>/^pattern|maxLength|minLength|maximum|minimum/.test(t)));function He(e,t){var r;let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>!0;if("object"!=typeof e||s()(e)||null===e||!t)return e;const a=v()({},e);return p()(r=h()(a)).call(r,(e=>{e===t&&n(a[e],e)?delete a[e]:a[e]=He(a[e],t,n)})),a}function Ke(e){if("string"==typeof e)return e;if(e&&e.toJS&&(e=e.toJS()),"object"==typeof e&&null!==e)try{return A()(e,null,2)}catch(t){return String(e)}return null==e?"":e.toString()}function Ze(e){return"number"==typeof e?e.toString():e}function Ge(e){let{returnAll:t=!1,allowHashes:r=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!j().Map.isMap(e))throw new Error("paramToIdentifier: received a non-Im.Map parameter as input");const n=e.get("name"),s=e.get("in");let a=[];return e&&e.hashCode&&s&&n&&r&&a.push(`${s}.${n}.hash-${e.hashCode()}`),s&&n&&a.push(`${s}.${n}`),a.push(n),t?a:a[0]||""}function Ye(e,t){var r;const n=Ge(e,{returnAll:!0});return d()(r=o()(n).call(n,(e=>t[e]))).call(r,(e=>void 0!==e))[0]}function Xe(){return et(se()(32).toString("base64"))}function Qe(e){return et(oe()("sha256").update(e).digest("base64"))}function et(e){return e.replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}const tt=e=>!e||!(!ue(e)||!e.isEmpty())},2518:(e,t,r)=>{"use strict";function n(e){return function(e){try{return!!JSON.parse(e)}catch(e){return null}}(e)?"json":null}r.d(t,{O:()=>n})},7504:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});const n=function(){var e={location:{},history:{},open:()=>{},close:()=>{},File:function(){}};if("undefined"==typeof window)return e;try{e=window;for(var t of["File","Blob","FormData"])t in window&&(e[t]=window[t])}catch(e){console.error(e)}return e}()},9069:(e,t,r)=>{"use strict";r.d(t,{Z:()=>p});var n=r(9998),s=r.n(n),a=r(2605),o=r.n(a),l=r(5572),i=r.n(l);const c=i().Set.of("type","format","items","default","maximum","exclusiveMaximum","minimum","exclusiveMinimum","maxLength","minLength","pattern","maxItems","minItems","uniqueItems","enum","multipleOf");function p(e){let{isOAS3:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!i().Map.isMap(e))return{schema:i().Map(),parameterContentMediaType:null};if(!t)return"body"===e.get("in")?{schema:e.get("schema",i().Map()),parameterContentMediaType:null}:{schema:s()(e).call(e,((e,t)=>o()(c).call(c,t))),parameterContentMediaType:null};if(e.get("content")){const t=e.get("content",i().Map({})).keySeq().first();return{schema:e.getIn(["content",t,"schema"],i().Map()),parameterContentMediaType:t}}return{schema:e.get("schema")?e.get("schema",i().Map()):i().Map(),parameterContentMediaType:null}}},7481:(e,t,r)=>{"use strict";r.d(t,{Z:()=>b});var n=r(7104),s=r.n(n),a=r(7834),o=r.n(a),l=r(1733),i=r.n(l),c=r(874),p=r.n(c),u=r(3580),d=r.n(u);const m=require("@babel/runtime-corejs3/core-js-stable/instance/find-index");var h=r.n(m),g=r(2611),f=r.n(g),y=r(541),v=r.n(y);const E=e=>t=>s()(e)&&s()(t)&&e.length===t.length&&o()(e).call(e,((e,r)=>e===t[r])),S=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return t};class C extends(f()){delete(e){const t=i()(p()(this).call(this)),r=d()(t).call(t,E(e));return super.delete(r)}get(e){const t=i()(p()(this).call(this)),r=d()(t).call(t,E(e));return super.get(r)}has(e){const t=i()(p()(this).call(this));return-1!==h()(t).call(t,E(e))}}const b=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:S;const{Cache:r}=v();v().Cache=C;const n=v()(e,t);return v().Cache=r,n}},5102:(e,t,r)=>{var n={"./all.js":5308,"./auth/actions.js":5812,"./auth/index.js":3705,"./auth/reducers.js":3962,"./auth/selectors.js":35,"./auth/spec-wrap-actions.js":8302,"./configs/actions.js":714,"./configs/helpers.js":2256,"./configs/index.js":1661,"./configs/reducers.js":7743,"./configs/selectors.js":9018,"./configs/spec-actions.js":2698,"./deep-linking/helpers.js":1970,"./deep-linking/index.js":4980,"./deep-linking/layout.js":2179,"./deep-linking/operation-tag-wrapper.jsx":4584,"./deep-linking/operation-wrapper.jsx":877,"./download-url.js":8011,"./err/actions.js":4966,"./err/error-transformers/hook.js":2860,"./err/error-transformers/transformers/not-of-type.js":2392,"./err/error-transformers/transformers/parameter-oneof.js":1835,"./err/index.js":7793,"./err/reducers.js":3527,"./err/selectors.js":7667,"./filter/index.js":9978,"./filter/opsFilter.js":4309,"./layout/actions.js":5474,"./layout/index.js":6821,"./layout/reducers.js":5672,"./layout/selectors.js":4400,"./layout/spec-extensions/wrap-selector.js":8989,"./logs/index.js":9150,"./oas3/actions.js":7002,"./oas3/auth-extensions/wrap-selectors.js":3723,"./oas3/components/callbacks.jsx":3427,"./oas3/components/http-auth.jsx":6775,"./oas3/components/index.js":6467,"./oas3/components/operation-link.jsx":5757,"./oas3/components/operation-servers.jsx":6796,"./oas3/components/request-body-editor.jsx":5327,"./oas3/components/request-body.jsx":2458,"./oas3/components/servers-container.jsx":9928,"./oas3/components/servers.jsx":6617,"./oas3/helpers.jsx":7779,"./oas3/index.js":7451,"./oas3/reducers.js":2109,"./oas3/selectors.js":5065,"./oas3/spec-extensions/selectors.js":1741,"./oas3/spec-extensions/wrap-selectors.js":2044,"./oas3/wrap-components/auth-item.jsx":356,"./oas3/wrap-components/index.js":7761,"./oas3/wrap-components/json-schema-string.jsx":287,"./oas3/wrap-components/markdown.jsx":2460,"./oas3/wrap-components/model.jsx":3499,"./oas3/wrap-components/online-validator-badge.js":58,"./oas3/wrap-components/version-stamp.jsx":9487,"./on-complete/index.js":8560,"./request-snippets/fn.js":8223,"./request-snippets/index.js":6575,"./request-snippets/request-snippets.jsx":4206,"./request-snippets/selectors.js":4669,"./safe-render/components/error-boundary.jsx":6195,"./safe-render/components/fallback.jsx":9403,"./safe-render/fn.jsx":6189,"./safe-render/index.js":9595,"./samples/fn.js":4128,"./samples/index.js":8883,"./spec/actions.js":9381,"./spec/index.js":7038,"./spec/reducers.js":32,"./spec/selectors.js":3881,"./spec/wrap-actions.js":7508,"./swagger-js/configs-wrap-actions.js":4852,"./swagger-js/index.js":8901,"./util/index.js":8525,"./view/fn.js":8347,"./view/index.js":3420,"./view/root-injects.jsx":290,"core/plugins/all.js":5308,"core/plugins/auth/actions.js":5812,"core/plugins/auth/index.js":3705,"core/plugins/auth/reducers.js":3962,"core/plugins/auth/selectors.js":35,"core/plugins/auth/spec-wrap-actions.js":8302,"core/plugins/configs/actions.js":714,"core/plugins/configs/helpers.js":2256,"core/plugins/configs/index.js":1661,"core/plugins/configs/reducers.js":7743,"core/plugins/configs/selectors.js":9018,"core/plugins/configs/spec-actions.js":2698,"core/plugins/deep-linking/helpers.js":1970,"core/plugins/deep-linking/index.js":4980,"core/plugins/deep-linking/layout.js":2179,"core/plugins/deep-linking/operation-tag-wrapper.jsx":4584,"core/plugins/deep-linking/operation-wrapper.jsx":877,"core/plugins/download-url.js":8011,"core/plugins/err/actions.js":4966,"core/plugins/err/error-transformers/hook.js":2860,"core/plugins/err/error-transformers/transformers/not-of-type.js":2392,"core/plugins/err/error-transformers/transformers/parameter-oneof.js":1835,"core/plugins/err/index.js":7793,"core/plugins/err/reducers.js":3527,"core/plugins/err/selectors.js":7667,"core/plugins/filter/index.js":9978,"core/plugins/filter/opsFilter.js":4309,"core/plugins/layout/actions.js":5474,"core/plugins/layout/index.js":6821,"core/plugins/layout/reducers.js":5672,"core/plugins/layout/selectors.js":4400,"core/plugins/layout/spec-extensions/wrap-selector.js":8989,"core/plugins/logs/index.js":9150,"core/plugins/oas3/actions.js":7002,"core/plugins/oas3/auth-extensions/wrap-selectors.js":3723,"core/plugins/oas3/components/callbacks.jsx":3427,"core/plugins/oas3/components/http-auth.jsx":6775,"core/plugins/oas3/components/index.js":6467,"core/plugins/oas3/components/operation-link.jsx":5757,"core/plugins/oas3/components/operation-servers.jsx":6796,"core/plugins/oas3/components/request-body-editor.jsx":5327,"core/plugins/oas3/components/request-body.jsx":2458,"core/plugins/oas3/components/servers-container.jsx":9928,"core/plugins/oas3/components/servers.jsx":6617,"core/plugins/oas3/helpers.jsx":7779,"core/plugins/oas3/index.js":7451,"core/plugins/oas3/reducers.js":2109,"core/plugins/oas3/selectors.js":5065,"core/plugins/oas3/spec-extensions/selectors.js":1741,"core/plugins/oas3/spec-extensions/wrap-selectors.js":2044,"core/plugins/oas3/wrap-components/auth-item.jsx":356,"core/plugins/oas3/wrap-components/index.js":7761,"core/plugins/oas3/wrap-components/json-schema-string.jsx":287,"core/plugins/oas3/wrap-components/markdown.jsx":2460,"core/plugins/oas3/wrap-components/model.jsx":3499,"core/plugins/oas3/wrap-components/online-validator-badge.js":58,"core/plugins/oas3/wrap-components/version-stamp.jsx":9487,"core/plugins/on-complete/index.js":8560,"core/plugins/request-snippets/fn.js":8223,"core/plugins/request-snippets/index.js":6575,"core/plugins/request-snippets/request-snippets.jsx":4206,"core/plugins/request-snippets/selectors.js":4669,"core/plugins/safe-render/components/error-boundary.jsx":6195,"core/plugins/safe-render/components/fallback.jsx":9403,"core/plugins/safe-render/fn.jsx":6189,"core/plugins/safe-render/index.js":9595,"core/plugins/samples/fn.js":4128,"core/plugins/samples/index.js":8883,"core/plugins/spec/actions.js":9381,"core/plugins/spec/index.js":7038,"core/plugins/spec/reducers.js":32,"core/plugins/spec/selectors.js":3881,"core/plugins/spec/wrap-actions.js":7508,"core/plugins/swagger-js/configs-wrap-actions.js":4852,"core/plugins/swagger-js/index.js":8901,"core/plugins/util/index.js":8525,"core/plugins/view/fn.js":8347,"core/plugins/view/index.js":3420,"core/plugins/view/root-injects.jsx":290};function s(e){var t=a(e);return r(t)}function a(e){if(!r.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}s.keys=function(){return Object.keys(n)},s.resolve=a,e.exports=s,s.id=5102},2517:e=>{"use strict";e.exports="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwcHgiICBoZWlnaHQ9IjIwMHB4IiAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgcHJlc2VydmVBc3BlY3RSYXRpbz0ieE1pZFlNaWQiIGNsYXNzPSJsZHMtcm9sbGluZyIgc3R5bGU9ImJhY2tncm91bmQtaW1hZ2U6IG5vbmU7IGJhY2tncm91bmQtcG9zaXRpb246IGluaXRpYWwgaW5pdGlhbDsgYmFja2dyb3VuZC1yZXBlYXQ6IGluaXRpYWwgaW5pdGlhbDsiPjxjaXJjbGUgY3g9IjUwIiBjeT0iNTAiIGZpbGw9Im5vbmUiIG5nLWF0dHItc3Ryb2tlPSJ7e2NvbmZpZy5jb2xvcn19IiBuZy1hdHRyLXN0cm9rZS13aWR0aD0ie3tjb25maWcud2lkdGh9fSIgbmctYXR0ci1yPSJ7e2NvbmZpZy5yYWRpdXN9fSIgbmctYXR0ci1zdHJva2UtZGFzaGFycmF5PSJ7e2NvbmZpZy5kYXNoYXJyYXl9fSIgc3Ryb2tlPSIjNTU1NTU1IiBzdHJva2Utd2lkdGg9IjEwIiByPSIzNSIgc3Ryb2tlLWRhc2hhcnJheT0iMTY0LjkzMzYxNDMxMzQ2NDE1IDU2Ljk3Nzg3MTQzNzgyMTM4Ij48YW5pbWF0ZVRyYW5zZm9ybSBhdHRyaWJ1dGVOYW1lPSJ0cmFuc2Zvcm0iIHR5cGU9InJvdGF0ZSIgY2FsY01vZGU9ImxpbmVhciIgdmFsdWVzPSIwIDUwIDUwOzM2MCA1MCA1MCIga2V5VGltZXM9IjA7MSIgZHVyPSIxcyIgYmVnaW49IjBzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSI+PC9hbmltYXRlVHJhbnNmb3JtPjwvY2lyY2xlPjwvc3ZnPgo="},5163:e=>{"use strict";e.exports='---\nurl: "https://petstore.swagger.io/v2/swagger.json"\ndom_id: "#swagger-ui"\nvalidatorUrl: "https://validator.swagger.io/validator"\n'},1733:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/array/from")},7104:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/array/is-array")},593:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/bind")},4883:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/concat")},7862:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/entries")},7834:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/every")},9998:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/filter")},3580:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/find")},4235:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/for-each")},2605:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/includes")},8493:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/index-of")},874:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/keys")},3942:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/map")},66:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/reduce")},600:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/slice")},5626:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/some")},9247:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/sort")},3262:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/starts-with")},7390:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/instance/trim")},8344:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/json/stringify")},2611:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/map")},4994:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/object/assign")},7252:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/object/keys")},9968:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/object/values")},9300:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/set-timeout")},9478:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/core-js-stable/url")},1093:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/helpers/defineProperty")},4250:e=>{"use strict";e.exports=require("@babel/runtime-corejs3/helpers/extends")},871:e=>{"use strict";e.exports=require("buffer")},9003:e=>{"use strict";e.exports=require("classnames")},5572:e=>{"use strict";e.exports=require("immutable")},9793:e=>{"use strict";e.exports=require("js-yaml")},1712:e=>{"use strict";e.exports=require("lodash/get")},5716:e=>{"use strict";e.exports=require("lodash/isFunction")},541:e=>{"use strict";e.exports=require("lodash/memoize")},580:e=>{"use strict";e.exports=require("prop-types")},185:e=>{"use strict";e.exports=require("randombytes")},6689:e=>{"use strict";e.exports=require("react")},2807:e=>{"use strict";e.exports=require("react-copy-to-clipboard")},8082:e=>{"use strict";e.exports=require("react-immutable-proptypes")},6695:e=>{"use strict";e.exports=require("redux")},963:e=>{"use strict";e.exports=require("remarkable")},6814:e=>{"use strict";e.exports=require("reselect")},41:e=>{"use strict";e.exports=require("serialize-error")},6765:e=>{"use strict";e.exports=require("swagger-client/es/helpers")},3883:e=>{"use strict";e.exports=require("url-parse")}},t={};function r(n){var s=t[n];if(void 0!==s)return s.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,r),a.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return(()=>{"use strict";r.d(n,{default:()=>Cn});var e={};r.r(e),r.d(e,{Button:()=>lr,Col:()=>ar,Collapse:()=>mr,Container:()=>nr,Input:()=>cr,Link:()=>ur,Row:()=>or,Select:()=>pr,TextArea:()=>ir});var t={};r.r(t),r.d(t,{JsonSchemaArrayItemFile:()=>on,JsonSchemaArrayItemText:()=>an,JsonSchemaForm:()=>rn,JsonSchema_array:()=>sn,JsonSchema_boolean:()=>ln,JsonSchema_object:()=>pn,JsonSchema_string:()=>nn});const s=require("@babel/runtime-corejs3/core-js-stable/instance/last-index-of");var a=r.n(s),o=r(9998),l=r.n(o),i=r(7252),c=r.n(i),p=r(8344),u=r.n(p);const d=require("deep-extend");var m=r.n(d),h=r(593),g=r.n(h),f=r(4994),y=r.n(f),v=r(600),E=r.n(v),S=r(7104),C=r.n(S),b=r(66),x=r.n(b),w=r(3942),_=r.n(w),A=r(4883),I=r.n(A),N=r(6689),q=r.n(N),R=r(6695),T=r(5572),P=r.n(T);const k=require("redux-immutable");var O=r(41);const M=require("lodash/merge");var j=r.n(M),V=r(4966),D=r(7504),L=r(1890);const U=e=>e;class z{constructor(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};var r,n,s;m()(this,{state:{},plugins:[],pluginsOptions:{},system:{configs:{},fn:{},components:{},rootInjects:{},statePlugins:{}},boundSystem:{},toolbox:{}},t),this.getSystem=g()(e=this._getSystem).call(e,this),this.store=(r=U,n=(0,T.fromJS)(this.state),s=this.getSystem,function(e,t,r){let n=[(0,L._5)(r)];const s=D.Z.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__||R.compose;return(0,R.createStore)(e,t,s((0,R.applyMiddleware)(...n)))}(r,n,s)),this.buildSystem(!1),this.register(this.plugins)}getStore(){return this.store}register(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];var r=B(e,this.getSystem(),this.pluginsOptions);J(this.system,r),t&&this.buildSystem();$.call(this.system,e,this.getSystem())&&this.buildSystem()}buildSystem(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=this.getStore().dispatch,r=this.getStore().getState;this.boundSystem=y()({},this.getRootInjects(),this.getWrappedAndBoundActions(t),this.getWrappedAndBoundSelectors(r,this.getSystem),this.getStateThunks(r),this.getFn(),this.getConfigs()),e&&this.rebuildReducer()}_getSystem(){return this.boundSystem}getRootInjects(){var e,t,r;return y()({getSystem:this.getSystem,getStore:g()(e=this.getStore).call(e,this),getComponents:g()(t=this.getComponents).call(t,this),getState:this.getStore().getState,getConfigs:g()(r=this._getConfigs).call(r,this),Im:P(),React:q()},this.system.rootInjects||{})}_getConfigs(){return this.system.configs}getConfigs(){return{configs:this.system.configs}}setConfigs(e){this.system.configs=e}rebuildReducer(){var e;this.store.replaceReducer((e=this.system.statePlugins,function(e){var t;let r=x()(t=c()(e)).call(t,((t,r)=>(t[r]=function(e){return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new T.Map,r=arguments.length>1?arguments[1]:void 0;if(!e)return t;let n=e[r.type];if(n){const e=F(n)(t,r);return null===e?t:e}return t}}(e[r]),t)),{});return c()(r).length?(0,k.combineReducers)(r):U}((0,L.Ay)(e,(e=>e.reducers)))))}getType(e){let t=e[0].toUpperCase()+E()(e).call(e,1);return(0,L.Q2)(this.system.statePlugins,((r,n)=>{let s=r[e];if(s)return{[n+t]:s}}))}getSelectors(){return this.getType("selectors")}getActions(){let e=this.getType("actions");return(0,L.Ay)(e,(e=>(0,L.Q2)(e,((e,t)=>{if((0,L.LQ)(e))return{[t]:e}}))))}getWrappedAndBoundActions(e){var t=this;let r=this.getBoundActions(e);return(0,L.Ay)(r,((e,r)=>{let n=this.system.statePlugins[E()(r).call(r,0,-7)].wrapActions;return n?(0,L.Ay)(e,((e,r)=>{let s=n[r];return s?(C()(s)||(s=[s]),x()(s).call(s,((e,r)=>{let n=function(){return r(e,t.getSystem())(...arguments)};if(!(0,L.LQ)(n))throw new TypeError("wrapActions needs to return a function that returns a new function (ie the wrapped action)");return F(n)}),e||Function.prototype)):e})):e}))}getWrappedAndBoundSelectors(e,t){var r=this;let n=this.getBoundSelectors(e,t);return(0,L.Ay)(n,((t,n)=>{let s=[E()(n).call(n,0,-9)],a=this.system.statePlugins[s].wrapSelectors;return a?(0,L.Ay)(t,((t,n)=>{let o=a[n];return o?(C()(o)||(o=[o]),x()(o).call(o,((t,n)=>{let a=function(){for(var a=arguments.length,o=new Array(a),l=0;l<a;l++)o[l]=arguments[l];return n(t,r.getSystem())(e().getIn(s),...o)};if(!(0,L.LQ)(a))throw new TypeError("wrapSelector needs to return a function that returns a new function (ie the wrapped action)");return a}),t||Function.prototype)):t})):t}))}getStates(e){var t;return x()(t=c()(this.system.statePlugins)).call(t,((t,r)=>(t[r]=e.get(r),t)),{})}getStateThunks(e){var t;return x()(t=c()(this.system.statePlugins)).call(t,((t,r)=>(t[r]=()=>e().get(r),t)),{})}getFn(){return{fn:this.system.fn}}getComponents(e){const t=this.system.components[e];return C()(t)?x()(t).call(t,((e,t)=>t(e,this.getSystem()))):void 0!==e?this.system.components[e]:this.system.components}getBoundSelectors(e,t){return(0,L.Ay)(this.getSelectors(),((r,n)=>{let s=[E()(n).call(n,0,-9)];const a=()=>e().getIn(s);return(0,L.Ay)(r,(e=>function(){for(var r=arguments.length,n=new Array(r),s=0;s<r;s++)n[s]=arguments[s];let o=F(e).apply(null,[a(),...n]);return"function"==typeof o&&(o=F(o)(t())),o}))}))}getBoundActions(e){e=e||this.getStore().dispatch;const t=this.getActions(),r=e=>"function"!=typeof e?(0,L.Ay)(e,(e=>r(e))):function(){var t=null;try{t=e(...arguments)}catch(e){t={type:V.NEW_THROWN_ERR,error:!0,payload:(0,O.serializeError)(e)}}finally{return t}};return(0,L.Ay)(t,(t=>(0,R.bindActionCreators)(r(t),e)))}getMapStateToProps(){return()=>y()({},this.getSystem())}getMapDispatchToProps(e){return t=>m()({},this.getWrappedAndBoundActions(t),this.getFn(),e)}}function B(e,t,r){if((0,L.Kn)(e)&&!(0,L.kJ)(e))return j()({},e);if((0,L.Wl)(e))return B(e(t),t,r);if((0,L.kJ)(e)){var n;const s="chain"===r.pluginLoadType?t.getComponents():{};return x()(n=_()(e).call(e,(e=>B(e,t,r)))).call(n,J,s)}return{}}function $(e,t){let{hasLoaded:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=r;return(0,L.Kn)(e)&&!(0,L.kJ)(e)&&"function"==typeof e.afterLoad&&(n=!0,F(e.afterLoad).call(this,t)),(0,L.Wl)(e)?$.call(this,e(t),t,{hasLoaded:n}):(0,L.kJ)(e)?_()(e).call(e,(e=>$.call(this,e,t,{hasLoaded:n}))):n}function J(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(0,L.Kn)(e))return{};if(!(0,L.Kn)(t))return e;t.wrapComponents&&((0,L.Ay)(t.wrapComponents,((r,n)=>{const s=e.components&&e.components[n];s&&C()(s)?(e.components[n]=I()(s).call(s,[r]),delete t.wrapComponents[n]):s&&(e.components[n]=[s,r],delete t.wrapComponents[n])})),c()(t.wrapComponents).length||delete t.wrapComponents);const{statePlugins:r}=e;if((0,L.Kn)(r))for(let e in r){const a=r[e];if(!(0,L.Kn)(a))continue;const{wrapActions:o,wrapSelectors:l}=a;if((0,L.Kn)(o))for(let r in o){let s=o[r];var n;if(C()(s)||(s=[s],o[r]=s),t&&t.statePlugins&&t.statePlugins[e]&&t.statePlugins[e].wrapActions&&t.statePlugins[e].wrapActions[r])t.statePlugins[e].wrapActions[r]=I()(n=o[r]).call(n,t.statePlugins[e].wrapActions[r])}if((0,L.Kn)(l))for(let r in l){let n=l[r];var s;if(C()(n)||(n=[n],l[r]=n),t&&t.statePlugins&&t.statePlugins[e]&&t.statePlugins[e].wrapSelectors&&t.statePlugins[e].wrapSelectors[r])t.statePlugins[e].wrapSelectors[r]=I()(s=l[r]).call(s,t.statePlugins[e].wrapSelectors[r])}}return m()(e,t)}function F(e){let{logErrors:t=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return"function"!=typeof e?e:function(){try{for(var r=arguments.length,n=new Array(r),s=0;s<r;s++)n[s]=arguments[s];return e.call(this,...n)}catch(e){return t&&console.error(e),null}}}var W=r(7793),H=r(6821),K=r(7038),Z=r(3420),G=r(8883),Y=r(6575),X=r(9150),Q=r(8901),ee=r(3705),te=r(8525),re=r(8011),ne=r(1661),se=r(4980),ae=r(9978),oe=r(8560),le=r(9595),ie=r(1093),ce=r.n(ie),pe=r(8493),ue=r.n(pe),de=(r(580),r(8082),r(6765));class me extends N.PureComponent{constructor(e,t){super(e,t),ce()(this,"toggleShown",(()=>{let{layoutActions:e,tag:t,operationId:r,isShown:n}=this.props;const s=this.getResolvedSubtree();n||void 0!==s||this.requestResolvedSubtree(),e.show(["operations",t,r],!n)})),ce()(this,"onCancelClick",(()=>{this.setState({tryItOutEnabled:!this.state.tryItOutEnabled})})),ce()(this,"onTryoutClick",(()=>{this.setState({tryItOutEnabled:!this.state.tryItOutEnabled})})),ce()(this,"onResetClick",(e=>{const t=this.props.oas3Selectors.selectDefaultRequestBodyValue(...e);this.props.oas3Actions.setRequestBodyValue({value:t,pathMethod:e})})),ce()(this,"onExecute",(()=>{this.setState({executeInProgress:!0})})),ce()(this,"getResolvedSubtree",(()=>{const{specSelectors:e,path:t,method:r,specPath:n}=this.props;return n?e.specResolvedSubtree(n.toJS()):e.specResolvedSubtree(["paths",t,r])})),ce()(this,"requestResolvedSubtree",(()=>{const{specActions:e,path:t,method:r,specPath:n}=this.props;return n?e.requestResolvedSubtree(n.toJS()):e.requestResolvedSubtree(["paths",t,r])}));const{tryItOutEnabled:r}=e.getConfigs();this.state={tryItOutEnabled:!0===r||"true"===r,executeInProgress:!1}}mapStateToProps(e,t){const{op:r,layoutSelectors:n,getConfigs:s}=t,{docExpansion:a,deepLinking:o,displayOperationId:l,displayRequestDuration:i,supportedSubmitMethods:c}=s(),p=n.showSummary(),u=r.getIn(["operation","__originalOperationId"])||r.getIn(["operation","operationId"])||(0,de.opId)(r.get("operation"),t.path,t.method)||r.get("id"),d=["operations",t.tag,u],m=o&&"false"!==o,h=ue()(c).call(c,t.method)>=0&&(void 0===t.allowTryItOut?t.specSelectors.allowTryItOutFor(t.path,t.method):t.allowTryItOut),g=r.getIn(["operation","security"])||t.specSelectors.security();return{operationId:u,isDeepLinkingEnabled:m,showSummary:p,displayOperationId:l,displayRequestDuration:i,allowTryItOut:h,security:g,isAuthorized:t.authSelectors.isAuthorized(g),isShown:n.isShown(d,"full"===a),jumpToKey:`paths.${t.path}.${t.method}`,response:t.specSelectors.responseFor(t.path,t.method),request:t.specSelectors.requestFor(t.path,t.method)}}componentDidMount(){const{isShown:e}=this.props,t=this.getResolvedSubtree();e&&void 0===t&&this.requestResolvedSubtree()}UNSAFE_componentWillReceiveProps(e){const{response:t,isShown:r}=e,n=this.getResolvedSubtree();t!==this.props.response&&this.setState({executeInProgress:!1}),r&&void 0===n&&this.requestResolvedSubtree()}render(){let{op:e,tag:t,path:r,method:n,security:s,isAuthorized:a,operationId:o,showSummary:l,isShown:i,jumpToKey:c,allowTryItOut:p,response:u,request:d,displayOperationId:m,displayRequestDuration:h,isDeepLinkingEnabled:g,specPath:f,specSelectors:y,specActions:v,getComponent:E,getConfigs:S,layoutSelectors:C,layoutActions:b,authActions:x,authSelectors:w,oas3Actions:_,oas3Selectors:A,fn:I}=this.props;const N=E("operation"),R=this.getResolvedSubtree()||(0,T.Map)(),P=(0,T.fromJS)({op:R,tag:t,path:r,summary:e.getIn(["operation","summary"])||"",deprecated:R.get("deprecated")||e.getIn(["operation","deprecated"])||!1,method:n,security:s,isAuthorized:a,operationId:o,originalOperationId:R.getIn(["operation","__originalOperationId"]),showSummary:l,isShown:i,jumpToKey:c,allowTryItOut:p,request:d,displayOperationId:m,displayRequestDuration:h,isDeepLinkingEnabled:g,executeInProgress:this.state.executeInProgress,tryItOutEnabled:this.state.tryItOutEnabled});return q().createElement(N,{operation:P,response:u,request:d,isShown:i,toggleShown:this.toggleShown,onTryoutClick:this.onTryoutClick,onResetClick:this.onResetClick,onCancelClick:this.onCancelClick,onExecute:this.onExecute,specPath:f,specActions:v,specSelectors:y,oas3Actions:_,oas3Selectors:A,layoutActions:b,layoutSelectors:C,authActions:x,authSelectors:w,getComponent:E,getConfigs:S,fn:I})}}ce()(me,"defaultProps",{showSummary:!0,response:null,allowTryItOut:!0,displayOperationId:!1,displayRequestDuration:!1});class he extends q().Component{getLayout(){let{getComponent:e,layoutSelectors:t}=this.props;const r=t.current(),n=e(r,!0);return n||(()=>q().createElement("h1",null,' No layout defined for "',r,'" '))}render(){const e=this.getLayout();return q().createElement(e,null)}}he.defaultProps={};class ge extends q().Component{constructor(){super(...arguments),ce()(this,"close",(()=>{let{authActions:e}=this.props;e.showDefinitions(!1)}))}render(){var e;let{authSelectors:t,authActions:r,getComponent:n,errSelectors:s,specSelectors:a,fn:{AST:o={}}}=this.props,l=t.shownDefinitions();const i=n("auths");return q().createElement("div",{className:"dialog-ux"},q().createElement("div",{className:"backdrop-ux"}),q().createElement("div",{className:"modal-ux"},q().createElement("div",{className:"modal-dialog-ux"},q().createElement("div",{className:"modal-ux-inner"},q().createElement("div",{className:"modal-ux-header"},q().createElement("h3",null,"Available authorizations"),q().createElement("button",{type:"button",className:"close-modal",onClick:this.close},q().createElement("svg",{width:"20",height:"20"},q().createElement("use",{href:"#close",xlinkHref:"#close"})))),q().createElement("div",{className:"modal-ux-content"},_()(e=l.valueSeq()).call(e,((e,l)=>q().createElement(i,{key:l,AST:o,definitions:e,getComponent:n,errSelectors:s,authSelectors:t,authActions:r,specSelectors:a}))))))))}}class fe extends q().Component{render(){let{isAuthorized:e,showPopup:t,onClick:r,getComponent:n}=this.props;const s=n("authorizationPopup",!0);return q().createElement("div",{className:"auth-wrapper"},q().createElement("button",{className:e?"btn authorize locked":"btn authorize unlocked",onClick:r},q().createElement("span",null,"Authorize"),q().createElement("svg",{width:"20",height:"20"},q().createElement("use",{href:e?"#locked":"#unlocked",xlinkHref:e?"#locked":"#unlocked"}))),t&&q().createElement(s,null))}}class ye extends q().Component{render(){const{authActions:e,authSelectors:t,specSelectors:r,getComponent:n}=this.props,s=r.securityDefinitions(),a=t.definitionsToAuthorize(),o=n("authorizeBtn");return s?q().createElement(o,{onClick:()=>e.showDefinitions(a),isAuthorized:!!t.authorized().size,showPopup:!!t.shownDefinitions(),getComponent:n}):null}}class ve extends q().Component{constructor(){super(...arguments),ce()(this,"onClick",(e=>{e.stopPropagation();let{onClick:t}=this.props;t&&t()}))}render(){let{isAuthorized:e}=this.props;return q().createElement("button",{className:e?"authorization__btn locked":"authorization__btn unlocked","aria-label":e?"authorization button locked":"authorization button unlocked",onClick:this.onClick},q().createElement("svg",{width:"20",height:"20"},q().createElement("use",{href:e?"#locked":"#unlocked",xlinkHref:e?"#locked":"#unlocked"})))}}class Ee extends q().Component{constructor(e,t){super(e,t),ce()(this,"onAuthChange",(e=>{let{name:t}=e;this.setState({[t]:e})})),ce()(this,"submitAuth",(e=>{e.preventDefault();let{authActions:t}=this.props;t.authorizeWithPersistOption(this.state)})),ce()(this,"logoutClick",(e=>{e.preventDefault();let{authActions:t,definitions:r}=this.props,n=_()(r).call(r,((e,t)=>t)).toArray();this.setState(x()(n).call(n,((e,t)=>(e[t]="",e)),{})),t.logoutWithPersistOption(n)})),ce()(this,"close",(e=>{e.preventDefault();let{authActions:t}=this.props;t.showDefinitions(!1)})),this.state={}}render(){var e;let{definitions:t,getComponent:r,authSelectors:n,errSelectors:s}=this.props;const a=r("AuthItem"),o=r("oauth2",!0),i=r("Button");let c=n.authorized(),p=l()(t).call(t,((e,t)=>!!c.get(t))),u=l()(t).call(t,(e=>"oauth2"!==e.get("type"))),d=l()(t).call(t,(e=>"oauth2"===e.get("type")));return q().createElement("div",{className:"auth-container"},!!u.size&&q().createElement("form",{onSubmit:this.submitAuth},_()(u).call(u,((e,t)=>q().createElement(a,{key:t,schema:e,name:t,getComponent:r,onAuthChange:this.onAuthChange,authorized:c,errSelectors:s}))).toArray(),q().createElement("div",{className:"auth-btn-wrapper"},u.size===p.size?q().createElement(i,{className:"btn modal-btn auth",onClick:this.logoutClick},"Logout"):q().createElement(i,{type:"submit",className:"btn modal-btn auth authorize"},"Authorize"),q().createElement(i,{className:"btn modal-btn auth btn-done",onClick:this.close},"Close"))),d&&d.size?q().createElement("div",null,q().createElement("div",{className:"scope-def"},q().createElement("p",null,"Scopes are used to grant an application different levels of access to data on behalf of the end user. Each API may declare one or more scopes."),q().createElement("p",null,"API requires the following scopes. Select which ones you want to grant to Swagger UI.")),_()(e=l()(t).call(t,(e=>"oauth2"===e.get("type")))).call(e,((e,t)=>q().createElement("div",{key:t},q().createElement(o,{authorized:c,schema:e,name:t})))).toArray()):null)}}class Se extends q().Component{render(){let{schema:e,name:t,getComponent:r,onAuthChange:n,authorized:s,errSelectors:a}=this.props;const o=r("apiKeyAuth"),l=r("basicAuth");let i;const c=e.get("type");switch(c){case"apiKey":i=q().createElement(o,{key:t,schema:e,name:t,errSelectors:a,authorized:s,getComponent:r,onChange:n});break;case"basic":i=q().createElement(l,{key:t,schema:e,name:t,errSelectors:a,authorized:s,getComponent:r,onChange:n});break;default:i=q().createElement("div",{key:t},"Unknown security definition type ",c)}return q().createElement("div",{key:`${t}-jump`},i)}}class Ce extends q().Component{render(){let{error:e}=this.props,t=e.get("level"),r=e.get("message"),n=e.get("source");return q().createElement("div",{className:"errors"},q().createElement("b",null,n," ",t),q().createElement("span",null,r))}}class be extends q().Component{constructor(e,t){super(e,t),ce()(this,"onChange",(e=>{let{onChange:t}=this.props,r=e.target.value,n=y()({},this.state,{value:r});this.setState(n),t(n)}));let{name:r,schema:n}=this.props,s=this.getValue();this.state={name:r,schema:n,value:s}}getValue(){let{name:e,authorized:t}=this.props;return t&&t.getIn([e,"value"])}render(){var e,t;let{schema:r,getComponent:n,errSelectors:s,name:a}=this.props;const o=n("Input"),i=n("Row"),c=n("Col"),p=n("authError"),u=n("Markdown",!0),d=n("JumpToPath",!0);let m=this.getValue(),h=l()(e=s.allErrors()).call(e,(e=>e.get("authId")===a));return q().createElement("div",null,q().createElement("h4",null,q().createElement("code",null,a||r.get("name"))," (apiKey)",q().createElement(d,{path:["securityDefinitions",a]})),m&&q().createElement("h6",null,"Authorized"),q().createElement(i,null,q().createElement(u,{source:r.get("description")})),q().createElement(i,null,q().createElement("p",null,"Name: ",q().createElement("code",null,r.get("name")))),q().createElement(i,null,q().createElement("p",null,"In: ",q().createElement("code",null,r.get("in")))),q().createElement(i,null,q().createElement("label",null,"Value:"),m?q().createElement("code",null," ****** "):q().createElement(c,null,q().createElement(o,{type:"text",onChange:this.onChange,autoFocus:!0}))),_()(t=h.valueSeq()).call(t,((e,t)=>q().createElement(p,{error:e,key:t}))))}}class xe extends q().Component{constructor(e,t){super(e,t),ce()(this,"onChange",(e=>{let{onChange:t}=this.props,{value:r,name:n}=e.target,s=this.state.value;s[n]=r,this.setState({value:s}),t(this.state)}));let{schema:r,name:n}=this.props,s=this.getValue().username;this.state={name:n,schema:r,value:s?{username:s}:{}}}getValue(){let{authorized:e,name:t}=this.props;return e&&e.getIn([t,"value"])||{}}render(){var e,t;let{schema:r,getComponent:n,name:s,errSelectors:a}=this.props;const o=n("Input"),i=n("Row"),c=n("Col"),p=n("authError"),u=n("JumpToPath",!0),d=n("Markdown",!0);let m=this.getValue().username,h=l()(e=a.allErrors()).call(e,(e=>e.get("authId")===s));return q().createElement("div",null,q().createElement("h4",null,"Basic authorization",q().createElement(u,{path:["securityDefinitions",s]})),m&&q().createElement("h6",null,"Authorized"),q().createElement(i,null,q().createElement(d,{source:r.get("description")})),q().createElement(i,null,q().createElement("label",null,"Username:"),m?q().createElement("code",null," ",m," "):q().createElement(c,null,q().createElement(o,{type:"text",required:"required",name:"username",onChange:this.onChange,autoFocus:!0}))),q().createElement(i,null,q().createElement("label",null,"Password:"),m?q().createElement("code",null," ****** "):q().createElement(c,null,q().createElement(o,{autoComplete:"new-password",name:"password",type:"password",onChange:this.onChange}))),_()(t=h.valueSeq()).call(t,((e,t)=>q().createElement(p,{error:e,key:t}))))}}function we(e){const{example:t,showValue:r,getComponent:n,getConfigs:s}=e,a=n("Markdown",!0),o=n("highlightCode");return t?q().createElement("div",{className:"example"},t.get("description")?q().createElement("section",{className:"example__section"},q().createElement("div",{className:"example__section-header"},"Example Description"),q().createElement("p",null,q().createElement(a,{source:t.get("description")}))):null,r&&t.has("value")?q().createElement("section",{className:"example__section"},q().createElement("div",{className:"example__section-header"},"Example Value"),q().createElement(o,{getConfigs:s,value:(0,L.Pz)(t.get("value"))})):null):null}var _e=r(2611),Ae=r.n(_e);class Ie extends q().PureComponent{constructor(){var e;super(...arguments),e=this,ce()(this,"_onSelect",(function(t){let{isSyntheticChange:r=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"function"==typeof e.props.onSelect&&e.props.onSelect(t,{isSyntheticChange:r})})),ce()(this,"_onDomSelect",(e=>{if("function"==typeof this.props.onSelect){const t=e.target.selectedOptions[0].getAttribute("value");this._onSelect(t,{isSyntheticChange:!1})}})),ce()(this,"getCurrentExample",(()=>{const{examples:e,currentExampleKey:t}=this.props,r=e.get(t),n=e.keySeq().first(),s=e.get(n);return r||s||Ae()({})}))}componentDidMount(){const{onSelect:e,examples:t}=this.props;if("function"==typeof e){const e=t.first(),r=t.keyOf(e);this._onSelect(r,{isSyntheticChange:!0})}}UNSAFE_componentWillReceiveProps(e){const{currentExampleKey:t,examples:r}=e;if(r!==this.props.examples&&!r.has(t)){const e=r.first(),t=r.keyOf(e);this._onSelect(t,{isSyntheticChange:!0})}}render(){const{examples:e,currentExampleKey:t,isValueModified:r,isModifiedValueAvailable:n,showLabels:s}=this.props;return q().createElement("div",{className:"examples-select"},s?q().createElement("span",{className:"examples-select__section-label"},"Examples: "):null,q().createElement("select",{className:"examples-select-element",onChange:this._onDomSelect,value:n&&r?"__MODIFIED__VALUE__":t||""},n?q().createElement("option",{value:"__MODIFIED__VALUE__"},"[Modified value]"):null,_()(e).call(e,((e,t)=>q().createElement("option",{key:t,value:t},e.get("summary")||t))).valueSeq()))}}ce()(Ie,"defaultProps",{examples:P().Map({}),onSelect:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return console.log("DEBUG: ExamplesSelect was not given an onSelect callback",...t)},currentExampleKey:null,showLabels:!0});const Ne=e=>T.List.isList(e)?e:(0,L.Pz)(e);class qe extends q().PureComponent{constructor(e){var t;super(e),t=this,ce()(this,"_getStateForCurrentNamespace",(()=>{const{currentNamespace:e}=this.props;return(this.state[e]||(0,T.Map)()).toObject()})),ce()(this,"_setStateForCurrentNamespace",(e=>{const{currentNamespace:t}=this.props;return this._setStateForNamespace(t,e)})),ce()(this,"_setStateForNamespace",((e,t)=>{const r=(this.state[e]||(0,T.Map)()).mergeDeep(t);return this.setState({[e]:r})})),ce()(this,"_isCurrentUserInputSameAsExampleValue",(()=>{const{currentUserInputValue:e}=this.props;return this._getCurrentExampleValue()===e})),ce()(this,"_getValueForExample",((e,t)=>{const{examples:r}=t||this.props;return Ne((r||(0,T.Map)({})).getIn([e,"value"]))})),ce()(this,"_getCurrentExampleValue",(e=>{const{currentKey:t}=e||this.props;return this._getValueForExample(t,e||this.props)})),ce()(this,"_onExamplesSelect",(function(e){let{isSyntheticChange:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{onSelect:n,updateValue:s,currentUserInputValue:a,userHasEditedBody:o}=t.props,{lastUserEditedValue:l}=t._getStateForCurrentNamespace(),i=t._getValueForExample(e);if("__MODIFIED__VALUE__"===e)return s(Ne(l)),t._setStateForCurrentNamespace({isModifiedValueSelected:!0});if("function"==typeof n){for(var c=arguments.length,p=new Array(c>2?c-2:0),u=2;u<c;u++)p[u-2]=arguments[u];n(e,{isSyntheticChange:r},...p)}t._setStateForCurrentNamespace({lastDownstreamValue:i,isModifiedValueSelected:r&&o||!!a&&a!==i}),r||"function"==typeof s&&s(Ne(i))}));const r=this._getCurrentExampleValue();this.state={[e.currentNamespace]:(0,T.Map)({lastUserEditedValue:this.props.currentUserInputValue,lastDownstreamValue:r,isModifiedValueSelected:this.props.userHasEditedBody||this.props.currentUserInputValue!==r})}}componentWillUnmount(){this.props.setRetainRequestBodyValueFlag(!1)}UNSAFE_componentWillReceiveProps(e){const{currentUserInputValue:t,examples:r,onSelect:n,userHasEditedBody:s}=e,{lastUserEditedValue:a,lastDownstreamValue:o}=this._getStateForCurrentNamespace(),i=this._getValueForExample(e.currentKey,e),c=l()(r).call(r,(e=>e.get("value")===t||(0,L.Pz)(e.get("value"))===t));if(c.size){let t;t=c.has(e.currentKey)?e.currentKey:c.keySeq().first(),n(t,{isSyntheticChange:!0})}else t!==this.props.currentUserInputValue&&t!==a&&t!==o&&(this.props.setRetainRequestBodyValueFlag(!0),this._setStateForNamespace(e.currentNamespace,{lastUserEditedValue:e.currentUserInputValue,isModifiedValueSelected:s||t!==i}))}render(){const{currentUserInputValue:e,examples:t,currentKey:r,getComponent:n,userHasEditedBody:s}=this.props,{lastDownstreamValue:a,lastUserEditedValue:o,isModifiedValueSelected:l}=this._getStateForCurrentNamespace(),i=n("ExamplesSelect");return q().createElement(i,{examples:t,currentExampleKey:r,onSelect:this._onExamplesSelect,isModifiedValueAvailable:!!o&&o!==a,isValueModified:void 0!==e&&l&&e!==this._getCurrentExampleValue()||s})}}ce()(qe,"defaultProps",{userHasEditedBody:!1,examples:(0,T.Map)({}),currentNamespace:"__DEFAULT__NAMESPACE__",setRetainRequestBodyValueFlag:()=>{},onSelect:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return console.log("ExamplesSelectValueRetainer: no `onSelect` function was provided",...t)},updateValue:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return console.log("ExamplesSelectValueRetainer: no `updateValue` function was provided",...t)}});var Re=r(1733),Te=r.n(Re),Pe=r(874),ke=r.n(Pe),Oe=r(2605),Me=r.n(Oe),je=r(3883),Ve=r.n(je);class De extends q().Component{constructor(e,t){super(e,t),ce()(this,"close",(e=>{e.preventDefault();let{authActions:t}=this.props;t.showDefinitions(!1)})),ce()(this,"authorize",(()=>{let{authActions:e,errActions:t,getConfigs:r,authSelectors:n,oas3Selectors:s}=this.props,a=r(),o=n.getConfigs();t.clear({authId:name,type:"auth",source:"auth"}),function(e){let{auth:t,authActions:r,errActions:n,configs:s,authConfigs:a={},currentServer:o}=e,{schema:l,scopes:i,name:c,clientId:p}=t,u=l.get("flow"),d=[];switch(u){case"password":return void r.authorizePassword(t);case"application":case"clientCredentials":case"client_credentials":return void r.authorizeApplication(t);case"accessCode":case"authorizationCode":case"authorization_code":d.push("response_type=code");break;case"implicit":d.push("response_type=token")}"string"==typeof p&&d.push("client_id="+encodeURIComponent(p));let m=s.oauth2RedirectUrl;if(void 0===m)return void n.newAuthErr({authId:c,source:"validation",level:"error",message:"oauth2RedirectUrl configuration is not passed. Oauth2 authorization cannot be performed."});d.push("redirect_uri="+encodeURIComponent(m));let h=[];if(C()(i)?h=i:P().List.isList(i)&&(h=i.toArray()),h.length>0){let e=a.scopeSeparator||" ";d.push("scope="+encodeURIComponent(h.join(e)))}let g=(0,L.r3)(new Date);if(d.push("state="+encodeURIComponent(g)),void 0!==a.realm&&d.push("realm="+encodeURIComponent(a.realm)),("authorizationCode"===u||"authorization_code"===u||"accessCode"===u)&&a.usePkceWithAuthorizationCodeGrant){const e=(0,L.Uj)(),r=(0,L.Xb)(e);d.push("code_challenge="+r),d.push("code_challenge_method=S256"),t.codeVerifier=e}let{additionalQueryStringParams:f}=a;for(let e in f){var y;void 0!==f[e]&&d.push(_()(y=[e,f[e]]).call(y,encodeURIComponent).join("="))}const v=l.get("authorizationUrl");let E;E=o?Ve()((0,L.Nm)(v),o,!0).toString():(0,L.Nm)(v);let S,b=[E,d.join("&")].join(-1===ue()(v).call(v,"?")?"?":"&");S="implicit"===u?r.preAuthorizeImplicit:a.useBasicAuthenticationWithAccessCodeGrant?r.authorizeAccessCodeWithBasicAuthentication:r.authorizeAccessCodeWithFormParams,r.authPopup(b,{auth:t,state:g,redirectUrl:m,callback:S,errCb:n.newAuthErr})}({auth:this.state,currentServer:s.serverEffectiveValue(s.selectedServer()),authActions:e,errActions:t,configs:a,authConfigs:o})})),ce()(this,"onScopeChange",(e=>{var t,r;let{target:n}=e,{checked:s}=n,a=n.dataset.value;if(s&&-1===ue()(t=this.state.scopes).call(t,a)){var o;let e=I()(o=this.state.scopes).call(o,[a]);this.setState({scopes:e})}else if(!s&&ue()(r=this.state.scopes).call(r,a)>-1){var i;this.setState({scopes:l()(i=this.state.scopes).call(i,(e=>e!==a))})}})),ce()(this,"onInputChange",(e=>{let{target:{dataset:{name:t},value:r}}=e,n={[t]:r};this.setState(n)})),ce()(this,"selectScopes",(e=>{var t;e.target.dataset.all?this.setState({scopes:Te()(ke()(t=this.props.schema.get("allowedScopes")||this.props.schema.get("scopes")).call(t))}):this.setState({scopes:[]})})),ce()(this,"logout",(e=>{e.preventDefault();let{authActions:t,errActions:r,name:n}=this.props;r.clear({authId:n,type:"auth",source:"auth"}),t.logoutWithPersistOption([n])}));let{name:r,schema:n,authorized:s,authSelectors:a}=this.props,o=s&&s.get(r),i=a.getConfigs()||{},c=o&&o.get("username")||"",p=o&&o.get("clientId")||i.clientId||"",u=o&&o.get("clientSecret")||i.clientSecret||"",d=o&&o.get("passwordType")||"basic",m=o&&o.get("scopes")||i.scopes||[];"string"==typeof m&&(m=m.split(i.scopeSeparator||" ")),this.state={appName:i.appName,name:r,schema:n,scopes:m,clientId:p,clientSecret:u,username:c,password:"",passwordType:d}}render(){var e,t;let{schema:r,getComponent:n,authSelectors:s,errSelectors:a,name:o,specSelectors:i}=this.props;const c=n("Input"),p=n("Row"),u=n("Col"),d=n("Button"),m=n("authError"),h=n("JumpToPath",!0),g=n("Markdown",!0),f=n("InitializedInput"),{isOAS3:y}=i;let v=y()?r.get("openIdConnectUrl"):null;const E="implicit",S="password",C=y()?v?"authorization_code":"authorizationCode":"accessCode",b=y()?v?"client_credentials":"clientCredentials":"application";let x=!!(s.getConfigs()||{}).usePkceWithAuthorizationCodeGrant,w=r.get("flow"),A=w===C&&x?w+" with PKCE":w,I=r.get("allowedScopes")||r.get("scopes"),N=!!s.authorized().get(o),R=l()(e=a.allErrors()).call(e,(e=>e.get("authId")===o)),T=!l()(R).call(R,(e=>"validation"===e.get("source"))).size,P=r.get("description");return q().createElement("div",null,q().createElement("h4",null,o," (OAuth2, ",A,") ",q().createElement(h,{path:["securityDefinitions",o]})),this.state.appName?q().createElement("h5",null,"Application: ",this.state.appName," "):null,P&&q().createElement(g,{source:r.get("description")}),N&&q().createElement("h6",null,"Authorized"),v&&q().createElement("p",null,"OpenID Connect URL: ",q().createElement("code",null,v)),(w===E||w===C)&&q().createElement("p",null,"Authorization URL: ",q().createElement("code",null,r.get("authorizationUrl"))),(w===S||w===C||w===b)&&q().createElement("p",null,"Token URL:",q().createElement("code",null," ",r.get("tokenUrl"))),q().createElement("p",{className:"flow"},"Flow: ",q().createElement("code",null,A)),w!==S?null:q().createElement(p,null,q().createElement(p,null,q().createElement("label",{htmlFor:"oauth_username"},"username:"),N?q().createElement("code",null," ",this.state.username," "):q().createElement(u,{tablet:10,desktop:10},q().createElement("input",{id:"oauth_username",type:"text","data-name":"username",onChange:this.onInputChange,autoFocus:!0}))),q().createElement(p,null,q().createElement("label",{htmlFor:"oauth_password"},"password:"),N?q().createElement("code",null," ****** "):q().createElement(u,{tablet:10,desktop:10},q().createElement("input",{id:"oauth_password",type:"password","data-name":"password",onChange:this.onInputChange}))),q().createElement(p,null,q().createElement("label",{htmlFor:"password_type"},"Client credentials location:"),N?q().createElement("code",null," ",this.state.passwordType," "):q().createElement(u,{tablet:10,desktop:10},q().createElement("select",{id:"password_type","data-name":"passwordType",onChange:this.onInputChange},q().createElement("option",{value:"basic"},"Authorization header"),q().createElement("option",{value:"request-body"},"Request body"))))),(w===b||w===E||w===C||w===S)&&(!N||N&&this.state.clientId)&&q().createElement(p,null,q().createElement("label",{htmlFor:"client_id"},"client_id:"),N?q().createElement("code",null," ****** "):q().createElement(u,{tablet:10,desktop:10},q().createElement(f,{id:"client_id",type:"text",required:w===S,initialValue:this.state.clientId,"data-name":"clientId",onChange:this.onInputChange}))),(w===b||w===C||w===S)&&q().createElement(p,null,q().createElement("label",{htmlFor:"client_secret"},"client_secret:"),N?q().createElement("code",null," ****** "):q().createElement(u,{tablet:10,desktop:10},q().createElement(f,{id:"client_secret",initialValue:this.state.clientSecret,type:"password","data-name":"clientSecret",onChange:this.onInputChange}))),!N&&I&&I.size?q().createElement("div",{className:"scopes"},q().createElement("h2",null,"Scopes:",q().createElement("a",{onClick:this.selectScopes,"data-all":!0},"select all"),q().createElement("a",{onClick:this.selectScopes},"select none")),_()(I).call(I,((e,t)=>{var r;return q().createElement(p,{key:t},q().createElement("div",{className:"checkbox"},q().createElement(c,{"data-value":t,id:`${t}-${w}-checkbox-${this.state.name}`,disabled:N,checked:Me()(r=this.state.scopes).call(r,t),type:"checkbox",onChange:this.onScopeChange}),q().createElement("label",{htmlFor:`${t}-${w}-checkbox-${this.state.name}`},q().createElement("span",{className:"item"}),q().createElement("div",{className:"text"},q().createElement("p",{className:"name"},t),q().createElement("p",{className:"description"},e)))))})).toArray()):null,_()(t=R.valueSeq()).call(t,((e,t)=>q().createElement(m,{error:e,key:t}))),q().createElement("div",{className:"auth-btn-wrapper"},T&&(N?q().createElement(d,{className:"btn modal-btn auth authorize",onClick:this.logout},"Logout"):q().createElement(d,{className:"btn modal-btn auth authorize",onClick:this.authorize},"Authorize")),q().createElement(d,{className:"btn modal-btn auth btn-done",onClick:this.close},"Close")))}}class Le extends N.Component{constructor(){super(...arguments),ce()(this,"onClick",(()=>{let{specActions:e,path:t,method:r}=this.props;e.clearResponse(t,r),e.clearRequest(t,r)}))}render(){return q().createElement("button",{className:"btn btn-clear opblock-control__btn",onClick:this.onClick},"Clear")}}const Ue=e=>{let{headers:t}=e;return q().createElement("div",null,q().createElement("h5",null,"Response headers"),q().createElement("pre",{className:"microlight"},t))},ze=e=>{let{duration:t}=e;return q().createElement("div",null,q().createElement("h5",null,"Request duration"),q().createElement("pre",{className:"microlight"},t," ms"))};class Be extends q().Component{shouldComponentUpdate(e){return this.props.response!==e.response||this.props.path!==e.path||this.props.method!==e.method||this.props.displayRequestDuration!==e.displayRequestDuration}render(){const{response:e,getComponent:t,getConfigs:r,displayRequestDuration:n,specSelectors:s,path:a,method:o}=this.props,{showMutatedRequest:l,requestSnippetsEnabled:i}=r(),p=l?s.mutatedRequestFor(a,o):s.requestFor(a,o),u=e.get("status"),d=p.get("url"),m=e.get("headers").toJS(),h=e.get("notDocumented"),g=e.get("error"),f=e.get("text"),y=e.get("duration"),v=c()(m),E=m["content-type"]||m["Content-Type"],S=t("responseBody"),b=_()(v).call(v,(e=>{var t=C()(m[e])?m[e].join():m[e];return q().createElement("span",{className:"headerline",key:e}," ",e,": ",t," ")})),x=0!==b.length,w=t("Markdown",!0),A=t("RequestSnippets",!0),I=t("curl");return q().createElement("div",null,p&&(!0===i||"true"===i?q().createElement(A,{request:p}):q().createElement(I,{request:p,getConfigs:r})),d&&q().createElement("div",null,q().createElement("div",{className:"request-url"},q().createElement("h4",null,"Request URL"),q().createElement("pre",{className:"microlight"},d))),q().createElement("h4",null,"Server response"),q().createElement("table",{className:"responses-table live-responses-table"},q().createElement("thead",null,q().createElement("tr",{className:"responses-header"},q().createElement("td",{className:"col_header response-col_status"},"Code"),q().createElement("td",{className:"col_header response-col_description"},"Details"))),q().createElement("tbody",null,q().createElement("tr",{className:"response"},q().createElement("td",{className:"response-col_status"},u,h?q().createElement("div",{className:"response-undocumented"},q().createElement("i",null," Undocumented ")):null),q().createElement("td",{className:"response-col_description"},g?q().createElement(w,{source:`${""!==e.get("name")?`${e.get("name")}: `:""}${e.get("message")}`}):null,f?q().createElement(S,{content:f,contentType:E,url:d,headers:m,getConfigs:r,getComponent:t}):null,x?q().createElement(Ue,{headers:b}):null,n&&y?q().createElement(ze,{duration:y}):null)))))}}var $e=r(5623);const Je=["get","put","post","delete","options","head","patch"],Fe=I()(Je).call(Je,["trace"]);class We extends q().Component{constructor(){super(...arguments),ce()(this,"renderOperationTag",((e,t)=>{const{specSelectors:r,getComponent:n,oas3Selectors:s,layoutSelectors:a,layoutActions:o,getConfigs:l}=this.props,i=n("OperationContainer",!0),c=n("OperationTag"),p=e.get("operations");return q().createElement(c,{key:"operation-"+t,tagObj:e,tag:t,oas3Selectors:s,layoutSelectors:a,layoutActions:o,getConfigs:l,getComponent:n,specUrl:r.url()},q().createElement("div",{className:"operation-tag-content"},_()(p).call(p,(e=>{const n=e.get("path"),s=e.get("method"),a=P().List(["paths",n,s]),o=r.isOAS3()?Fe:Je;return-1===ue()(o).call(o,s)?null:q().createElement(i,{key:`${n}-${s}`,specPath:a,op:e,path:n,method:s,tag:t})})).toArray()))}))}render(){let{specSelectors:e}=this.props;const t=e.taggedOperations();return 0===t.size?q().createElement("h3",null," No operations defined in spec!"):q().createElement("div",null,_()(t).call(t,this.renderOperationTag).toArray(),t.size<1?q().createElement("h3",null," No operations defined in spec! "):null)}}var He=r(9478),Ke=r.n(He);function Ze(e){return e.match(/^(?:[a-z]+:)?\/\//i)}function Ge(e,t){return e?Ze(e)?(r=e).match(/^\/\//i)?`${window.location.protocol}${r}`:r:new(Ke())(e,t).href:t;var r}function Ye(e,t){let{selectedServer:r=""}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!e)return;if(Ze(e))return e;const n=Ge(r,t);return Ze(n)?new(Ke())(e,n).href:new(Ke())(e,window.location.href).href}function Xe(e,t){let{selectedServer:r=""}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{return Ye(e,t,{selectedServer:r})}catch{return}}class Qe extends q().Component{render(){const{tagObj:e,tag:t,children:r,oas3Selectors:n,layoutSelectors:s,layoutActions:a,getConfigs:o,getComponent:l,specUrl:i}=this.props;let{docExpansion:c,deepLinking:p}=o();const u=p&&"false"!==p,d=l("Collapse"),m=l("Markdown",!0),h=l("DeepLink"),g=l("Link");let f,y=e.getIn(["tagDetails","description"],null),v=e.getIn(["tagDetails","externalDocs","description"]),E=e.getIn(["tagDetails","externalDocs","url"]);f=(0,L.Wl)(n)&&(0,L.Wl)(n.selectedServer)?Xe(E,i,{selectedServer:n.selectedServer()}):E;let S=["operations-tag",t],C=s.isShown(S,"full"===c||"list"===c);return q().createElement("div",{className:C?"opblock-tag-section is-open":"opblock-tag-section"},q().createElement("h3",{onClick:()=>a.show(S,!C),className:y?"opblock-tag":"opblock-tag no-desc",id:_()(S).call(S,(e=>(0,L.J6)(e))).join("-"),"data-tag":t,"data-is-open":C},q().createElement(h,{enabled:u,isShown:C,path:(0,L.oJ)(t),text:t}),y?q().createElement("small",null,q().createElement(m,{source:y})):q().createElement("small",null),f?q().createElement("div",{className:"info__externaldocs"},q().createElement("small",null,q().createElement(g,{href:(0,L.Nm)(f),onClick:e=>e.stopPropagation(),target:"_blank"},v||f))):null,q().createElement("button",{"aria-expanded":C,className:"expand-operation",title:C?"Collapse operation":"Expand operation",onClick:()=>a.show(S,!C)},q().createElement("svg",{className:"arrow",width:"20",height:"20","aria-hidden":"true",focusable:"false"},q().createElement("use",{href:C?"#large-arrow-up":"#large-arrow-down",xlinkHref:C?"#large-arrow-up":"#large-arrow-down"})))),q().createElement(d,{isOpened:C},r))}}ce()(Qe,"defaultProps",{tagObj:P().fromJS({}),tag:""});class et extends N.PureComponent{render(){let{specPath:e,response:t,request:n,toggleShown:s,onTryoutClick:a,onResetClick:o,onCancelClick:l,onExecute:i,fn:c,getComponent:p,getConfigs:u,specActions:d,specSelectors:m,authActions:h,authSelectors:g,oas3Actions:f,oas3Selectors:y}=this.props,v=this.props.operation,{deprecated:E,isShown:S,path:C,method:b,op:x,tag:w,operationId:A,allowTryItOut:I,displayRequestDuration:N,tryItOutEnabled:R,executeInProgress:T}=v.toJS(),{description:P,externalDocs:k,schemes:O}=x;const M=k?Xe(k.url,m.url(),{selectedServer:y.selectedServer()}):"";let j=v.getIn(["op"]),V=j.get("responses"),D=(0,L.gp)(j,["parameters"]),U=m.operationScheme(C,b),z=["operations",w,A],B=(0,L.nX)(j);const $=p("responses"),J=p("parameters"),F=p("execute"),W=p("clear"),H=p("Collapse"),K=p("Markdown",!0),Z=p("schemes"),G=p("OperationServers"),Y=p("OperationExt"),X=p("OperationSummary"),Q=p("Link"),{showExtensions:ee}=u();if(V&&t&&t.size>0){let e=!V.get(String(t.get("status")))&&!V.get("default");t=t.set("notDocumented",e)}let te=[C,b];const re=m.validationErrors([C,b]);return q().createElement("div",{className:E?"opblock opblock-deprecated":S?`opblock opblock-${b} is-open`:`opblock opblock-${b}`,id:(0,L.J6)(z.join("-"))},q().createElement(X,{operationProps:v,isShown:S,toggleShown:s,getComponent:p,authActions:h,authSelectors:g,specPath:e}),q().createElement(H,{isOpened:S},q().createElement("div",{className:"opblock-body"},j&&j.size||null===j?null:q().createElement("img",{height:"32px",width:"32px",src:r(2517),className:"opblock-loading-animation"}),E&&q().createElement("h4",{className:"opblock-title_normal"}," Warning: Deprecated"),P&&q().createElement("div",{className:"opblock-description-wrapper"},q().createElement("div",{className:"opblock-description"},q().createElement(K,{source:P}))),M?q().createElement("div",{className:"opblock-external-docs-wrapper"},q().createElement("h4",{className:"opblock-title_normal"},"Find more details"),q().createElement("div",{className:"opblock-external-docs"},k.description&&q().createElement("span",{className:"opblock-external-docs__description"},q().createElement(K,{source:k.description})),q().createElement(Q,{target:"_blank",className:"opblock-external-docs__link",href:(0,L.Nm)(M)},M))):null,j&&j.size?q().createElement(J,{parameters:D,specPath:e.push("parameters"),operation:j,onChangeKey:te,onTryoutClick:a,onResetClick:o,onCancelClick:l,tryItOutEnabled:R,allowTryItOut:I,fn:c,getComponent:p,specActions:d,specSelectors:m,pathMethod:[C,b],getConfigs:u,oas3Actions:f,oas3Selectors:y}):null,R?q().createElement(G,{getComponent:p,path:C,method:b,operationServers:j.get("servers"),pathServers:m.paths().getIn([C,"servers"]),getSelectedServer:y.selectedServer,setSelectedServer:f.setSelectedServer,setServerVariableValue:f.setServerVariableValue,getServerVariable:y.serverVariableValue,getEffectiveServerValue:y.serverEffectiveValue}):null,R&&I&&O&&O.size?q().createElement("div",{className:"opblock-schemes"},q().createElement(Z,{schemes:O,path:C,method:b,specActions:d,currentScheme:U})):null,!R||!I||re.length<=0?null:q().createElement("div",{className:"validation-errors errors-wrapper"},"Please correct the following validation errors and try again.",q().createElement("ul",null,_()(re).call(re,((e,t)=>q().createElement("li",{key:t}," ",e," "))))),q().createElement("div",{className:R&&t&&I?"btn-group":"execute-wrapper"},R&&I?q().createElement(F,{operation:j,specActions:d,specSelectors:m,oas3Selectors:y,oas3Actions:f,path:C,method:b,onExecute:i,disabled:T}):null,R&&t&&I?q().createElement(W,{specActions:d,path:C,method:b}):null),T?q().createElement("div",{className:"loading-container"},q().createElement("div",{className:"loading"})):null,V?q().createElement($,{responses:V,request:n,tryItOutResponse:t,getComponent:p,getConfigs:u,specSelectors:m,oas3Actions:f,oas3Selectors:y,specActions:d,produces:m.producesOptionsFor([C,b]),producesValue:m.currentProducesFor([C,b]),specPath:e.push("responses"),path:C,method:b,displayRequestDuration:N,fn:c}):null,ee&&B.size?q().createElement(Y,{extensions:B,getComponent:p}):null)))}}ce()(et,"defaultProps",{operation:null,response:null,request:null,specPath:(0,T.List)(),summary:""});const tt=require("lodash/toString");var rt=r.n(tt);class nt extends N.PureComponent{render(){let{isShown:e,toggleShown:t,getComponent:r,authActions:n,authSelectors:s,operationProps:a,specPath:o}=this.props,{summary:l,isAuthorized:i,method:c,op:p,showSummary:u,path:d,operationId:m,originalOperationId:h,displayOperationId:g}=a.toJS(),{summary:f}=p,y=a.get("security");const v=r("authorizeOperationBtn"),E=r("OperationSummaryMethod"),S=r("OperationSummaryPath"),C=r("JumpToPath",!0),b=r("CopyToClipboardBtn",!0),x=y&&!!y.count(),w=x&&1===y.size&&y.first().isEmpty(),_=!x||w;return q().createElement("div",{className:`opblock-summary opblock-summary-${c}`},q().createElement("button",{"aria-label":`${c} ${d.replace(/\//g,"​/")}`,"aria-expanded":e,className:"opblock-summary-control",onClick:t},q().createElement(E,{method:c}),q().createElement(S,{getComponent:r,operationProps:a,specPath:o}),u?q().createElement("div",{className:"opblock-summary-description"},rt()(f||l)):null,g&&(h||m)?q().createElement("span",{className:"opblock-summary-operation-id"},h||m):null,q().createElement("svg",{className:"arrow",width:"20",height:"20","aria-hidden":"true",focusable:"false"},q().createElement("use",{href:e?"#large-arrow-up":"#large-arrow-down",xlinkHref:e?"#large-arrow-up":"#large-arrow-down"}))),_?null:q().createElement(v,{isAuthorized:i,onClick:()=>{const e=s.definitionsForRequirements(y);n.showDefinitions(e)}}),q().createElement(b,{textToCopy:`${o.get(1)}`}),q().createElement(C,{path:o}))}}ce()(nt,"defaultProps",{operationProps:null,specPath:(0,T.List)(),summary:""});class st extends N.PureComponent{render(){let{method:e}=this.props;return q().createElement("span",{className:"opblock-summary-method"},e.toUpperCase())}}ce()(st,"defaultProps",{operationProps:null});const at=require("@babel/runtime-corejs3/core-js-stable/instance/splice");var ot=r.n(at);class lt extends N.PureComponent{render(){let{getComponent:e,operationProps:t}=this.props,{deprecated:r,isShown:n,path:s,tag:a,operationId:o,isDeepLinkingEnabled:l}=t.toJS();const i=s.split(/(?=\/)/g);for(let e=1;e<i.length;e+=2)ot()(i).call(i,e,0,q().createElement("wbr",{key:e}));const c=e("DeepLink");return q().createElement("span",{className:r?"opblock-summary-path__deprecated":"opblock-summary-path","data-path":s},q().createElement(c,{enabled:l,isShown:n,path:(0,L.oJ)(`${a}/${o}`),text:i}))}}const it=e=>{var t;let{extensions:r,getComponent:n}=e,s=n("OperationExtRow");return q().createElement("div",{className:"opblock-section"},q().createElement("div",{className:"opblock-section-header"},q().createElement("h4",null,"Extensions")),q().createElement("div",{className:"table-container"},q().createElement("table",null,q().createElement("thead",null,q().createElement("tr",null,q().createElement("td",{className:"col_header"},"Field"),q().createElement("td",{className:"col_header"},"Value"))),q().createElement("tbody",null,_()(t=r.entrySeq()).call(t,(e=>{let[t,r]=e;return q().createElement(s,{key:`${t}-${r}`,xKey:t,xVal:r})}))))))},ct=e=>{let{xKey:t,xVal:r}=e;const n=r?r.toJS?r.toJS():r:null;return q().createElement("tr",null,q().createElement("td",null,t),q().createElement("td",null,u()(n)))};var pt=r(4235),ut=r.n(pt),dt=r(9003),mt=r.n(dt),ht=r(6068),gt=r(1712),ft=r.n(gt),yt=r(5716),vt=r.n(yt);const Et=require("js-file-download");var St=r.n(Et),Ct=r(2807);const bt=e=>{let{value:t,fileName:r,className:n,downloadable:s,getConfigs:a,canCopy:o,language:i}=e;const c=vt()(a)?a():null,p=!1!==ft()(c,"syntaxHighlight")&&ft()(c,"syntaxHighlight.activated",!0),u=(0,N.useRef)(null);(0,N.useEffect)((()=>{var e;const t=l()(e=Te()(u.current.childNodes)).call(e,(e=>!!e.nodeType&&e.classList.contains("microlight")));return ut()(t).call(t,(e=>e.addEventListener("mousewheel",d,{passive:!1}))),()=>{ut()(t).call(t,(e=>e.removeEventListener("mousewheel",d)))}}),[t,n,i]);const d=e=>{const{target:t,deltaY:r}=e,{scrollHeight:n,offsetHeight:s,scrollTop:a}=t;n>s&&(0===a&&r<0||s+a>=n&&r>0)&&e.preventDefault()};return q().createElement("div",{className:"highlight-code",ref:u},s?q().createElement("div",{className:"download-contents",onClick:()=>{St()(t,r)}},"Download"):null,o&&q().createElement("div",{className:"copy-to-clipboard"},q().createElement(Ct.CopyToClipboard,{text:t},q().createElement("button",null))),p?q().createElement(ht.d3,{language:i,className:mt()(n,"microlight"),style:(0,ht.C2)(ft()(c,"syntaxHighlight.theme","agate"))},t):q().createElement("pre",{className:mt()(n,"microlight")},t))};bt.defaultProps={fileName:"response.txt"};const xt=bt;class wt extends q().Component{constructor(){super(...arguments),ce()(this,"onChangeProducesWrapper",(e=>this.props.specActions.changeProducesValue([this.props.path,this.props.method],e))),ce()(this,"onResponseContentTypeChange",(e=>{let{controlsAcceptHeader:t,value:r}=e;const{oas3Actions:n,path:s,method:a}=this.props;t&&n.setResponseContentType({value:r,path:s,method:a})}))}render(){var e;let{responses:t,tryItOutResponse:r,getComponent:n,getConfigs:s,specSelectors:a,fn:o,producesValue:l,displayRequestDuration:i,specPath:c,path:p,method:u,oas3Selectors:d,oas3Actions:m}=this.props,h=(0,L.iQ)(t);const g=n("contentType"),f=n("liveResponse"),y=n("response");let v=this.props.produces&&this.props.produces.size?this.props.produces:wt.defaultProps.produces;const E=a.isOAS3()?(0,L.QG)(t):null,S=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"_";return e.replace(/[^\w-]/g,t)}(`${u}${p}_responses`),C=`${S}_select`;return q().createElement("div",{className:"responses-wrapper"},q().createElement("div",{className:"opblock-section-header"},q().createElement("h4",null,"Responses"),a.isOAS3()?null:q().createElement("label",{htmlFor:C},q().createElement("span",null,"Response content type"),q().createElement(g,{value:l,ariaControls:S,ariaLabel:"Response content type",className:"execute-content-type",contentTypes:v,controlId:C,onChange:this.onChangeProducesWrapper}))),q().createElement("div",{className:"responses-inner"},r?q().createElement("div",null,q().createElement(f,{response:r,getComponent:n,getConfigs:s,specSelectors:a,path:this.props.path,method:this.props.method,displayRequestDuration:i}),q().createElement("h4",null,"Responses")):null,q().createElement("table",{"aria-live":"polite",className:"responses-table",id:S,role:"region"},q().createElement("thead",null,q().createElement("tr",{className:"responses-header"},q().createElement("td",{className:"col_header response-col_status"},"Code"),q().createElement("td",{className:"col_header response-col_description"},"Description"),a.isOAS3()?q().createElement("td",{className:"col col_header response-col_links"},"Links"):null)),q().createElement("tbody",null,_()(e=t.entrySeq()).call(e,(e=>{let[t,i]=e,g=r&&r.get("status")==t?"response_current":"";return q().createElement(y,{key:t,path:p,method:u,specPath:c.push(t),isDefault:h===t,fn:o,className:g,code:t,response:i,specSelectors:a,controlsAcceptHeader:i===E,onContentTypeChange:this.onResponseContentTypeChange,contentType:l,getConfigs:s,activeExamplesKey:d.activeExamplesMember(p,u,"responses",t),oas3Actions:m,getComponent:n})})).toArray()))))}}ce()(wt,"defaultProps",{tryItOutResponse:null,produces:(0,T.fromJS)(["application/json"]),displayRequestDuration:!1});const _t=require("@babel/runtime-corejs3/core-js-stable/instance/values");var At=r.n(_t),It=r(2518);class Nt extends q().Component{constructor(e,t){super(e,t),ce()(this,"_onContentTypeChange",(e=>{const{onContentTypeChange:t,controlsAcceptHeader:r}=this.props;this.setState({responseContentType:e}),t({value:e,controlsAcceptHeader:r})})),ce()(this,"getTargetExamplesKey",(()=>{const{response:e,contentType:t,activeExamplesKey:r}=this.props,n=this.state.responseContentType||t,s=e.getIn(["content",n],(0,T.Map)({})).get("examples",null).keySeq().first();return r||s})),this.state={responseContentType:""}}render(){var e,t;let{path:r,method:n,code:s,response:a,className:o,specPath:l,fn:i,getComponent:c,getConfigs:p,specSelectors:u,contentType:d,controlsAcceptHeader:m,oas3Actions:h}=this.props,{inferSchema:g}=i,f=u.isOAS3();const{showExtensions:y}=p();let v=y?(0,L.nX)(a):null,E=a.get("headers"),S=a.get("links");const C=c("ResponseExtension"),b=c("headers"),x=c("highlightCode"),w=c("modelExample"),A=c("Markdown",!0),I=c("operationLink"),N=c("contentType"),R=c("ExamplesSelect"),P=c("Example");var k,O;const M=this.state.responseContentType||d,j=a.getIn(["content",M],(0,T.Map)({})),V=j.get("examples",null);if(f){const e=j.get("schema");k=e?g(e.toJS()):null,O=e?(0,T.List)(["content",this.state.responseContentType,"schema"]):l}else k=a.get("schema"),O=a.has("schema")?l.push("schema"):l;let D,U,z=!1,B={includeReadOnly:!0};if(f){var $;if(U=null===($=j.get("schema"))||void 0===$?void 0:$.toJS(),V){const e=this.getTargetExamplesKey(),t=e=>e.get("value");D=t(V.get(e,(0,T.Map)({}))),void 0===D&&(D=t(At()(V).call(V).next().value)),z=!0}else void 0!==j.get("example")&&(D=j.get("example"),z=!0)}else{U=k,B={...B,includeWriteOnly:!0};const e=a.getIn(["examples",M]);e&&(D=e,z=!0)}let J=((e,t,r)=>{if(null!=e){let n=null;return(0,It.O)(e)&&(n="json"),q().createElement("div",null,q().createElement(t,{className:"example",getConfigs:r,language:n,value:(0,L.Pz)(e)}))}return null})((0,L.xi)(U,M,B,z?D:void 0),x,p);return q().createElement("tr",{className:"response "+(o||""),"data-code":s},q().createElement("td",{className:"response-col_status"},s),q().createElement("td",{className:"response-col_description"},q().createElement("div",{className:"response-col_description__inner"},q().createElement(A,{source:a.get("description")})),y&&v.size?_()(e=v.entrySeq()).call(e,(e=>{let[t,r]=e;return q().createElement(C,{key:`${t}-${r}`,xKey:t,xVal:r})})):null,f&&a.get("content")?q().createElement("section",{className:"response-controls"},q().createElement("div",{className:mt()("response-control-media-type",{"response-control-media-type--accept-controller":m})},q().createElement("small",{className:"response-control-media-type__title"},"Media type"),q().createElement(N,{value:this.state.responseContentType,contentTypes:a.get("content")?a.get("content").keySeq():(0,T.Seq)(),onChange:this._onContentTypeChange,ariaLabel:"Media Type"}),m?q().createElement("small",{className:"response-control-media-type__accept-message"},"Controls ",q().createElement("code",null,"Accept")," header."):null),V?q().createElement("div",{className:"response-control-examples"},q().createElement("small",{className:"response-control-examples__title"},"Examples"),q().createElement(R,{examples:V,currentExampleKey:this.getTargetExamplesKey(),onSelect:e=>h.setActiveExamplesMember({name:e,pathMethod:[r,n],contextType:"responses",contextName:s}),showLabels:!1})):null):null,J||k?q().createElement(w,{specPath:O,getComponent:c,getConfigs:p,specSelectors:u,schema:(0,L.oG)(k),example:J,includeReadOnly:!0}):null,f&&V?q().createElement(P,{example:V.get(this.getTargetExamplesKey(),(0,T.Map)({})),getComponent:c,getConfigs:p,omitValue:!0}):null,E?q().createElement(b,{headers:E,getComponent:c}):null),f?q().createElement("td",{className:"response-col_links"},S?_()(t=S.toSeq().entrySeq()).call(t,(e=>{let[t,r]=e;return q().createElement(I,{key:t,name:t,link:r,getComponent:c})})):q().createElement("i",null,"No links")):null)}}ce()(Nt,"defaultProps",{response:(0,T.fromJS)({}),onContentTypeChange:()=>{}});const qt=e=>{let{xKey:t,xVal:r}=e;return q().createElement("div",{className:"response__extension"},t,": ",String(r))},Rt=require("xml-but-prettier");var Tt=r.n(Rt);const Pt=require("lodash/toLower");var kt=r.n(Pt);class Ot extends q().PureComponent{constructor(){super(...arguments),ce()(this,"state",{parsedContent:null}),ce()(this,"updateParsedContent",(e=>{const{content:t}=this.props;if(e!==t)if(t&&t instanceof Blob){var r=new FileReader;r.onload=()=>{this.setState({parsedContent:r.result})},r.readAsText(t)}else this.setState({parsedContent:t.toString()})}))}componentDidMount(){this.updateParsedContent(null)}componentDidUpdate(e){this.updateParsedContent(e.content)}render(){let{content:e,contentType:t,url:r,headers:n={},getConfigs:s,getComponent:o}=this.props;const{parsedContent:l}=this.state,i=o("highlightCode"),c="response_"+(new Date).getTime();let p,d;if(r=r||"",/^application\/octet-stream/i.test(t)||n["Content-Disposition"]&&/attachment/i.test(n["Content-Disposition"])||n["content-disposition"]&&/attachment/i.test(n["content-disposition"])||n["Content-Description"]&&/File Transfer/i.test(n["Content-Description"])||n["content-description"]&&/File Transfer/i.test(n["content-description"]))if("Blob"in window){let s=t||"text/html",o=e instanceof Blob?e:new Blob([e],{type:s}),l=Ke().createObjectURL(o),i=[s,r.substr(a()(r).call(r,"/")+1),l].join(":"),c=n["content-disposition"]||n["Content-Disposition"];if(void 0!==c){let e=(0,L.DR)(c);null!==e&&(i=e)}d=D.Z.navigator&&D.Z.navigator.msSaveOrOpenBlob?q().createElement("div",null,q().createElement("a",{href:l,onClick:()=>D.Z.navigator.msSaveOrOpenBlob(o,i)},"Download file")):q().createElement("div",null,q().createElement("a",{href:l,download:i},"Download file"))}else d=q().createElement("pre",{className:"microlight"},"Download headers detected but your browser does not support downloading binary via XHR (Blob).");else if(/json/i.test(t)){let t=null;(0,It.O)(e)&&(t="json");try{p=u()(JSON.parse(e),null,"  ")}catch(t){p="can't parse JSON.  Raw result:\n\n"+e}d=q().createElement(i,{language:t,downloadable:!0,fileName:`${c}.json`,value:p,getConfigs:s,canCopy:!0})}else/xml/i.test(t)?(p=Tt()(e,{textNodesOnSameLine:!0,indentor:"  "}),d=q().createElement(i,{downloadable:!0,fileName:`${c}.xml`,value:p,getConfigs:s,canCopy:!0})):d="text/html"===kt()(t)||/text\/plain/.test(t)?q().createElement(i,{downloadable:!0,fileName:`${c}.html`,value:e,getConfigs:s,canCopy:!0}):"text/csv"===kt()(t)||/text\/csv/.test(t)?q().createElement(i,{downloadable:!0,fileName:`${c}.csv`,value:e,getConfigs:s,canCopy:!0}):/^image\//i.test(t)?Me()(t).call(t,"svg")?q().createElement("div",null," ",e," "):q().createElement("img",{src:Ke().createObjectURL(e)}):/^audio\//i.test(t)?q().createElement("pre",{className:"microlight"},q().createElement("audio",{controls:!0,key:r},q().createElement("source",{src:r,type:t}))):"string"==typeof e?q().createElement(i,{downloadable:!0,fileName:`${c}.txt`,value:e,getConfigs:s,canCopy:!0}):e.size>0?l?q().createElement("div",null,q().createElement("p",{className:"i"},"Unrecognized response type; displaying content as text."),q().createElement(i,{downloadable:!0,fileName:`${c}.txt`,value:l,getConfigs:s,canCopy:!0})):q().createElement("p",{className:"i"},"Unrecognized response type; unable to display."):null;return d?q().createElement("div",null,q().createElement("h5",null,"Response body"),d):null}}var Mt=r(9968),jt=r.n(Mt);class Vt extends N.Component{constructor(e){super(e),ce()(this,"onChange",((e,t,r)=>{let{specActions:{changeParamByIdentity:n},onChangeKey:s}=this.props;n(s,e,t,r)})),ce()(this,"onChangeConsumesWrapper",(e=>{let{specActions:{changeConsumesValue:t},onChangeKey:r}=this.props;t(r,e)})),ce()(this,"toggleTab",(e=>"parameters"===e?this.setState({parametersVisible:!0,callbackVisible:!1}):"callbacks"===e?this.setState({callbackVisible:!0,parametersVisible:!1}):void 0)),ce()(this,"onChangeMediaType",(e=>{let{value:t,pathMethod:r}=e,{specActions:n,oas3Selectors:s,oas3Actions:a}=this.props;const o=s.hasUserEditedBody(...r),l=s.shouldRetainRequestBodyValue(...r);a.setRequestContentType({value:t,pathMethod:r}),a.initRequestBodyValidateError({pathMethod:r}),o||(l||a.setRequestBodyValue({value:void 0,pathMethod:r}),n.clearResponse(...r),n.clearRequest(...r),n.clearValidateParams(r))})),this.state={callbackVisible:!1,parametersVisible:!0}}render(){var e;let{onTryoutClick:t,onResetClick:r,parameters:n,allowTryItOut:s,tryItOutEnabled:a,specPath:o,fn:l,getComponent:i,getConfigs:c,specSelectors:p,specActions:u,pathMethod:d,oas3Actions:m,oas3Selectors:h,operation:g}=this.props;const f=i("parameterRow"),y=i("TryItOutButton"),v=i("contentType"),S=i("Callbacks",!0),C=i("RequestBody",!0),b=a&&s,w=p.isOAS3(),A=g.get("requestBody"),N=x()(e=jt()(x()(n).call(n,((e,t)=>{const r=t.get("in");return e[r]??(e[r]=[]),e[r].push(t),e}),{}))).call(e,((e,t)=>I()(e).call(e,t)),[]);return q().createElement("div",{className:"opblock-section"},q().createElement("div",{className:"opblock-section-header"},w?q().createElement("div",{className:"tab-header"},q().createElement("div",{onClick:()=>this.toggleTab("parameters"),className:`tab-item ${this.state.parametersVisible&&"active"}`},q().createElement("h4",{className:"opblock-title"},q().createElement("span",null,"Parameters"))),g.get("callbacks")?q().createElement("div",{onClick:()=>this.toggleTab("callbacks"),className:`tab-item ${this.state.callbackVisible&&"active"}`},q().createElement("h4",{className:"opblock-title"},q().createElement("span",null,"Callbacks"))):null):q().createElement("div",{className:"tab-header"},q().createElement("h4",{className:"opblock-title"},"Parameters")),s?q().createElement(y,{isOAS3:p.isOAS3(),hasUserEditedBody:h.hasUserEditedBody(...d),enabled:a,onCancelClick:this.props.onCancelClick,onTryoutClick:t,onResetClick:()=>r(d)}):null),this.state.parametersVisible?q().createElement("div",{className:"parameters-container"},N.length?q().createElement("div",{className:"table-container"},q().createElement("table",{className:"parameters"},q().createElement("thead",null,q().createElement("tr",null,q().createElement("th",{className:"col_header parameters-col_name"},"Name"),q().createElement("th",{className:"col_header parameters-col_description"},"Description"))),q().createElement("tbody",null,_()(N).call(N,((e,t)=>q().createElement(f,{fn:l,specPath:o.push(t.toString()),getComponent:i,getConfigs:c,rawParam:e,param:p.parameterWithMetaByIdentity(d,e),key:`${e.get("in")}.${e.get("name")}`,onChange:this.onChange,onChangeConsumes:this.onChangeConsumesWrapper,specSelectors:p,specActions:u,oas3Actions:m,oas3Selectors:h,pathMethod:d,isExecute:b})))))):q().createElement("div",{className:"opblock-description-wrapper"},q().createElement("p",null,"No parameters"))):null,this.state.callbackVisible?q().createElement("div",{className:"callbacks-container opblock-description-wrapper"},q().createElement(S,{callbacks:(0,T.Map)(g.get("callbacks")),specPath:E()(o).call(o,0,-1).push("callbacks")})):null,w&&A&&this.state.parametersVisible&&q().createElement("div",{className:"opblock-section opblock-section-request-body"},q().createElement("div",{className:"opblock-section-header"},q().createElement("h4",{className:`opblock-title parameter__name ${A.get("required")&&"required"}`},"Request body"),q().createElement("label",null,q().createElement(v,{value:h.requestContentType(...d),contentTypes:A.get("content",(0,T.List)()).keySeq(),onChange:e=>{this.onChangeMediaType({value:e,pathMethod:d})},className:"body-param-content-type",ariaLabel:"Request content type"}))),q().createElement("div",{className:"opblock-description-wrapper"},q().createElement(C,{setRetainRequestBodyValueFlag:e=>m.setRetainRequestBodyValueFlag({value:e,pathMethod:d}),userHasEditedBody:h.hasUserEditedBody(...d),specPath:E()(o).call(o,0,-1).push("requestBody"),requestBody:A,requestBodyValue:h.requestBodyValue(...d),requestBodyInclusionSetting:h.requestBodyInclusionSetting(...d),requestBodyErrors:h.requestBodyErrors(...d),isExecute:b,getConfigs:c,activeExamplesKey:h.activeExamplesMember(...d,"requestBody","requestBody"),updateActiveExamplesKey:e=>{this.props.oas3Actions.setActiveExamplesMember({name:e,pathMethod:this.props.pathMethod,contextType:"requestBody",contextName:"requestBody"})},onChange:(e,t)=>{if(t){const r=h.requestBodyValue(...d),n=T.Map.isMap(r)?r:(0,T.Map)();return m.setRequestBodyValue({pathMethod:d,value:n.setIn(t,e)})}m.setRequestBodyValue({value:e,pathMethod:d})},onChangeIncludeEmpty:(e,t)=>{m.setRequestBodyInclusion({pathMethod:d,value:t,name:e})},contentType:h.requestContentType(...d)}))))}}ce()(Vt,"defaultProps",{onTryoutClick:Function.prototype,onCancelClick:Function.prototype,tryItOutEnabled:!1,allowTryItOut:!0,onChangeKey:[],specPath:[]});const Dt=e=>{let{xKey:t,xVal:r}=e;return q().createElement("div",{className:"parameter__extension"},t,": ",String(r))},Lt={onChange:()=>{},isIncludedOptions:{}};class Ut extends N.Component{constructor(){super(...arguments),ce()(this,"onCheckboxChange",(e=>{const{onChange:t}=this.props;t(e.target.checked)}))}componentDidMount(){const{isIncludedOptions:e,onChange:t}=this.props,{shouldDispatchInit:r,defaultValue:n}=e;r&&t(n)}render(){let{isIncluded:e,isDisabled:t}=this.props;return q().createElement("div",null,q().createElement("label",{className:mt()("parameter__empty_value_toggle",{disabled:t})},q().createElement("input",{type:"checkbox",disabled:t,checked:!t&&e,onChange:this.onCheckboxChange}),"Send empty value"))}}ce()(Ut,"defaultProps",Lt);var zt=r(9069);class Bt extends N.Component{constructor(e,t){var r;super(e,t),r=this,ce()(this,"onChangeWrapper",(function(e){let t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],{onChange:s,rawParam:a}=r.props;return t=""===e||e&&0===e.size?null:e,s(a,t,n)})),ce()(this,"_onExampleSelect",(e=>{this.props.oas3Actions.setActiveExamplesMember({name:e,pathMethod:this.props.pathMethod,contextType:"parameters",contextName:this.getParamKey()})})),ce()(this,"onChangeIncludeEmpty",(e=>{let{specActions:t,param:r,pathMethod:n}=this.props;const s=r.get("name"),a=r.get("in");return t.updateEmptyParamInclusion(n,s,a,e)})),ce()(this,"setDefaultValue",(()=>{let{specSelectors:e,pathMethod:t,rawParam:r,oas3Selectors:n}=this.props;const s=e.parameterWithMetaByIdentity(t,r)||(0,T.Map)(),{schema:a}=(0,zt.Z)(s,{isOAS3:e.isOAS3()}),o=s.get("content",(0,T.Map)()).keySeq().first(),l=a?(0,L.xi)(a.toJS(),o,{includeWriteOnly:!0}):null;if(s&&void 0===s.get("value")&&"body"!==s.get("in")){let r;if(e.isSwagger2())r=void 0!==s.get("x-example")?s.get("x-example"):void 0!==s.getIn(["schema","example"])?s.getIn(["schema","example"]):a&&a.getIn(["default"]);else if(e.isOAS3()){const e=n.activeExamplesMember(...t,"parameters",this.getParamKey());r=void 0!==s.getIn(["examples",e,"value"])?s.getIn(["examples",e,"value"]):void 0!==s.getIn(["content",o,"example"])?s.getIn(["content",o,"example"]):void 0!==s.get("example")?s.get("example"):void 0!==(a&&a.get("example"))?a&&a.get("example"):void 0!==(a&&a.get("default"))?a&&a.get("default"):s.get("default")}void 0===r||T.List.isList(r)||(r=(0,L.Pz)(r)),void 0!==r?this.onChangeWrapper(r):a&&"object"===a.get("type")&&l&&!s.get("examples")&&this.onChangeWrapper(T.List.isList(l)?l:(0,L.Pz)(l))}})),this.setDefaultValue()}UNSAFE_componentWillReceiveProps(e){let t,{specSelectors:r,pathMethod:n,rawParam:s}=e,a=r.isOAS3(),o=r.parameterWithMetaByIdentity(n,s)||new T.Map;if(o=o.isEmpty()?s:o,a){let{schema:e}=(0,zt.Z)(o,{isOAS3:a});t=e?e.get("enum"):void 0}else t=o?o.get("enum"):void 0;let l,i=o?o.get("value"):void 0;void 0!==i?l=i:s.get("required")&&t&&t.size&&(l=t.first()),void 0!==l&&l!==i&&this.onChangeWrapper((0,L.D$)(l)),this.setDefaultValue()}getParamKey(){const{param:e}=this.props;return e?`${e.get("name")}-${e.get("in")}`:null}render(){var e,t;let{param:r,rawParam:n,getComponent:s,getConfigs:a,isExecute:o,fn:l,onChangeConsumes:i,specSelectors:c,pathMethod:p,specPath:u,oas3Selectors:d}=this.props,m=c.isOAS3();const{showExtensions:h,showCommonExtensions:g}=a();if(r||(r=n),!n)return null;const f=s("JsonSchemaForm"),y=s("ParamBody");let v=r.get("in"),E="body"!==v?null:q().createElement(y,{getComponent:s,getConfigs:a,fn:l,param:r,consumes:c.consumesOptionsFor(p),consumesValue:c.contentTypeValues(p).get("requestContentType"),onChange:this.onChangeWrapper,onChangeConsumes:i,isExecute:o,specSelectors:c,pathMethod:p});const S=s("modelExample"),C=s("Markdown",!0),b=s("ParameterExt"),x=s("ParameterIncludeEmpty"),w=s("ExamplesSelectValueRetainer"),A=s("Example");let I,N,R,P,{schema:k}=(0,zt.Z)(r,{isOAS3:m}),O=c.parameterWithMetaByIdentity(p,n)||(0,T.Map)(),M=k?k.get("format"):null,j=k?k.get("type"):null,V=k?k.getIn(["items","type"]):null,U="formData"===v,z="FormData"in D.Z,B=r.get("required"),$=O?O.get("value"):"",J=g?(0,L.po)(k):null,F=h?(0,L.nX)(r):null,W=!1;return void 0!==r&&k&&(I=k.get("items")),void 0!==I?(N=I.get("enum"),R=I.get("default")):k&&(N=k.get("enum")),N&&N.size&&N.size>0&&(W=!0),void 0!==r&&(k&&(R=k.get("default")),void 0===R&&(R=r.get("default")),P=r.get("example"),void 0===P&&(P=r.get("x-example"))),q().createElement("tr",{"data-param-name":r.get("name"),"data-param-in":r.get("in")},q().createElement("td",{className:"parameters-col_name"},q().createElement("div",{className:B?"parameter__name required":"parameter__name"},r.get("name"),B?q().createElement("span",null," *"):null),q().createElement("div",{className:"parameter__type"},j,V&&`[${V}]`,M&&q().createElement("span",{className:"prop-format"},"($",M,")")),q().createElement("div",{className:"parameter__deprecated"},m&&r.get("deprecated")?"deprecated":null),q().createElement("div",{className:"parameter__in"},"(",r.get("in"),")"),g&&J.size?_()(e=J.entrySeq()).call(e,(e=>{let[t,r]=e;return q().createElement(b,{key:`${t}-${r}`,xKey:t,xVal:r})})):null,h&&F.size?_()(t=F.entrySeq()).call(t,(e=>{let[t,r]=e;return q().createElement(b,{key:`${t}-${r}`,xKey:t,xVal:r})})):null),q().createElement("td",{className:"parameters-col_description"},r.get("description")?q().createElement(C,{source:r.get("description")}):null,!E&&o||!W?null:q().createElement(C,{className:"parameter__enum",source:"<i>Available values</i> : "+_()(N).call(N,(function(e){return e})).toArray().join(", ")}),!E&&o||void 0===R?null:q().createElement(C,{className:"parameter__default",source:"<i>Default value</i> : "+R}),!E&&o||void 0===P?null:q().createElement(C,{source:"<i>Example</i> : "+P}),U&&!z&&q().createElement("div",null,"Error: your browser does not support FormData"),m&&r.get("examples")?q().createElement("section",{className:"parameter-controls"},q().createElement(w,{examples:r.get("examples"),onSelect:this._onExampleSelect,updateValue:this.onChangeWrapper,getComponent:s,defaultToFirstExample:!0,currentKey:d.activeExamplesMember(...p,"parameters",this.getParamKey()),currentUserInputValue:$})):null,E?null:q().createElement(f,{fn:l,getComponent:s,value:$,required:B,disabled:!o,description:r.get("name"),onChange:this.onChangeWrapper,errors:O.get("errors"),schema:k}),E&&k?q().createElement(S,{getComponent:s,specPath:u.push("schema"),getConfigs:a,isExecute:o,specSelectors:c,schema:k,example:E,includeWriteOnly:!0}):null,!E&&o&&r.get("allowEmptyValue")?q().createElement(x,{onChange:this.onChangeIncludeEmpty,isIncluded:c.parameterInclusionSettingFor(p,r.get("name"),r.get("in")),isDisabled:!(0,L.O2)($)}):null,m&&r.get("examples")?q().createElement(A,{example:r.getIn(["examples",d.activeExamplesMember(...p,"parameters",this.getParamKey())]),getComponent:s,getConfigs:a}):null))}}var $t=r(9300),Jt=r.n($t);class Ft extends N.Component{constructor(){super(...arguments),ce()(this,"handleValidateParameters",(()=>{let{specSelectors:e,specActions:t,path:r,method:n}=this.props;return t.validateParams([r,n]),e.validateBeforeExecute([r,n])})),ce()(this,"handleValidateRequestBody",(()=>{let{path:e,method:t,specSelectors:r,oas3Selectors:n,oas3Actions:s}=this.props,a={missingBodyValue:!1,missingRequiredKeys:[]};s.clearRequestBodyValidateError({path:e,method:t});let o=r.getOAS3RequiredRequestBodyContentType([e,t]),l=n.requestBodyValue(e,t),i=n.validateBeforeExecute([e,t]),c=n.requestContentType(e,t);if(!i)return a.missingBodyValue=!0,s.setRequestBodyValidateError({path:e,method:t,validationErrors:a}),!1;if(!o)return!0;let p=n.validateShallowRequired({oas3RequiredRequestBodyContentType:o,oas3RequestContentType:c,oas3RequestBodyValue:l});return!p||p.length<1||(ut()(p).call(p,(e=>{a.missingRequiredKeys.push(e)})),s.setRequestBodyValidateError({path:e,method:t,validationErrors:a}),!1)})),ce()(this,"handleValidationResultPass",(()=>{let{specActions:e,operation:t,path:r,method:n}=this.props;this.props.onExecute&&this.props.onExecute(),e.execute({operation:t,path:r,method:n})})),ce()(this,"handleValidationResultFail",(()=>{let{specActions:e,path:t,method:r}=this.props;e.clearValidateParams([t,r]),Jt()((()=>{e.validateParams([t,r])}),40)})),ce()(this,"handleValidationResult",(e=>{e?this.handleValidationResultPass():this.handleValidationResultFail()})),ce()(this,"onClick",(()=>{let e=this.handleValidateParameters(),t=this.handleValidateRequestBody(),r=e&&t;this.handleValidationResult(r)})),ce()(this,"onChangeProducesWrapper",(e=>this.props.specActions.changeProducesValue([this.props.path,this.props.method],e)))}render(){const{disabled:e}=this.props;return q().createElement("button",{className:"btn execute opblock-control__btn",onClick:this.onClick,disabled:e},"Execute")}}class Wt extends q().Component{render(){var e;let{headers:t,getComponent:r}=this.props;const n=r("Property"),s=r("Markdown",!0);return t&&t.size?q().createElement("div",{className:"headers-wrapper"},q().createElement("h4",{className:"headers__title"},"Headers:"),q().createElement("table",{className:"headers"},q().createElement("thead",null,q().createElement("tr",{className:"header-row"},q().createElement("th",{className:"header-col"},"Name"),q().createElement("th",{className:"header-col"},"Description"),q().createElement("th",{className:"header-col"},"Type"))),q().createElement("tbody",null,_()(e=t.entrySeq()).call(e,(e=>{let[t,r]=e;if(!P().Map.isMap(r))return null;const a=r.get("description"),o=r.getIn(["schema"])?r.getIn(["schema","type"]):r.getIn(["type"]),l=r.getIn(["schema","example"]);return q().createElement("tr",{key:t},q().createElement("td",{className:"header-col"},t),q().createElement("td",{className:"header-col"},a?q().createElement(s,{source:a}):null),q().createElement("td",{className:"header-col"},o," ",l?q().createElement(n,{propKey:"Example",propVal:l,propClass:"header-example"}):null))})).toArray()))):null}}class Ht extends q().Component{render(){let{editorActions:e,errSelectors:t,layoutSelectors:r,layoutActions:n,getComponent:s}=this.props;const a=s("Collapse");if(e&&e.jumpToLine)var o=e.jumpToLine;let i=t.allErrors(),c=l()(i).call(i,(e=>"thrown"===e.get("type")||"error"===e.get("level")));if(!c||c.count()<1)return null;let p=r.isShown(["errorPane"],!0),u=c.sortBy((e=>e.get("line")));return q().createElement("pre",{className:"errors-wrapper"},q().createElement("hgroup",{className:"error"},q().createElement("h4",{className:"errors__title"},"Errors"),q().createElement("button",{className:"btn errors__clear-btn",onClick:()=>n.show(["errorPane"],!p)},p?"Hide":"Show")),q().createElement(a,{isOpened:p,animated:!0},q().createElement("div",{className:"errors"},_()(u).call(u,((e,t)=>{let r=e.get("type");return"thrown"===r||"auth"===r?q().createElement(Kt,{key:t,error:e.get("error")||e,jumpToLine:o}):"spec"===r?q().createElement(Zt,{key:t,error:e,jumpToLine:o}):void 0})))))}}const Kt=e=>{let{error:t,jumpToLine:r}=e;if(!t)return null;let n=t.get("line");return q().createElement("div",{className:"error-wrapper"},t?q().createElement("div",null,q().createElement("h4",null,t.get("source")&&t.get("level")?Gt(t.get("source"))+" "+t.get("level"):"",t.get("path")?q().createElement("small",null," at ",t.get("path")):null),q().createElement("span",{className:"message thrown"},t.get("message")),q().createElement("div",{className:"error-line"},n&&r?q().createElement("a",{onClick:g()(r).call(r,null,n)},"Jump to line ",n):null)):null)},Zt=e=>{let{error:t,jumpToLine:r}=e,n=null;return t.get("path")?n=T.List.isList(t.get("path"))?q().createElement("small",null,"at ",t.get("path").join(".")):q().createElement("small",null,"at ",t.get("path")):t.get("line")&&!r&&(n=q().createElement("small",null,"on line ",t.get("line"))),q().createElement("div",{className:"error-wrapper"},t?q().createElement("div",null,q().createElement("h4",null,Gt(t.get("source"))+" "+t.get("level")," ",n),q().createElement("span",{className:"message"},t.get("message")),q().createElement("div",{className:"error-line"},r?q().createElement("a",{onClick:g()(r).call(r,null,t.get("line"))},"Jump to line ",t.get("line")):null)):null)};function Gt(e){var t;return _()(t=(e||"").split(" ")).call(t,(e=>e[0].toUpperCase()+E()(e).call(e,1))).join(" ")}Kt.defaultProps={jumpToLine:null};class Yt extends q().Component{constructor(){super(...arguments),ce()(this,"onChangeWrapper",(e=>this.props.onChange(e.target.value)))}componentDidMount(){this.props.contentTypes&&this.props.onChange(this.props.contentTypes.first())}UNSAFE_componentWillReceiveProps(e){var t;e.contentTypes&&e.contentTypes.size&&(Me()(t=e.contentTypes).call(t,e.value)||e.onChange(e.contentTypes.first()))}render(){let{ariaControls:e,ariaLabel:t,className:r,contentTypes:n,controlId:s,value:a}=this.props;return n&&n.size?q().createElement("div",{className:"content-type-wrapper "+(r||"")},q().createElement("select",{"aria-controls":e,"aria-label":t,className:"content-type",id:s,onChange:this.onChangeWrapper,value:a||""},_()(n).call(n,(e=>q().createElement("option",{key:e,value:e},e))).toArray())):null}}ce()(Yt,"defaultProps",{onChange:()=>{},value:null,contentTypes:(0,T.fromJS)(["application/json"])});var Xt=r(4250),Qt=r.n(Xt),er=r(7390),tr=r.n(er);function rr(){for(var e,t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return tr()(e=l()(r).call(r,(e=>!!e)).join(" ")).call(e)}class nr extends q().Component{render(){let{fullscreen:e,full:t,...r}=this.props;if(e)return q().createElement("section",r);let n="swagger-container"+(t?"-full":"");return q().createElement("section",Qt()({},r,{className:rr(r.className,n)}))}}const sr={mobile:"",tablet:"-tablet",desktop:"-desktop",large:"-hd"};class ar extends q().Component{render(){const{hide:e,keepContents:t,mobile:r,tablet:n,desktop:s,large:a,...o}=this.props;if(e&&!t)return q().createElement("span",null);let l=[];for(let e in sr){if(!Object.prototype.hasOwnProperty.call(sr,e))continue;let t=sr[e];if(e in this.props){let r=this.props[e];if(r<1){l.push("none"+t);continue}l.push("block"+t),l.push("col-"+r+t)}}e&&l.push("hidden");let i=rr(o.className,...l);return q().createElement("section",Qt()({},o,{className:i}))}}class or extends q().Component{render(){return q().createElement("div",Qt()({},this.props,{className:rr(this.props.className,"wrapper")}))}}class lr extends q().Component{render(){return q().createElement("button",Qt()({},this.props,{className:rr(this.props.className,"button")}))}}ce()(lr,"defaultProps",{className:""});const ir=e=>q().createElement("textarea",e),cr=e=>q().createElement("input",e);class pr extends q().Component{constructor(e,t){let r;super(e,t),ce()(this,"onChange",(e=>{let t,{onChange:r,multiple:n}=this.props,s=E()([]).call(e.target.options);var a;n?t=_()(a=l()(s).call(s,(function(e){return e.selected}))).call(a,(function(e){return e.value})):t=e.target.value;this.setState({value:t}),r&&r(t)})),r=e.value?e.value:e.multiple?[""]:"",this.state={value:r}}UNSAFE_componentWillReceiveProps(e){e.value!==this.props.value&&this.setState({value:e.value})}render(){var e,t;let{allowedValues:r,multiple:n,allowEmptyValue:s,disabled:a}=this.props,o=(null===(e=this.state.value)||void 0===e||null===(t=e.toJS)||void 0===t?void 0:t.call(e))||this.state.value;return q().createElement("select",{className:this.props.className,multiple:n,value:o,onChange:this.onChange,disabled:a},s?q().createElement("option",{value:""},"--"):null,_()(r).call(r,(function(e,t){return q().createElement("option",{key:t,value:String(e)},String(e))})))}}ce()(pr,"defaultProps",{multiple:!1,allowEmptyValue:!0});class ur extends q().Component{render(){return q().createElement("a",Qt()({},this.props,{rel:"noopener noreferrer",className:rr(this.props.className,"link")}))}}const dr=e=>{let{children:t}=e;return q().createElement("div",{className:"no-margin"}," ",t," ")};class mr extends q().Component{renderNotAnimated(){return this.props.isOpened?q().createElement(dr,null,this.props.children):q().createElement("noscript",null)}render(){let{animated:e,isOpened:t,children:r}=this.props;return e?(r=t?r:null,q().createElement(dr,null,r)):this.renderNotAnimated()}}ce()(mr,"defaultProps",{isOpened:!1,animated:!1});class hr extends q().Component{constructor(){var e;super(...arguments),this.setTagShown=g()(e=this._setTagShown).call(e,this)}_setTagShown(e,t){this.props.layoutActions.show(e,t)}showOp(e,t){let{layoutActions:r}=this.props;r.show(e,t)}render(){let{specSelectors:e,layoutSelectors:t,layoutActions:r,getComponent:n}=this.props,s=e.taggedOperations();const a=n("Collapse");return q().createElement("div",null,q().createElement("h4",{className:"overview-title"},"Overview"),_()(s).call(s,((e,n)=>{let s=e.get("operations"),o=["overview-tags",n],l=t.isShown(o,!0);return q().createElement("div",{key:"overview-"+n},q().createElement("h4",{onClick:()=>r.show(o,!l),className:"link overview-tag"}," ",l?"-":"+",n),q().createElement(a,{isOpened:l,animated:!0},_()(s).call(s,(e=>{let{path:n,method:s,id:a}=e.toObject(),o="operations",l=a,i=t.isShown([o,l]);return q().createElement(gr,{key:a,path:n,method:s,id:n+"-"+s,shown:i,showOpId:l,showOpIdPrefix:o,href:`#operation-${l}`,onClick:r.show})})).toArray()))})).toArray(),s.size<1&&q().createElement("h3",null," No operations defined in spec! "))}}class gr extends q().Component{constructor(e){var t;super(e),this.onClick=g()(t=this._onClick).call(t,this)}_onClick(){let{showOpId:e,showOpIdPrefix:t,onClick:r,shown:n}=this.props;r([t,e],!n)}render(){let{id:e,method:t,shown:r,href:n}=this.props;return q().createElement(ur,{href:n,onClick:this.onClick,className:"block opblock-link "+(r?"shown":"")},q().createElement("div",null,q().createElement("small",{className:`bold-label-${t}`},t.toUpperCase()),q().createElement("span",{className:"bold-label"},e)))}}class fr extends q().Component{componentDidMount(){this.props.initialValue&&(this.inputRef.value=this.props.initialValue)}render(){const{value:e,defaultValue:t,initialValue:r,...n}=this.props;return q().createElement("input",Qt()({},n,{ref:e=>this.inputRef=e}))}}class yr extends q().Component{render(){let{host:e,basePath:t}=this.props;return q().createElement("pre",{className:"base-url"},"[ Base URL: ",e,t," ]")}}class vr extends q().Component{render(){let{data:e,getComponent:t,selectedServer:r,url:n}=this.props,s=e.get("name")||"the developer",a=Xe(e.get("url"),n,{selectedServer:r}),o=e.get("email");const l=t("Link");return q().createElement("div",{className:"info__contact"},a&&q().createElement("div",null,q().createElement(l,{href:(0,L.Nm)(a),target:"_blank"},s," - Website")),o&&q().createElement(l,{href:(0,L.Nm)(`mailto:${o}`)},a?`Send email to ${s}`:`Contact ${s}`))}}class Er extends q().Component{render(){let{license:e,getComponent:t,selectedServer:r,url:n}=this.props;const s=t("Link");let a=e.get("name")||"License",o=Xe(e.get("url"),n,{selectedServer:r});return q().createElement("div",{className:"info__license"},o?q().createElement(s,{target:"_blank",href:(0,L.Nm)(o)},a):q().createElement("span",null,a))}}class Sr extends q().PureComponent{render(){const{url:e,getComponent:t}=this.props,r=t("Link");return q().createElement(r,{target:"_blank",href:(0,L.Nm)(e)},q().createElement("span",{className:"url"}," ",e))}}class Cr extends q().Component{render(){let{info:e,url:t,host:r,basePath:n,getComponent:s,externalDocs:a,selectedServer:o,url:l}=this.props,i=e.get("version"),c=e.get("description"),p=e.get("title"),u=Xe(e.get("termsOfService"),l,{selectedServer:o}),d=e.get("contact"),m=e.get("license"),h=Xe(a&&a.get("url"),l,{selectedServer:o}),g=a&&a.get("description");const f=s("Markdown",!0),y=s("Link"),v=s("VersionStamp"),E=s("InfoUrl"),S=s("InfoBasePath");return q().createElement("div",{className:"info"},q().createElement("hgroup",{className:"main"},q().createElement("h2",{className:"title"},p,i&&q().createElement(v,{version:i})),r||n?q().createElement(S,{host:r,basePath:n}):null,t&&q().createElement(E,{getComponent:s,url:t})),q().createElement("div",{className:"description"},q().createElement(f,{source:c})),u&&q().createElement("div",{className:"info__tos"},q().createElement(y,{target:"_blank",href:(0,L.Nm)(u)},"Terms of service")),d&&d.size?q().createElement(vr,{getComponent:s,data:d,selectedServer:o,url:t}):null,m&&m.size?q().createElement(Er,{getComponent:s,license:m,selectedServer:o,url:t}):null,h?q().createElement(y,{className:"info__extdocs",target:"_blank",href:(0,L.Nm)(h)},g||h):null)}}class br extends q().Component{render(){const{specSelectors:e,getComponent:t,oas3Selectors:r}=this.props,n=e.info(),s=e.url(),a=e.basePath(),o=e.host(),l=e.externalDocs(),i=r.selectedServer(),c=t("info");return q().createElement("div",null,n&&n.count()?q().createElement(c,{info:n,url:s,host:o,basePath:a,externalDocs:l,getComponent:t,selectedServer:i}):null)}}class xr extends q().Component{render(){return null}}class wr extends q().Component{render(){return q().createElement("div",{className:"view-line-link copy-to-clipboard",title:"Copy to clipboard"},q().createElement(Ct.CopyToClipboard,{text:this.props.textToCopy},q().createElement("svg",{width:"15",height:"16"},q().createElement("use",{href:"#copy",xlinkHref:"#copy"}))))}}class _r extends q().Component{render(){return q().createElement("div",{className:"footer"})}}class Ar extends q().Component{constructor(){super(...arguments),ce()(this,"onFilterChange",(e=>{const{target:{value:t}}=e;this.props.layoutActions.updateFilter(t)}))}render(){const{specSelectors:e,layoutSelectors:t,getComponent:r}=this.props,n=r("Col"),s="loading"===e.loadingStatus(),a="failed"===e.loadingStatus(),o=t.currentFilter(),l=["operation-filter-input"];return a&&l.push("failed"),s&&l.push("loading"),q().createElement("div",null,null===o||!1===o||"false"===o?null:q().createElement("div",{className:"filter-container"},q().createElement(n,{className:"filter wrapper",mobile:12},q().createElement("input",{className:l.join(" "),placeholder:"Filter by tag",type:"text",onChange:this.onFilterChange,value:!0===o||"true"===o?"":o,disabled:s}))))}}const Ir=Function.prototype;class Nr extends N.PureComponent{constructor(e,t){super(e,t),ce()(this,"updateValues",(e=>{let{param:t,isExecute:r,consumesValue:n=""}=e,s=/xml/i.test(n),a=/json/i.test(n),o=s?t.get("value_xml"):t.get("value");if(void 0!==o){let e=!o&&a?"{}":o;this.setState({value:e}),this.onChange(e,{isXml:s,isEditBox:r})}else s?this.onChange(this.sample("xml"),{isXml:s,isEditBox:r}):this.onChange(this.sample(),{isEditBox:r})})),ce()(this,"sample",(e=>{let{param:t,fn:{inferSchema:r}}=this.props,n=r(t.toJS());return(0,L.xi)(n,e,{includeWriteOnly:!0})})),ce()(this,"onChange",((e,t)=>{let{isEditBox:r,isXml:n}=t;this.setState({value:e,isEditBox:r}),this._onChange(e,n)})),ce()(this,"_onChange",((e,t)=>{(this.props.onChange||Ir)(e,t)})),ce()(this,"handleOnChange",(e=>{const{consumesValue:t}=this.props,r=/xml/i.test(t),n=e.target.value;this.onChange(n,{isXml:r,isEditBox:this.state.isEditBox})})),ce()(this,"toggleIsEditBox",(()=>this.setState((e=>({isEditBox:!e.isEditBox}))))),this.state={isEditBox:!1,value:""}}componentDidMount(){this.updateValues.call(this,this.props)}UNSAFE_componentWillReceiveProps(e){this.updateValues.call(this,e)}render(){let{onChangeConsumes:e,param:t,isExecute:r,specSelectors:n,pathMethod:s,getConfigs:a,getComponent:o}=this.props;const l=o("Button"),i=o("TextArea"),c=o("highlightCode"),p=o("contentType");let u=(n?n.parameterWithMetaByIdentity(s,t):t).get("errors",(0,T.List)()),d=n.contentTypeValues(s).get("requestContentType"),m=this.props.consumes&&this.props.consumes.size?this.props.consumes:Nr.defaultProp.consumes,{value:h,isEditBox:g}=this.state,f=null;return(0,It.O)(h)&&(f="json"),q().createElement("div",{className:"body-param","data-param-name":t.get("name"),"data-param-in":t.get("in")},g&&r?q().createElement(i,{className:"body-param__text"+(u.count()?" invalid":""),value:h,onChange:this.handleOnChange}):h&&q().createElement(c,{className:"body-param__example",language:f,getConfigs:a,value:h}),q().createElement("div",{className:"body-param-options"},r?q().createElement("div",{className:"body-param-edit"},q().createElement(l,{className:g?"btn cancel body-param__example-edit":"btn edit body-param__example-edit",onClick:this.toggleIsEditBox},g?"Cancel":"Edit")):null,q().createElement("label",{htmlFor:""},q().createElement("span",null,"Parameter content type"),q().createElement(p,{value:d,contentTypes:m,onChange:e,className:"body-param-content-type",ariaLabel:"Parameter content type"}))))}}ce()(Nr,"defaultProp",{consumes:(0,T.fromJS)(["application/json"]),param:(0,T.fromJS)({}),onChange:Ir,onChangeConsumes:Ir});var qr=r(8223);class Rr extends q().Component{render(){let{request:e,getConfigs:t}=this.props,r=(0,qr.requestSnippetGenerator_curl_bash)(e);const n=t(),s=ft()(n,"syntaxHighlight.activated")?q().createElement(ht.d3,{language:"bash",className:"curl microlight",style:(0,ht.C2)(ft()(n,"syntaxHighlight.theme"))},r):q().createElement("textarea",{readOnly:!0,className:"curl",value:r});return q().createElement("div",{className:"curl-command"},q().createElement("h4",null,"Curl"),q().createElement("div",{className:"copy-to-clipboard"},q().createElement(Ct.CopyToClipboard,{text:r},q().createElement("button",null))),q().createElement("div",null,s))}}class Tr extends q().Component{constructor(){super(...arguments),ce()(this,"onChange",(e=>{this.setScheme(e.target.value)})),ce()(this,"setScheme",(e=>{let{path:t,method:r,specActions:n}=this.props;n.setScheme(e,t,r)}))}UNSAFE_componentWillMount(){let{schemes:e}=this.props;this.setScheme(e.first())}UNSAFE_componentWillReceiveProps(e){var t;this.props.currentScheme&&Me()(t=e.schemes).call(t,this.props.currentScheme)||this.setScheme(e.schemes.first())}render(){var e;let{schemes:t,currentScheme:r}=this.props;return q().createElement("label",{htmlFor:"schemes"},q().createElement("span",{className:"schemes-title"},"Schemes"),q().createElement("select",{onChange:this.onChange,value:r},_()(e=t.valueSeq()).call(e,(e=>q().createElement("option",{value:e,key:e},e))).toArray()))}}class Pr extends q().Component{render(){const{specActions:e,specSelectors:t,getComponent:r}=this.props,n=t.operationScheme(),s=t.schemes(),a=r("schemes");return s&&s.size?q().createElement(a,{currentScheme:n,schemes:s,specActions:e}):null}}class kr extends N.Component{constructor(e,t){super(e,t),ce()(this,"toggleCollapsed",(()=>{this.props.onToggle&&this.props.onToggle(this.props.modelName,!this.state.expanded),this.setState({expanded:!this.state.expanded})})),ce()(this,"onLoad",(e=>{if(e&&this.props.layoutSelectors){const t=this.props.layoutSelectors.getScrollToKey();P().is(t,this.props.specPath)&&this.toggleCollapsed(),this.props.layoutActions.readyToScroll(this.props.specPath,e.parentElement)}}));let{expanded:r,collapsedContent:n}=this.props;this.state={expanded:r,collapsedContent:n||kr.defaultProps.collapsedContent}}componentDidMount(){const{hideSelfOnExpand:e,expanded:t,modelName:r}=this.props;e&&t&&this.props.onToggle(r,t)}UNSAFE_componentWillReceiveProps(e){this.props.expanded!==e.expanded&&this.setState({expanded:e.expanded})}render(){const{title:e,classes:t}=this.props;return this.state.expanded&&this.props.hideSelfOnExpand?q().createElement("span",{className:t||""},this.props.children):q().createElement("span",{className:t||"",ref:this.onLoad},q().createElement("button",{"aria-expanded":this.state.expanded,className:"model-box-control",onClick:this.toggleCollapsed},e&&q().createElement("span",{className:"pointer"},e),q().createElement("span",{className:"model-toggle"+(this.state.expanded?"":" collapsed")}),!this.state.expanded&&q().createElement("span",null,this.state.collapsedContent)),this.state.expanded&&this.props.children)}}ce()(kr,"defaultProps",{collapsedContent:"{...}",expanded:!1,title:null,onToggle:()=>{},hideSelfOnExpand:!1,specPath:P().List([])});var Or=r(185),Mr=r.n(Or);class jr extends q().Component{constructor(e,t){super(e,t),ce()(this,"activeTab",(e=>{let{target:{dataset:{name:t}}}=e;this.setState({activeTab:t})}));let{getConfigs:r,isExecute:n}=this.props,{defaultModelRendering:s}=r(),a=s;"example"!==s&&"model"!==s&&(a="example"),n&&(a="example"),this.state={activeTab:a}}UNSAFE_componentWillReceiveProps(e){e.isExecute&&!this.props.isExecute&&this.props.example&&this.setState({activeTab:"example"})}render(){let{getComponent:e,specSelectors:t,schema:r,example:n,isExecute:s,getConfigs:a,specPath:o,includeReadOnly:l,includeWriteOnly:i}=this.props,{defaultModelExpandDepth:c}=a();const p=e("ModelWrapper"),u=e("highlightCode"),d=Mr()(5).toString("base64"),m=Mr()(5).toString("base64"),h=Mr()(5).toString("base64"),g=Mr()(5).toString("base64");let f=t.isOAS3();return q().createElement("div",{className:"model-example"},q().createElement("ul",{className:"tab",role:"tablist"},q().createElement("li",{className:mt()("tabitem",{active:"example"===this.state.activeTab}),role:"presentation"},q().createElement("button",{"aria-controls":m,"aria-selected":"example"===this.state.activeTab,className:"tablinks","data-name":"example",id:d,onClick:this.activeTab,role:"tab"},s?"Edit Value":"Example Value")),r&&q().createElement("li",{className:mt()("tabitem",{active:"model"===this.state.activeTab}),role:"presentation"},q().createElement("button",{"aria-controls":g,"aria-selected":"model"===this.state.activeTab,className:mt()("tablinks",{inactive:s}),"data-name":"model",id:h,onClick:this.activeTab,role:"tab"},f?"Schema":"Model"))),"example"===this.state.activeTab&&q().createElement("div",{"aria-hidden":"example"!==this.state.activeTab,"aria-labelledby":d,"data-name":"examplePanel",id:m,role:"tabpanel",tabIndex:"0"},n||q().createElement(u,{value:"(no example available)",getConfigs:a})),"model"===this.state.activeTab&&q().createElement("div",{"aria-hidden":"example"===this.state.activeTab,"aria-labelledby":h,"data-name":"modelPanel",id:g,role:"tabpanel",tabIndex:"0"},q().createElement(p,{schema:r,getComponent:e,getConfigs:a,specSelectors:t,expandDepth:c,specPath:o,includeReadOnly:l,includeWriteOnly:i})))}}class Vr extends N.Component{constructor(){super(...arguments),ce()(this,"onToggle",((e,t)=>{this.props.layoutActions&&this.props.layoutActions.show(this.props.fullPath,t)}))}render(){let{getComponent:e,getConfigs:t}=this.props;const r=e("Model");let n;return this.props.layoutSelectors&&(n=this.props.layoutSelectors.isShown(this.props.fullPath)),q().createElement("div",{className:"model-box"},q().createElement(r,Qt()({},this.props,{getConfigs:t,expanded:n,depth:1,onToggle:this.onToggle,expandDepth:this.props.expandDepth||0})))}}var Dr=r(6024);class Lr extends N.Component{constructor(){super(...arguments),ce()(this,"getSchemaBasePath",(()=>this.props.specSelectors.isOAS3()?["components","schemas"]:["definitions"])),ce()(this,"getCollapsedContent",(()=>" ")),ce()(this,"handleToggle",((e,t)=>{const{layoutActions:r}=this.props;r.show([...this.getSchemaBasePath(),e],t),t&&this.props.specActions.requestResolvedSubtree([...this.getSchemaBasePath(),e])})),ce()(this,"onLoadModels",(e=>{e&&this.props.layoutActions.readyToScroll(this.getSchemaBasePath(),e)})),ce()(this,"onLoadModel",(e=>{if(e){const t=e.getAttribute("data-name");this.props.layoutActions.readyToScroll([...this.getSchemaBasePath(),t],e)}}))}render(){var e;let{specSelectors:t,getComponent:r,layoutSelectors:n,layoutActions:s,getConfigs:a}=this.props,o=t.definitions(),{docExpansion:l,defaultModelsExpandDepth:i}=a();if(!o.size||i<0)return null;const c=this.getSchemaBasePath();let p=n.isShown(c,i>0&&"none"!==l);const u=t.isOAS3(),d=r("ModelWrapper"),m=r("Collapse"),h=r("ModelCollapse"),g=r("JumpToPath",!0);return q().createElement("section",{className:p?"models is-open":"models",ref:this.onLoadModels},q().createElement("h4",null,q().createElement("button",{"aria-expanded":p,className:"models-control",onClick:()=>s.show(c,!p)},q().createElement("span",null,u?"Schemas":"Models"),q().createElement("svg",{width:"20",height:"20","aria-hidden":"true",focusable:"false"},q().createElement("use",{xlinkHref:p?"#large-arrow-up":"#large-arrow-down"})))),q().createElement(m,{isOpened:p},_()(e=o.entrySeq()).call(e,(e=>{let[o]=e;const l=[...c,o],p=P().List(l),u=t.specResolvedSubtree(l),m=t.specJson().getIn(l),f=T.Map.isMap(u)?u:P().Map(),y=T.Map.isMap(m)?m:P().Map(),v=f.get("title")||y.get("title")||o,E=n.isShown(l,!1);E&&0===f.size&&y.size>0&&this.props.specActions.requestResolvedSubtree(l);const S=q().createElement(d,{name:o,expandDepth:i,schema:f||P().Map(),displayName:v,fullPath:l,specPath:p,getComponent:r,specSelectors:t,getConfigs:a,layoutSelectors:n,layoutActions:s,includeReadOnly:!0,includeWriteOnly:!0}),C=q().createElement("span",{className:"model-box"},q().createElement("span",{className:"model model-title"},v));return q().createElement("div",{id:`model-${o}`,className:"model-container",key:`models-section-${o}`,"data-name":o,ref:this.onLoadModel},q().createElement("span",{className:"models-jump-to-path"},q().createElement(g,{specPath:p})),q().createElement(h,{classes:"model-box",collapsedContent:this.getCollapsedContent(o),onToggle:this.handleToggle,title:C,displayName:v,modelName:o,specPath:p,layoutSelectors:n,layoutActions:s,hideSelfOnExpand:!0,expanded:i>0&&E},S))})).toArray()))}}const Ur=e=>{let{value:t,getComponent:r}=e,n=r("ModelCollapse"),s=q().createElement("span",null,"Array [ ",t.count()," ]");return q().createElement("span",{className:"prop-enum"},"Enum:",q().createElement("br",null),q().createElement(n,{collapsedContent:s},"[ ",t.join(", ")," ]"))};class zr extends N.Component{render(){var e,t,r,n;let{schema:s,name:a,displayName:o,isRef:i,getComponent:c,getConfigs:p,depth:d,onToggle:m,expanded:h,specPath:g,...f}=this.props,{specSelectors:y,expandDepth:v,includeReadOnly:S,includeWriteOnly:C}=f;const{isOAS3:b}=y;if(!s)return null;const{showExtensions:x}=p();let w=s.get("description"),A=s.get("properties"),I=s.get("additionalProperties"),N=s.get("title")||o||a,R=s.get("required"),P=l()(s).call(s,((e,t)=>{var r;return-1!==ue()(r=["maxProperties","minProperties","nullable","example"]).call(r,t)})),k=s.get("deprecated"),O=s.getIn(["externalDocs","url"]),M=s.getIn(["externalDocs","description"]);const j=c("JumpToPath",!0),V=c("Markdown",!0),D=c("Model"),U=c("ModelCollapse"),z=c("Property"),B=c("Link"),$=()=>q().createElement("span",{className:"model-jump-to-path"},q().createElement(j,{specPath:g})),J=q().createElement("span",null,q().createElement("span",null,"{"),"...",q().createElement("span",null,"}"),i?q().createElement($,null):""),F=y.isOAS3()?s.get("anyOf"):null,W=y.isOAS3()?s.get("oneOf"):null,H=y.isOAS3()?s.get("not"):null,K=N&&q().createElement("span",{className:"model-title"},i&&s.get("$$ref")&&q().createElement("span",{className:"model-hint"},s.get("$$ref")),q().createElement("span",{className:"model-title__text"},N));return q().createElement("span",{className:"model"},q().createElement(U,{modelName:a,title:K,onToggle:m,expanded:!!h||d<=v,collapsedContent:J},q().createElement("span",{className:"brace-open object"},"{"),i?q().createElement($,null):null,q().createElement("span",{className:"inner-object"},q().createElement("table",{className:"model"},q().createElement("tbody",null,w?q().createElement("tr",{className:"description"},q().createElement("td",null,"description:"),q().createElement("td",null,q().createElement(V,{source:w}))):null,O&&q().createElement("tr",{className:"external-docs"},q().createElement("td",null,"externalDocs:"),q().createElement("td",null,q().createElement(B,{target:"_blank",href:(0,L.Nm)(O)},M||O))),k?q().createElement("tr",{className:"property"},q().createElement("td",null,"deprecated:"),q().createElement("td",null,"true")):null,A&&A.size?_()(e=l()(t=A.entrySeq()).call(t,(e=>{let[,t]=e;return(!t.get("readOnly")||S)&&(!t.get("writeOnly")||C)}))).call(e,(e=>{let[t,r]=e,n=b()&&r.get("deprecated"),s=T.List.isList(R)&&R.contains(t),o=["property-row"];return n&&o.push("deprecated"),s&&o.push("required"),q().createElement("tr",{key:t,className:o.join(" ")},q().createElement("td",null,t,s&&q().createElement("span",{className:"star"},"*")),q().createElement("td",null,q().createElement(D,Qt()({key:`object-${a}-${t}_${r}`},f,{required:s,getComponent:c,specPath:g.push("properties",t),getConfigs:p,schema:r,depth:d+1}))))})).toArray():null,x?q().createElement("tr",null,q().createElement("td",null," ")):null,x?_()(r=s.entrySeq()).call(r,(e=>{let[t,r]=e;if("x-"!==E()(t).call(t,0,2))return;const n=r?r.toJS?r.toJS():r:null;return q().createElement("tr",{key:t,className:"extension"},q().createElement("td",null,t),q().createElement("td",null,u()(n)))})).toArray():null,I&&I.size?q().createElement("tr",null,q().createElement("td",null,"< * >:"),q().createElement("td",null,q().createElement(D,Qt()({},f,{required:!1,getComponent:c,specPath:g.push("additionalProperties"),getConfigs:p,schema:I,depth:d+1})))):null,F?q().createElement("tr",null,q().createElement("td",null,"anyOf ->"),q().createElement("td",null,_()(F).call(F,((e,t)=>q().createElement("div",{key:t},q().createElement(D,Qt()({},f,{required:!1,getComponent:c,specPath:g.push("anyOf",t),getConfigs:p,schema:e,depth:d+1}))))))):null,W?q().createElement("tr",null,q().createElement("td",null,"oneOf ->"),q().createElement("td",null,_()(W).call(W,((e,t)=>q().createElement("div",{key:t},q().createElement(D,Qt()({},f,{required:!1,getComponent:c,specPath:g.push("oneOf",t),getConfigs:p,schema:e,depth:d+1}))))))):null,H?q().createElement("tr",null,q().createElement("td",null,"not ->"),q().createElement("td",null,q().createElement("div",null,q().createElement(D,Qt()({},f,{required:!1,getComponent:c,specPath:g.push("not"),getConfigs:p,schema:H,depth:d+1}))))):null))),q().createElement("span",{className:"brace-close"},"}")),P.size?_()(n=P.entrySeq()).call(n,(e=>{let[t,r]=e;return q().createElement(z,{key:`${t}-${r}`,propKey:t,propVal:r,propClass:"property"})})):null)}}class Br extends N.Component{render(){var e;let{getComponent:t,getConfigs:r,schema:n,depth:s,expandDepth:a,name:o,displayName:i,specPath:c}=this.props,p=n.get("description"),u=n.get("items"),d=n.get("title")||i||o,m=l()(n).call(n,((e,t)=>{var r;return-1===ue()(r=["type","items","description","$$ref","externalDocs"]).call(r,t)})),h=n.getIn(["externalDocs","url"]),g=n.getIn(["externalDocs","description"]);const f=t("Markdown",!0),y=t("ModelCollapse"),v=t("Model"),E=t("Property"),S=t("Link"),C=d&&q().createElement("span",{className:"model-title"},q().createElement("span",{className:"model-title__text"},d));return q().createElement("span",{className:"model"},q().createElement(y,{title:C,expanded:s<=a,collapsedContent:"[...]"},"[",m.size?_()(e=m.entrySeq()).call(e,(e=>{let[t,r]=e;return q().createElement(E,{key:`${t}-${r}`,propKey:t,propVal:r,propClass:"property"})})):null,p?q().createElement(f,{source:p}):m.size?q().createElement("div",{className:"markdown"}):null,h&&q().createElement("div",{className:"external-docs"},q().createElement(S,{target:"_blank",href:(0,L.Nm)(h)},g||h)),q().createElement("span",null,q().createElement(v,Qt()({},this.props,{getConfigs:r,specPath:c.push("items"),name:null,schema:u,required:!1,depth:s+1}))),"]"))}}const $r="property primitive";class Jr extends N.Component{render(){var e,t,r;let{schema:n,getComponent:s,getConfigs:a,name:o,displayName:i,depth:c,expandDepth:p}=this.props;const{showExtensions:u}=a();if(!n||!n.get)return q().createElement("div",null);let d=n.get("type"),m=n.get("format"),h=n.get("xml"),g=n.get("enum"),f=n.get("title")||i||o,y=n.get("description"),v=(0,L.nX)(n),E=l()(n).call(n,((e,t)=>{var r;return-1===ue()(r=["enum","type","format","description","$$ref","externalDocs"]).call(r,t)})).filterNot(((e,t)=>v.has(t))),S=n.getIn(["externalDocs","url"]),C=n.getIn(["externalDocs","description"]);const b=s("Markdown",!0),x=s("EnumModel"),w=s("Property"),A=s("ModelCollapse"),I=s("Link"),N=f&&q().createElement("span",{className:"model-title"},q().createElement("span",{className:"model-title__text"},f));return q().createElement("span",{className:"model"},q().createElement(A,{title:N,expanded:c<=p,collapsedContent:"[...]",hideSelfOnExpand:p!==c},q().createElement("span",{className:"prop"},o&&c>1&&q().createElement("span",{className:"prop-name"},f),q().createElement("span",{className:"prop-type"},d),m&&q().createElement("span",{className:"prop-format"},"($",m,")"),E.size?_()(e=E.entrySeq()).call(e,(e=>{let[t,r]=e;return q().createElement(w,{key:`${t}-${r}`,propKey:t,propVal:r,propClass:$r})})):null,u&&v.size?_()(t=v.entrySeq()).call(t,(e=>{let[t,r]=e;return q().createElement(w,{key:`${t}-${r}`,propKey:t,propVal:r,propClass:$r})})):null,y?q().createElement(b,{source:y}):null,S&&q().createElement("div",{className:"external-docs"},q().createElement(I,{target:"_blank",href:(0,L.Nm)(S)},C||S)),h&&h.size?q().createElement("span",null,q().createElement("br",null),q().createElement("span",{className:$r},"xml:"),_()(r=h.entrySeq()).call(r,(e=>{let[t,r]=e;return q().createElement("span",{key:`${t}-${r}`,className:$r},q().createElement("br",null),"   ",t,": ",String(r))})).toArray()):null,g&&q().createElement(x,{value:g,getComponent:s}))))}}const Fr=e=>{let{propKey:t,propVal:r,propClass:n}=e;return q().createElement("span",{className:n},q().createElement("br",null),t,": ",String(r))};class Wr extends q().Component{render(){const{onTryoutClick:e,onCancelClick:t,onResetClick:r,enabled:n,hasUserEditedBody:s,isOAS3:a}=this.props,o=a&&s;return q().createElement("div",{className:o?"try-out btn-group":"try-out"},n?q().createElement("button",{className:"btn try-out__btn cancel",onClick:t},"Cancel"):q().createElement("button",{className:"btn try-out__btn",onClick:e},"Try it out "),o&&q().createElement("button",{className:"btn try-out__btn reset",onClick:r},"Reset"))}}ce()(Wr,"defaultProps",{onTryoutClick:Function.prototype,onCancelClick:Function.prototype,onResetClick:Function.prototype,enabled:!1,hasUserEditedBody:!1,isOAS3:!1});class Hr extends q().PureComponent{render(){const{bypass:e,isSwagger2:t,isOAS3:r,alsoShow:n}=this.props;return e?q().createElement("div",null,this.props.children):t&&r?q().createElement("div",{className:"version-pragma"},n,q().createElement("div",{className:"version-pragma__message version-pragma__message--ambiguous"},q().createElement("div",null,q().createElement("h3",null,"Unable to render this definition"),q().createElement("p",null,q().createElement("code",null,"swagger")," and ",q().createElement("code",null,"openapi")," fields cannot be present in the same Swagger or OpenAPI definition. Please remove one of the fields."),q().createElement("p",null,"Supported version fields are ",q().createElement("code",null,"swagger: ",'"2.0"')," and those that match ",q().createElement("code",null,"openapi: 3.0.n")," (for example, ",q().createElement("code",null,"openapi: 3.0.0"),").")))):t||r?q().createElement("div",null,this.props.children):q().createElement("div",{className:"version-pragma"},n,q().createElement("div",{className:"version-pragma__message version-pragma__message--missing"},q().createElement("div",null,q().createElement("h3",null,"Unable to render this definition"),q().createElement("p",null,"The provided definition does not specify a valid version field."),q().createElement("p",null,"Please indicate a valid Swagger or OpenAPI version field. Supported version fields are ",q().createElement("code",null,"swagger: ",'"2.0"')," and those that match ",q().createElement("code",null,"openapi: 3.0.n")," (for example, ",q().createElement("code",null,"openapi: 3.0.0"),")."))))}}ce()(Hr,"defaultProps",{alsoShow:null,children:null,bypass:!1});const Kr=e=>{let{version:t}=e;return q().createElement("small",null,q().createElement("pre",{className:"version"}," ",t," "))},Zr=e=>{let{enabled:t,path:r,text:n}=e;return q().createElement("a",{className:"nostyle",onClick:t?e=>e.preventDefault():null,href:t?`#/${r}`:null},q().createElement("span",null,n))},Gr=()=>q().createElement("div",null,q().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",className:"svg-assets"},q().createElement("defs",null,q().createElement("symbol",{viewBox:"0 0 20 20",id:"unlocked"},q().createElement("path",{d:"M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"})),q().createElement("symbol",{viewBox:"0 0 20 20",id:"locked"},q().createElement("path",{d:"M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8zM12 8H8V5.199C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8z"})),q().createElement("symbol",{viewBox:"0 0 20 20",id:"close"},q().createElement("path",{d:"M14.348 14.849c-.469.469-1.229.469-1.697 0L10 11.819l-2.651 3.029c-.469.469-1.229.469-1.697 0-.469-.469-.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-.469-.469-.469-1.228 0-1.697.469-.469 1.228-.469 1.697 0L10 8.183l2.651-3.031c.469-.469 1.228-.469 1.697 0 .469.469.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c.469.469.469 1.229 0 1.698z"})),q().createElement("symbol",{viewBox:"0 0 20 20",id:"large-arrow"},q().createElement("path",{d:"M13.25 10L6.109 2.58c-.268-.27-.268-.707 0-.979.268-.27.701-.27.969 0l7.83 7.908c.268.271.268.709 0 .979l-7.83 7.908c-.268.271-.701.27-.969 0-.268-.269-.268-.707 0-.979L13.25 10z"})),q().createElement("symbol",{viewBox:"0 0 20 20",id:"large-arrow-down"},q().createElement("path",{d:"M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"})),q().createElement("symbol",{viewBox:"0 0 20 20",id:"large-arrow-up"},q().createElement("path",{d:"M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"})),q().createElement("symbol",{viewBox:"0 0 24 24",id:"jump-to"},q().createElement("path",{d:"M19 7v4H5.83l3.58-3.59L8 6l-6 6 6 6 1.41-1.41L5.83 13H21V7z"})),q().createElement("symbol",{viewBox:"0 0 24 24",id:"expand"},q().createElement("path",{d:"M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"})),q().createElement("symbol",{viewBox:"0 0 15 16",id:"copy"},q().createElement("g",{transform:"translate(2, -1)"},q().createElement("path",{fill:"#ffffff",fillRule:"evenodd",d:"M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"}))))));var Yr=r(2552);class Xr extends q().Component{render(){let{errSelectors:e,specSelectors:t,getComponent:r}=this.props,n=r("SvgAssets"),s=r("InfoContainer",!0),a=r("VersionPragmaFilter"),o=r("operations",!0),l=r("Models",!0),i=r("Row"),c=r("Col"),p=r("errors",!0);const u=r("ServersContainer",!0),d=r("SchemesContainer",!0),m=r("AuthorizeBtnContainer",!0),h=r("FilterContainer",!0);let g=t.isSwagger2(),f=t.isOAS3();const y=!t.specStr(),v=t.loadingStatus();let E=null;if("loading"===v&&(E=q().createElement("div",{className:"info"},q().createElement("div",{className:"loading-container"},q().createElement("div",{className:"loading"})))),"failed"===v&&(E=q().createElement("div",{className:"info"},q().createElement("div",{className:"loading-container"},q().createElement("h4",{className:"title"},"Failed to load API definition."),q().createElement(p,null)))),"failedConfig"===v){const t=e.lastError(),r=t?t.get("message"):"";E=q().createElement("div",{className:"info failed-config"},q().createElement("div",{className:"loading-container"},q().createElement("h4",{className:"title"},"Failed to load remote configuration."),q().createElement("p",null,r)))}if(!E&&y&&(E=q().createElement("h4",null,"No API definition provided.")),E)return q().createElement("div",{className:"swagger-ui"},q().createElement("div",{className:"loading-container"},E));const S=t.servers(),C=t.schemes(),b=S&&S.size,x=C&&C.size,w=!!t.securityDefinitions();return q().createElement("div",{className:"swagger-ui"},q().createElement(n,null),q().createElement(a,{isSwagger2:g,isOAS3:f,alsoShow:q().createElement(p,null)},q().createElement(p,null),q().createElement(i,{className:"information-container"},q().createElement(c,{mobile:12},q().createElement(s,null))),b||x||w?q().createElement("div",{className:"scheme-container"},q().createElement(c,{className:"schemes wrapper",mobile:12},b?q().createElement(u,null):null,x?q().createElement(d,null):null,w?q().createElement(m,null):null)):null,q().createElement(h,null),q().createElement(i,null,q().createElement(c,{mobile:12,desktop:12},q().createElement(o,null))),q().createElement(i,null,q().createElement(c,{mobile:12,desktop:12},q().createElement(l,null)))))}}const Qr=require("react-debounce-input");var en=r.n(Qr);const tn={value:"",onChange:()=>{},schema:{},keyName:"",required:!1,errors:(0,T.List)()};class rn extends N.Component{componentDidMount(){const{dispatchInitialValue:e,value:t,onChange:r}=this.props;e?r(t):!1===e&&r("")}render(){let{schema:e,errors:t,value:r,onChange:n,getComponent:s,fn:a,disabled:o}=this.props;const l=e&&e.get?e.get("format"):null,i=e&&e.get?e.get("type"):null;let c=e=>s(e,!1,{failSilently:!0}),p=i?c(l?`JsonSchema_${i}_${l}`:`JsonSchema_${i}`):s("JsonSchema_string");return p||(p=s("JsonSchema_string")),q().createElement(p,Qt()({},this.props,{errors:t,fn:a,getComponent:s,value:r,onChange:n,schema:e,disabled:o}))}}ce()(rn,"defaultProps",tn);class nn extends N.Component{constructor(){super(...arguments),ce()(this,"onChange",(e=>{const t=this.props.schema&&"file"===this.props.schema.get("type")?e.target.files[0]:e.target.value;this.props.onChange(t,this.props.keyName)})),ce()(this,"onEnumChange",(e=>this.props.onChange(e)))}render(){let{getComponent:e,value:t,schema:r,errors:n,required:s,description:a,disabled:o}=this.props;const l=r&&r.get?r.get("enum"):null,i=r&&r.get?r.get("format"):null,c=r&&r.get?r.get("type"):null,p=r&&r.get?r.get("in"):null;if(t||(t=""),n=n.toJS?n.toJS():[],l){const r=e("Select");return q().createElement(r,{className:n.length?"invalid":"",title:n.length?n:"",allowedValues:[...l],value:t,allowEmptyValue:!s,disabled:o,onChange:this.onEnumChange})}const u=o||p&&"formData"===p&&!("FormData"in window),d=e("Input");return c&&"file"===c?q().createElement(d,{type:"file",className:n.length?"invalid":"",title:n.length?n:"",onChange:this.onChange,disabled:u}):q().createElement(en(),{type:i&&"password"===i?"password":"text",className:n.length?"invalid":"",title:n.length?n:"",value:t,minLength:0,debounceTimeout:350,placeholder:a,onChange:this.onChange,disabled:u})}}ce()(nn,"defaultProps",tn);class sn extends N.PureComponent{constructor(e,t){super(e,t),ce()(this,"onChange",(()=>{this.props.onChange(this.state.value)})),ce()(this,"onItemChange",((e,t)=>{this.setState((r=>{let{value:n}=r;return{value:n.set(t,e)}}),this.onChange)})),ce()(this,"removeItem",(e=>{this.setState((t=>{let{value:r}=t;return{value:r.delete(e)}}),this.onChange)})),ce()(this,"addItem",(()=>{let e=un(this.state.value);this.setState((()=>({value:e.push((0,L.xi)(this.state.schema.get("items"),!1,{includeWriteOnly:!0}))})),this.onChange)})),ce()(this,"onEnumChange",(e=>{this.setState((()=>({value:e})),this.onChange)})),this.state={value:un(e.value),schema:e.schema}}UNSAFE_componentWillReceiveProps(e){const t=un(e.value);t!==this.state.value&&this.setState({value:t}),e.schema!==this.state.schema&&this.setState({schema:e.schema})}render(){var e;let{getComponent:t,required:r,schema:n,errors:s,fn:a,disabled:o}=this.props;s=s.toJS?s.toJS():C()(s)?s:[];const i=l()(s).call(s,(e=>"string"==typeof e)),c=_()(e=l()(s).call(s,(e=>void 0!==e.needRemove))).call(e,(e=>e.error)),p=this.state.value,u=!!(p&&p.count&&p.count()>0),d=n.getIn(["items","enum"]),m=n.getIn(["items","type"]),h=n.getIn(["items","format"]),g=n.get("items");let f,y=!1,v="file"===m||"string"===m&&"binary"===h;if(m&&h?f=t(`JsonSchema_${m}_${h}`):"boolean"!==m&&"array"!==m&&"object"!==m||(f=t(`JsonSchema_${m}`)),f||v||(y=!0),d){const e=t("Select");return q().createElement(e,{className:s.length?"invalid":"",title:s.length?s:"",multiple:!0,value:p,disabled:o,allowedValues:d,allowEmptyValue:!r,onChange:this.onEnumChange})}const E=t("Button");return q().createElement("div",{className:"json-schema-array"},u?_()(p).call(p,((e,r)=>{var n;const i=(0,T.fromJS)([..._()(n=l()(s).call(s,(e=>e.index===r))).call(n,(e=>e.error))]);return q().createElement("div",{key:r,className:"json-schema-form-item"},v?q().createElement(on,{value:e,onChange:e=>this.onItemChange(e,r),disabled:o,errors:i,getComponent:t}):y?q().createElement(an,{value:e,onChange:e=>this.onItemChange(e,r),disabled:o,errors:i}):q().createElement(f,Qt()({},this.props,{value:e,onChange:e=>this.onItemChange(e,r),disabled:o,errors:i,schema:g,getComponent:t,fn:a})),o?null:q().createElement(E,{className:`btn btn-sm json-schema-form-item-remove ${c.length?"invalid":null}`,title:c.length?c:"",onClick:()=>this.removeItem(r)}," - "))})):null,o?null:q().createElement(E,{className:`btn btn-sm json-schema-form-item-add ${i.length?"invalid":null}`,title:i.length?i:"",onClick:this.addItem},"Add ",m?`${m} `:"","item"))}}ce()(sn,"defaultProps",tn);class an extends N.Component{constructor(){super(...arguments),ce()(this,"onChange",(e=>{const t=e.target.value;this.props.onChange(t,this.props.keyName)}))}render(){let{value:e,errors:t,description:r,disabled:n}=this.props;return e||(e=""),t=t.toJS?t.toJS():[],q().createElement(en(),{type:"text",className:t.length?"invalid":"",title:t.length?t:"",value:e,minLength:0,debounceTimeout:350,placeholder:r,onChange:this.onChange,disabled:n})}}ce()(an,"defaultProps",tn);class on extends N.Component{constructor(){super(...arguments),ce()(this,"onFileChange",(e=>{const t=e.target.files[0];this.props.onChange(t,this.props.keyName)}))}render(){let{getComponent:e,errors:t,disabled:r}=this.props;const n=e("Input"),s=r||!("FormData"in window);return q().createElement(n,{type:"file",className:t.length?"invalid":"",title:t.length?t:"",onChange:this.onFileChange,disabled:s})}}ce()(on,"defaultProps",tn);class ln extends N.Component{constructor(){super(...arguments),ce()(this,"onEnumChange",(e=>this.props.onChange(e)))}render(){let{getComponent:e,value:t,errors:r,schema:n,required:s,disabled:a}=this.props;r=r.toJS?r.toJS():[];let o=n&&n.get?n.get("enum"):null,l=!o||!s,i=!o&&["true","false"];const c=e("Select");return q().createElement(c,{className:r.length?"invalid":"",title:r.length?r:"",value:String(t),disabled:a,allowedValues:o?[...o]:i,allowEmptyValue:l,onChange:this.onEnumChange})}}ce()(ln,"defaultProps",tn);const cn=e=>_()(e).call(e,(e=>{const t=void 0!==e.propKey?e.propKey:e.index;let r="string"==typeof e?e:"string"==typeof e.error?e.error:null;if(!t&&r)return r;let n=e.error,s=`/${e.propKey}`;for(;"object"==typeof n;){const e=void 0!==n.propKey?n.propKey:n.index;if(void 0===e)break;if(s+=`/${e}`,!n.error)break;n=n.error}return`${s}: ${n}`}));class pn extends N.PureComponent{constructor(){super(),ce()(this,"onChange",(e=>{this.props.onChange(e)})),ce()(this,"handleOnChange",(e=>{const t=e.target.value;this.onChange(t)}))}render(){let{getComponent:e,value:t,errors:r,disabled:n}=this.props;const s=e("TextArea");return r=r.toJS?r.toJS():C()(r)?r:[],q().createElement("div",null,q().createElement(s,{className:mt()({invalid:r.length}),title:r.length?cn(r).join(", "):"",value:(0,L.Pz)(t),disabled:n,onChange:this.handleOnChange}))}}function un(e){return T.List.isList(e)?e:C()(e)?(0,T.fromJS)(e):(0,T.List)()}function dn(){let r={components:{App:he,authorizationPopup:ge,authorizeBtn:fe,AuthorizeBtnContainer:ye,authorizeOperationBtn:ve,auths:Ee,AuthItem:Se,authError:Ce,oauth2:De,apiKeyAuth:be,basicAuth:xe,clear:Le,liveResponse:Be,InitializedInput:fr,info:Cr,InfoContainer:br,JumpToPath:xr,CopyToClipboardBtn:wr,onlineValidatorBadge:$e.Z,operations:We,operation:et,OperationSummary:nt,OperationSummaryMethod:st,OperationSummaryPath:lt,highlightCode:xt,responses:wt,response:Nt,ResponseExtension:qt,responseBody:Ot,parameters:Vt,parameterRow:Bt,execute:Ft,headers:Wt,errors:Ht,contentType:Yt,overview:hr,footer:_r,FilterContainer:Ar,ParamBody:Nr,curl:Rr,schemes:Tr,SchemesContainer:Pr,modelExample:jr,ModelWrapper:Vr,ModelCollapse:kr,Model:Dr.Z,Models:Lr,EnumModel:Ur,ObjectModel:zr,ArrayModel:Br,PrimitiveModel:Jr,Property:Fr,TryItOutButton:Wr,Markdown:Yr.Z,BaseLayout:Xr,VersionPragmaFilter:Hr,VersionStamp:Kr,OperationExt:it,OperationExtRow:ct,ParameterExt:Dt,ParameterIncludeEmpty:Ut,OperationTag:Qe,OperationContainer:me,DeepLink:Zr,InfoUrl:Sr,InfoBasePath:yr,SvgAssets:Gr,Example:we,ExamplesSelect:Ie,ExamplesSelectValueRetainer:qe}},n={components:e},s={components:t};return[ne.default,te.default,X.default,Z.default,K.default,W.default,H.default,G.default,r,n,Q.default,s,ee.default,re.default,se.default,ae.default,oe.default,Y.default,(0,le.default)()]}ce()(pn,"defaultProps",tn);var mn=r(7451);function hn(){return[dn,mn.default]}var gn=r(5308);const{GIT_DIRTY:fn,GIT_COMMIT:yn,PACKAGE_VERSION:vn,BUILD_TIME:En}={PACKAGE_VERSION:"4.15.5",GIT_COMMIT:"gc858a26",GIT_DIRTY:!0,BUILD_TIME:"Wed, 09 Nov 2022 06:53:00 GMT"};function Sn(e){var t;D.Z.versions=D.Z.versions||{},D.Z.versions.swaggerUi={version:vn,gitRevision:yn,gitDirty:fn,buildTimestamp:En};const r={dom_id:null,domNode:null,spec:{},url:"",urls:null,layout:"BaseLayout",docExpansion:"list",maxDisplayedTags:null,filter:null,validatorUrl:"https://validator.swagger.io/validator",oauth2RedirectUrl:`${window.location.protocol}//${window.location.host}${window.location.pathname.substring(0,a()(t=window.location.pathname).call(t,"/"))}/oauth2-redirect.html`,persistAuthorization:!1,configs:{},custom:{},displayOperationId:!1,displayRequestDuration:!1,deepLinking:!1,tryItOutEnabled:!1,requestInterceptor:e=>e,responseInterceptor:e=>e,showMutatedRequest:!0,defaultModelRendering:"example",defaultModelExpandDepth:1,defaultModelsExpandDepth:1,showExtensions:!1,showCommonExtensions:!1,withCredentials:void 0,requestSnippetsEnabled:!1,requestSnippets:{generators:{curl_bash:{title:"cURL (bash)",syntax:"bash"},curl_powershell:{title:"cURL (PowerShell)",syntax:"powershell"},curl_cmd:{title:"cURL (CMD)",syntax:"bash"}},defaultExpanded:!0,languages:null},supportedSubmitMethods:["get","put","post","delete","options","head","patch","trace"],queryConfigEnabled:!1,presets:[hn],plugins:[],pluginsOptions:{pluginLoadType:"legacy"},initialState:{},fn:{},components:{},syntaxHighlight:{activated:!0,theme:"agate"}};let n=e.queryConfigEnabled?(0,L.UG)():{};const s=e.domNode;delete e.domNode;const o=m()({},r,e,n),i={system:{configs:o.configs},plugins:o.presets,pluginsOptions:o.pluginsOptions,state:m()({layout:{layout:o.layout,filter:l()(o)},spec:{spec:"",url:o.url},requestSnippets:o.requestSnippets},o.initialState)};if(o.initialState)for(var p in o.initialState)Object.prototype.hasOwnProperty.call(o.initialState,p)&&void 0===o.initialState[p]&&delete i.state[p];var d=new z(i);d.register([o.plugins,()=>({fn:o.fn,components:o.components,state:o.state})]);var h=d.getSystem();const g=e=>{let t=h.specSelectors.getLocalConfig?h.specSelectors.getLocalConfig():{},r=m()({},t,o,e||{},n);if(s&&(r.domNode=s),d.setConfigs(r),h.configsActions.loaded(),null!==e&&(!n.url&&"object"==typeof r.spec&&c()(r.spec).length?(h.specActions.updateUrl(""),h.specActions.updateLoadingStatus("success"),h.specActions.updateSpec(u()(r.spec))):h.specActions.download&&r.url&&!r.urls&&(h.specActions.updateUrl(r.url),h.specActions.download(r.url))),r.domNode)h.render(r.domNode,"App");else if(r.dom_id){let e=document.querySelector(r.dom_id);h.render(e,"App")}else null===r.dom_id||null===r.domNode||console.error("Skipped rendering: no `dom_id` or `domNode` was specified");return h},f=n.config||o.configUrl;return f&&h.specActions&&h.specActions.getConfigByUrl?(h.specActions.getConfigByUrl({url:f,loadRemoteConfig:!0,requestInterceptor:o.requestInterceptor,responseInterceptor:o.responseInterceptor},g),h):g()}Sn.presets={apis:hn},Sn.plugins=gn.default;const Cn=Sn})(),n=n.default})()}));
//# sourceMappingURL=swagger-ui.js.map