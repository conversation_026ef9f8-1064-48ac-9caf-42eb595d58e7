<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Wexflow.Core</name>
    </assembly>
    <members>
        <member name="T:Wexflow.Core.Attribute">
            <summary>
            Attribute.
            </summary>
            <remarks>
            Creates a new instance of Attribute.
            </remarks>
            <param name="name">Attribute name.</param>
            <param name="value">Attribute value.</param>
        </member>
        <member name="M:Wexflow.Core.Attribute.#ctor(System.String,System.String)">
            <summary>
            Attribute.
            </summary>
            <remarks>
            Creates a new instance of Attribute.
            </remarks>
            <param name="name">Attribute name.</param>
            <param name="value">Attribute value.</param>
        </member>
        <member name="P:Wexflow.Core.Attribute.Name">
            <summary>
            Attribute name.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Attribute.Value">
            <summary>
            Attribute value.
            </summary>
        </member>
        <member name="T:Wexflow.Core.DbType">
            <summary>
            Database type
            </summary>
        </member>
        <member name="F:Wexflow.Core.DbType.LiteDB">
            <summary>
            LiteDB
            </summary>
        </member>
        <member name="F:Wexflow.Core.DbType.MongoDB">
            <summary>
            MongoDB
            </summary>
        </member>
        <member name="F:Wexflow.Core.DbType.RavenDB">
            <summary>
            RavenDB
            </summary>
        </member>
        <member name="F:Wexflow.Core.DbType.PostgreSQL">
            <summary>
            PostgreSQL
            </summary>
        </member>
        <member name="F:Wexflow.Core.DbType.SQLServer">
            <summary>
            SQLServer
            </summary>
        </member>
        <member name="F:Wexflow.Core.DbType.MySQL">
            <summary>
            MySQL
            </summary>
        </member>
        <member name="F:Wexflow.Core.DbType.SQLite">
            <summary>
            SQLite
            </summary>
        </member>
        <member name="F:Wexflow.Core.DbType.Firebird">
            <summary>
            Firebird
            </summary>
        </member>
        <member name="F:Wexflow.Core.DbType.Oracle">
            <summary>
            Oracle
            </summary>
        </member>
        <member name="F:Wexflow.Core.DbType.MariaDB">
            <summary>
            MariaDB
            </summary>
        </member>
        <member name="T:Wexflow.Core.Entity">
            <summary>
            Entity.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Entity.TaskId">
            <summary>
            Task Id.
            </summary>
        </member>
        <member name="T:Wexflow.Core.ExecutionGraph.Flowchart.Case">
            <summary>
            Case.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Flowchart.Case.Value">
            <summary>
            Case value.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Flowchart.Case.Nodes">
            <summary>
            Case nodes.
            </summary>
        </member>
        <member name="M:Wexflow.Core.ExecutionGraph.Flowchart.Case.#ctor(System.String,System.Collections.Generic.IEnumerable{Wexflow.Core.ExecutionGraph.Node})">
            <summary>
            Creates a new case.
            </summary>
            <param name="val">Case value.</param>
            <param name="nodes">Case nodes.</param>
        </member>
        <member name="T:Wexflow.Core.ExecutionGraph.Flowchart.If">
            <summary>
            If flowchart node.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Flowchart.If.IfId">
            <summary>
            If Id.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Flowchart.If.DoNodes">
            <summary>
            Do Nodes.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Flowchart.If.ElseNodes">
            <summary>
            Else nodes.
            </summary>
        </member>
        <member name="M:Wexflow.Core.ExecutionGraph.Flowchart.If.#ctor(System.Int32,System.Int32,System.Int32,System.Collections.Generic.IEnumerable{Wexflow.Core.ExecutionGraph.Node},System.Collections.Generic.IEnumerable{Wexflow.Core.ExecutionGraph.Node})">
            <summary>
            Creates a new If flowchart node.
            </summary>
            <param name="id">Id.</param>
            <param name="parentId">Parent Id.</param>
            <param name="ifId">If Id.</param>
            <param name="doNodes">Do nodes.</param>
            <param name="elseNodes">Else nodes.</param>
        </member>
        <member name="T:Wexflow.Core.ExecutionGraph.Flowchart.Switch">
            <summary>
            Switch flowchart node.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Flowchart.Switch.SwitchId">
            <summary>
            Switch id.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Flowchart.Switch.Cases">
            <summary>
            Cases.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Flowchart.Switch.Default">
            <summary>
            Default case.
            </summary>
        </member>
        <member name="M:Wexflow.Core.ExecutionGraph.Flowchart.Switch.#ctor(System.Int32,System.Int32,System.Int32,System.Collections.Generic.IEnumerable{Wexflow.Core.ExecutionGraph.Flowchart.Case},System.Collections.Generic.IEnumerable{Wexflow.Core.ExecutionGraph.Node})">
            <summary>
            Creates a new Switch flowchart node.
            </summary>
            <param name="id">Id.</param>
            <param name="parentId">Parent Id.</param>
            <param name="switchId">Switch Id.</param>
            <param name="cases">Cases.</param>
            <param name="default">Default case.</param>
        </member>
        <member name="T:Wexflow.Core.ExecutionGraph.Flowchart.While">
            <summary>
            While flowchart node.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Flowchart.While.WhileId">
            <summary>
            While Id.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Flowchart.While.Nodes">
            <summary>
            Nodes.
            </summary>
        </member>
        <member name="M:Wexflow.Core.ExecutionGraph.Flowchart.While.#ctor(System.Int32,System.Int32,System.Int32,System.Collections.Generic.IEnumerable{Wexflow.Core.ExecutionGraph.Node})">
            <summary>
            Creates a new While flowchart node.
            </summary>
            <param name="id">Id.</param>
            <param name="parentId">Parent Id.</param>
            <param name="whileId">While Id.</param>
            <param name="nodes">Nodes.</param>
        </member>
        <member name="T:Wexflow.Core.ExecutionGraph.Graph">
            <summary>
            Execution graph.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Graph.Nodes">
            <summary>
            Nodes.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Graph.OnSuccess">
            <summary>
            OnSuccess event.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Graph.OnWarning">
            <summary>
            OnWarning event.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Graph.OnError">
            <summary>
            OnError event.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Graph.OnRejected">
            <summary>
            OnRejected event.
            </summary>
        </member>
        <member name="M:Wexflow.Core.ExecutionGraph.Graph.#ctor(System.Collections.Generic.IEnumerable{Wexflow.Core.ExecutionGraph.Node},Wexflow.Core.ExecutionGraph.GraphEvent,Wexflow.Core.ExecutionGraph.GraphEvent,Wexflow.Core.ExecutionGraph.GraphEvent,Wexflow.Core.ExecutionGraph.GraphEvent)">
            <summary>
            Creates a new execution graph.
            </summary>
            <param name="nodes">Nodes.</param>
            <param name="onSuccess">OnSuccess event.</param>
            <param name="onWarning">OnWarning event.</param>
            <param name="onError">OnError event.</param>
            <param name="onRejected">OnRejected event.</param>
        </member>
        <member name="T:Wexflow.Core.ExecutionGraph.GraphEvent">
            <summary>
            Graph event.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.GraphEvent.Nodes">
            <summary>
            Nodes.
            </summary>
        </member>
        <member name="M:Wexflow.Core.ExecutionGraph.GraphEvent.#ctor(System.Collections.Generic.IEnumerable{Wexflow.Core.ExecutionGraph.Node})">
            <summary>
            Creates a new graph event.
            </summary>
            <param name="nodes">Nodes.</param>
        </member>
        <member name="T:Wexflow.Core.ExecutionGraph.Node">
            <summary>
            Node.
            </summary>
            <remarks>
            Creates a new node.
            </remarks>
            <param name="id">Node id.</param>
            <param name="parentId">Node parent id.</param>
        </member>
        <member name="M:Wexflow.Core.ExecutionGraph.Node.#ctor(System.Int32,System.Int32)">
            <summary>
            Node.
            </summary>
            <remarks>
            Creates a new node.
            </remarks>
            <param name="id">Node id.</param>
            <param name="parentId">Node parent id.</param>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Node.Id">
            <summary>
            Node Id.
            </summary>
        </member>
        <member name="P:Wexflow.Core.ExecutionGraph.Node.ParentId">
            <summary>
            Node parent Id.
            </summary>
        </member>
        <member name="T:Wexflow.Core.FileInf">
            <summary>
            FileInf.
            </summary>
        </member>
        <member name="P:Wexflow.Core.FileInf.Path">
            <summary>
            File path.
            </summary>
        </member>
        <member name="P:Wexflow.Core.FileInf.FileName">
            <summary>
            File name.
            </summary>
        </member>
        <member name="P:Wexflow.Core.FileInf.TaskId">
            <summary>
            Task Id.
            </summary>
        </member>
        <member name="P:Wexflow.Core.FileInf.RenameTo">
            <summary>
            RenameTo.
            </summary>
        </member>
        <member name="P:Wexflow.Core.FileInf.RenameToOrName">
            <summary>
            RenameToOrName.
            </summary>
        </member>
        <member name="P:Wexflow.Core.FileInf.Tags">
            <summary>
            List of tags.
            </summary>
        </member>
        <member name="P:Wexflow.Core.FileInf.FileInfo">
            <summary>
            File system info from <see cref="P:Wexflow.Core.FileInf.Path"/>.
            </summary>
        </member>
        <member name="M:Wexflow.Core.FileInf.#ctor(System.String,System.Int32)">
            <summary>
            Creates a new instance of FileInf.
            </summary>
            <param name="path">File path.</param>
            <param name="taskId">Task Id.</param>
        </member>
        <member name="M:Wexflow.Core.FileInf.ToString">
            <summary>
            FileInfo to string.
            </summary>
            <returns>FileInf as an XElement.ToString().</returns>
        </member>
        <member name="M:Wexflow.Core.FileInf.ToXElement">
            <summary>
            FileInf To XElement.
            </summary>
            <returns>FileInf as an XElement.</returns>
        </member>
        <member name="T:Wexflow.Core.Job">
            <summary>
            Workflow job used for queuing.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Job.Workflow">
            <summary>
            Workflow.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Job.QueuedOn">
            <summary>
            Queued on date time.
            </summary>
        </member>
        <member name="T:Wexflow.Core.LaunchType">
            <summary>
            Launch type.
            </summary>
        </member>
        <member name="F:Wexflow.Core.LaunchType.Startup">
            <summary>
            The workflow starts when Wexflow engine starts.
            </summary>
        </member>
        <member name="F:Wexflow.Core.LaunchType.Trigger">
            <summary>
            The workflow must be triggered manually to start.
            </summary>
        </member>
        <member name="F:Wexflow.Core.LaunchType.Periodic">
            <summary>
            The workflow starts periodically.
            </summary>
        </member>
        <member name="F:Wexflow.Core.LaunchType.Cron">
            <summary>
            The workflow starts depending on the cron scheduling expression
            </summary>
        </member>
        <member name="T:Wexflow.Core.Logger">
            <summary>
            Logger.
            </summary>
        </member>
        <member name="M:Wexflow.Core.Logger.Info(System.String)">
            <summary>
            Logs an information message.
            </summary>
            <param name="msg">Log message.</param>
        </member>
        <member name="M:Wexflow.Core.Logger.InfoFormat(System.String,System.Object[])">
            <summary>
            Logs a formatted information message.
            </summary>
            <param name="msg">Formatted log message.</param>
            <param name="args">Arguments.</param>
        </member>
        <member name="M:Wexflow.Core.Logger.Debug(System.String)">
            <summary>
            Logs a Debug log message.
            </summary>
            <param name="msg">Log message.</param>
        </member>
        <member name="M:Wexflow.Core.Logger.DebugFormat(System.String,System.Object[])">
            <summary>
            Logs a formatted debug message.
            </summary>
            <param name="msg">Log message.</param>
            <param name="args">Arguments.</param>
        </member>
        <member name="M:Wexflow.Core.Logger.Error(System.String)">
            <summary>
            Logs an error log message.
            </summary>
            <param name="msg">Log message.</param>
        </member>
        <member name="M:Wexflow.Core.Logger.ErrorFormat(System.String,System.Object[])">
            <summary>
            Logs a formatted error message.
            </summary>
            <param name="msg">Log message.</param>
            <param name="args">Arguments.</param>
        </member>
        <member name="M:Wexflow.Core.Logger.Error(System.String,System.Exception)">
            <summary>
            Logs an error message and an exception.
            </summary>
            <param name="msg">Log message.</param>
            <param name="e">Exception.</param>
        </member>
        <member name="M:Wexflow.Core.Logger.ErrorFormat(System.String,System.Exception,System.Object[])">
            <summary>
            Logs a formatted log message and an exception.
            </summary>
            <param name="msg">Formatted log message.</param>
            <param name="e">Exception.</param>
            <param name="args">Arguments.</param>
        </member>
        <member name="T:Wexflow.Core.LogLevel">
            <summary>
            Log level
            </summary>
        </member>
        <member name="F:Wexflow.Core.LogLevel.Debug">
            <summary>
            All logs and debug logs.
            </summary>
        </member>
        <member name="F:Wexflow.Core.LogLevel.All">
            <summary>
            All logs without debug logs.
            </summary>
        </member>
        <member name="F:Wexflow.Core.LogLevel.Severely">
            <summary>
            Only last workflow log and error logs.
            </summary>
        </member>
        <member name="F:Wexflow.Core.LogLevel.Minimum">
            <summary>
            Only last workflow log.
            </summary>
        </member>
        <member name="F:Wexflow.Core.LogLevel.None">
            <summary>
            No logs.
            </summary>
        </member>
        <member name="T:Wexflow.Core.PollingFileSystemWatcher.PollingFileSystemWatcher">
            <summary>
            PollingFileSystemWatcher can be used to monitor changes to a file system directory
            </summary>
            <remarks>
            This type is similar to FileSystemWatcher, but unlike FileSystemWatcher it is fully reliable,
            at the cost of some performance overhead. 
            Instead of relying on Win32 file notification APIs, it periodically scans the watched directory to discover changes.
            This means that sooner or later it will discover every change.
            FileSystemWatcher's Win32 APIs can drop some events in rare circumstances, which is often an acceptable compromise.
            In scenarios where events cannot be missed, PollingFileSystemWatcher should be used.
            Note: When a watched file is renamed, one or two notifications will be made.
            Note: When no changes are detected, PollingFileSystemWatcher will not allocate memory on the GC heap.
            </remarks>
        </member>
        <member name="M:Wexflow.Core.PollingFileSystemWatcher.PollingFileSystemWatcher.#ctor(System.String,System.String,System.IO.EnumerationOptions)">
            <summary>
            Creates an instance of a watcher
            </summary>
            <param name="path">The path to watch.</param>
            <param name="filter">The type of files to watch. For example, "*.txt" watches for changes to all text files.</param>
            <param name="options">Options.</param>
        </member>
        <member name="P:Wexflow.Core.PollingFileSystemWatcher.PollingFileSystemWatcher.PollingInterval">
            <summary>
            The number of milliseconds to wait until checking the file system again
            </summary>
        </member>
        <member name="E:Wexflow.Core.PollingFileSystemWatcher.PollingFileSystemWatcher.Changed">
            <summary>
            This callback is called when any change (Created, Deleted, Changed) is detected in any watched file.
            </summary>
        </member>
        <member name="M:Wexflow.Core.PollingFileSystemWatcher.PollingFileSystemWatcher.Dispose">
            <summary>
            Disposes the timer used for polling.
            </summary>
        </member>
        <member name="T:Wexflow.Core.Setting">
            <summary>
            Setting.
            </summary>
            <remarks>
            Creates a new setting.
            </remarks>
            <param name="name">Setting name.</param>
            <param name="value">Setting value.</param>
            <param name="attributes">Setting attributes.</param>
        </member>
        <member name="M:Wexflow.Core.Setting.#ctor(System.String,System.String,Wexflow.Core.Attribute[])">
            <summary>
            Setting.
            </summary>
            <remarks>
            Creates a new setting.
            </remarks>
            <param name="name">Setting name.</param>
            <param name="value">Setting value.</param>
            <param name="attributes">Setting attributes.</param>
        </member>
        <member name="P:Wexflow.Core.Setting.Name">
            <summary>
            Setting name.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Setting.Value">
            <summary>
            Settings value.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Setting.Attributes">
            <summary>
            Setting attributes.
            </summary>
        </member>
        <member name="T:Wexflow.Core.Status">
            <summary>
            Status.
            </summary>
        </member>
        <member name="F:Wexflow.Core.Status.Success">
            <summary>
            Success.
            </summary>
        </member>
        <member name="F:Wexflow.Core.Status.Warning">
            <summary>
            Warning.
            </summary>
        </member>
        <member name="F:Wexflow.Core.Status.Error">
            <summary>
            Error.
            </summary>
        </member>
        <member name="F:Wexflow.Core.Status.Rejected">
            <summary>
            Rejected.
            </summary>
        </member>
        <member name="T:Wexflow.Core.Tag">
            <summary>
            Tag.
            </summary>
            <remarks>
            Creates a new tag.
            </remarks>
            <param name="key">Tag key.</param>
            <param name="value">Tag value.</param>
        </member>
        <member name="M:Wexflow.Core.Tag.#ctor(System.String,System.String)">
            <summary>
            Tag.
            </summary>
            <remarks>
            Creates a new tag.
            </remarks>
            <param name="key">Tag key.</param>
            <param name="value">Tag value.</param>
        </member>
        <member name="P:Wexflow.Core.Tag.Key">
            <summary>
            Tag key.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Tag.Value">
            <summary>
            Tag value.
            </summary>
        </member>
        <member name="T:Wexflow.Core.Task">
            <summary>
            Task.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Task.Id">
            <summary>
            Task Id.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Task.Name">
            <summary>
            Task name.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Task.Description">
            <summary>
            Task description.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Task.IsEnabled">
            <summary>
            Shows whether this task is enabled or not.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Task.IsWaitingForApproval">
            <summary>
            Shows whether this task is waiting for approval.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Task.Settings">
            <summary>
            Task settings.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Task.Workflow">
            <summary>
            Workflow.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Task.Logs">
            <summary>
            Log messages.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Task.IsStopped">
            <summary>
            Indicates whether this task has been stopped or not.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Task.Files">
            <summary>
            Task files.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Task.Entities">
            <summary>
            Task entities.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Task.SharedMemory">
            <summary>
            Hashtable used as shared memory for tasks.
            </summary>
        </member>
        <member name="M:Wexflow.Core.Task.#ctor(System.Xml.Linq.XElement,Wexflow.Core.Workflow)">
            <summary>
            Creates a new task.
            </summary>
            <param name="xe">XElement.</param>
            <param name="wf">Workflow.</param>
        </member>
        <member name="M:Wexflow.Core.Task.Run">
            <summary>
            Starts the task.
            </summary>
            <returns>Task status.</returns>
        </member>
        <member name="M:Wexflow.Core.Task.Stop">
            <summary>
            Stops the current task.
            </summary>
        </member>
        <member name="M:Wexflow.Core.Task.GetSetting(System.String)">
            <summary>
            Returns a setting value from its name.
            </summary>
            <param name="name">Setting name.</param>
            <returns>Setting value.</returns>
        </member>
        <member name="M:Wexflow.Core.Task.GetSetting(System.String,System.String)">
            <summary>
            Returns a setting value from its name and returns a default value if the setting value is not found.
            </summary>
            <param name="name">Setting name.</param>
            <param name="defaultValue">Default value.</param>
            <returns>Setting value.</returns>
        </member>
        <member name="M:Wexflow.Core.Task.GetSetting``1(System.String,``0)">
            <summary>
            Returns a setting value from its name and returns a default value if the setting value is not found.
            </summary>
            <param name="name">Setting name.</param>
            <param name="defaultValue">Default value.</param>
            <returns>Setting value.</returns>
        </member>
        <member name="M:Wexflow.Core.Task.GetSettingInt(System.String,System.Int32)">
            <summary>
            Returns a setting value from its name and returns a default value if the setting value is not found.
            </summary>
            <param name="name">Setting name.</param>
            <param name="defaultValue">Default value.</param>
            <returns>Setting value.</returns>
        </member>
        <member name="M:Wexflow.Core.Task.GetSettingBool(System.String,System.Boolean)">
            <summary>
            Returns a setting value from its name and returns a default value if the setting value is not found.
            </summary>
            <param name="name">Setting name.</param>
            <param name="defaultValue">Default value.</param>
            <returns>Setting value.</returns>
        </member>
        <member name="M:Wexflow.Core.Task.GetSettings(System.String)">
            <summary>
            Returns a list of setting values from a setting name.
            </summary>
            <param name="name">Setting name.</param>
            <returns>A list of setting values.</returns>
        </member>
        <member name="M:Wexflow.Core.Task.GetSettingsInt(System.String)">
            <summary>
            Returns a list of integers from a setting name.
            </summary>
            <param name="name">Setting name.</param>
            <returns>A list of integers.</returns>
        </member>
        <member name="M:Wexflow.Core.Task.GetXSettings(System.String)">
            <summary>
            Returns a list of setting values as XElements from a setting name.
            </summary>
            <param name="name">Setting name.</param>
            <returns>A list of setting values as XElements.</returns>
        </member>
        <member name="M:Wexflow.Core.Task.SelectFiles">
            <summary>
            Returns a list of the files loaded by this task through selectFiles setting.
            </summary>
            <returns>A list of the files loaded by this task through selectFiles setting.</returns>
        </member>
        <member name="M:Wexflow.Core.Task.QueryFiles(System.Collections.Generic.IEnumerable{Wexflow.Core.FileInf},System.Xml.Linq.XElement)">
            <summary>
            Filters a list of files from the tags in selectFiles setting.
            </summary>
            <param name="files">Files to filter.</param>
            <param name="xSelectFile">selectFile as an XElement</param>
            <returns>A list of files from the tags in selectFiles setting.</returns>
        </member>
        <member name="M:Wexflow.Core.Task.SelectEntities">
            <summary>
            Returns a list of the entities loaded by this task through selectEntities setting.
            </summary>
            <returns>A list of the entities loaded by this task through selectEntities setting.</returns>
        </member>
        <member name="M:Wexflow.Core.Task.SelectObject">
            <summary>
            Returns an object from the Hashtable through selectObject setting.
            </summary>
            <returns>An object from the Hashtable through selectObject setting.</returns>
        </member>
        <member name="M:Wexflow.Core.Task.Info(System.String)">
            <summary>
            Logs an information message.
            </summary>
            <param name="msg">Log message.</param>
        </member>
        <member name="M:Wexflow.Core.Task.InfoFormat(System.String,System.Object[])">
            <summary>
            Logs a formatted information message.
            </summary>
            <param name="msg">Formatted log message.</param>
            <param name="args">Arguments.</param>
        </member>
        <member name="M:Wexflow.Core.Task.Debug(System.String)">
            <summary>
            Logs a Debug log message.
            </summary>
            <param name="msg">Log message.</param>
        </member>
        <member name="M:Wexflow.Core.Task.DebugFormat(System.String,System.Object[])">
            <summary>
            Logs a formatted debug message.
            </summary>
            <param name="msg">Log message.</param>
            <param name="args">Arguments.</param>
        </member>
        <member name="M:Wexflow.Core.Task.Error(System.String)">
            <summary>
            Logs an error log message.
            </summary>
            <param name="msg">Log message.</param>
        </member>
        <member name="M:Wexflow.Core.Task.ErrorFormat(System.String,System.Object[])">
            <summary>
            Logs a formatted error message.
            </summary>
            <param name="msg">Log message.</param>
            <param name="args">Arguments.</param>
        </member>
        <member name="M:Wexflow.Core.Task.Error(System.String,System.Exception)">
            <summary>
            Logs an error message and an exception.
            </summary>
            <param name="msg">Log message.</param>
            <param name="e">Exception.</param>
        </member>
        <member name="M:Wexflow.Core.Task.ErrorFormat(System.String,System.Exception,System.Object[])">
            <summary>
            Logs a formatted log message and an exception.
            </summary>
            <param name="msg">Formatted log message.</param>
            <param name="e">Exception.</param>
            <param name="args">Arguments.</param>
        </member>
        <member name="M:Wexflow.Core.Task.WaitOne">
            <summary>
            This method is necessary for suspend/resume the current task.
            </summary>
        </member>
        <member name="T:Wexflow.Core.TaskStatus">
            <summary>
            Task status.
            </summary>
            <remarks>
            Creates a new TaskStatus. This constructor is designed for sequential tasks.
            </remarks>
            <param name="status">Status.</param>
        </member>
        <member name="M:Wexflow.Core.TaskStatus.#ctor(Wexflow.Core.Status)">
            <summary>
            Task status.
            </summary>
            <remarks>
            Creates a new TaskStatus. This constructor is designed for sequential tasks.
            </remarks>
            <param name="status">Status.</param>
        </member>
        <member name="P:Wexflow.Core.TaskStatus.Status">
            <summary>
            Status.
            </summary>
        </member>
        <member name="P:Wexflow.Core.TaskStatus.Condition">
            <summary>
            If and While condition.
            </summary>
        </member>
        <member name="P:Wexflow.Core.TaskStatus.SwitchValue">
            <summary>
            Switch/Case value.
            </summary>
        </member>
        <member name="M:Wexflow.Core.TaskStatus.#ctor(Wexflow.Core.Status,System.Boolean)">
            <summary>
            Creates a new TaskStatus. This constructor is designed for If/While flowchart tasks.
            </summary>
            <param name="status">Status.</param>
            <param name="condition">Condition value.</param>
        </member>
        <member name="M:Wexflow.Core.TaskStatus.#ctor(Wexflow.Core.Status,System.String)">
            <summary>
            Creates a new TaskStatus. This constructor is designed for Switch flowchart tasks.
            </summary>
            <param name="status">Status.</param>
            <param name="switchValue">Switch value.</param>
        </member>
        <member name="M:Wexflow.Core.TaskStatus.#ctor(Wexflow.Core.Status,System.Boolean,System.String)">
            <summary>
            Creates a new TaskStatus. This constructor is designed for If/While and Switch flowchart tasks.
            </summary>
            <param name="status">Status.</param>
            <param name="condition">Condition value.</param>
            <param name="switchValue">Switch value.</param>
        </member>
        <member name="T:Wexflow.Core.Variable">
            <summary>
            Variable class.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Variable.Key">
            <summary>
            Variable key.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Variable.Value">
            <summary>
            Variable value.
            </summary>
        </member>
        <member name="T:Wexflow.Core.WexflowEngine">
            <summary>
            Wexflow engine.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.LogLevel">
            <summary>
            Log level.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.DbFolderName">
            <summary>
            Records db folder name.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.SuperAdminUsername">
            <summary>
            Super-admin user name.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.SettingsFile">
            <summary>
            Settings file path.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.EnableWorkflowsHotFolder">
            <summary>
            Indicates whether workflows hot folder is enabled or not.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.EnableEmailNotifications">
            <summary>
            Indicates whether email notifications are enabled or not.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.SmptHost">
            <summary>
            SMTP host.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.SmtpPort">
            <summary>
            SMTP port.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.SmtpEnableSsl">
            <summary>
             Indicates whether to enable SMTP SSL or not.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.SmtpUser">
            <summary>
            SMTP user.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.SmtpPassword">
            <summary>
            SMTP password.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.SmtpFrom">
            <summary>
            SMTP from.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.Workflows">
            <summary>
            List of the Workflows loaded by Wexflow engine.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.Database">
            <summary>
            Database.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.WorkflowsFolder">
            <summary>
            Workflows hot folder path.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.RecordsFolder">
            <summary>
            Records folder path.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.RecordsHotFolder">
            <summary>
            Records hot folder path.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.TempFolder">
            <summary>
            Temp folder path.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.RecordsTempFolder">
            <summary>
            Workflows temp folder used for global variables parsing.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.TasksFolder">
            <summary>
            Tasks folder path.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.ApprovalFolder">
            <summary>
            Approval folder path.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.XsdPath">
            <summary>
            XSD path.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.TasksNamesFile">
            <summary>
            Tasks names file path.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.TasksSettingsFile">
            <summary>
            Tasks settings file path.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.DbType">
            <summary>
            Database type.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.ConnectionString">
            <summary>
            Database connection string.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.GlobalVariablesFile">
            <summary>
            Global variables file.
            </summary>
        </member>
        <member name="P:Wexflow.Core.WexflowEngine.GlobalVariables">
            <summary>
            Global variables.
            </summary>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.#ctor(System.String,Wexflow.Core.LogLevel,System.Boolean,System.String,System.Boolean,System.String,System.Int32,System.Boolean,System.String,System.String,System.String)">
            <summary>
            Creates a new instance of Wexflow engine.
            </summary>
            <param name="settingsFile">Settings file path.</param>
            <param name="logLevel">Log level.</param>
            <param name="enableWorkflowsHotFolder">Indicates whether workflows hot folder is enabled or not.</param>
            <param name="superAdminUsername">Super-admin username.</param>
            <param name="enableEmailNotifications"></param>
            <param name="smtpHost">SMTP host.</param>
            <param name="smtpPort">SMTP port.</param>
            <param name="smtpEnableSsl">SMTP enable ssl.</param>
            <param name="smtpUser">SMTP user.</param>
            <param name="smtpPassword">SMTP password.</param>
            <param name="smtpFrom">SMTP from.</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.IsCronExpressionValid(System.String)">
            <summary>
            Checks whether a cron expression is valid or not.
            </summary>
            <param name="expression">Cron expression</param>
            <returns></returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.StopCronJobs(Wexflow.Core.Workflow)">
            <summary>
            Stops cron jobs.
            </summary>
            <param name="workflow">Workflow.</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.SaveWorkflow(System.String,Wexflow.Core.Db.UserProfile,System.String,System.Boolean)">
            <summary>
            Saves a workflow in the database.
            </summary>
            <param name="xml">XML of the workflow.</param>
            <param name="userId">User id.</param>
            <param name="userProfile">User profile.</param>
            <param name="schedule">Indicates whether to schedule the workflow or not.</param>
            <returns>Workflow db id.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetWorkflowId(System.String)">
            <summary>
            Get workflow id from xml 
            </summary>
            <param name="xml"></param>
            <returns></returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.SaveWorkflowFromFile(System.String,Wexflow.Core.Db.UserProfile,System.String,System.Boolean)">
            <summary>
            Saves a workflow from its file
            </summary>
            <param name="userId">User Id</param>
            <param name="userProfile">User Profile</param>
            <param name="filePath">Workflow File Path</param>
            <param name="schedule">Indicates whether to schedule the workflow or not.</param>
            <returns>Workflow DB Id</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.DeleteWorkflow(System.String)">
            <summary>
            Deletes a workflow from the database.
            </summary>
            <param name="dbId">DB ID.</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.DeleteWorkflows(System.String[])">
            <summary>
            Deletes workflows from the database.
            </summary>
            <param name="dbIds">DB IDs</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.InsertUserWorkflowRelation(System.String,System.String)">
            <summary>
            Inserts a user workflow relation in DB.
            </summary>
            <param name="userId">User DB ID.</param>
            <param name="workflowId">Workflow DB ID.</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.DeleteUserWorkflowRelations(System.String)">
            <summary>
            Deletes user workflow relations.
            </summary>
            <param name="userId">User DB ID.</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetUserWorkflows(System.String)">
            <summary>
            Returns user workflows.
            </summary>
            <param name="userId">User DB ID.</param>
            <returns>User worklofws.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.CheckUserWorkflow(System.String,System.String)">
            <summary>
            Checks whether a user have access to a workflow.
            </summary>
            <param name="userId">User id.</param>
            <param name="workflowId">Workflow db id.</param>
            <returns>true/false.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetAdministrators(System.String,Wexflow.Core.Db.UserOrderBy)">
            <summary>
            Returns administrators search result.
            </summary>
            <param name="keyword">Keyword.</param>
            <param name="uo">User Order By.</param>
            <returns>Administrators search result.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetNonRestrictedUsers">
            <summary>
            Returns non restricted users.
            </summary>
            <returns>Non restricted users.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.Run">
            <summary>
            Starts Wexflow engine.
            </summary>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.Stop(System.Boolean,System.Boolean)">
            <summary>
            Stops Wexflow engine.
            </summary>
            <param name="stopQuartzScheduler">Tells if Quartz scheduler should be stopped or not.</param>
            <param name="clearStatusCountAndEntries">Indicates whether to clear statusCount and entries.</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetWorkflow(System.Int32)">
            <summary>
            Gets a workflow.
            </summary>
            <param name="workflowId">Workflow Id.</param>
            <returns></returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.StartWorkflow(System.String,System.Int32,System.Collections.Generic.List{Wexflow.Core.Variable})">
            <summary>
            Starts a workflow.
            </summary>
            <param name="startedBy">Username of the user that started the workflow.</param>
            <param name="workflowId">Workflow Id.</param>
            <param name="restVariables">Rest variables</param>
            <returns>Instance id.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.StopWorkflow(System.Int32,System.Guid,System.String)">
            <summary>
            Stops a workflow.
            </summary>
            <param name="workflowId">Workflow Id.</param>
            <param name="instanceId">Job instance Id.</param>
            <param name="stoppedBy">Username of the user who stopped the workflow.</param>
            <returns>Result.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.SuspendWorkflow(System.Int32,System.Guid)">
            <summary>
            Suspends a workflow.
            </summary>
            <param name="workflowId">Workflow Id.</param>
            <param name="instanceId">Job instance Id.</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.ResumeWorkflow(System.Int32,System.Guid)">
            <summary>
            Resumes a workflow.
            </summary>
            <param name="workflowId">Workflow Id.</param>
            <param name="instanceId">Job instance Id.</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.ApproveWorkflow(System.Int32,System.Guid,System.String)">
            <summary>
            Resumes a workflow.
            </summary>
            <param name="workflowId">Workflow Id.</param>
            <param name="instanceId">Job instance Id.</param>
            <param name="approvedBy">Username of the user who approved the workflow.</param>
            <returns>Result.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.RejectWorkflow(System.Int32,System.Guid,System.String)">
            <summary>
            Rejects a workflow.
            </summary>
            <param name="workflowId">Workflow Id.</param>
            <param name="instanceId">Job instance Id.</param>
            <param name="rejectedBy">Username of the user who rejected the workflow.</param>
            <returns>Result.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetStatusCount">
            <summary>
            Returns status count
            </summary>
            <returns>Returns status count</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.InsertUser(System.String,System.String,Wexflow.Core.Db.UserProfile,System.String)">
            <summary>
            Inserts a user.
            </summary>
            <param name="username">Username.</param>
            <param name="password">Password.</param>
            <param name="userProfile">User profile.</param>
            <param name="email">Email.</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.UpdateUser(System.String,System.String,System.String,Wexflow.Core.Db.UserProfile,System.String)">
            <summary>
            Updates a user.
            </summary>
            <param name="userId">User's id.</param>
            <param name="username">Username.</param>
            <param name="password">Password.</param>
            <param name="userProfile">User's profile.</param>
            <param name="email">User's email.</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.UpdateUsernameAndEmailAndUserProfile(System.String,System.String,System.String,System.Int32)">
            <summary>
            Updates username and email.
            </summary>
            <param name="userId">User Id.</param>
            <param name="username">New username.</param>
            <param name="email">New email.</param>
            <param name="up">User profile.</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.DeleteUser(System.String,System.String)">
            <summary>
            Deletes a user.
            </summary>
            <param name="username">Username.</param>
            <param name="password">Password.</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetUser(System.String)">
            <summary>
            Gets a user.
            </summary>
            <param name="username">Username.</param>
            <returns></returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetUserById(System.String)">
            <summary>
            Gets a user by Id.
            </summary>
            <param name="userId">User id.</param>
            <returns>User.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetUsers">
            <summary>
            Returns all the users.
            </summary>
            <returns>All the users.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetUsers(System.String,Wexflow.Core.Db.UserOrderBy)">
            <summary>
            Search for users.
            </summary>
            <returns>All the users.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.UpdatePassword(System.String,System.String)">
            <summary>
            Updates user password.
            </summary>
            <param name="username">Username.</param>
            <param name="password">Password.</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetHistoryEntries(System.String,System.DateTime,System.DateTime,System.Int32,System.Int32,Wexflow.Core.Db.EntryOrderBy)">
            <summary>
            Returns the entries by a keyword.
            </summary>
            <param name="keyword">Search keyword.</param>
            <param name="from">Date From.</param>
            <param name="to">Date To.</param>
            <param name="page">Page number.</param>
            <param name="entriesCount">Number of entries.</param>
            <param name="heo">EntryOrderBy</param>
            <returns>Returns all the entries</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetEntries(System.String,System.DateTime,System.DateTime,System.Int32,System.Int32,Wexflow.Core.Db.EntryOrderBy)">
            <summary>
            Returns the entries by a keyword.
            </summary>
            <param name="keyword">Search keyword.</param>
            <param name="from">Date From.</param>
            <param name="to">Date To.</param>
            <param name="page">Page number.</param>
            <param name="entriesCount">Number of entries.</param>
            <param name="heo">EntryOrderBy</param>
            <returns>Returns all the entries</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetEntry(System.Int32)">
            <summary>
            Returns latest workflow entry.
            </summary>
            <param name="workflowId">Workflow Id.</param>
            <returns></returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetEntry(System.Int32,System.Guid)">
            <summary>
            Returns a workflow job entry.
            </summary>
            <param name="workflowId">Workflow Id.</param>
            <param name="jobId">Job Id.</param>
            <returns></returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetHistoryEntriesCount(System.String,System.DateTime,System.DateTime)">
            <summary>
            Gets the number of history entries by search keyword and date filter.
            </summary>
            <param name="keyword">Search keyword.</param>
            <param name="from">Date from.</param>
            <param name="to">Date to.</param>
            <returns></returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetEntriesCount(System.String,System.DateTime,System.DateTime)">
            <summary>
            Gets the number of entries by search keyword and date filter.
            </summary>
            <param name="keyword">Search keyword.</param>
            <param name="from">Date from.</param>
            <param name="to">Date to.</param>
            <returns></returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetHistoryEntryStatusDateMin">
            <summary>
            Returns Status Date Min value.
            </summary>
            <returns>Status Date Min value.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetHistoryEntryStatusDateMax">
            <summary>
            Returns Status Date Max value.
            </summary>
            <returns>Status Date Max value.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetEntryStatusDateMin">
            <summary>
            Returns Status Date Min value.
            </summary>
            <returns>Status Date Min value.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetEntryStatusDateMax">
            <summary>
            Returns Status Date Max value.
            </summary>
            <returns>Status Date Max value.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetEntryLogs(System.String)">
            <summary>
            Returns entry logs.
            </summary>
            <param name="entryId">Entry id.</param>
            <returns>Entry logs.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetHistoryEntryLogs(System.String)">
            <summary>
            Returns entry logs.
            </summary>
            <param name="entryId">Entry id.</param>
            <returns>Entry logs.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.IsDirectoryEmpty(System.String)">
            <summary>
            Checks if a directory is empty.
            </summary>
            <param name="path">Directory path.</param>
            <returns>Result.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.SaveRecord(System.String,Wexflow.Core.Db.Record,System.Collections.Generic.List{Wexflow.Core.Db.Version})">
            <summary>
            Saves a record in the database.
            </summary>
            <param name="recordId">Record id.</param>
            <param name="record">Record.</param>
            <param name="versions">Version.</param>
            <returns></returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.SaveRecordFromFile(System.String,System.String)">
            <summary>
            Saves a new record from a file.
            </summary>
            <param name="filePath">File path.</param>
            <param name="createdBy">Created by username.</param>
            <returns>Record Id.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.DeleteRecords(System.String[])">
            <summary>
            Deletes records.
            </summary>
            <param name="recordIds">Record ids.</param>
            <returns>Result.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetRecords(System.String)">
            <summary>
            Returns records by keyword.
            </summary>
            <param name="keyword">Keyword.</param>
            <returns>Records by keyword</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetRecordsCreatedByOrAssignedTo(System.String,System.String,System.String)">
            <summary>
            Returns the records assigned to a user by keyword.
            </summary>
            <param name="createdBy">Created by user id.</param>
            <param name="assignedTo">Assigned to user id.</param>
            <param name="keyword">Keyword.</param>
            <returns>Records assigned to a user by keyword.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetRecordsCreatedBy(System.String)">
            <summary>
            Returns the records created by a user.
            </summary>
            <param name="createdBy">User id.</param>
            <returns>Records created by a user.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetVersions(System.String)">
            <summary>
            returns record versions.
            </summary>
            <param name="recordId">Record id.</param>
            <returns>record versions.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.InsertNotification(Wexflow.Core.Db.Notification)">
            <summary>
            Inserts a notification in the database.
            </summary>
            <param name="notification">Notification.</param>
            <returns>Notification id.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.MarkNotificationsAsRead(System.String[])">
            <summary>
            Marks notifications as read.
            </summary>
            <param name="notificationIds">Notification Ids.</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.MarkNotificationsAsUnread(System.String[])">
            <summary>
            Marks notifications as unread.
            </summary>
            <param name="notificationIds">Notification Ids.</param>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.DeleteNotifications(System.String[])">
            <summary>
            Deletes notifications.
            </summary>
            <param name="notificationIds">Notification ids.</param>
            <returns>Result.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetNotifications(System.String,System.String)">
            <summary>
            Returns the notifications assigned to a user.
            </summary>
            <param name="assignedTo">User id.</param>
            <param name="keyword">Keyword.</param>
            <returns>Notifications assigned to a user.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.HasNotifications(System.String)">
            <summary>
            Indicates whether the user has notifications or not.
            </summary>
            <param name="assignedTo">Assigned to user id.</param>
            <returns></returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.InsertApprover(Wexflow.Core.Db.Approver)">
            <summary>
            Inserts an approver.
            </summary>
            <param name="approver">Approver.</param>
            <returns>Approver Id.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.UpdateApprover(System.String,Wexflow.Core.Db.Approver)">
            <summary>
            Inserts an approver.
            </summary>
            <param name="approverId">Approver Id.</param>
            <param name="approver">Approver.</param>
            <returns>Result.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.DeleteApprovedApprovers(System.String)">
            <summary>
            Deletes approved approvers of a record.
            </summary>
            <param name="recordId">Record Id.</param>
            <returns>Result.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.GetApprovers(System.String)">
            <summary>
            Retrieves approvers by record Id.
            </summary>
            <param name="recordId">Record Id.</param>
            <returns>Approvers.</returns>
        </member>
        <member name="M:Wexflow.Core.WexflowEngine.IsFileLocked(System.String)">
            <summary>
            Checks whether a file is locked or no.
            </summary>
            <param name="filePath">File path.</param>
            <returns>Result.</returns>
        </member>
        <member name="T:Wexflow.Core.Workflow">
            <summary>
            Workflow.
            </summary>
        </member>
        <member name="F:Wexflow.Core.Workflow.KEY_SIZE">
            <summary>
            This constant is used to determine the key size of the encryption algorithm in bits.
            We divide this by 8 within the code below to get the equivalent number of bytes.
            </summary>
        </member>
        <member name="F:Wexflow.Core.Workflow.DERIVATION_ITERATIONS">
            <summary>
            This constant determines the number of iterations for the password bytes generation function. 
            </summary>
        </member>
        <member name="F:Wexflow.Core.Workflow.PASS_PHRASE">
            <summary>
            PassPhrase.
            </summary>
        </member>
        <member name="F:Wexflow.Core.Workflow.START_ID">
            <summary>
            Default parent node id to start with in the execution graph.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.WexflowEngine">
            <summary>
            Wexflow engine.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.DbId">
            <summary>
            Database ID.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.StartedBy">
            <summary>
            Username of the user that started the workflow.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.ApprovedBy">
            <summary>
            Username of the user that started the workflow.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.RejectedBy">
            <summary>
            Username of the user that started the workflow.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.StoppedBy">
            <summary>
            Username of the user that started the workflow.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.FilePath">
            <summary>
            Workflow file path.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.Xml">
            <summary>
            XML of the workflow.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.WexflowTempFolder">
            <summary>
            Wexflow temp folder.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.WorkflowTempFolder">
            <summary>
            Workflow temp folder.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.ApprovalFolder">
            <summary>
            Approval folder.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.XsdPath">
            <summary>
            XSD path.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.Id">
            <summary>
            Workflow Id.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.Name">
            <summary>
            Workflow name.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.Description">
            <summary>
            Workflow description.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.LaunchType">
            <summary>
            Workflow lanch type.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.Period">
            <summary>
            Workflow period.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.CronExpression">
            <summary>
            Cron expression
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.IsEnabled">
            <summary>
            Shows whether this workflow is enabled or not.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.IsApproval">
            <summary>
            Shows whether this workflow is an approval workflow or not.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.EnableParallelJobs">
            <summary>
            Shows whether workflow jobs are executed in parallel. Otherwise jobs are queued. Defaults to true.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.IsWaitingForApproval">
            <summary>
            Shows whether this workflow is waiting for approval.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.IsRejected">
            <summary>
            Shows whether this workflow is rejected or not.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.IsRunning">
            <summary>
            Shows whether this workflow is running or not.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.IsPaused">
            <summary>
            Shows whether this workflow is suspended or not.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.Tasks">
            <summary>
            Workflow tasks.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.FilesPerTask">
            <summary>
            Workflow files.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.EntitiesPerTask">
            <summary>
            Workflow entities.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.JobId">
            <summary>
            Job Id.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.ParallelJobId">
            <summary>
            Parallel job Id.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.LogTag">
            <summary>
            Log tag.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.XmlNamespaceManager">
            <summary>
            Xml Namespace Manager.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.ExecutionGraph">
            <summary>
            Execution graph.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.XDoc">
            <summary>
            Workflow XDocument.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.XNamespaceWf">
            <summary>
            XNamespace.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.IsExecutionGraphEmpty">
            <summary>
            Shows whether the execution graph is empty or not.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.SharedMemory">
            <summary>
            Hashtable used as shared memory for tasks.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.Database">
            <summary>
            Database.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.GlobalVariables">
            <summary>
            Global variables.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.LocalVariables">
            <summary>
            Local variables.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.RestVariables">
            <summary>
            Rest variables.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.TasksFolder">
            <summary>
            Tasks folder.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.Jobs">
            <summary>
            Workflow jobs.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.InstanceId">
            <summary>
            Instance Id.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.Logs">
            <summary>
            Log messages.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.StartedOn">
            <summary>
            Started on date time.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.RetryCount">
            <summary>
            Number of retry times in case of failures. Default is 0.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.RetryTimeout">
            <summary>
            The retry timeout between two tries. Default is 1500ms.
            </summary>
        </member>
        <member name="P:Wexflow.Core.Workflow.JobStatus">
            <summary>
            Job Status.
            </summary>
        </member>
        <member name="M:Wexflow.Core.Workflow.#ctor(Wexflow.Core.WexflowEngine,System.Int32,System.Collections.Generic.Dictionary{System.Guid,Wexflow.Core.Workflow},System.String,System.String,System.String,System.String,System.String,System.String,Wexflow.Core.Db.Db,Wexflow.Core.Variable[])">
            <summary>
            Creates a new workflow.
            </summary>
            <param name="wexflowEngine">Wexflow engine.</param>
            <param name="jobId">First job Id.</param>
            <param name="jobs">Workflow jobs.</param>
            <param name="dbId">Database ID.</param>
            <param name="xml">XML of the workflow.</param>
            <param name="wexflowTempFolder">Wexflow temp folder.</param>
            <param name="tasksFolder">Tasks folder.</param>
            <param name="approvalFolder">Approval folder.</param>
            <param name="xsdPath">XSD path.</param>
            <param name="database">Database.</param>
            <param name="globalVariables">Global variables.</param>
        </member>
        <member name="M:Wexflow.Core.Workflow.ToString">
            <summary>
            Returns informations about this workflow.
            </summary>
            <returns>Informations about this workflow.</returns>
        </member>
        <member name="M:Wexflow.Core.Workflow.StartAsync(System.String,System.Collections.Generic.List{Wexflow.Core.Variable})">
            <summary>
            Starts this workflow asynchronously.
            </summary>
            <param name="startedBy">Username of the user that started the workflow.</param>
            <param name="restVariables">Rest variables</param>
            <returns>Instance Id.</returns>
        </member>
        <member name="M:Wexflow.Core.Workflow.StartSync(System.String,System.Guid,System.Boolean@,System.Collections.Generic.List{Wexflow.Core.Variable})">
            <summary>
            Starts this workflow synchronously.
            </summary>
            <param name="startedBy">Username of the user that started the workflow.</param>
            <param name="instanceId">Instance id.</param>
            <param name="resultWarning">Indicates whether the final result is warning or not.</param>
            <param name="restVariables">Rest variables</param>
            <returns>Result.</returns>
        </member>
        <member name="M:Wexflow.Core.Workflow.Stop(System.String)">
            <summary>
            Stops this workflow.
            </summary>
            <param name="stoppedBy">Username of the user who stopped the workflow.</param>
        </member>
        <member name="M:Wexflow.Core.Workflow.WaitOne">
            <summary>
            If "unset" the thread will wait otherwise it will continue.
            </summary>
        </member>
        <member name="M:Wexflow.Core.Workflow.Suspend">
            <summary>
            Suspends this workflow.
            </summary>
        </member>
        <member name="M:Wexflow.Core.Workflow.Resume">
            <summary>
            Resumes this workflow.
            </summary>
        </member>
        <member name="M:Wexflow.Core.Workflow.Approve(System.String)">
            <summary>
            Approves the current workflow.
            </summary>
            <param name="approvedBy">Username of the user who approved the workflow.</param>
        </member>
        <member name="M:Wexflow.Core.Workflow.Reject(System.String)">
            <summary>
            Rejects the current workflow.
            </summary>
            <param name="rejectedBy">Username of the user who rejected the workflow.</param>
        </member>
        <member name="T:Wexflow.Core.WorkflowJob">
            <summary>
            Quartz Workflow Job.
            </summary>
        </member>
        <member name="M:Wexflow.Core.WorkflowJob.Quartz#IJob#Execute(Quartz.IJobExecutionContext)">
            <summary>
            Executes workflow the job
            </summary>
            <param name="context">Job context.</param>
        </member>
    </members>
</doc>
