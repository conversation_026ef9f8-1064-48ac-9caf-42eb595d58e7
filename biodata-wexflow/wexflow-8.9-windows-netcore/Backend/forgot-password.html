﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>Wexflow</title>
    <link rel="icon" href="images/logo.png" type="image/png">

    <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="css/jquery.toast.min.css" />
    <link rel="stylesheet" type="text/css" href="css/globals.css" />
    <link rel="stylesheet" type="text/css" href="css/forgot-password.css" />

    <!--<link rel="stylesheet" type="text/css" href="css/forgot-password.min.css" />-->

    <script type="text/javascript" src="js/settings.js"></script>
    <script type="text/javascript" src="js/language.js"></script>

    <script type="text/javascript" src="js/jquery.min.js"></script>
    <script type="text/javascript" src="js/jquery.toast.min.js"></script>
    <script type="text/javascript" src="js/bootstrap.min.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/language.core.js"></script>
    <script type="text/javascript" src="js/forgot-password.js"></script>

    <!--<script type="text/javascript" src="js/forgot-password.min.js"></script>-->
</head>
<body>
    <div id="header">
        <div id="lang" href="#" class="header-right"></div>
        <a id="about" href="https://wexflow.github.io" class="header-right">About</a>
        <a id="help" href="https://github.com/aelassas/Wexflow/wiki" class="header-right">Help</a>
    </div>
    <div id="container">
        <table>
            <tr>
                <td>
                    <label id="lbl-username">Username</label>
                </td>
                <td>
                    <input type="text" name="username" id="txt-username" class="text" autocomplete="off" />
                </td>
                <td>
                    <input id="btn-submit" type="submit" value="Submit" class="btn btn-primary btn-xs" />
                </td>
            </tr>
        </table>
    </div>
    <script type="text/javascript">
        window.onload = function () {
            new window.ForgotPassword();
        };
    </script>
</body>
</html>