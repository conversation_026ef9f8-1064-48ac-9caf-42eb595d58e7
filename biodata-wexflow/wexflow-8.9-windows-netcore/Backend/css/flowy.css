.dragging {
    z-index: 111 !important
}

.block {
    position: absolute;
    z-index: 9
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 60px;
    background-color: #217ce8;
    margin-top: -5px;
    opacity: 1;
    transition: all .3s cubic-bezier(.05,.03,.35,1);
    transform: scale(1);
    position: absolute;
    z-index: 2;
    visibility: hidden;
}

.invisible {
    opacity: 0 !important;
    transform: scale(0)
}

/*.indicator:after {
    content: "";
    display: block;
    width: 12px;
    height: 12px;
    background-color: #217ce8;
    transform: scale(1.7);
    opacity: .2;
    border-radius: 60px;
}*/

.arrowblock {
    position: absolute;
    width: 100%;
    overflow: visible;
    pointer-events: none;
}

    .arrowblock svg {
        width: -webkit-fill-available;
        overflow: visible;
    }
