﻿/*navigation*/
#navigation {
    height: 71px;
    background-color: #FFF;
    border: 1px solid #E8E8EF;
    width: 100%;
    box-sizing: border-box;
    position: fixed;
    top: 45px;
    z-index: 9;
    display: none;
}

#right-side {
    float: right;
    margin-top: 15px;
}

#btn-new-record {
    font-family: Roboto;
    font-weight: 500;
    font-size: 14px;
    color: #FFF;
    background-color: #447eb1;
    border-radius: 5px;
    width: 143px;
    height: 38px;
    margin-left: 10px;
    display: inline-block;
    vertical-align: top;
    text-align: center;
    line-height: 38px;
    margin-right: 20px;
    transition: all .2s cubic-bezier(.05,.03,.35,1);
}

#btn-delete {
    font-family: Roboto;
    font-weight: 500;
    font-size: 14px;
    color: #FFF;
    background-color: #D9534F;
    border-radius: 5px;
    width: 143px;
    height: 38px;
    margin-left: 10px;
    display: inline-block;
    vertical-align: top;
    text-align: center;
    line-height: 38px;
    margin-right: 20px;
    transition: all .2s cubic-bezier(.05,.03,.35,1);
}

#btn-new-record:hover, #btn-delete:hover {
    cursor: pointer;
    opacity: .9;
}

#left-side {
    display: inline-block;
    vertical-align: middle;
    margin-left: 20px;
}

#search input {
    width: 318px;
    height: 40px;
    background-color: #FFF;
    border: 1px solid #E8E8EF;
    box-sizing: border-box;
    box-shadow: 0px 2px 8px rgba(34,34,87,0.05);
    border-radius: 5px;
    text-indent: 35px;
    font-family: Roboto;
    font-size: 16px;
}

#search {
    margin-top: 13px;
}

#search img {
    position: absolute;
    margin-top: 10px;
    width: 18px;
    margin-left: 12px;
}

/*content*/
#content {
    display: none;
    position: absolute;
    top: 120px;
    right: 0;
    left: 0;
    bottom: 0;
    overflow: auto;
}

#content .thead-dark {
    background: #272727;
    color: #EEEEEE;
}

#content tbody tr:hover {
    background: #EEEEEE;
    cursor: pointer;
}

#content #records-table .id {
    display: none;
}

#content #records-table .check {
    width: 45px;
}

#content #records-table .name {
    width: 350px;
}

/*edit-record*/
#edit-record{
    display: none;
}

.edit-record-table{
    width: 730px;
}

.edit-record-title {
    font-weight: bold;
    width: 150px;
}

.edit-record-value{
    width: calc(100% - 150px);
}

.edit-record-value input, textarea{
    width: 100%;
    margin-bottom: 7px;
}

.edit-record-value textarea{
    height: 100px;
    resize: vertical;
}

.record-versions {
    width: 100%;
}

.version-file-name{
    width: 300px;
}

.version-created-on{
    width: 150px;
}

.version-file-size {
    width: 100px;
}

.version-delete{
    width: 50px;
}

.version-delete input{
    width: auto;
}

#edit-record-footer {
    display: none;
}

#edit-record-footer-content {
    height: 40px;
    background: #fff;
}

.record-save {
    font-family: Roboto;
    font-weight: 500;
    font-size: 14px;
    color: #FFF;
    background-color: #447eb1;
    border-radius: 5px;
    width: 143px;
    height: 38px;
    margin-left: 10px;
    display: inline-block;
    vertical-align: top;
    text-align: center;
    line-height: 38px;
    margin-right: 20px;
    transition: all .2s cubic-bezier(.05,.03,.35,1);
    float: right;
}

.record-cancel {
    font-family: Roboto;
    font-weight: 500;
    font-size: 14px;
    color: #FFF;
    background-color: #E8942D;
    border-radius: 5px;
    width: 143px;
    height: 38px;
    margin-left: 10px;
    display: inline-block;
    vertical-align: top;
    text-align: center;
    line-height: 38px;
    margin-right: 20px;
    transition: all .2s cubic-bezier(.05,.03,.35,1);
    float: right;
}

.record-delete {
    font-family: Roboto;
    font-weight: 500;
    font-size: 14px;
    color: #FFF;
    background-color: #D9534F;
    border-radius: 5px;
    width: 143px;
    height: 38px;
    margin-left: 10px;
    display: inline-block;
    vertical-align: top;
    text-align: center;
    line-height: 38px;
    margin-right: 20px;
    transition: all .2s cubic-bezier(.05,.03,.35,1);
    float: right;
}

.record-save:hover, .record-cancel:hover, .record-delete:hover {
    cursor: pointer;
    opacity: .9;
}

.record-versions .version-id{
    display: none;
}

.edit-record-td-approvers {
    height: 55px;
}

.record-approvers tbody tr:hover {
    background: #EEEEEE;
}

/*.row-approved {
    background: #c5f1c5;
}*/

.label {
    color: #EEEEEE;
    padding: 5px;
}

.label-approved {
    background-color: #272727;
}