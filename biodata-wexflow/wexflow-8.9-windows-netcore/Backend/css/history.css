﻿#entries-action {
    position: absolute;
    top: 55px;
    right: 0;
    left: 5px;
    display: none;
}

    #entries-action label {
        font-weight: bold;
        margin-top: 3px;
    }

    #entries-action .action-right {
        float: right;
    }

    #entries-action #btn-search {
        margin-right: 10px;
        margin-left: 5px;
        margin-top: 1px;
    }

    #entries-action #txt-from, #txt-to {
        width: 120px;
        margin-right: 15px;
        margin-left: 5px;
    }

#entries {
    position: absolute;
    top: 90px;
    right: 0;
    left: 10px;
    bottom: 0;
    overflow: hidden;
    display: none;
}

    /* table style */
    #entries #entries-table {
        display: block;
        table-layout: fixed;
        margin: 0;
    }

        #entries #entries-table thead, tbody {
            display: block;
        }

        #entries #entries-table tbody {
            overflow-y: auto;
            overflow-x: auto;
        }

        #entries #entries-table .thead-dark {
            background: #272727;
            color: #EEEEEE;
        }

        #entries #entries-table tbody tr:hover {
            background: #EEEEEE;
        }

        #entries #entries-table .status {
            width: 120px;
        }

        #entries #entries-table .date {
            width: 135px;
        }

        #entries #entries-table .id {
            width: 45px;
        }

        #entries #entries-table .name {
            width: 200px;
        }

        #entries #entries-table .lt {
            width: 100px;
        }

        #entries #entries-table .desc {
            width: 200px;
        }

        #entries #entries-table tr:hover {
            cursor: pointer;
        }

        #entries #entries-table th:hover {
            cursor: pointer;
        }

        #entries #entries-table .selected, #entries #entries-table .selected:hover {
            background-color: #ffb347;
        }

#entries-table img {
    margin-right: 10px;
}
