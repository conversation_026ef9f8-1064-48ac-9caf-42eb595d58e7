﻿body {
    background: #fafafa;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 13px;
    font-weight: 400;
    line-height: 1.43;
}

.jq-toast-single {
    width: 300px;
}

#header {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 43px;
    background: #29a85c;
    color: #fff;
    box-shadow: 0px 2px 4px -1px rgb(0 0 0 / 20%), 0px 4px 5px 0px rgb(0 0 0 / 14%), 0px 1px 10px 0px rgb(0 0 0 / 12%);
}

    #header a {
        color: #FFFFFF;
        font-size: 12px;
        margin: 20px;
        float: left;
        font-weight: bold;
        -webkit-font-smoothing: antialiased;
        line-height: 5px;
        white-space: nowrap;
    }

    #header .header-right {
        float: right;
    }

#lang {
    margin-top: 10px;
    margin-right: 20px;
    color: #000;
}

    #lang .lang {
        cursor: pointer;
        padding-right: 3px;
        padding-left: 3px;
    }

        #lang .lang:hover {
            opacity: .8;
        }

.dropdown-menu {
    min-width: 95px;
}

#lnk-notifications {
    margin: 0 !important;
    margin-top: 10px !important;
}

.btn-primary:hover {
    opacity: .9;
}

input {
    border: 1px solid #dadada;
    border-radius: 4px;
    padding: 2px;
}

.jBox-overlay {
    background-color: rgba(0, 0, 0, .72);
}

#lnk-manager, #lnk-designer, #lnk-approval, #lnk-records, #lnk-users, #lnk-profiles, #lnk-notifications {
    display: none;
}
