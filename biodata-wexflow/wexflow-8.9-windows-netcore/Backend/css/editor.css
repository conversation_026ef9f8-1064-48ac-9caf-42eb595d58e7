﻿
body {
    background: #FFFFFF;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 12px;
}

.jq-toast-single {
    width: 300px;
}

.toggle{
    display:none;
}
#header {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 43px;
    background: #29A85C;
    color: #FFFFFF;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.3);
}

#header a {
    color: #FFFFFF;
    font-size: 12px;
    margin: 20px;
    float: left;
    font-weight: bold;
    -webkit-font-smoothing: antialiased;
    line-height: 5px;
    white-space: nowrap;
}

#header a:hover {
    text-decoration: underline;
}

#header .header-right{
	float: right;
}

#header #lnk-manager, #lnk-designer, #lnk-editor, #lnk-approval, #lnk-users, #lnk-profiles {
    display: none;
}

#wf-designer{
	position: absolute; 
	top: 35px; 
	right:0; 
	bottom:0; 
	left:0;
    display: none;
}

#wf-container {
    font-size: 12px;
    position: relative;
    min-width: 600px;
    min-height: 400px;
    width: 100%;
    height: 100%;
}

#wf-search {
    position: absolute;
    top: 17px;
    left: 10px;
}

#wf-search #wf-search-text-container {
    float: left;
}

#wf-search #wf-search-text-container #wf-search-text {
    width: 140px;
}

#wf-search #wf-search-action {
    margin-left: 10px;
    width: 70px;
}

#wf-workflows {
    position: absolute;
    top: 55px;
    bottom: 0;
    left: 10px;
    overflow: auto;
    width: 250px;
}
/* wf-workflows-table */
#wf-workflows #wf-workflows-table .wf-id
{
    width: 45px;    
}

#wf-workflows #wf-workflows-table .wf-n
{
    width: 200px;    
}

#wf-workflows #wf-workflows-table tbody tr:hover
{
    cursor: pointer;  
}

#wf-workflows #wf-workflows-table .selected, #wf-workflows #wf-workflows-table .selected:hover
{
    background-color: #ffb347;
}

#wf-workflows #wf-workflows-table .thead-dark {
    background: #272727;
    color: #EEEEEE;
}

#wf-workflows #wf-workflows-table tbody tr:hover{
    background: #EEEEEE;
}

/* Wexflow Designer right panel*/
#wf-container button {
    cursor: pointer;
}

#wf-designer-right-panel {
    position: absolute;
    top: 63px;
    right: 0;
    bottom: 0;
    left: 270px;
    overflow-y: auto;
    overflow-x: hidden;
}

#wf-designer-right-panel a:visited, a:active, a:link{
    text-decoration: none;
}

#wf-designer-right-panel a:hover{
    text-decoration: underline;
}

#wf-local-vars, #wf-tasks, .wf-task, .wf-add-setting-title {
    margin-top: 10px;
}

#wf-local-vars h5 button {
    float: right;
}

.wf-add-setting-title {
    margin-bottom: 10px;
}

.wf-designer-table {
    border-spacing: 0;
    border: #ddd solid 1px;
}

    .wf-designer-table tr {
        background-color: #f1f1f1;
    }

        .wf-designer-table tr > td {
            padding: 8px;
            vertical-align: top;
        }

    .wf-designer-table .wf-title {
        text-align: left;
        vertical-align: bottom;
        /* border-bottom: 1px solid #373737; */
        background-color: #f1f1f1;
        color: #373737;
        font-weight: bold;
    }

.wf-task-title {
    background-color: #f1f1f1;
    color: #373737;
    padding: 10px;
    margin-bottom: 0;
    border-top: #ddd 1px solid;
    border-right: #ddd 1px solid;
    border-left: #ddd 1px solid;
}


.wf-value, .wf-value> input {
    width: 100%;
}

.wf-setting-value-td, .wf-setting-value-td input {
    width: 100%;
}

.wf-action-left {
    width: 94px;    
}

.wf-action-right {
    float: right;
    margin-right: 10px;
    width: 94px;
    display: none;
}
.wf-task-id, .wf-task-name, .wf-task-desc, .wf-task-enabled, .wf-settings, .wf-setting-name, .wf-setting-value, .wf-attribute-name, .wf-attribute-value, .wf-remove-setting, .wf-add-attribute, .wf-remove-attribute, .wf-attributes, .wf-task, .wf-task-title-label, .wf-taskxml-container {
    visibility: visible;
}
.wf-add-attribute {
    width: 100px;
}
.wf-add-setting {
    margin-left: 10px;
}
.wf-show-taskxml, .wf-show-doc {
    margin-right: 10px;
}

.wf-remove-task, .wf-add-setting, .wf-show-taskxml, .wf-show-doc {
    float: right;
}
.wf-add-setting {
    margin-right: 10px;
}

#wf-add-task {
    margin-top: 15px;
    margin-bottom: 15px;
    display: none;
}
.wf-add-attribute-td {
    display: none;
}
.wf-remove-task {
    display: none;
}
#wf-add-workflow, #wf-xml {
    width: 94px;
}
#wf-action {
    position: absolute;
    top: 10px;
    right: 0;
    left: 263px;
    padding: 7px;
    display: none;
}

#wf-execution-graph-title {
    background-color: #f1f1f1;
    padding: 10px;
    margin-top: 10px;
}

#wf-execution-graph {
    position: absolute;
    right: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
}

.wf-taskxml {
    display: none;
}

/*end of table style*/

.wf-var-key{
    width: 150px !important;
}