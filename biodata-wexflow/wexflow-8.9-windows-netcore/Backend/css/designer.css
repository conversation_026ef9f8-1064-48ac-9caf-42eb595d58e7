﻿body,
html {
    margin: 0px;
    padding: 0px;
    overflow: hidden;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImdyaWQiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTSAwIDEwIEwgNDAgMTAgTSAxMCAwIEwgMTAgNDAgTSAwIDIwIEwgNDAgMjAgTSAyMCAwIEwgMjAgNDAgTSAwIDMwIEwgNDAgMzAgTSAzMCAwIEwgMzAgNDAiIGZpbGw9Im5vbmUiIHN0cm9rZT0iI2QwZDBkMCIgb3BhY2l0eT0iMC4yIiBzdHJva2Utd2lkdGg9IjEiLz48cGF0aCBkPSJNIDQwIDAgTCAwIDAgMCA0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZDBkMGQwIiBzdHJva2Utd2lkdGg9IjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48L3N2Zz4=");
    background-position: -1px -1px;
    background-color: rgba(0, 0, 0, .015);
}

#navigation,
#leftcard,
#propwrap,
#wfclose,
#wfpropwrap,
#canvas {
    display: none;
}

#navigation {
    height: 71px;
    background-color: #FFF;
    border: 1px solid #E8E8EF;
    width: 100%;
    box-sizing: border-box;
    position: fixed;
    top: 45px;
    z-index: 9;
    user-select: none;
}

#back {
    width: 40px;
    height: 40px;
    border-radius: 100px;
    background-color: #F1F4FC;
    text-align: center;
    display: inline-block;
    vertical-align: top;
    margin-top: 12px;
    margin-right: 10px
}

    #back img {
        margin-top: 13px;
    }

#names {
    display: inline-block;
    vertical-align: top;
}

#title {
    font-weight: 500;
    font-size: 16px;
    color: #393C44;
    margin-bottom: 0px;
}

#subtitle {
    color: #808292;
    font-size: 14px;
    margin-top: 5px;
}

#leftside {
    display: inline-block;
    vertical-align: middle;
    margin-left: 20px;
}

#centerswitch {
    position: absolute;
    width: 422px;
    left: 50%;
    margin-left: -371px;
    top: 15px;
}

#leftswitch {
    border: 1px solid #E8E8EF;
    background-color: #F0F0F0;
    width: 111px;
    height: 39px;
    line-height: 39px;
    border-radius: 5px 0px 0px 5px;
    color: #393C44;
    display: inline-block;
    font-size: 14px;
    text-align: center;
    cursor: pointer;
}

#graphswitch {
    color: #808292;
    border-radius: 0px 5px 5px 0px;
    border: 1px solid #E8E8EF;
    height: 39px;
    width: 102px;
    display: inline-block;
    font-size: 14px;
    line-height: 39px;
    text-align: center;
    margin-left: -5px;
    cursor: pointer;
}

#middleswitch {
    color: #808292;
    border-radius: 0px 5px 5px 0px;
    border: 1px solid #E8E8EF;
    height: 39px;
    width: 102px;
    display: inline-block;
    font-size: 14px;
    line-height: 39px;
    text-align: center;
    margin-left: -5px;
    cursor: pointer;
}

#rightswitch {
    color: #808292;
    border-radius: 0px 5px 5px 0px;
    border: 1px solid #E8E8EF;
    height: 39px;
    width: 102px;
    display: inline-block;
    font-size: 14px;
    line-height: 39px;
    text-align: center;
    margin-left: -5px;
    cursor: pointer;
}

    #leftswitch:hover,
    #middleswitch:hover,
    #rightswitch:hover,
    #graphswitch:hover {
        border: 1px solid #E8E8EF;
        opacity: .7;
    }

#exporttype {
    width: 100px;
}

#export,
#import,
#run,
#save {
    font-weight: 500;
    font-size: 14px;
    color: #A6A6B3;
    width: 95px;
    height: 38px;
    border: 1px solid #E8E8EF;
    border-radius: 5px;
    text-align: center;
    line-height: 38px;
    display: inline-block;
    vertical-align: top;
    transition: all .2s cubic-bezier(.05, .03, .35, 1);
}

#run,
#save {
    background-color: #00455f;
    color: #fff;
}

#save {
    margin-right: 20px;
}

    #export:hover,
    #import:hover,
    #run:hover,
    #save:hover {
        cursor: pointer;
    }

#export:hover,
#import:hover {
    opacity: .7;
}

#run:hover,
#save:hover {
    opacity: .85;
}

#browse {
    font-weight: 500;
    font-size: 14px;
    color: #A6A6B3;
    width: 95px;
    height: 38px;
    border: 1px solid #E8E8EF;
    border-radius: 5px;
    text-align: center;
    line-height: 38px;
    display: inline-block;
    vertical-align: top;
    transition: all .2s cubic-bezier(.05, .03, .35, 1);
    margin-top: 15px;
}

    #browse:hover {
        cursor: pointer;
        opacity: .8;
    }

#newworkflow {
    font-weight: 500;
    font-size: 14px;
    color: #FFF;
    background-color: #217ce8;
    border-radius: 5px;
    width: 143px;
    height: 38px;
    margin-left: 10px;
    display: inline-block;
    vertical-align: top;
    text-align: center;
    line-height: 38px;
    transition: all .2s cubic-bezier(.05, .03, .35, 1);
}

    #newworkflow:hover {
        cursor: pointer;
        opacity: .9;
    }

#buttonsright {
    float: right;
    margin-top: 15px;
}

#leftcard {
    width: 363px;
    background-color: #FFF;
    border: 1px solid #E8E8EF;
    box-sizing: border-box;
    padding-top: 60px;
    padding-left: 20px;
    height: 100%;
    position: absolute;
    top: 45px;
    left: -361px;
    z-index: 2;
}

#search input {
    width: 318px;
    height: 40px;
    background-color: #FFF;
    border: 1px solid #E8E8EF;
    box-sizing: border-box;
    box-shadow: 0px 2px 8px rgba(34, 34, 87, 0.05);
    border-radius: 5px;
    text-indent: 35px;
    font-size: 16px;
}

#searchworkflows input {
    width: 318px;
    height: 40px;
    background-color: #FFF;
    border: 1px solid #E8E8EF;
    box-sizing: border-box;
    box-shadow: 0px 2px 8px rgba(34, 34, 87, 0.05);
    border-radius: 5px;
    text-indent: 35px;
    font-size: 16px;
}

::-webkit-input-placeholder {
    /* Edge */
    color: #C9C9D5;
}

:-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: #C9C9D5
}

/*::placeholder {
color: #C9C9D5;
}*/

#search {
    margin-top: 13px;
}

    #search img {
        position: absolute;
        margin-top: 10px;
        width: 18px;
        margin-left: 12px;
    }

#searchworkflows img {
    position: absolute;
    margin-top: 10px;
    width: 18px;
    margin-left: 12px;
}

/*#header {
font-size: 20px;

font-weight: bold;
color: #393C44;
}*/
#triggers {
    margin-left: 20px;
    font-weight: 500;
    font-size: 14px;
    text-align: center;
    color: #808292;
    width: calc(88% / 3);
    height: 48px;
    line-height: 48px;
    display: inline-block;
    float: left;
}

.navactive:after {
    display: block;
    content: "";
    width: 100%;
    height: 4px;
    background-color: #217ce8;
    margin-top: -4px;
}

#actions {
    font-weight: 500;
    color: #808292;
    font-size: 14px;
    height: 48px;
    line-height: 48px;
    width: calc(88% / 3);
    text-align: center;
    float: left;
}

#loggers {
    width: calc(88% / 3);
    display: inline-block;
    font-weight: 500;
    color: #808292;
    font-size: 14px;
    height: 48px;
    line-height: 48px;
    text-align: center;
}

#footer {
    position: absolute;
    left: 0;
    padding-left: 20px;
    line-height: 40px;
    bottom: 0;
    width: 362px;
    border: 1px solid #E8E8EF;
    height: 67px;
    box-sizing: border-box;
    background-color: #FFF;
    font-size: 14px;
}

    #footer a {
        text-decoration: none;
        color: #393C44;
        transition: all .2s cubic-bezier(.05, .03, .35, 1);
    }

        #footer a:hover {
            opacity: .9;
        }

    #footer span {
        color: #808292;
    }

    #footer p {
        display: inline-block;
        color: #808292;
    }

    #footer img {
        margin-left: 5px;
        margin-right: 5px;
    }

.blockelem:first-child {
    margin-top: 20px
}

.blockelem {
    padding-top: 10px;
    width: 318px;
    border: 1px solid transparent;
    transition-property: box-shadow, height;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(.05, .03, .35, 1);
    border-radius: 5px;
    box-shadow: 0px 0px 30px rgba(22, 33, 74, 0);
    box-sizing: border-box;
}

    .blockelem:hover {
        box-shadow: 0px 4px 30px rgba(22, 33, 74, 0.08);
        border-radius: 5px;
        background-color: #FFF;
        cursor: pointer;
    }

.grabme,
.blockico {
    display: inline-block;
}

.grabme {
    margin-top: 10px;
    margin-left: 10px;
    margin-bottom: -14px;
    width: 15px;
}

#blocklist {
    height: calc(100% - 95px);
    overflow: auto;
    margin-left: -20px;
    padding-left: 20px;
}

#proplist {
    height: calc(100% - 145px);
    overflow: auto;
    padding-top: 10px;
}

.blockin {
    display: inline-block;
    vertical-align: top;
    margin-left: 12px;
}

.blockico {
    width: 36px;
    height: 36px;
    background-color: #F1F4FC;
    border-radius: 5px;
    text-align: center;
    white-space: nowrap;
}

    .blockico span {
        height: 100%;
        width: 0px;
        display: inline-block;
        vertical-align: middle;
    }

    .blockico img {
        vertical-align: middle;
        margin-left: auto;
        margin-right: auto;
        display: inline-block;
    }

.blocktext {
    display: inline-block;
    width: 220px;
    vertical-align: top;
    margin-left: 12px
}

.blocktitle {
    margin: 0px !important;
    padding: 0px !important;
    font-weight: 500;
    font-size: 16px;
    color: #393C44;
}

.blockdesc {
    margin-top: 5px;
    color: #808292;
    font-size: 14px;
    line-height: 21px;
}

.blockdisabled {
    background-color: #F0F2F9;
    opacity: .5;
}

#closecard {
    position: absolute;
    margin-left: 340px;
    margin-top: 11px;
    background-color: #FFF;
    border-bottom: 1px solid #E8E8EF;
    border-right: 1px solid #E8E8EF;
    border-top: 1px solid #E8E8EF;
    width: 53px;
    height: 53px;
    text-align: center;
    z-index: 10;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

    #closecard img {
        margin-top: 15px
    }

    #closecard:hover {
        cursor: pointer;
        opacity: .9;
    }

#canvas {
    position: absolute;
    /*    width: calc(100% - 672px);
    height: calc(100% - 115px);*/
    top: 115px;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 0;
    overflow-y: auto;
    overflow-x: hidden;
}

#propwrap {
    position: absolute;
    right: 0;
    top: 115px;
    width: 311px;
    height: 100%;
    padding-left: 20px;
    overflow: hidden;
    z-index: -2;
}

#properties {
    position: absolute;
    height: 100%;
    width: 311px;
    background-color: #FFF;
    right: -150px;
    opacity: 0;
    z-index: 2;
    top: 0;
    box-shadow: -4px 0px 40px rgba(26, 26, 73, 0);
    padding-left: 20px;
    transition: all .25s cubic-bezier(.05, .03, .35, 1);
}

.itson {
    z-index: 2 !important;
}

.expanded {
    right: 0 !important;
    opacity: 1 !important;
    box-shadow: -4px 0px 40px rgba(26, 26, 73, 0.05);
    z-index: 2;
}

#header2 {
    font-size: 20px;
    font-weight: bold;
    color: #393C44;
}

#taskdoc {
    position: absolute;
    top: 7px;
    cursor: pointer;
}

    #taskdoc.badge {
        background-color: #1a73e8;
    }

    #taskdoc:hover {
        opacity: .9;
    }

#close {
    position: absolute;
    right: 20px;
    z-index: 9999;
    transition: all .25s cubic-bezier(.05, .03, .35, 1);
}

    #close:hover {
        cursor: pointer;
        opacity: .9;
    }

#wfpropwrap {
    position: fixed;
    right: -331px;
    top: 115px;
    width: 311px;
    height: 100%;
    bottom: 0;
    z-index: 2;
}

#wfproperties {
    position: absolute;
    height: 100%;
    width: 311px;
    background-color: #FFF;
    top: 0;
    bottom: 0;
    padding-left: 20px;
    transition: all .25s cubic-bezier(.05, .03, .35, 1);
    right: 0 !important;
    opacity: 1 !important;
    box-shadow: -4px 0px 40px rgba(26, 26, 73, 0.05);
    z-index: 2;
}

#wfclose {
    position: absolute;
    top: 117px;
    right: 0;
    background-color: #FFF;
    border-bottom: 1px solid #E8E8EF;
    border-left: 1px solid #E8E8EF;
    border-top: 1px solid #E8E8EF;
    width: 53px;
    height: 53px;
    text-align: center;
    z-index: 10;
}

    #wfclose:hover {
        cursor: pointer;
        opacity: .9;
    }

    #wfclose img {
        margin-top: 15px
    }

#wfheader2 {
    font-size: 20px;
    font-weight: bold;
    color: #393C44;
}

#savehelp {
    font-size: 13px;
    color: #515151;
}

#wfproplist {
    height: calc(100% - 255px);
    overflow: auto;
    padding-top: 10px;
}

.inputlabel {
    font-size: 14px;
    font-weight: bold;
    color: #253134;
    margin-top: 3px;
}

.inputtext {
    width: calc(100% - 10px);
}

.dropme {
    background-color: #FFF;
    border-radius: 5px;
    border: 1px solid #E8E8EF;
    box-shadow: 0px 2px 8px rgba(34, 34, 87, 0.05);
    font-size: 14px;
    color: #253134;
    text-indent: 20px;
    height: 40px;
    line-height: 40px;
    width: 287px;
    margin-bottom: 25px;
}

    .dropme img {
        margin-top: 17px;
        float: right;
        margin-right: 15px;
    }

.checkus {
    margin-bottom: 10px;
}

    .checkus img {
        display: inline-block;
        vertical-align: middle;
    }

    .checkus p {
        display: inline-block;
        font-size: 14px;
        vertical-align: middle;
        margin-left: 10px;
    }

#divisionthing {
    height: 1px;
    width: 100%;
    background-color: #E8E8EF;
    position: absolute;
    right: 0;
    bottom: 80px;
}

#removeblock {
    border-radius: 5px;
    position: absolute;
    font-size: 14px;
    text-align: center;
    width: 287px;
    height: 38px;
    line-height: 38px;
    color: #fff;
    border: 1px solid #E8E8EF;
    transition: all .3s cubic-bezier(.05, .03, .35, 1);
    margin-top: 10px;
    background-color: #d9534f;
}

    #removeblock:hover {
        cursor: pointer;
        opacity: .9;
    }

#removeworkflow {
    border-radius: 5px;
    position: absolute;
    font-size: 14px;
    text-align: center;
    width: 287px;
    height: 38px;
    line-height: 38px;
    color: #fff;
    border: 1px solid #E8E8EF;
    transition: all .3s cubic-bezier(.05, .03, .35, 1);
    background-color: #d9534f;
    margin-top: 55px;
    display: none;
}

    #removeworkflow:hover {
        cursor: pointer;
        opacity: .9;
    }

.noselect {
    -webkit-touch-callout: none;
    /* iOS Safari */
    -webkit-user-select: none;
    /* Safari */
    /*-khtml-user-select: none;*/
    /* Konqueror HTML */
    -moz-user-select: none;
    /* Old versions of Firefox */
    -ms-user-select: none;
    /* Internet Explorer/Edge */
    user-select: none;
    /* Non-prefixed version, currently
							  supported by Chrome, Opera and Firefox */
}

.blockyname {
    font-weight: 500;
    color: #253134;
    display: inline-block;
    vertical-align: middle;
    margin-left: 8px;
    font-size: 16px;
}

.blockyleft img {
    display: inline-block;
    vertical-align: middle;
}

.blockyright {
    display: inline-block;
    float: right;
    vertical-align: middle;
    margin-right: 0px;
    margin-top: 5px;
    width: 28px;
    height: 28px;
    border-radius: 5px;
    text-align: center;
    background-color: #FFF;
    transition: all .3s cubic-bezier(.05, .03, .35, 1);
    z-index: 10;
}

    /*.blockyright:hover {
background-color: #F1F4FC;
cursor: pointer;
}

.blockyright img {
margin-top: 12px;
}*/

    .blockyright .removediagblock {
        margin-top: -15px;
    }

        .blockyright .removediagblock:hover {
            background-color: #F1F4FC;
            cursor: pointer;
        }

.blockyleft {
    display: inline-block;
    margin-left: 20px;
}

.blockydiv {
    width: 100%;
    height: 1px;
    background-color: #E9E9EF;
}

.blockyinfo {
    font-size: 14px;
    color: #808292;
    margin-top: 15px;
    padding: 0 10px;
    margin-bottom: 20px;
}

    .blockyinfo span {
        color: #253134;
        font-weight: 500;
        display: inline-block;
        border-bottom: 1px solid #D3DCEA;
        line-height: 20px;
        text-indent: 0px;
    }

.block {
    background-color: #FFF;
    margin-top: 0px !important;
    box-shadow: 0px 4px 30px rgba(22, 33, 74, 0.05);
}

.selectedblock {
    border: 2px solid #6ca5ec;
    box-shadow: 0px 4px 30px rgba(22, 33, 74, 0.08);
}

@media only screen and (max-width: 832px) {
    #centerswitch {
        display: none;
    }
}

@media only screen and (max-width: 560px) {
    #names {
        display: none;
    }
}

#code-container {
    display: none;
    position: absolute;
    top: 116px;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
}

#browser {
    display: none;
}

/* wf-workflows-table */
.wf-workflows-table {
    display: block;
    table-layout: fixed;
    margin: 0;
}

    .wf-workflows-table thead,
    tbody {
        display: block;
    }

    .wf-workflows-table tbody {
        height: calc(100% - 50px);
        overflow-y: auto;
        overflow-x: auto;
    }

    .wf-workflows-table .wf-id {
        width: 65px;
    }

    .wf-workflows-table .wf-n {
        width: 300px;
    }

    .wf-workflows-table .wf-d {
        width: 500px;
    }

    .wf-workflows-table tbody tr:hover {
        cursor: pointer;
    }

    .wf-workflows-table .selected,
    .wf-workflows-table .selected:hover {
        background-color: #ffb347;
    }

    .wf-workflows-table .thead-dark {
        background: #272727;
        color: #EEEEEE;
    }

    .wf-workflows-table tbody tr:hover {
        background: #EEEEEE;
    }

    .wf-workflows-table .selected,
    #wf-workflows .wf-workflows-table .selected:hover {
        background-color: #ffb347;
    }

#openworkflow {
    font-weight: 500;
    font-size: 14px;
    color: #FFF;
    background-color: #217ce8;
    border-radius: 5px;
    width: 143px;
    height: 38px;
    margin-left: 10px;
    vertical-align: top;
    text-align: center;
    line-height: 38px;
    margin-right: 20px;
    transition: all .2s cubic-bezier(.05, .03, .35, 1);
    float: right;
}

    #openworkflow:hover {
        cursor: pointer;
        opacity: .9;
    }

#deleteworkflows {
    font-weight: 500;
    font-size: 14px;
    color: #FFF;
    background-color: #d9534f;
    border-radius: 5px;
    width: 143px;
    height: 38px;
    margin-left: 10px;
    display: inline-block;
    vertical-align: top;
    text-align: center;
    line-height: 38px;
    margin-right: 5px;
    transition: all .2s cubic-bezier(.05, .03, .35, 1);
    float: right;
}

    #deleteworkflows:hover {
        cursor: pointer;
        opacity: .9;
    }

#exportmodal {
    display: none;
}

#exportworkflow {
    font-weight: 500;
    font-size: 14px;
    color: #FFF;
    background-color: #217ce8;
    border-radius: 5px;
    width: 143px;
    height: 38px;
    margin-left: 10px;
    display: inline-block;
    vertical-align: top;
    text-align: center;
    line-height: 38px;
    margin-right: 20px;
    transition: all .2s cubic-bezier(.05, .03, .35, 1);
    float: right;
}

    #exportworkflow:hover {
        cursor: pointer;
        opacity: .9;
    }

.jBox-footer {
    height: 55px;
}

.jq-toast-wrap {
    z-index: 99999 !important;
}

#overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .72);
    opacity: 1;
    z-index: 9999;
    display: none;
}

    #overlay #onverlay-content {
        color: #fff;
        position: absolute;
        margin: auto;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        width: 100px;
        height: 100px;
        font-size: 40px;
    }

#blocklyArea {
    display: none;
    position: absolute;
    top: 116px;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
}

#blocklyDiv {
    position: absolute;
}

#wf-add-var {
    float: right;
}

.wf-local-vars {
    width: 100%;
    margin-top: 20px;
    border-collapse: separate;
    border-spacing: 0 5px;
    border-spacing: 5px;
}

.wf-var-key,
.wf-var-value,
.wf-remove-var {
    margin-right: 5px;
}

#task-settings-table {
    width: 100%;
}

.wf-new-setting {
    margin-bottom: 5px;
    display: none;
}

.wf-remove-setting {
    float: right;
    margin-right: 10px;
    margin-bottom: 5px;
    margin-top: 5px;
}

.wf-setting-name {
    width: 150px;
}

.wf-setting-value {
    width: 265px;
}
