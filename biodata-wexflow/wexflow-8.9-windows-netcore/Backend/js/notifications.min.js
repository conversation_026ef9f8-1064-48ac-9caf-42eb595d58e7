if(function(t,e){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return e(t)}:e(t)}("undefined"!=typeof window?window:this,function(t,e){"use strict";var n=[],i=Object.getPrototypeOf,o=n.slice,r=n.flat?function(t){return n.flat.call(t)}:function(t){return n.concat.apply([],t)},s=n.push,a=n.indexOf,l={},c=l.toString,u=l.hasOwnProperty,d=u.toString,f=d.call(Object),p={},h=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType},g=function(t){return null!=t&&t===t.window},m=t.document,v={type:!0,src:!0,nonce:!0,noModule:!0};function y(t,e,n){var i,o,r=(n=n||m).createElement("script");if(r.text=t,e)for(i in v)(o=e[i]||e.getAttribute&&e.getAttribute(i))&&r.setAttribute(i,o);n.head.appendChild(r).parentNode.removeChild(r)}function b(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?l[c.call(t)]||"object":typeof t}var x="3.5.0",w=function(t,e){return new w.fn.init(t,e)};function T(t){var e=!!t&&"length"in t&&t.length,n=b(t);return!h(t)&&!g(t)&&("array"===n||0===e||"number"==typeof e&&0<e&&e-1 in t)}w.fn=w.prototype={jquery:x,constructor:w,length:0,toArray:function(){return o.call(this)},get:function(t){return null==t?o.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=w.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return w.each(this,t)},map:function(t){return this.pushStack(w.map(this,function(e,n){return t.call(e,n,e)}))},slice:function(){return this.pushStack(o.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(w.grep(this,function(t,e){return(e+1)%2}))},odd:function(){return this.pushStack(w.grep(this,function(t,e){return e%2}))},eq:function(t){var e=this.length,n=+t+(t<0?e:0);return this.pushStack(0<=n&&n<e?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:s,sort:n.sort,splice:n.splice},w.extend=w.fn.extend=function(){var t,e,n,i,o,r,s=arguments[0]||{},a=1,l=arguments.length,c=!1;for("boolean"==typeof s&&(c=s,s=arguments[a]||{},a++),"object"==typeof s||h(s)||(s={}),a===l&&(s=this,a--);a<l;a++)if(null!=(t=arguments[a]))for(e in t)i=t[e],"__proto__"!==e&&s!==i&&(c&&i&&(w.isPlainObject(i)||(o=Array.isArray(i)))?(n=s[e],r=o&&!Array.isArray(n)?[]:o||w.isPlainObject(n)?n:{},o=!1,s[e]=w.extend(c,r,i)):void 0!==i&&(s[e]=i));return s},w.extend({expando:"jQuery"+(x+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,n;return!(!t||"[object Object]"!==c.call(t)||(e=i(t))&&("function"!=typeof(n=u.call(e,"constructor")&&e.constructor)||d.call(n)!==f))},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t,e,n){y(t,{nonce:e&&e.nonce},n)},each:function(t,e){var n,i=0;if(T(t))for(n=t.length;i<n&&!1!==e.call(t[i],i,t[i]);i++);else for(i in t)if(!1===e.call(t[i],i,t[i]))break;return t},makeArray:function(t,e){var n=e||[];return null!=t&&(T(Object(t))?w.merge(n,"string"==typeof t?[t]:t):s.call(n,t)),n},inArray:function(t,e,n){return null==e?-1:a.call(e,t,n)},merge:function(t,e){for(var n=+e.length,i=0,o=t.length;i<n;i++)t[o++]=e[i];return t.length=o,t},grep:function(t,e,n){for(var i=[],o=0,r=t.length,s=!n;o<r;o++)!e(t[o],o)!==s&&i.push(t[o]);return i},map:function(t,e,n){var i,o,s=0,a=[];if(T(t))for(i=t.length;s<i;s++)null!=(o=e(t[s],s,n))&&a.push(o);else for(s in t)null!=(o=e(t[s],s,n))&&a.push(o);return r(a)},guid:1,support:p}),"function"==typeof Symbol&&(w.fn[Symbol.iterator]=n[Symbol.iterator]),w.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){l["[object "+e+"]"]=e.toLowerCase()});var C=function(t){var e,n,i,o,r,s,a,l,c,u,d,f,p,h,g,m,v,y,b,x="sizzle"+1*new Date,w=t.document,T=0,C=0,E=lt(),k=lt(),S=lt(),A=lt(),$=function(t,e){return t===e&&(d=!0),0},N={}.hasOwnProperty,D=[],j=D.pop,L=D.push,H=D.push,I=D.slice,O=function(t,e){for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n;return-1},q="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",P="[\\x20\\t\\r\\n\\f]",B="(?:\\\\[\\da-fA-F]{1,6}"+P+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",M="\\["+P+"*("+B+")(?:"+P+"*([*^$|!~]?=)"+P+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+B+"))|)"+P+"*\\]",R=":("+B+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+M+")*)|.*)\\)|)",_=new RegExp(P+"+","g"),W=new RegExp("^"+P+"+|((?:^|[^\\\\])(?:\\\\.)*)"+P+"+$","g"),U=new RegExp("^"+P+"*,"+P+"*"),F=new RegExp("^"+P+"*([>+~]|"+P+")"+P+"*"),z=new RegExp(P+"|>"),V=new RegExp(R),X=new RegExp("^"+B+"$"),Q={ID:new RegExp("^#("+B+")"),CLASS:new RegExp("^\\.("+B+")"),TAG:new RegExp("^("+B+"|[*])"),ATTR:new RegExp("^"+M),PSEUDO:new RegExp("^"+R),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+P+"*(even|odd|(([+-]|)(\\d*)n|)"+P+"*(?:([+-]|)"+P+"*(\\d+)|))"+P+"*\\)|)","i"),bool:new RegExp("^(?:"+q+")$","i"),needsContext:new RegExp("^"+P+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+P+"*((?:-\\d)?\\d*)"+P+"*\\)|)(?=[^-]|$)","i")},Y=/HTML$/i,G=/^(?:input|select|textarea|button)$/i,J=/^h\d$/i,K=/^[^{]+\{\s*\[native \w/,Z=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,tt=/[+~]/,et=new RegExp("\\\\[\\da-fA-F]{1,6}"+P+"?|\\\\([^\\r\\n\\f])","g"),nt=function(t,e){var n="0x"+t.slice(1)-65536;return e||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},it=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,ot=function(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t},rt=function(){f()},st=xt(function(t){return!0===t.disabled&&"fieldset"===t.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{H.apply(D=I.call(w.childNodes),w.childNodes),D[w.childNodes.length].nodeType}catch(e){H={apply:D.length?function(t,e){L.apply(t,I.call(e))}:function(t,e){for(var n=t.length,i=0;t[n++]=e[i++];);t.length=n-1}}}function at(t,e,i,o){var r,a,c,u,d,h,v,y=e&&e.ownerDocument,w=e?e.nodeType:9;if(i=i||[],"string"!=typeof t||!t||1!==w&&9!==w&&11!==w)return i;if(!o&&(f(e),e=e||p,g)){if(11!==w&&(d=Z.exec(t)))if(r=d[1]){if(9===w){if(!(c=e.getElementById(r)))return i;if(c.id===r)return i.push(c),i}else if(y&&(c=y.getElementById(r))&&b(e,c)&&c.id===r)return i.push(c),i}else{if(d[2])return H.apply(i,e.getElementsByTagName(t)),i;if((r=d[3])&&n.getElementsByClassName&&e.getElementsByClassName)return H.apply(i,e.getElementsByClassName(r)),i}if(n.qsa&&!A[t+" "]&&(!m||!m.test(t))&&(1!==w||"object"!==e.nodeName.toLowerCase())){if(v=t,y=e,1===w&&(z.test(t)||F.test(t))){for((y=tt.test(t)&&vt(e.parentNode)||e)===e&&n.scope||((u=e.getAttribute("id"))?u=u.replace(it,ot):e.setAttribute("id",u=x)),a=(h=s(t)).length;a--;)h[a]=(u?"#"+u:":scope")+" "+bt(h[a]);v=h.join(",")}try{return H.apply(i,y.querySelectorAll(v)),i}catch(e){A(t,!0)}finally{u===x&&e.removeAttribute("id")}}}return l(t.replace(W,"$1"),e,i,o)}function lt(){var t=[];return function e(n,o){return t.push(n+" ")>i.cacheLength&&delete e[t.shift()],e[n+" "]=o}}function ct(t){return t[x]=!0,t}function ut(t){var e=p.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function dt(t,e){for(var n=t.split("|"),o=n.length;o--;)i.attrHandle[n[o]]=e}function ft(t,e){var n=e&&t,i=n&&1===t.nodeType&&1===e.nodeType&&t.sourceIndex-e.sourceIndex;if(i)return i;if(n)for(;n=n.nextSibling;)if(n===e)return-1;return t?1:-1}function pt(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}function ht(t){return function(e){var n=e.nodeName.toLowerCase();return("input"===n||"button"===n)&&e.type===t}}function gt(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&st(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function mt(t){return ct(function(e){return e=+e,ct(function(n,i){for(var o,r=t([],n.length,e),s=r.length;s--;)n[o=r[s]]&&(n[o]=!(i[o]=n[o]))})})}function vt(t){return t&&void 0!==t.getElementsByTagName&&t}for(e in n=at.support={},r=at.isXML=function(t){var e=t.namespaceURI,n=(t.ownerDocument||t).documentElement;return!Y.test(e||n&&n.nodeName||"HTML")},f=at.setDocument=function(t){var e,o,s=t?t.ownerDocument||t:w;return s!=p&&9===s.nodeType&&s.documentElement&&(h=(p=s).documentElement,g=!r(p),w!=p&&(o=p.defaultView)&&o.top!==o&&(o.addEventListener?o.addEventListener("unload",rt,!1):o.attachEvent&&o.attachEvent("onunload",rt)),n.scope=ut(function(t){return h.appendChild(t).appendChild(p.createElement("div")),void 0!==t.querySelectorAll&&!t.querySelectorAll(":scope fieldset div").length}),n.attributes=ut(function(t){return t.className="i",!t.getAttribute("className")}),n.getElementsByTagName=ut(function(t){return t.appendChild(p.createComment("")),!t.getElementsByTagName("*").length}),n.getElementsByClassName=K.test(p.getElementsByClassName),n.getById=ut(function(t){return h.appendChild(t).id=x,!p.getElementsByName||!p.getElementsByName(x).length}),n.getById?(i.filter.ID=function(t){var e=t.replace(et,nt);return function(t){return t.getAttribute("id")===e}},i.find.ID=function(t,e){if(void 0!==e.getElementById&&g){var n=e.getElementById(t);return n?[n]:[]}}):(i.filter.ID=function(t){var e=t.replace(et,nt);return function(t){var n=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return n&&n.value===e}},i.find.ID=function(t,e){if(void 0!==e.getElementById&&g){var n,i,o,r=e.getElementById(t);if(r){if((n=r.getAttributeNode("id"))&&n.value===t)return[r];for(o=e.getElementsByName(t),i=0;r=o[i++];)if((n=r.getAttributeNode("id"))&&n.value===t)return[r]}return[]}}),i.find.TAG=n.getElementsByTagName?function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):n.qsa?e.querySelectorAll(t):void 0}:function(t,e){var n,i=[],o=0,r=e.getElementsByTagName(t);if("*"===t){for(;n=r[o++];)1===n.nodeType&&i.push(n);return i}return r},i.find.CLASS=n.getElementsByClassName&&function(t,e){if(void 0!==e.getElementsByClassName&&g)return e.getElementsByClassName(t)},v=[],m=[],(n.qsa=K.test(p.querySelectorAll))&&(ut(function(t){var e;h.appendChild(t).innerHTML="<a id='"+x+"'></a><select id='"+x+"-\r\\' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+P+"*(?:''|\"\")"),t.querySelectorAll("[selected]").length||m.push("\\["+P+"*(?:value|"+q+")"),t.querySelectorAll("[id~="+x+"-]").length||m.push("~="),(e=p.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||m.push("\\["+P+"*name"+P+"*="+P+"*(?:''|\"\")"),t.querySelectorAll(":checked").length||m.push(":checked"),t.querySelectorAll("a#"+x+"+*").length||m.push(".#.+[+~]"),t.querySelectorAll("\\\f"),m.push("[\\r\\n\\f]")}),ut(function(t){t.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var e=p.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&m.push("name"+P+"*[*^$|!~]?="),2!==t.querySelectorAll(":enabled").length&&m.push(":enabled",":disabled"),h.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&m.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),m.push(",.*:")})),(n.matchesSelector=K.test(y=h.matches||h.webkitMatchesSelector||h.mozMatchesSelector||h.oMatchesSelector||h.msMatchesSelector))&&ut(function(t){n.disconnectedMatch=y.call(t,"*"),y.call(t,"[s!='']:x"),v.push("!=",R)}),m=m.length&&new RegExp(m.join("|")),v=v.length&&new RegExp(v.join("|")),e=K.test(h.compareDocumentPosition),b=e||K.test(h.contains)?function(t,e){var n=9===t.nodeType?t.documentElement:t,i=e&&e.parentNode;return t===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):t.compareDocumentPosition&&16&t.compareDocumentPosition(i)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},$=e?function(t,e){if(t===e)return d=!0,0;var i=!t.compareDocumentPosition-!e.compareDocumentPosition;return i||(1&(i=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!n.sortDetached&&e.compareDocumentPosition(t)===i?t==p||t.ownerDocument==w&&b(w,t)?-1:e==p||e.ownerDocument==w&&b(w,e)?1:u?O(u,t)-O(u,e):0:4&i?-1:1)}:function(t,e){if(t===e)return d=!0,0;var n,i=0,o=t.parentNode,r=e.parentNode,s=[t],a=[e];if(!o||!r)return t==p?-1:e==p?1:o?-1:r?1:u?O(u,t)-O(u,e):0;if(o===r)return ft(t,e);for(n=t;n=n.parentNode;)s.unshift(n);for(n=e;n=n.parentNode;)a.unshift(n);for(;s[i]===a[i];)i++;return i?ft(s[i],a[i]):s[i]==w?-1:a[i]==w?1:0}),p},at.matches=function(t,e){return at(t,null,null,e)},at.matchesSelector=function(t,e){if(f(t),n.matchesSelector&&g&&!A[e+" "]&&(!v||!v.test(e))&&(!m||!m.test(e)))try{var i=y.call(t,e);if(i||n.disconnectedMatch||t.document&&11!==t.document.nodeType)return i}catch(t){A(e,!0)}return 0<at(e,p,null,[t]).length},at.contains=function(t,e){return(t.ownerDocument||t)!=p&&f(t),b(t,e)},at.attr=function(t,e){(t.ownerDocument||t)!=p&&f(t);var o=i.attrHandle[e.toLowerCase()],r=o&&N.call(i.attrHandle,e.toLowerCase())?o(t,e,!g):void 0;return void 0!==r?r:n.attributes||!g?t.getAttribute(e):(r=t.getAttributeNode(e))&&r.specified?r.value:null},at.escape=function(t){return(t+"").replace(it,ot)},at.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},at.uniqueSort=function(t){var e,i=[],o=0,r=0;if(d=!n.detectDuplicates,u=!n.sortStable&&t.slice(0),t.sort($),d){for(;e=t[r++];)e===t[r]&&(o=i.push(r));for(;o--;)t.splice(i[o],1)}return u=null,t},o=at.getText=function(t){var e,n="",i=0,r=t.nodeType;if(r){if(1===r||9===r||11===r){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)n+=o(t)}else if(3===r||4===r)return t.nodeValue}else for(;e=t[i++];)n+=o(e);return n},(i=at.selectors={cacheLength:50,createPseudo:ct,match:Q,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(et,nt),t[3]=(t[3]||t[4]||t[5]||"").replace(et,nt),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||at.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&at.error(t[0]),t},PSEUDO:function(t){var e,n=!t[6]&&t[2];return Q.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&V.test(n)&&(e=s(n,!0))&&(e=n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(et,nt).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===e}},CLASS:function(t){var e=E[t+" "];return e||(e=new RegExp("(^|"+P+")"+t+"("+P+"|$)"))&&E(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(t,e,n){return function(i){var o=at.attr(i,t);return null==o?"!="===e:!e||(o+="","="===e?o===n:"!="===e?o!==n:"^="===e?n&&0===o.indexOf(n):"*="===e?n&&-1<o.indexOf(n):"$="===e?n&&o.slice(-n.length)===n:"~="===e?-1<(" "+o.replace(_," ")+" ").indexOf(n):"|="===e&&(o===n||o.slice(0,n.length+1)===n+"-"))}},CHILD:function(t,e,n,i,o){var r="nth"!==t.slice(0,3),s="last"!==t.slice(-4),a="of-type"===e;return 1===i&&0===o?function(t){return!!t.parentNode}:function(e,n,l){var c,u,d,f,p,h,g=r!==s?"nextSibling":"previousSibling",m=e.parentNode,v=a&&e.nodeName.toLowerCase(),y=!l&&!a,b=!1;if(m){if(r){for(;g;){for(f=e;f=f[g];)if(a?f.nodeName.toLowerCase()===v:1===f.nodeType)return!1;h=g="only"===t&&!h&&"nextSibling"}return!0}if(h=[s?m.firstChild:m.lastChild],s&&y){for(b=(p=(c=(u=(d=(f=m)[x]||(f[x]={}))[f.uniqueID]||(d[f.uniqueID]={}))[t]||[])[0]===T&&c[1])&&c[2],f=p&&m.childNodes[p];f=++p&&f&&f[g]||(b=p=0)||h.pop();)if(1===f.nodeType&&++b&&f===e){u[t]=[T,p,b];break}}else if(y&&(b=p=(c=(u=(d=(f=e)[x]||(f[x]={}))[f.uniqueID]||(d[f.uniqueID]={}))[t]||[])[0]===T&&c[1]),!1===b)for(;(f=++p&&f&&f[g]||(b=p=0)||h.pop())&&((a?f.nodeName.toLowerCase()!==v:1!==f.nodeType)||!++b||(y&&((u=(d=f[x]||(f[x]={}))[f.uniqueID]||(d[f.uniqueID]={}))[t]=[T,b]),f!==e)););return(b-=o)===i||b%i==0&&0<=b/i}}},PSEUDO:function(t,e){var n,o=i.pseudos[t]||i.setFilters[t.toLowerCase()]||at.error("unsupported pseudo: "+t);return o[x]?o(e):1<o.length?(n=[t,t,"",e],i.setFilters.hasOwnProperty(t.toLowerCase())?ct(function(t,n){for(var i,r=o(t,e),s=r.length;s--;)t[i=O(t,r[s])]=!(n[i]=r[s])}):function(t){return o(t,0,n)}):o}},pseudos:{not:ct(function(t){var e=[],n=[],i=a(t.replace(W,"$1"));return i[x]?ct(function(t,e,n,o){for(var r,s=i(t,null,o,[]),a=t.length;a--;)(r=s[a])&&(t[a]=!(e[a]=r))}):function(t,o,r){return e[0]=t,i(e,null,r,n),e[0]=null,!n.pop()}}),has:ct(function(t){return function(e){return 0<at(t,e).length}}),contains:ct(function(t){return t=t.replace(et,nt),function(e){return-1<(e.textContent||o(e)).indexOf(t)}}),lang:ct(function(t){return X.test(t||"")||at.error("unsupported lang: "+t),t=t.replace(et,nt).toLowerCase(),function(e){var n;do{if(n=g?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(n=n.toLowerCase())===t||0===n.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var n=t.location&&t.location.hash;return n&&n.slice(1)===e.id},root:function(t){return t===h},focus:function(t){return t===p.activeElement&&(!p.hasFocus||p.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},enabled:gt(!1),disabled:gt(!0),checked:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!i.pseudos.empty(t)},header:function(t){return J.test(t.nodeName)},input:function(t){return G.test(t.nodeName)},button:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},text:function(t){var e;return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:mt(function(){return[0]}),last:mt(function(t,e){return[e-1]}),eq:mt(function(t,e,n){return[n<0?n+e:n]}),even:mt(function(t,e){for(var n=0;n<e;n+=2)t.push(n);return t}),odd:mt(function(t,e){for(var n=1;n<e;n+=2)t.push(n);return t}),lt:mt(function(t,e,n){for(var i=n<0?n+e:e<n?e:n;0<=--i;)t.push(i);return t}),gt:mt(function(t,e,n){for(var i=n<0?n+e:n;++i<e;)t.push(i);return t})}}).pseudos.nth=i.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})i.pseudos[e]=pt(e);for(e in{submit:!0,reset:!0})i.pseudos[e]=ht(e);function yt(){}function bt(t){for(var e=0,n=t.length,i="";e<n;e++)i+=t[e].value;return i}function xt(t,e,n){var i=e.dir,o=e.next,r=o||i,s=n&&"parentNode"===r,a=C++;return e.first?function(e,n,o){for(;e=e[i];)if(1===e.nodeType||s)return t(e,n,o);return!1}:function(e,n,l){var c,u,d,f=[T,a];if(l){for(;e=e[i];)if((1===e.nodeType||s)&&t(e,n,l))return!0}else for(;e=e[i];)if(1===e.nodeType||s)if(u=(d=e[x]||(e[x]={}))[e.uniqueID]||(d[e.uniqueID]={}),o&&o===e.nodeName.toLowerCase())e=e[i]||e;else{if((c=u[r])&&c[0]===T&&c[1]===a)return f[2]=c[2];if((u[r]=f)[2]=t(e,n,l))return!0}return!1}}function wt(t){return 1<t.length?function(e,n,i){for(var o=t.length;o--;)if(!t[o](e,n,i))return!1;return!0}:t[0]}function Tt(t,e,n,i,o){for(var r,s=[],a=0,l=t.length,c=null!=e;a<l;a++)(r=t[a])&&(n&&!n(r,i,o)||(s.push(r),c&&e.push(a)));return s}function Ct(t,e,n,i,o,r){return i&&!i[x]&&(i=Ct(i)),o&&!o[x]&&(o=Ct(o,r)),ct(function(r,s,a,l){var c,u,d,f=[],p=[],h=s.length,g=r||function(t,e,n){for(var i=0,o=e.length;i<o;i++)at(t,e[i],n);return n}(e||"*",a.nodeType?[a]:a,[]),m=!t||!r&&e?g:Tt(g,f,t,a,l),v=n?o||(r?t:h||i)?[]:s:m;if(n&&n(m,v,a,l),i)for(c=Tt(v,p),i(c,[],a,l),u=c.length;u--;)(d=c[u])&&(v[p[u]]=!(m[p[u]]=d));if(r){if(o||t){if(o){for(c=[],u=v.length;u--;)(d=v[u])&&c.push(m[u]=d);o(null,v=[],c,l)}for(u=v.length;u--;)(d=v[u])&&-1<(c=o?O(r,d):f[u])&&(r[c]=!(s[c]=d))}}else v=Tt(v===s?v.splice(h,v.length):v),o?o(null,s,v,l):H.apply(s,v)})}function Et(t){for(var e,n,o,r=t.length,s=i.relative[t[0].type],a=s||i.relative[" "],l=s?1:0,u=xt(function(t){return t===e},a,!0),d=xt(function(t){return-1<O(e,t)},a,!0),f=[function(t,n,i){var o=!s&&(i||n!==c)||((e=n).nodeType?u(t,n,i):d(t,n,i));return e=null,o}];l<r;l++)if(n=i.relative[t[l].type])f=[xt(wt(f),n)];else{if((n=i.filter[t[l].type].apply(null,t[l].matches))[x]){for(o=++l;o<r&&!i.relative[t[o].type];o++);return Ct(1<l&&wt(f),1<l&&bt(t.slice(0,l-1).concat({value:" "===t[l-2].type?"*":""})).replace(W,"$1"),n,l<o&&Et(t.slice(l,o)),o<r&&Et(t=t.slice(o)),o<r&&bt(t))}f.push(n)}return wt(f)}return yt.prototype=i.filters=i.pseudos,i.setFilters=new yt,s=at.tokenize=function(t,e){var n,o,r,s,a,l,c,u=k[t+" "];if(u)return e?0:u.slice(0);for(a=t,l=[],c=i.preFilter;a;){for(s in n&&!(o=U.exec(a))||(o&&(a=a.slice(o[0].length)||a),l.push(r=[])),n=!1,(o=F.exec(a))&&(n=o.shift(),r.push({value:n,type:o[0].replace(W," ")}),a=a.slice(n.length)),i.filter)!(o=Q[s].exec(a))||c[s]&&!(o=c[s](o))||(n=o.shift(),r.push({value:n,type:s,matches:o}),a=a.slice(n.length));if(!n)break}return e?a.length:a?at.error(t):k(t,l).slice(0)},a=at.compile=function(t,e){var n,o,r,a,l,u,d=[],h=[],m=S[t+" "];if(!m){for(e||(e=s(t)),n=e.length;n--;)(m=Et(e[n]))[x]?d.push(m):h.push(m);(m=S(t,(o=h,a=0<(r=d).length,l=0<o.length,u=function(t,e,n,s,u){var d,h,m,v=0,y="0",b=t&&[],x=[],w=c,C=t||l&&i.find.TAG("*",u),E=T+=null==w?1:Math.random()||.1,k=C.length;for(u&&(c=e==p||e||u);y!==k&&null!=(d=C[y]);y++){if(l&&d){for(h=0,e||d.ownerDocument==p||(f(d),n=!g);m=o[h++];)if(m(d,e||p,n)){s.push(d);break}u&&(T=E)}a&&((d=!m&&d)&&v--,t&&b.push(d))}if(v+=y,a&&y!==v){for(h=0;m=r[h++];)m(b,x,e,n);if(t){if(0<v)for(;y--;)b[y]||x[y]||(x[y]=j.call(s));x=Tt(x)}H.apply(s,x),u&&!t&&0<x.length&&1<v+r.length&&at.uniqueSort(s)}return u&&(T=E,c=w),b},a?ct(u):u))).selector=t}return m},l=at.select=function(t,e,n,o){var r,l,c,u,d,f="function"==typeof t&&t,p=!o&&s(t=f.selector||t);if(n=n||[],1===p.length){if(2<(l=p[0]=p[0].slice(0)).length&&"ID"===(c=l[0]).type&&9===e.nodeType&&g&&i.relative[l[1].type]){if(!(e=(i.find.ID(c.matches[0].replace(et,nt),e)||[])[0]))return n;f&&(e=e.parentNode),t=t.slice(l.shift().value.length)}for(r=Q.needsContext.test(t)?0:l.length;r--&&(c=l[r],!i.relative[u=c.type]);)if((d=i.find[u])&&(o=d(c.matches[0].replace(et,nt),tt.test(l[0].type)&&vt(e.parentNode)||e))){if(l.splice(r,1),!(t=o.length&&bt(l)))return H.apply(n,o),n;break}}return(f||a(t,p))(o,e,!g,n,!e||tt.test(t)&&vt(e.parentNode)||e),n},n.sortStable=x.split("").sort($).join("")===x,n.detectDuplicates=!!d,f(),n.sortDetached=ut(function(t){return 1&t.compareDocumentPosition(p.createElement("fieldset"))}),ut(function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")})||dt("type|href|height|width",function(t,e,n){if(!n)return t.getAttribute(e,"type"===e.toLowerCase()?1:2)}),n.attributes&&ut(function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")})||dt("value",function(t,e,n){if(!n&&"input"===t.nodeName.toLowerCase())return t.defaultValue}),ut(function(t){return null==t.getAttribute("disabled")})||dt(q,function(t,e,n){var i;if(!n)return!0===t[e]?e.toLowerCase():(i=t.getAttributeNode(e))&&i.specified?i.value:null}),at}(t);w.find=C,w.expr=C.selectors,w.expr[":"]=w.expr.pseudos,w.uniqueSort=w.unique=C.uniqueSort,w.text=C.getText,w.isXMLDoc=C.isXML,w.contains=C.contains,w.escapeSelector=C.escape;var E=function(t,e,n){for(var i=[],o=void 0!==n;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(o&&w(t).is(n))break;i.push(t)}return i},k=function(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n},S=w.expr.match.needsContext;function A(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}var $=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function N(t,e,n){return h(e)?w.grep(t,function(t,i){return!!e.call(t,i,t)!==n}):e.nodeType?w.grep(t,function(t){return t===e!==n}):"string"!=typeof e?w.grep(t,function(t){return-1<a.call(e,t)!==n}):w.filter(e,t,n)}w.filter=function(t,e,n){var i=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===i.nodeType?w.find.matchesSelector(i,t)?[i]:[]:w.find.matches(t,w.grep(e,function(t){return 1===t.nodeType}))},w.fn.extend({find:function(t){var e,n,i=this.length,o=this;if("string"!=typeof t)return this.pushStack(w(t).filter(function(){for(e=0;e<i;e++)if(w.contains(o[e],this))return!0}));for(n=this.pushStack([]),e=0;e<i;e++)w.find(t,o[e],n);return 1<i?w.uniqueSort(n):n},filter:function(t){return this.pushStack(N(this,t||[],!1))},not:function(t){return this.pushStack(N(this,t||[],!0))},is:function(t){return!!N(this,"string"==typeof t&&S.test(t)?w(t):t||[],!1).length}});var D,j=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(w.fn.init=function(t,e,n){var i,o;if(!t)return this;if(n=n||D,"string"==typeof t){if(!(i="<"===t[0]&&">"===t[t.length-1]&&3<=t.length?[null,t,null]:j.exec(t))||!i[1]&&e)return!e||e.jquery?(e||n).find(t):this.constructor(e).find(t);if(i[1]){if(e=e instanceof w?e[0]:e,w.merge(this,w.parseHTML(i[1],e&&e.nodeType?e.ownerDocument||e:m,!0)),$.test(i[1])&&w.isPlainObject(e))for(i in e)h(this[i])?this[i](e[i]):this.attr(i,e[i]);return this}return(o=m.getElementById(i[2]))&&(this[0]=o,this.length=1),this}return t.nodeType?(this[0]=t,this.length=1,this):h(t)?void 0!==n.ready?n.ready(t):t(w):w.makeArray(t,this)}).prototype=w.fn,D=w(m);var L=/^(?:parents|prev(?:Until|All))/,H={children:!0,contents:!0,next:!0,prev:!0};function I(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}w.fn.extend({has:function(t){var e=w(t,this),n=e.length;return this.filter(function(){for(var t=0;t<n;t++)if(w.contains(this,e[t]))return!0})},closest:function(t,e){var n,i=0,o=this.length,r=[],s="string"!=typeof t&&w(t);if(!S.test(t))for(;i<o;i++)for(n=this[i];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(s?-1<s.index(n):1===n.nodeType&&w.find.matchesSelector(n,t))){r.push(n);break}return this.pushStack(1<r.length?w.uniqueSort(r):r)},index:function(t){return t?"string"==typeof t?a.call(w(t),this[0]):a.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(w.uniqueSort(w.merge(this.get(),w(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),w.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return E(t,"parentNode")},parentsUntil:function(t,e,n){return E(t,"parentNode",n)},next:function(t){return I(t,"nextSibling")},prev:function(t){return I(t,"previousSibling")},nextAll:function(t){return E(t,"nextSibling")},prevAll:function(t){return E(t,"previousSibling")},nextUntil:function(t,e,n){return E(t,"nextSibling",n)},prevUntil:function(t,e,n){return E(t,"previousSibling",n)},siblings:function(t){return k((t.parentNode||{}).firstChild,t)},children:function(t){return k(t.firstChild)},contents:function(t){return null!=t.contentDocument&&i(t.contentDocument)?t.contentDocument:(A(t,"template")&&(t=t.content||t),w.merge([],t.childNodes))}},function(t,e){w.fn[t]=function(n,i){var o=w.map(this,e,n);return"Until"!==t.slice(-5)&&(i=n),i&&"string"==typeof i&&(o=w.filter(i,o)),1<this.length&&(H[t]||w.uniqueSort(o),L.test(t)&&o.reverse()),this.pushStack(o)}});var O=/[^\x20\t\r\n\f]+/g;function q(t){return t}function P(t){throw t}function B(t,e,n,i){var o;try{t&&h(o=t.promise)?o.call(t).done(e).fail(n):t&&h(o=t.then)?o.call(t,e,n):e.apply(void 0,[t].slice(i))}catch(t){n.apply(void 0,[t])}}w.Callbacks=function(t){var e,n;t="string"==typeof t?(e=t,n={},w.each(e.match(O)||[],function(t,e){n[e]=!0}),n):w.extend({},t);var i,o,r,s,a=[],l=[],c=-1,u=function(){for(s=s||t.once,r=i=!0;l.length;c=-1)for(o=l.shift();++c<a.length;)!1===a[c].apply(o[0],o[1])&&t.stopOnFalse&&(c=a.length,o=!1);t.memory||(o=!1),i=!1,s&&(a=o?[]:"")},d={add:function(){return a&&(o&&!i&&(c=a.length-1,l.push(o)),function e(n){w.each(n,function(n,i){h(i)?t.unique&&d.has(i)||a.push(i):i&&i.length&&"string"!==b(i)&&e(i)})}(arguments),o&&!i&&u()),this},remove:function(){return w.each(arguments,function(t,e){for(var n;-1<(n=w.inArray(e,a,n));)a.splice(n,1),n<=c&&c--}),this},has:function(t){return t?-1<w.inArray(t,a):0<a.length},empty:function(){return a&&(a=[]),this},disable:function(){return s=l=[],a=o="",this},disabled:function(){return!a},lock:function(){return s=l=[],o||i||(a=o=""),this},locked:function(){return!!s},fireWith:function(t,e){return s||(e=[t,(e=e||[]).slice?e.slice():e],l.push(e),i||u()),this},fire:function(){return d.fireWith(this,arguments),this},fired:function(){return!!r}};return d},w.extend({Deferred:function(e){var n=[["notify","progress",w.Callbacks("memory"),w.Callbacks("memory"),2],["resolve","done",w.Callbacks("once memory"),w.Callbacks("once memory"),0,"resolved"],["reject","fail",w.Callbacks("once memory"),w.Callbacks("once memory"),1,"rejected"]],i="pending",o={state:function(){return i},always:function(){return r.done(arguments).fail(arguments),this},catch:function(t){return o.then(null,t)},pipe:function(){var t=arguments;return w.Deferred(function(e){w.each(n,function(n,i){var o=h(t[i[4]])&&t[i[4]];r[i[1]](function(){var t=o&&o.apply(this,arguments);t&&h(t.promise)?t.promise().progress(e.notify).done(e.resolve).fail(e.reject):e[i[0]+"With"](this,o?[t]:arguments)})}),t=null}).promise()},then:function(e,i,o){var r=0;function s(e,n,i,o){return function(){var a=this,l=arguments,c=function(){var t,c;if(!(e<r)){if((t=i.apply(a,l))===n.promise())throw new TypeError("Thenable self-resolution");c=t&&("object"==typeof t||"function"==typeof t)&&t.then,h(c)?o?c.call(t,s(r,n,q,o),s(r,n,P,o)):(r++,c.call(t,s(r,n,q,o),s(r,n,P,o),s(r,n,q,n.notifyWith))):(i!==q&&(a=void 0,l=[t]),(o||n.resolveWith)(a,l))}},u=o?c:function(){try{c()}catch(t){w.Deferred.exceptionHook&&w.Deferred.exceptionHook(t,u.stackTrace),r<=e+1&&(i!==P&&(a=void 0,l=[t]),n.rejectWith(a,l))}};e?u():(w.Deferred.getStackHook&&(u.stackTrace=w.Deferred.getStackHook()),t.setTimeout(u))}}return w.Deferred(function(t){n[0][3].add(s(0,t,h(o)?o:q,t.notifyWith)),n[1][3].add(s(0,t,h(e)?e:q)),n[2][3].add(s(0,t,h(i)?i:P))}).promise()},promise:function(t){return null!=t?w.extend(t,o):o}},r={};return w.each(n,function(t,e){var s=e[2],a=e[5];o[e[1]]=s.add,a&&s.add(function(){i=a},n[3-t][2].disable,n[3-t][3].disable,n[0][2].lock,n[0][3].lock),s.add(e[3].fire),r[e[0]]=function(){return r[e[0]+"With"](this===r?void 0:this,arguments),this},r[e[0]+"With"]=s.fireWith}),o.promise(r),e&&e.call(r,r),r},when:function(t){var e=arguments.length,n=e,i=Array(n),r=o.call(arguments),s=w.Deferred(),a=function(t){return function(n){i[t]=this,r[t]=1<arguments.length?o.call(arguments):n,--e||s.resolveWith(i,r)}};if(e<=1&&(B(t,s.done(a(n)).resolve,s.reject,!e),"pending"===s.state()||h(r[n]&&r[n].then)))return s.then();for(;n--;)B(r[n],a(n),s.reject);return s.promise()}});var M=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;w.Deferred.exceptionHook=function(e,n){t.console&&t.console.warn&&e&&M.test(e.name)&&t.console.warn("jQuery.Deferred exception: "+e.message,e.stack,n)},w.readyException=function(e){t.setTimeout(function(){throw e})};var R=w.Deferred();function _(){m.removeEventListener("DOMContentLoaded",_),t.removeEventListener("load",_),w.ready()}w.fn.ready=function(t){return R.then(t).catch(function(t){w.readyException(t)}),this},w.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--w.readyWait:w.isReady)||(w.isReady=!0)!==t&&0<--w.readyWait||R.resolveWith(m,[w])}}),w.ready.then=R.then,"complete"===m.readyState||"loading"!==m.readyState&&!m.documentElement.doScroll?t.setTimeout(w.ready):(m.addEventListener("DOMContentLoaded",_),t.addEventListener("load",_));var W=function(t,e,n,i,o,r,s){var a=0,l=t.length,c=null==n;if("object"===b(n))for(a in o=!0,n)W(t,e,a,n[a],!0,r,s);else if(void 0!==i&&(o=!0,h(i)||(s=!0),c&&(s?(e.call(t,i),e=null):(c=e,e=function(t,e,n){return c.call(w(t),n)})),e))for(;a<l;a++)e(t[a],n,s?i:i.call(t[a],a,e(t[a],n)));return o?t:c?e.call(t):l?e(t[0],n):r},U=/^-ms-/,F=/-([a-z])/g;function z(t,e){return e.toUpperCase()}function V(t){return t.replace(U,"ms-").replace(F,z)}var X=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType};function Q(){this.expando=w.expando+Q.uid++}Q.uid=1,Q.prototype={cache:function(t){var e=t[this.expando];return e||(e=Object.create(null),X(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,n){var i,o=this.cache(t);if("string"==typeof e)o[V(e)]=n;else for(i in e)o[V(i)]=e[i];return o},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][V(e)]},access:function(t,e,n){return void 0===e||e&&"string"==typeof e&&void 0===n?this.get(t,e):(this.set(t,e,n),void 0!==n?n:e)},remove:function(t,e){var n,i=t[this.expando];if(void 0!==i){if(void 0!==e){n=(e=Array.isArray(e)?e.map(V):(e=V(e))in i?[e]:e.match(O)||[]).length;for(;n--;)delete i[e[n]]}(void 0===e||w.isEmptyObject(i))&&(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!w.isEmptyObject(e)}};var Y=new Q,G=new Q,J=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,K=/[A-Z]/g;function Z(t,e,n){var i,o;if(void 0===n&&1===t.nodeType)if(i="data-"+e.replace(K,"-$&").toLowerCase(),"string"==typeof(n=t.getAttribute(i))){try{n="true"===(o=n)||"false"!==o&&("null"===o?null:o===+o+""?+o:J.test(o)?JSON.parse(o):o)}catch(t){}G.set(t,e,n)}else n=void 0;return n}w.extend({hasData:function(t){return G.hasData(t)||Y.hasData(t)},data:function(t,e,n){return G.access(t,e,n)},removeData:function(t,e){G.remove(t,e)},_data:function(t,e,n){return Y.access(t,e,n)},_removeData:function(t,e){Y.remove(t,e)}}),w.fn.extend({data:function(t,e){var n,i,o,r=this[0],s=r&&r.attributes;if(void 0===t){if(this.length&&(o=G.get(r),1===r.nodeType&&!Y.get(r,"hasDataAttrs"))){for(n=s.length;n--;)s[n]&&0===(i=s[n].name).indexOf("data-")&&(i=V(i.slice(5)),Z(r,i,o[i]));Y.set(r,"hasDataAttrs",!0)}return o}return"object"==typeof t?this.each(function(){G.set(this,t)}):W(this,function(e){var n;if(r&&void 0===e)return void 0!==(n=G.get(r,t))?n:void 0!==(n=Z(r,t))?n:void 0;this.each(function(){G.set(this,t,e)})},null,e,1<arguments.length,null,!0)},removeData:function(t){return this.each(function(){G.remove(this,t)})}}),w.extend({queue:function(t,e,n){var i;if(t)return e=(e||"fx")+"queue",i=Y.get(t,e),n&&(!i||Array.isArray(n)?i=Y.access(t,e,w.makeArray(n)):i.push(n)),i||[]},dequeue:function(t,e){e=e||"fx";var n=w.queue(t,e),i=n.length,o=n.shift(),r=w._queueHooks(t,e);"inprogress"===o&&(o=n.shift(),i--),o&&("fx"===e&&n.unshift("inprogress"),delete r.stop,o.call(t,function(){w.dequeue(t,e)},r)),!i&&r&&r.empty.fire()},_queueHooks:function(t,e){var n=e+"queueHooks";return Y.get(t,n)||Y.access(t,n,{empty:w.Callbacks("once memory").add(function(){Y.remove(t,[e+"queue",n])})})}}),w.fn.extend({queue:function(t,e){var n=2;return"string"!=typeof t&&(e=t,t="fx",n--),arguments.length<n?w.queue(this[0],t):void 0===e?this:this.each(function(){var n=w.queue(this,t,e);w._queueHooks(this,t),"fx"===t&&"inprogress"!==n[0]&&w.dequeue(this,t)})},dequeue:function(t){return this.each(function(){w.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var n,i=1,o=w.Deferred(),r=this,s=this.length,a=function(){--i||o.resolveWith(r,[r])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";s--;)(n=Y.get(r[s],t+"queueHooks"))&&n.empty&&(i++,n.empty.add(a));return a(),o.promise(e)}});var tt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,et=new RegExp("^(?:([+-])=|)("+tt+")([a-z%]*)$","i"),nt=["Top","Right","Bottom","Left"],it=m.documentElement,ot=function(t){return w.contains(t.ownerDocument,t)},rt={composed:!0};it.getRootNode&&(ot=function(t){return w.contains(t.ownerDocument,t)||t.getRootNode(rt)===t.ownerDocument});var st=function(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&ot(t)&&"none"===w.css(t,"display")};function at(t,e,n,i){var o,r,s=20,a=i?function(){return i.cur()}:function(){return w.css(t,e,"")},l=a(),c=n&&n[3]||(w.cssNumber[e]?"":"px"),u=t.nodeType&&(w.cssNumber[e]||"px"!==c&&+l)&&et.exec(w.css(t,e));if(u&&u[3]!==c){for(l/=2,c=c||u[3],u=+l||1;s--;)w.style(t,e,u+c),(1-r)*(1-(r=a()/l||.5))<=0&&(s=0),u/=r;u*=2,w.style(t,e,u+c),n=n||[]}return n&&(u=+u||+l||0,o=n[1]?u+(n[1]+1)*n[2]:+n[2],i&&(i.unit=c,i.start=u,i.end=o)),o}var lt={};function ct(t,e){for(var n,i,o,r,s,a,l,c=[],u=0,d=t.length;u<d;u++)(i=t[u]).style&&(n=i.style.display,e?("none"===n&&(c[u]=Y.get(i,"display")||null,c[u]||(i.style.display="")),""===i.style.display&&st(i)&&(c[u]=(l=s=r=void 0,s=(o=i).ownerDocument,a=o.nodeName,(l=lt[a])||(r=s.body.appendChild(s.createElement(a)),l=w.css(r,"display"),r.parentNode.removeChild(r),"none"===l&&(l="block"),lt[a]=l)))):"none"!==n&&(c[u]="none",Y.set(i,"display",n)));for(u=0;u<d;u++)null!=c[u]&&(t[u].style.display=c[u]);return t}w.fn.extend({show:function(){return ct(this,!0)},hide:function(){return ct(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){st(this)?w(this).show():w(this).hide()})}});var ut,dt,ft=/^(?:checkbox|radio)$/i,pt=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,ht=/^$|^module$|\/(?:java|ecma)script/i;ut=m.createDocumentFragment().appendChild(m.createElement("div")),(dt=m.createElement("input")).setAttribute("type","radio"),dt.setAttribute("checked","checked"),dt.setAttribute("name","t"),ut.appendChild(dt),p.checkClone=ut.cloneNode(!0).cloneNode(!0).lastChild.checked,ut.innerHTML="<textarea>x</textarea>",p.noCloneChecked=!!ut.cloneNode(!0).lastChild.defaultValue,ut.innerHTML="<option></option>",p.option=!!ut.lastChild;var gt={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function mt(t,e){var n;return n=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[],void 0===e||e&&A(t,e)?w.merge([t],n):n}function vt(t,e){for(var n=0,i=t.length;n<i;n++)Y.set(t[n],"globalEval",!e||Y.get(e[n],"globalEval"))}gt.tbody=gt.tfoot=gt.colgroup=gt.caption=gt.thead,gt.th=gt.td,p.option||(gt.optgroup=gt.option=[1,"<select multiple='multiple'>","</select>"]);var yt=/<|&#?\w+;/;function bt(t,e,n,i,o){for(var r,s,a,l,c,u,d=e.createDocumentFragment(),f=[],p=0,h=t.length;p<h;p++)if((r=t[p])||0===r)if("object"===b(r))w.merge(f,r.nodeType?[r]:r);else if(yt.test(r)){for(s=s||d.appendChild(e.createElement("div")),a=(pt.exec(r)||["",""])[1].toLowerCase(),l=gt[a]||gt._default,s.innerHTML=l[1]+w.htmlPrefilter(r)+l[2],u=l[0];u--;)s=s.lastChild;w.merge(f,s.childNodes),(s=d.firstChild).textContent=""}else f.push(e.createTextNode(r));for(d.textContent="",p=0;r=f[p++];)if(i&&-1<w.inArray(r,i))o&&o.push(r);else if(c=ot(r),s=mt(d.appendChild(r),"script"),c&&vt(s),n)for(u=0;r=s[u++];)ht.test(r.type||"")&&n.push(r);return d}var xt=/^key/,wt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Tt=/^([^.]*)(?:\.(.+)|)/;function Ct(){return!0}function Et(){return!1}function kt(t,e){return t===function(){try{return m.activeElement}catch(t){}}()==("focus"===e)}function St(t,e,n,i,o,r){var s,a;if("object"==typeof e){for(a in"string"!=typeof n&&(i=i||n,n=void 0),e)St(t,a,n,i,e[a],r);return t}if(null==i&&null==o?(o=n,i=n=void 0):null==o&&("string"==typeof n?(o=i,i=void 0):(o=i,i=n,n=void 0)),!1===o)o=Et;else if(!o)return t;return 1===r&&(s=o,(o=function(t){return w().off(t),s.apply(this,arguments)}).guid=s.guid||(s.guid=w.guid++)),t.each(function(){w.event.add(this,e,o,i,n)})}function At(t,e,n){n?(Y.set(t,e,!1),w.event.add(t,e,{namespace:!1,handler:function(t){var i,r,s=Y.get(this,e);if(1&t.isTrigger&&this[e]){if(s.length)(w.event.special[e]||{}).delegateType&&t.stopPropagation();else if(s=o.call(arguments),Y.set(this,e,s),i=n(this,e),this[e](),s!==(r=Y.get(this,e))||i?Y.set(this,e,!1):r={},s!==r)return t.stopImmediatePropagation(),t.preventDefault(),r.value}else s.length&&(Y.set(this,e,{value:w.event.trigger(w.extend(s[0],w.Event.prototype),s.slice(1),this)}),t.stopImmediatePropagation())}})):void 0===Y.get(t,e)&&w.event.add(t,e,Ct)}w.event={global:{},add:function(t,e,n,i,o){var r,s,a,l,c,u,d,f,p,h,g,m=Y.get(t);if(X(t))for(n.handler&&(n=(r=n).handler,o=r.selector),o&&w.find.matchesSelector(it,o),n.guid||(n.guid=w.guid++),(l=m.events)||(l=m.events=Object.create(null)),(s=m.handle)||(s=m.handle=function(e){return void 0!==w&&w.event.triggered!==e.type?w.event.dispatch.apply(t,arguments):void 0}),c=(e=(e||"").match(O)||[""]).length;c--;)p=g=(a=Tt.exec(e[c])||[])[1],h=(a[2]||"").split(".").sort(),p&&(d=w.event.special[p]||{},p=(o?d.delegateType:d.bindType)||p,d=w.event.special[p]||{},u=w.extend({type:p,origType:g,data:i,handler:n,guid:n.guid,selector:o,needsContext:o&&w.expr.match.needsContext.test(o),namespace:h.join(".")},r),(f=l[p])||((f=l[p]=[]).delegateCount=0,d.setup&&!1!==d.setup.call(t,i,h,s)||t.addEventListener&&t.addEventListener(p,s)),d.add&&(d.add.call(t,u),u.handler.guid||(u.handler.guid=n.guid)),o?f.splice(f.delegateCount++,0,u):f.push(u),w.event.global[p]=!0)},remove:function(t,e,n,i,o){var r,s,a,l,c,u,d,f,p,h,g,m=Y.hasData(t)&&Y.get(t);if(m&&(l=m.events)){for(c=(e=(e||"").match(O)||[""]).length;c--;)if(p=g=(a=Tt.exec(e[c])||[])[1],h=(a[2]||"").split(".").sort(),p){for(d=w.event.special[p]||{},f=l[p=(i?d.delegateType:d.bindType)||p]||[],a=a[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=r=f.length;r--;)u=f[r],!o&&g!==u.origType||n&&n.guid!==u.guid||a&&!a.test(u.namespace)||i&&i!==u.selector&&("**"!==i||!u.selector)||(f.splice(r,1),u.selector&&f.delegateCount--,d.remove&&d.remove.call(t,u));s&&!f.length&&(d.teardown&&!1!==d.teardown.call(t,h,m.handle)||w.removeEvent(t,p,m.handle),delete l[p])}else for(p in l)w.event.remove(t,p+e[c],n,i,!0);w.isEmptyObject(l)&&Y.remove(t,"handle events")}},dispatch:function(t){var e,n,i,o,r,s,a=new Array(arguments.length),l=w.event.fix(t),c=(Y.get(this,"events")||Object.create(null))[l.type]||[],u=w.event.special[l.type]||{};for(a[0]=l,e=1;e<arguments.length;e++)a[e]=arguments[e];if(l.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,l)){for(s=w.event.handlers.call(this,l,c),e=0;(o=s[e++])&&!l.isPropagationStopped();)for(l.currentTarget=o.elem,n=0;(r=o.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==r.namespace&&!l.rnamespace.test(r.namespace)||(l.handleObj=r,l.data=r.data,void 0!==(i=((w.event.special[r.origType]||{}).handle||r.handler).apply(o.elem,a))&&!1===(l.result=i)&&(l.preventDefault(),l.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,l),l.result}},handlers:function(t,e){var n,i,o,r,s,a=[],l=e.delegateCount,c=t.target;if(l&&c.nodeType&&!("click"===t.type&&1<=t.button))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==t.type||!0!==c.disabled)){for(r=[],s={},n=0;n<l;n++)void 0===s[o=(i=e[n]).selector+" "]&&(s[o]=i.needsContext?-1<w(o,this).index(c):w.find(o,this,null,[c]).length),s[o]&&r.push(i);r.length&&a.push({elem:c,handlers:r})}return c=this,l<e.length&&a.push({elem:c,handlers:e.slice(l)}),a},addProp:function(t,e){Object.defineProperty(w.Event.prototype,t,{enumerable:!0,configurable:!0,get:h(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[w.expando]?t:new w.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){var e=this||t;return ft.test(e.type)&&e.click&&A(e,"input")&&At(e,"click",Ct),!1},trigger:function(t){var e=this||t;return ft.test(e.type)&&e.click&&A(e,"input")&&At(e,"click"),!0},_default:function(t){var e=t.target;return ft.test(e.type)&&e.click&&A(e,"input")&&Y.get(e,"click")||A(e,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},w.removeEvent=function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n)},w.Event=function(t,e){if(!(this instanceof w.Event))return new w.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?Ct:Et,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&w.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[w.expando]=!0},w.Event.prototype={constructor:w.Event,isDefaultPrevented:Et,isPropagationStopped:Et,isImmediatePropagationStopped:Et,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=Ct,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=Ct,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=Ct,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},w.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(t){var e=t.button;return null==t.which&&xt.test(t.type)?null!=t.charCode?t.charCode:t.keyCode:!t.which&&void 0!==e&&wt.test(t.type)?1&e?1:2&e?3:4&e?2:0:t.which}},w.event.addProp),w.each({focus:"focusin",blur:"focusout"},function(t,e){w.event.special[t]={setup:function(){return At(this,t,kt),!1},trigger:function(){return At(this,t),!0},delegateType:e}}),w.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,e){w.event.special[t]={delegateType:e,bindType:e,handle:function(t){var n,i=t.relatedTarget,o=t.handleObj;return i&&(i===this||w.contains(this,i))||(t.type=o.origType,n=o.handler.apply(this,arguments),t.type=e),n}}}),w.fn.extend({on:function(t,e,n,i){return St(this,t,e,n,i)},one:function(t,e,n,i){return St(this,t,e,n,i,1)},off:function(t,e,n){var i,o;if(t&&t.preventDefault&&t.handleObj)return i=t.handleObj,w(t.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof t){for(o in t)this.off(o,e,t[o]);return this}return!1!==e&&"function"!=typeof e||(n=e,e=void 0),!1===n&&(n=Et),this.each(function(){w.event.remove(this,t,n,e)})}});var $t=/<script|<style|<link/i,Nt=/checked\s*(?:[^=]|=\s*.checked.)/i,Dt=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function jt(t,e){return A(t,"table")&&A(11!==e.nodeType?e:e.firstChild,"tr")&&w(t).children("tbody")[0]||t}function Lt(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function Ht(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function It(t,e){var n,i,o,r,s,a;if(1===e.nodeType){if(Y.hasData(t)&&(a=Y.get(t).events))for(o in Y.remove(e,"handle events"),a)for(n=0,i=a[o].length;n<i;n++)w.event.add(e,o,a[o][n]);G.hasData(t)&&(r=G.access(t),s=w.extend({},r),G.set(e,s))}}function Ot(t,e,n,i){e=r(e);var o,s,a,l,c,u,d=0,f=t.length,g=f-1,m=e[0],v=h(m);if(v||1<f&&"string"==typeof m&&!p.checkClone&&Nt.test(m))return t.each(function(o){var r=t.eq(o);v&&(e[0]=m.call(this,o,r.html())),Ot(r,e,n,i)});if(f&&(s=(o=bt(e,t[0].ownerDocument,!1,t,i)).firstChild,1===o.childNodes.length&&(o=s),s||i)){for(l=(a=w.map(mt(o,"script"),Lt)).length;d<f;d++)c=o,d!==g&&(c=w.clone(c,!0,!0),l&&w.merge(a,mt(c,"script"))),n.call(t[d],c,d);if(l)for(u=a[a.length-1].ownerDocument,w.map(a,Ht),d=0;d<l;d++)c=a[d],ht.test(c.type||"")&&!Y.access(c,"globalEval")&&w.contains(u,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?w._evalUrl&&!c.noModule&&w._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},u):y(c.textContent.replace(Dt,""),c,u))}return t}function qt(t,e,n){for(var i,o=e?w.filter(e,t):t,r=0;null!=(i=o[r]);r++)n||1!==i.nodeType||w.cleanData(mt(i)),i.parentNode&&(n&&ot(i)&&vt(mt(i,"script")),i.parentNode.removeChild(i));return t}w.extend({htmlPrefilter:function(t){return t},clone:function(t,e,n){var i,o,r,s,a,l,c,u=t.cloneNode(!0),d=ot(t);if(!(p.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||w.isXMLDoc(t)))for(s=mt(u),i=0,o=(r=mt(t)).length;i<o;i++)a=r[i],"input"===(c=(l=s[i]).nodeName.toLowerCase())&&ft.test(a.type)?l.checked=a.checked:"input"!==c&&"textarea"!==c||(l.defaultValue=a.defaultValue);if(e)if(n)for(r=r||mt(t),s=s||mt(u),i=0,o=r.length;i<o;i++)It(r[i],s[i]);else It(t,u);return 0<(s=mt(u,"script")).length&&vt(s,!d&&mt(t,"script")),u},cleanData:function(t){for(var e,n,i,o=w.event.special,r=0;void 0!==(n=t[r]);r++)if(X(n)){if(e=n[Y.expando]){if(e.events)for(i in e.events)o[i]?w.event.remove(n,i):w.removeEvent(n,i,e.handle);n[Y.expando]=void 0}n[G.expando]&&(n[G.expando]=void 0)}}}),w.fn.extend({detach:function(t){return qt(this,t,!0)},remove:function(t){return qt(this,t)},text:function(t){return W(this,function(t){return void 0===t?w.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)})},null,t,arguments.length)},append:function(){return Ot(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||jt(this,t).appendChild(t)})},prepend:function(){return Ot(this,arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=jt(this,t);e.insertBefore(t,e.firstChild)}})},before:function(){return Ot(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return Ot(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(w.cleanData(mt(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return w.clone(this,t,e)})},html:function(t){return W(this,function(t){var e=this[0]||{},n=0,i=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!$t.test(t)&&!gt[(pt.exec(t)||["",""])[1].toLowerCase()]){t=w.htmlPrefilter(t);try{for(;n<i;n++)1===(e=this[n]||{}).nodeType&&(w.cleanData(mt(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var t=[];return Ot(this,arguments,function(e){var n=this.parentNode;w.inArray(this,t)<0&&(w.cleanData(mt(this)),n&&n.replaceChild(e,this))},t)}}),w.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,e){w.fn[t]=function(t){for(var n,i=[],o=w(t),r=o.length-1,a=0;a<=r;a++)n=a===r?this:this.clone(!0),w(o[a])[e](n),s.apply(i,n.get());return this.pushStack(i)}});var Pt=new RegExp("^("+tt+")(?!px)[a-z%]+$","i"),Bt=function(e){var n=e.ownerDocument.defaultView;return n&&n.opener||(n=t),n.getComputedStyle(e)},Mt=function(t,e,n){var i,o,r={};for(o in e)r[o]=t.style[o],t.style[o]=e[o];for(o in i=n.call(t),e)t.style[o]=r[o];return i},Rt=new RegExp(nt.join("|"),"i");function _t(t,e,n){var i,o,r,s,a=t.style;return(n=n||Bt(t))&&(""!==(s=n.getPropertyValue(e)||n[e])||ot(t)||(s=w.style(t,e)),!p.pixelBoxStyles()&&Pt.test(s)&&Rt.test(e)&&(i=a.width,o=a.minWidth,r=a.maxWidth,a.minWidth=a.maxWidth=a.width=s,s=n.width,a.width=i,a.minWidth=o,a.maxWidth=r)),void 0!==s?s+"":s}function Wt(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}!function(){function e(){if(u){c.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",u.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",it.appendChild(c).appendChild(u);var e=t.getComputedStyle(u);i="1%"!==e.top,l=12===n(e.marginLeft),u.style.right="60%",s=36===n(e.right),o=36===n(e.width),u.style.position="absolute",r=12===n(u.offsetWidth/3),it.removeChild(c),u=null}}function n(t){return Math.round(parseFloat(t))}var i,o,r,s,a,l,c=m.createElement("div"),u=m.createElement("div");u.style&&(u.style.backgroundClip="content-box",u.cloneNode(!0).style.backgroundClip="",p.clearCloneStyle="content-box"===u.style.backgroundClip,w.extend(p,{boxSizingReliable:function(){return e(),o},pixelBoxStyles:function(){return e(),s},pixelPosition:function(){return e(),i},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),r},reliableTrDimensions:function(){var e,n,i,o;return null==a&&(e=m.createElement("table"),n=m.createElement("tr"),i=m.createElement("div"),e.style.cssText="position:absolute;left:-11111px",n.style.height="1px",i.style.height="9px",it.appendChild(e).appendChild(n).appendChild(i),o=t.getComputedStyle(n),a=3<parseInt(o.height),it.removeChild(e)),a}}))}();var Ut=["Webkit","Moz","ms"],Ft=m.createElement("div").style,zt={};function Vt(t){return w.cssProps[t]||zt[t]||(t in Ft?t:zt[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),n=Ut.length;n--;)if((t=Ut[n]+e)in Ft)return t}(t)||t)}var Xt=/^(none|table(?!-c[ea]).+)/,Qt=/^--/,Yt={position:"absolute",visibility:"hidden",display:"block"},Gt={letterSpacing:"0",fontWeight:"400"};function Jt(t,e,n){var i=et.exec(e);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):e}function Kt(t,e,n,i,o,r){var s="width"===e?1:0,a=0,l=0;if(n===(i?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(l+=w.css(t,n+nt[s],!0,o)),i?("content"===n&&(l-=w.css(t,"padding"+nt[s],!0,o)),"margin"!==n&&(l-=w.css(t,"border"+nt[s]+"Width",!0,o))):(l+=w.css(t,"padding"+nt[s],!0,o),"padding"!==n?l+=w.css(t,"border"+nt[s]+"Width",!0,o):a+=w.css(t,"border"+nt[s]+"Width",!0,o));return!i&&0<=r&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-r-l-a-.5))||0),l}function Zt(t,e,n){var i=Bt(t),o=(!p.boxSizingReliable()||n)&&"border-box"===w.css(t,"boxSizing",!1,i),r=o,s=_t(t,e,i),a="offset"+e[0].toUpperCase()+e.slice(1);if(Pt.test(s)){if(!n)return s;s="auto"}return(!p.boxSizingReliable()&&o||!p.reliableTrDimensions()&&A(t,"tr")||"auto"===s||!parseFloat(s)&&"inline"===w.css(t,"display",!1,i))&&t.getClientRects().length&&(o="border-box"===w.css(t,"boxSizing",!1,i),(r=a in t)&&(s=t[a])),(s=parseFloat(s)||0)+Kt(t,e,n||(o?"border":"content"),r,i,s)+"px"}function te(t,e,n,i,o){return new te.prototype.init(t,e,n,i,o)}w.extend({cssHooks:{opacity:{get:function(t,e){if(e){var n=_t(t,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(t,e,n,i){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var o,r,s,a=V(e),l=Qt.test(e),c=t.style;if(l||(e=Vt(a)),s=w.cssHooks[e]||w.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(o=s.get(t,!1,i))?o:c[e];"string"==(r=typeof n)&&(o=et.exec(n))&&o[1]&&(n=at(t,e,o),r="number"),null!=n&&n==n&&("number"!==r||l||(n+=o&&o[3]||(w.cssNumber[a]?"":"px")),p.clearCloneStyle||""!==n||0!==e.indexOf("background")||(c[e]="inherit"),s&&"set"in s&&void 0===(n=s.set(t,n,i))||(l?c.setProperty(e,n):c[e]=n))}},css:function(t,e,n,i){var o,r,s,a=V(e);return Qt.test(e)||(e=Vt(a)),(s=w.cssHooks[e]||w.cssHooks[a])&&"get"in s&&(o=s.get(t,!0,n)),void 0===o&&(o=_t(t,e,i)),"normal"===o&&e in Gt&&(o=Gt[e]),""===n||n?(r=parseFloat(o),!0===n||isFinite(r)?r||0:o):o}}),w.each(["height","width"],function(t,e){w.cssHooks[e]={get:function(t,n,i){if(n)return!Xt.test(w.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?Zt(t,e,i):Mt(t,Yt,function(){return Zt(t,e,i)})},set:function(t,n,i){var o,r=Bt(t),s=!p.scrollboxSize()&&"absolute"===r.position,a=(s||i)&&"border-box"===w.css(t,"boxSizing",!1,r),l=i?Kt(t,e,i,a,r):0;return a&&s&&(l-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(r[e])-Kt(t,e,"border",!1,r)-.5)),l&&(o=et.exec(n))&&"px"!==(o[3]||"px")&&(t.style[e]=n,n=w.css(t,e)),Jt(0,n,l)}}}),w.cssHooks.marginLeft=Wt(p.reliableMarginLeft,function(t,e){if(e)return(parseFloat(_t(t,"marginLeft"))||t.getBoundingClientRect().left-Mt(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}))+"px"}),w.each({margin:"",padding:"",border:"Width"},function(t,e){w.cssHooks[t+e]={expand:function(n){for(var i=0,o={},r="string"==typeof n?n.split(" "):[n];i<4;i++)o[t+nt[i]+e]=r[i]||r[i-2]||r[0];return o}},"margin"!==t&&(w.cssHooks[t+e].set=Jt)}),w.fn.extend({css:function(t,e){return W(this,function(t,e,n){var i,o,r={},s=0;if(Array.isArray(e)){for(i=Bt(t),o=e.length;s<o;s++)r[e[s]]=w.css(t,e[s],!1,i);return r}return void 0!==n?w.style(t,e,n):w.css(t,e)},t,e,1<arguments.length)}}),((w.Tween=te).prototype={constructor:te,init:function(t,e,n,i,o,r){this.elem=t,this.prop=n,this.easing=o||w.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=i,this.unit=r||(w.cssNumber[n]?"":"px")},cur:function(){var t=te.propHooks[this.prop];return t&&t.get?t.get(this):te.propHooks._default.get(this)},run:function(t){var e,n=te.propHooks[this.prop];return this.options.duration?this.pos=e=w.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):te.propHooks._default.set(this),this}}).init.prototype=te.prototype,(te.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=w.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){w.fx.step[t.prop]?w.fx.step[t.prop](t):1!==t.elem.nodeType||!w.cssHooks[t.prop]&&null==t.elem.style[Vt(t.prop)]?t.elem[t.prop]=t.now:w.style(t.elem,t.prop,t.now+t.unit)}}}).scrollTop=te.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},w.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},w.fx=te.prototype.init,w.fx.step={};var ee,ne,ie,oe,re=/^(?:toggle|show|hide)$/,se=/queueHooks$/;function ae(){ne&&(!1===m.hidden&&t.requestAnimationFrame?t.requestAnimationFrame(ae):t.setTimeout(ae,w.fx.interval),w.fx.tick())}function le(){return t.setTimeout(function(){ee=void 0}),ee=Date.now()}function ce(t,e){var n,i=0,o={height:t};for(e=e?1:0;i<4;i+=2-e)o["margin"+(n=nt[i])]=o["padding"+n]=t;return e&&(o.opacity=o.width=t),o}function ue(t,e,n){for(var i,o=(de.tweeners[e]||[]).concat(de.tweeners["*"]),r=0,s=o.length;r<s;r++)if(i=o[r].call(n,e,t))return i}function de(t,e,n){var i,o,r=0,s=de.prefilters.length,a=w.Deferred().always(function(){delete l.elem}),l=function(){if(o)return!1;for(var e=ee||le(),n=Math.max(0,c.startTime+c.duration-e),i=1-(n/c.duration||0),r=0,s=c.tweens.length;r<s;r++)c.tweens[r].run(i);return a.notifyWith(t,[c,i,n]),i<1&&s?n:(s||a.notifyWith(t,[c,1,0]),a.resolveWith(t,[c]),!1)},c=a.promise({elem:t,props:w.extend({},e),opts:w.extend(!0,{specialEasing:{},easing:w.easing._default},n),originalProperties:e,originalOptions:n,startTime:ee||le(),duration:n.duration,tweens:[],createTween:function(e,n){var i=w.Tween(t,c.opts,e,n,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(i),i},stop:function(e){var n=0,i=e?c.tweens.length:0;if(o)return this;for(o=!0;n<i;n++)c.tweens[n].run(1);return e?(a.notifyWith(t,[c,1,0]),a.resolveWith(t,[c,e])):a.rejectWith(t,[c,e]),this}}),u=c.props;for(function(t,e){var n,i,o,r,s;for(n in t)if(o=e[i=V(n)],r=t[n],Array.isArray(r)&&(o=r[1],r=t[n]=r[0]),n!==i&&(t[i]=r,delete t[n]),(s=w.cssHooks[i])&&"expand"in s)for(n in r=s.expand(r),delete t[i],r)n in t||(t[n]=r[n],e[n]=o);else e[i]=o}(u,c.opts.specialEasing);r<s;r++)if(i=de.prefilters[r].call(c,t,u,c.opts))return h(i.stop)&&(w._queueHooks(c.elem,c.opts.queue).stop=i.stop.bind(i)),i;return w.map(u,ue,c),h(c.opts.start)&&c.opts.start.call(t,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),w.fx.timer(w.extend(l,{elem:t,anim:c,queue:c.opts.queue})),c}w.Animation=w.extend(de,{tweeners:{"*":[function(t,e){var n=this.createTween(t,e);return at(n.elem,t,et.exec(e),n),n}]},tweener:function(t,e){h(t)?(e=t,t=["*"]):t=t.match(O);for(var n,i=0,o=t.length;i<o;i++)n=t[i],de.tweeners[n]=de.tweeners[n]||[],de.tweeners[n].unshift(e)},prefilters:[function(t,e,n){var i,o,r,s,a,l,c,u,d="width"in e||"height"in e,f=this,p={},h=t.style,g=t.nodeType&&st(t),m=Y.get(t,"fxshow");for(i in n.queue||(null==(s=w._queueHooks(t,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,f.always(function(){f.always(function(){s.unqueued--,w.queue(t,"fx").length||s.empty.fire()})})),e)if(o=e[i],re.test(o)){if(delete e[i],r=r||"toggle"===o,o===(g?"hide":"show")){if("show"!==o||!m||void 0===m[i])continue;g=!0}p[i]=m&&m[i]||w.style(t,i)}if((l=!w.isEmptyObject(e))||!w.isEmptyObject(p))for(i in d&&1===t.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(c=m&&m.display)&&(c=Y.get(t,"display")),"none"===(u=w.css(t,"display"))&&(c?u=c:(ct([t],!0),c=t.style.display||c,u=w.css(t,"display"),ct([t]))),("inline"===u||"inline-block"===u&&null!=c)&&"none"===w.css(t,"float")&&(l||(f.done(function(){h.display=c}),null==c&&(u=h.display,c="none"===u?"":u)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",f.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),l=!1,p)l||(m?"hidden"in m&&(g=m.hidden):m=Y.access(t,"fxshow",{display:c}),r&&(m.hidden=!g),g&&ct([t],!0),f.done(function(){for(i in g||ct([t]),Y.remove(t,"fxshow"),p)w.style(t,i,p[i])})),l=ue(g?m[i]:0,i,f),i in m||(m[i]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?de.prefilters.unshift(t):de.prefilters.push(t)}}),w.speed=function(t,e,n){var i=t&&"object"==typeof t?w.extend({},t):{complete:n||!n&&e||h(t)&&t,duration:t,easing:n&&e||e&&!h(e)&&e};return w.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in w.fx.speeds?i.duration=w.fx.speeds[i.duration]:i.duration=w.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){h(i.old)&&i.old.call(this),i.queue&&w.dequeue(this,i.queue)},i},w.fn.extend({fadeTo:function(t,e,n,i){return this.filter(st).css("opacity",0).show().end().animate({opacity:e},t,n,i)},animate:function(t,e,n,i){var o=w.isEmptyObject(t),r=w.speed(e,n,i),s=function(){var e=de(this,w.extend({},t),r);(o||Y.get(this,"finish"))&&e.stop(!0)};return s.finish=s,o||!1===r.queue?this.each(s):this.queue(r.queue,s)},stop:function(t,e,n){var i=function(t){var e=t.stop;delete t.stop,e(n)};return"string"!=typeof t&&(n=e,e=t,t=void 0),e&&this.queue(t||"fx",[]),this.each(function(){var e=!0,o=null!=t&&t+"queueHooks",r=w.timers,s=Y.get(this);if(o)s[o]&&s[o].stop&&i(s[o]);else for(o in s)s[o]&&s[o].stop&&se.test(o)&&i(s[o]);for(o=r.length;o--;)r[o].elem!==this||null!=t&&r[o].queue!==t||(r[o].anim.stop(n),e=!1,r.splice(o,1));!e&&n||w.dequeue(this,t)})},finish:function(t){return!1!==t&&(t=t||"fx"),this.each(function(){var e,n=Y.get(this),i=n[t+"queue"],o=n[t+"queueHooks"],r=w.timers,s=i?i.length:0;for(n.finish=!0,w.queue(this,t,[]),o&&o.stop&&o.stop.call(this,!0),e=r.length;e--;)r[e].elem===this&&r[e].queue===t&&(r[e].anim.stop(!0),r.splice(e,1));for(e=0;e<s;e++)i[e]&&i[e].finish&&i[e].finish.call(this);delete n.finish})}}),w.each(["toggle","show","hide"],function(t,e){var n=w.fn[e];w.fn[e]=function(t,i,o){return null==t||"boolean"==typeof t?n.apply(this,arguments):this.animate(ce(e,!0),t,i,o)}}),w.each({slideDown:ce("show"),slideUp:ce("hide"),slideToggle:ce("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,e){w.fn[t]=function(t,n,i){return this.animate(e,t,n,i)}}),w.timers=[],w.fx.tick=function(){var t,e=0,n=w.timers;for(ee=Date.now();e<n.length;e++)(t=n[e])()||n[e]!==t||n.splice(e--,1);n.length||w.fx.stop(),ee=void 0},w.fx.timer=function(t){w.timers.push(t),w.fx.start()},w.fx.interval=13,w.fx.start=function(){ne||(ne=!0,ae())},w.fx.stop=function(){ne=null},w.fx.speeds={slow:600,fast:200,_default:400},w.fn.delay=function(e,n){return e=w.fx&&w.fx.speeds[e]||e,n=n||"fx",this.queue(n,function(n,i){var o=t.setTimeout(n,e);i.stop=function(){t.clearTimeout(o)}})},ie=m.createElement("input"),oe=m.createElement("select").appendChild(m.createElement("option")),ie.type="checkbox",p.checkOn=""!==ie.value,p.optSelected=oe.selected,(ie=m.createElement("input")).value="t",ie.type="radio",p.radioValue="t"===ie.value;var fe,pe=w.expr.attrHandle;w.fn.extend({attr:function(t,e){return W(this,w.attr,t,e,1<arguments.length)},removeAttr:function(t){return this.each(function(){w.removeAttr(this,t)})}}),w.extend({attr:function(t,e,n){var i,o,r=t.nodeType;if(3!==r&&8!==r&&2!==r)return void 0===t.getAttribute?w.prop(t,e,n):(1===r&&w.isXMLDoc(t)||(o=w.attrHooks[e.toLowerCase()]||(w.expr.match.bool.test(e)?fe:void 0)),void 0!==n?null===n?void w.removeAttr(t,e):o&&"set"in o&&void 0!==(i=o.set(t,n,e))?i:(t.setAttribute(e,n+""),n):o&&"get"in o&&null!==(i=o.get(t,e))?i:null==(i=w.find.attr(t,e))?void 0:i)},attrHooks:{type:{set:function(t,e){if(!p.radioValue&&"radio"===e&&A(t,"input")){var n=t.value;return t.setAttribute("type",e),n&&(t.value=n),e}}}},removeAttr:function(t,e){var n,i=0,o=e&&e.match(O);if(o&&1===t.nodeType)for(;n=o[i++];)t.removeAttribute(n)}}),fe={set:function(t,e,n){return!1===e?w.removeAttr(t,n):t.setAttribute(n,n),n}},w.each(w.expr.match.bool.source.match(/\w+/g),function(t,e){var n=pe[e]||w.find.attr;pe[e]=function(t,e,i){var o,r,s=e.toLowerCase();return i||(r=pe[s],pe[s]=o,o=null!=n(t,e,i)?s:null,pe[s]=r),o}});var he=/^(?:input|select|textarea|button)$/i,ge=/^(?:a|area)$/i;function me(t){return(t.match(O)||[]).join(" ")}function ve(t){return t.getAttribute&&t.getAttribute("class")||""}function ye(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(O)||[]}w.fn.extend({prop:function(t,e){return W(this,w.prop,t,e,1<arguments.length)},removeProp:function(t){return this.each(function(){delete this[w.propFix[t]||t]})}}),w.extend({prop:function(t,e,n){var i,o,r=t.nodeType;if(3!==r&&8!==r&&2!==r)return 1===r&&w.isXMLDoc(t)||(e=w.propFix[e]||e,o=w.propHooks[e]),void 0!==n?o&&"set"in o&&void 0!==(i=o.set(t,n,e))?i:t[e]=n:o&&"get"in o&&null!==(i=o.get(t,e))?i:t[e]},propHooks:{tabIndex:{get:function(t){var e=w.find.attr(t,"tabindex");return e?parseInt(e,10):he.test(t.nodeName)||ge.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),p.optSelected||(w.propHooks.selected={get:function(t){var e=t.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),w.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){w.propFix[this.toLowerCase()]=this}),w.fn.extend({addClass:function(t){var e,n,i,o,r,s,a,l=0;if(h(t))return this.each(function(e){w(this).addClass(t.call(this,e,ve(this)))});if((e=ye(t)).length)for(;n=this[l++];)if(o=ve(n),i=1===n.nodeType&&" "+me(o)+" "){for(s=0;r=e[s++];)i.indexOf(" "+r+" ")<0&&(i+=r+" ");o!==(a=me(i))&&n.setAttribute("class",a)}return this},removeClass:function(t){var e,n,i,o,r,s,a,l=0;if(h(t))return this.each(function(e){w(this).removeClass(t.call(this,e,ve(this)))});if(!arguments.length)return this.attr("class","");if((e=ye(t)).length)for(;n=this[l++];)if(o=ve(n),i=1===n.nodeType&&" "+me(o)+" "){for(s=0;r=e[s++];)for(;-1<i.indexOf(" "+r+" ");)i=i.replace(" "+r+" "," ");o!==(a=me(i))&&n.setAttribute("class",a)}return this},toggleClass:function(t,e){var n=typeof t,i="string"===n||Array.isArray(t);return"boolean"==typeof e&&i?e?this.addClass(t):this.removeClass(t):h(t)?this.each(function(n){w(this).toggleClass(t.call(this,n,ve(this),e),e)}):this.each(function(){var e,o,r,s;if(i)for(o=0,r=w(this),s=ye(t);e=s[o++];)r.hasClass(e)?r.removeClass(e):r.addClass(e);else void 0!==t&&"boolean"!==n||((e=ve(this))&&Y.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",e||!1===t?"":Y.get(this,"__className__")||""))})},hasClass:function(t){var e,n,i=0;for(e=" "+t+" ";n=this[i++];)if(1===n.nodeType&&-1<(" "+me(ve(n))+" ").indexOf(e))return!0;return!1}});var be=/\r/g;w.fn.extend({val:function(t){var e,n,i,o=this[0];return arguments.length?(i=h(t),this.each(function(n){var o;1===this.nodeType&&(null==(o=i?t.call(this,n,w(this).val()):t)?o="":"number"==typeof o?o+="":Array.isArray(o)&&(o=w.map(o,function(t){return null==t?"":t+""})),(e=w.valHooks[this.type]||w.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,o,"value")||(this.value=o))})):o?(e=w.valHooks[o.type]||w.valHooks[o.nodeName.toLowerCase()])&&"get"in e&&void 0!==(n=e.get(o,"value"))?n:"string"==typeof(n=o.value)?n.replace(be,""):null==n?"":n:void 0}}),w.extend({valHooks:{option:{get:function(t){var e=w.find.attr(t,"value");return null!=e?e:me(w.text(t))}},select:{get:function(t){var e,n,i,o=t.options,r=t.selectedIndex,s="select-one"===t.type,a=s?null:[],l=s?r+1:o.length;for(i=r<0?l:s?r:0;i<l;i++)if(((n=o[i]).selected||i===r)&&!n.disabled&&(!n.parentNode.disabled||!A(n.parentNode,"optgroup"))){if(e=w(n).val(),s)return e;a.push(e)}return a},set:function(t,e){for(var n,i,o=t.options,r=w.makeArray(e),s=o.length;s--;)((i=o[s]).selected=-1<w.inArray(w.valHooks.option.get(i),r))&&(n=!0);return n||(t.selectedIndex=-1),r}}}}),w.each(["radio","checkbox"],function(){w.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=-1<w.inArray(w(t).val(),e)}},p.checkOn||(w.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})}),p.focusin="onfocusin"in t;var xe=/^(?:focusinfocus|focusoutblur)$/,we=function(t){t.stopPropagation()};w.extend(w.event,{trigger:function(e,n,i,o){var r,s,a,l,c,d,f,p,v=[i||m],y=u.call(e,"type")?e.type:e,b=u.call(e,"namespace")?e.namespace.split("."):[];if(s=p=a=i=i||m,3!==i.nodeType&&8!==i.nodeType&&!xe.test(y+w.event.triggered)&&(-1<y.indexOf(".")&&(y=(b=y.split(".")).shift(),b.sort()),c=y.indexOf(":")<0&&"on"+y,(e=e[w.expando]?e:new w.Event(y,"object"==typeof e&&e)).isTrigger=o?2:3,e.namespace=b.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+b.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=i),n=null==n?[e]:w.makeArray(n,[e]),f=w.event.special[y]||{},o||!f.trigger||!1!==f.trigger.apply(i,n))){if(!o&&!f.noBubble&&!g(i)){for(l=f.delegateType||y,xe.test(l+y)||(s=s.parentNode);s;s=s.parentNode)v.push(s),a=s;a===(i.ownerDocument||m)&&v.push(a.defaultView||a.parentWindow||t)}for(r=0;(s=v[r++])&&!e.isPropagationStopped();)p=s,e.type=1<r?l:f.bindType||y,(d=(Y.get(s,"events")||Object.create(null))[e.type]&&Y.get(s,"handle"))&&d.apply(s,n),(d=c&&s[c])&&d.apply&&X(s)&&(e.result=d.apply(s,n),!1===e.result&&e.preventDefault());return e.type=y,o||e.isDefaultPrevented()||f._default&&!1!==f._default.apply(v.pop(),n)||!X(i)||c&&h(i[y])&&!g(i)&&((a=i[c])&&(i[c]=null),w.event.triggered=y,e.isPropagationStopped()&&p.addEventListener(y,we),i[y](),e.isPropagationStopped()&&p.removeEventListener(y,we),w.event.triggered=void 0,a&&(i[c]=a)),e.result}},simulate:function(t,e,n){var i=w.extend(new w.Event,n,{type:t,isSimulated:!0});w.event.trigger(i,null,e)}}),w.fn.extend({trigger:function(t,e){return this.each(function(){w.event.trigger(t,e,this)})},triggerHandler:function(t,e){var n=this[0];if(n)return w.event.trigger(t,e,n,!0)}}),p.focusin||w.each({focus:"focusin",blur:"focusout"},function(t,e){var n=function(t){w.event.simulate(e,t.target,w.event.fix(t))};w.event.special[e]={setup:function(){var i=this.ownerDocument||this.document||this,o=Y.access(i,e);o||i.addEventListener(t,n,!0),Y.access(i,e,(o||0)+1)},teardown:function(){var i=this.ownerDocument||this.document||this,o=Y.access(i,e)-1;o?Y.access(i,e,o):(i.removeEventListener(t,n,!0),Y.remove(i,e))}}});var Te=t.location,Ce={guid:Date.now()},Ee=/\?/;w.parseXML=function(e){var n;if(!e||"string"!=typeof e)return null;try{n=(new t.DOMParser).parseFromString(e,"text/xml")}catch(e){n=void 0}return n&&!n.getElementsByTagName("parsererror").length||w.error("Invalid XML: "+e),n};var ke=/\[\]$/,Se=/\r?\n/g,Ae=/^(?:submit|button|image|reset|file)$/i,$e=/^(?:input|select|textarea|keygen)/i;function Ne(t,e,n,i){var o;if(Array.isArray(e))w.each(e,function(e,o){n||ke.test(t)?i(t,o):Ne(t+"["+("object"==typeof o&&null!=o?e:"")+"]",o,n,i)});else if(n||"object"!==b(e))i(t,e);else for(o in e)Ne(t+"["+o+"]",e[o],n,i)}w.param=function(t,e){var n,i=[],o=function(t,e){var n=h(e)?e():e;i[i.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==n?"":n)};if(null==t)return"";if(Array.isArray(t)||t.jquery&&!w.isPlainObject(t))w.each(t,function(){o(this.name,this.value)});else for(n in t)Ne(n,t[n],e,o);return i.join("&")},w.fn.extend({serialize:function(){return w.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=w.prop(this,"elements");return t?w.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!w(this).is(":disabled")&&$e.test(this.nodeName)&&!Ae.test(t)&&(this.checked||!ft.test(t))}).map(function(t,e){var n=w(this).val();return null==n?null:Array.isArray(n)?w.map(n,function(t){return{name:e.name,value:t.replace(Se,"\r\n")}}):{name:e.name,value:n.replace(Se,"\r\n")}}).get()}});var De=/%20/g,je=/#.*$/,Le=/([?&])_=[^&]*/,He=/^(.*?):[ \t]*([^\r\n]*)$/gm,Ie=/^(?:GET|HEAD)$/,Oe=/^\/\//,qe={},Pe={},Be="*/".concat("*"),Me=m.createElement("a");function Re(t){return function(e,n){"string"!=typeof e&&(n=e,e="*");var i,o=0,r=e.toLowerCase().match(O)||[];if(h(n))for(;i=r[o++];)"+"===i[0]?(i=i.slice(1)||"*",(t[i]=t[i]||[]).unshift(n)):(t[i]=t[i]||[]).push(n)}}function _e(t,e,n,i){var o={},r=t===Pe;function s(a){var l;return o[a]=!0,w.each(t[a]||[],function(t,a){var c=a(e,n,i);return"string"!=typeof c||r||o[c]?r?!(l=c):void 0:(e.dataTypes.unshift(c),s(c),!1)}),l}return s(e.dataTypes[0])||!o["*"]&&s("*")}function We(t,e){var n,i,o=w.ajaxSettings.flatOptions||{};for(n in e)void 0!==e[n]&&((o[n]?t:i||(i={}))[n]=e[n]);return i&&w.extend(!0,t,i),t}Me.href=Te.href,w.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Te.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Te.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Be,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":w.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?We(We(t,w.ajaxSettings),e):We(w.ajaxSettings,t)},ajaxPrefilter:Re(qe),ajaxTransport:Re(Pe),ajax:function(e,n){"object"==typeof e&&(n=e,e=void 0),n=n||{};var i,o,r,s,a,l,c,u,d,f,p=w.ajaxSetup({},n),h=p.context||p,g=p.context&&(h.nodeType||h.jquery)?w(h):w.event,v=w.Deferred(),y=w.Callbacks("once memory"),b=p.statusCode||{},x={},T={},C="canceled",E={readyState:0,getResponseHeader:function(t){var e;if(c){if(!s)for(s={};e=He.exec(r);)s[e[1].toLowerCase()+" "]=(s[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=s[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return c?r:null},setRequestHeader:function(t,e){return null==c&&(t=T[t.toLowerCase()]=T[t.toLowerCase()]||t,x[t]=e),this},overrideMimeType:function(t){return null==c&&(p.mimeType=t),this},statusCode:function(t){var e;if(t)if(c)E.always(t[E.status]);else for(e in t)b[e]=[b[e],t[e]];return this},abort:function(t){var e=t||C;return i&&i.abort(e),k(0,e),this}};if(v.promise(E),p.url=((e||p.url||Te.href)+"").replace(Oe,Te.protocol+"//"),p.type=n.method||n.type||p.method||p.type,p.dataTypes=(p.dataType||"*").toLowerCase().match(O)||[""],null==p.crossDomain){l=m.createElement("a");try{l.href=p.url,l.href=l.href,p.crossDomain=Me.protocol+"//"+Me.host!=l.protocol+"//"+l.host}catch(e){p.crossDomain=!0}}if(p.data&&p.processData&&"string"!=typeof p.data&&(p.data=w.param(p.data,p.traditional)),_e(qe,p,n,E),c)return E;for(d in(u=w.event&&p.global)&&0==w.active++&&w.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!Ie.test(p.type),o=p.url.replace(je,""),p.hasContent?p.data&&p.processData&&0===(p.contentType||"").indexOf("application/x-www-form-urlencoded")&&(p.data=p.data.replace(De,"+")):(f=p.url.slice(o.length),p.data&&(p.processData||"string"==typeof p.data)&&(o+=(Ee.test(o)?"&":"?")+p.data,delete p.data),!1===p.cache&&(o=o.replace(Le,"$1"),f=(Ee.test(o)?"&":"?")+"_="+Ce.guid+++f),p.url=o+f),p.ifModified&&(w.lastModified[o]&&E.setRequestHeader("If-Modified-Since",w.lastModified[o]),w.etag[o]&&E.setRequestHeader("If-None-Match",w.etag[o])),(p.data&&p.hasContent&&!1!==p.contentType||n.contentType)&&E.setRequestHeader("Content-Type",p.contentType),E.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+Be+"; q=0.01":""):p.accepts["*"]),p.headers)E.setRequestHeader(d,p.headers[d]);if(p.beforeSend&&(!1===p.beforeSend.call(h,E,p)||c))return E.abort();if(C="abort",y.add(p.complete),E.done(p.success),E.fail(p.error),i=_e(Pe,p,n,E)){if(E.readyState=1,u&&g.trigger("ajaxSend",[E,p]),c)return E;p.async&&0<p.timeout&&(a=t.setTimeout(function(){E.abort("timeout")},p.timeout));try{c=!1,i.send(x,k)}catch(e){if(c)throw e;k(-1,e)}}else k(-1,"No Transport");function k(e,n,s,l){var d,f,m,x,T,C=n;c||(c=!0,a&&t.clearTimeout(a),i=void 0,r=l||"",E.readyState=0<e?4:0,d=200<=e&&e<300||304===e,s&&(x=function(t,e,n){for(var i,o,r,s,a=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=t.mimeType||e.getResponseHeader("Content-Type"));if(i)for(o in a)if(a[o]&&a[o].test(i)){l.unshift(o);break}if(l[0]in n)r=l[0];else{for(o in n){if(!l[0]||t.converters[o+" "+l[0]]){r=o;break}s||(s=o)}r=r||s}if(r)return r!==l[0]&&l.unshift(r),n[r]}(p,E,s)),!d&&-1<w.inArray("script",p.dataTypes)&&(p.converters["text script"]=function(){}),x=function(t,e,n,i){var o,r,s,a,l,c={},u=t.dataTypes.slice();if(u[1])for(s in t.converters)c[s.toLowerCase()]=t.converters[s];for(r=u.shift();r;)if(t.responseFields[r]&&(n[t.responseFields[r]]=e),!l&&i&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=r,r=u.shift())if("*"===r)r=l;else if("*"!==l&&l!==r){if(!(s=c[l+" "+r]||c["* "+r]))for(o in c)if((a=o.split(" "))[1]===r&&(s=c[l+" "+a[0]]||c["* "+a[0]])){!0===s?s=c[o]:!0!==c[o]&&(r=a[0],u.unshift(a[1]));break}if(!0!==s)if(s&&t.throws)e=s(e);else try{e=s(e)}catch(t){return{state:"parsererror",error:s?t:"No conversion from "+l+" to "+r}}}return{state:"success",data:e}}(p,x,E,d),d?(p.ifModified&&((T=E.getResponseHeader("Last-Modified"))&&(w.lastModified[o]=T),(T=E.getResponseHeader("etag"))&&(w.etag[o]=T)),204===e||"HEAD"===p.type?C="nocontent":304===e?C="notmodified":(C=x.state,f=x.data,d=!(m=x.error))):(m=C,!e&&C||(C="error",e<0&&(e=0))),E.status=e,E.statusText=(n||C)+"",d?v.resolveWith(h,[f,C,E]):v.rejectWith(h,[E,C,m]),E.statusCode(b),b=void 0,u&&g.trigger(d?"ajaxSuccess":"ajaxError",[E,p,d?f:m]),y.fireWith(h,[E,C]),u&&(g.trigger("ajaxComplete",[E,p]),--w.active||w.event.trigger("ajaxStop")))}return E},getJSON:function(t,e,n){return w.get(t,e,n,"json")},getScript:function(t,e){return w.get(t,void 0,e,"script")}}),w.each(["get","post"],function(t,e){w[e]=function(t,n,i,o){return h(n)&&(o=o||i,i=n,n=void 0),w.ajax(w.extend({url:t,type:e,dataType:o,data:n,success:i},w.isPlainObject(t)&&t))}}),w.ajaxPrefilter(function(t){var e;for(e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")}),w._evalUrl=function(t,e,n){return w.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){w.globalEval(t,e,n)}})},w.fn.extend({wrapAll:function(t){var e;return this[0]&&(h(t)&&(t=t.call(this[0])),e=w(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t}).append(this)),this},wrapInner:function(t){return h(t)?this.each(function(e){w(this).wrapInner(t.call(this,e))}):this.each(function(){var e=w(this),n=e.contents();n.length?n.wrapAll(t):e.append(t)})},wrap:function(t){var e=h(t);return this.each(function(n){w(this).wrapAll(e?t.call(this,n):t)})},unwrap:function(t){return this.parent(t).not("body").each(function(){w(this).replaceWith(this.childNodes)}),this}}),w.expr.pseudos.hidden=function(t){return!w.expr.pseudos.visible(t)},w.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},w.ajaxSettings.xhr=function(){try{return new t.XMLHttpRequest}catch(t){}};var Ue={0:200,1223:204},Fe=w.ajaxSettings.xhr();p.cors=!!Fe&&"withCredentials"in Fe,p.ajax=Fe=!!Fe,w.ajaxTransport(function(e){var n,i;if(p.cors||Fe&&!e.crossDomain)return{send:function(o,r){var s,a=e.xhr();if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(s in e.xhrFields)a[s]=e.xhrFields[s];for(s in e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),e.crossDomain||o["X-Requested-With"]||(o["X-Requested-With"]="XMLHttpRequest"),o)a.setRequestHeader(s,o[s]);n=function(t){return function(){n&&(n=i=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===t?a.abort():"error"===t?"number"!=typeof a.status?r(0,"error"):r(a.status,a.statusText):r(Ue[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=n(),i=a.onerror=a.ontimeout=n("error"),void 0!==a.onabort?a.onabort=i:a.onreadystatechange=function(){4===a.readyState&&t.setTimeout(function(){n&&i()})},n=n("abort");try{a.send(e.hasContent&&e.data||null)}catch(o){if(n)throw o}},abort:function(){n&&n()}}}),w.ajaxPrefilter(function(t){t.crossDomain&&(t.contents.script=!1)}),w.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return w.globalEval(t),t}}}),w.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")}),w.ajaxTransport("script",function(t){var e,n;if(t.crossDomain||t.scriptAttrs)return{send:function(i,o){e=w("<script>").attr(t.scriptAttrs||{}).prop({charset:t.scriptCharset,src:t.url}).on("load error",n=function(t){e.remove(),n=null,t&&o("error"===t.type?404:200,t.type)}),m.head.appendChild(e[0])},abort:function(){n&&n()}}});var ze,Ve=[],Xe=/(=)\?(?=&|$)|\?\?/;w.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=Ve.pop()||w.expando+"_"+Ce.guid++;return this[t]=!0,t}}),w.ajaxPrefilter("json jsonp",function(e,n,i){var o,r,s,a=!1!==e.jsonp&&(Xe.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Xe.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return o=e.jsonpCallback=h(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Xe,"$1"+o):!1!==e.jsonp&&(e.url+=(Ee.test(e.url)?"&":"?")+e.jsonp+"="+o),e.converters["script json"]=function(){return s||w.error(o+" was not called"),s[0]},e.dataTypes[0]="json",r=t[o],t[o]=function(){s=arguments},i.always(function(){void 0===r?w(t).removeProp(o):t[o]=r,e[o]&&(e.jsonpCallback=n.jsonpCallback,Ve.push(o)),s&&h(r)&&r(s[0]),s=r=void 0}),"script"}),p.createHTMLDocument=((ze=m.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===ze.childNodes.length),w.parseHTML=function(t,e,n){return"string"!=typeof t?[]:("boolean"==typeof e&&(n=e,e=!1),e||(p.createHTMLDocument?((i=(e=m.implementation.createHTMLDocument("")).createElement("base")).href=m.location.href,e.head.appendChild(i)):e=m),r=!n&&[],(o=$.exec(t))?[e.createElement(o[1])]:(o=bt([t],e,r),r&&r.length&&w(r).remove(),w.merge([],o.childNodes)));var i,o,r},w.fn.load=function(t,e,n){var i,o,r,s=this,a=t.indexOf(" ");return-1<a&&(i=me(t.slice(a)),t=t.slice(0,a)),h(e)?(n=e,e=void 0):e&&"object"==typeof e&&(o="POST"),0<s.length&&w.ajax({url:t,type:o||"GET",dataType:"html",data:e}).done(function(t){r=arguments,s.html(i?w("<div>").append(w.parseHTML(t)).find(i):t)}).always(n&&function(t,e){s.each(function(){n.apply(this,r||[t.responseText,e,t])})}),this},w.expr.pseudos.animated=function(t){return w.grep(w.timers,function(e){return t===e.elem}).length},w.offset={setOffset:function(t,e,n){var i,o,r,s,a,l,c=w.css(t,"position"),u=w(t),d={};"static"===c&&(t.style.position="relative"),a=u.offset(),r=w.css(t,"top"),l=w.css(t,"left"),("absolute"===c||"fixed"===c)&&-1<(r+l).indexOf("auto")?(s=(i=u.position()).top,o=i.left):(s=parseFloat(r)||0,o=parseFloat(l)||0),h(e)&&(e=e.call(t,n,w.extend({},a))),null!=e.top&&(d.top=e.top-a.top+s),null!=e.left&&(d.left=e.left-a.left+o),"using"in e?e.using.call(t,d):("number"==typeof d.top&&(d.top+="px"),"number"==typeof d.left&&(d.left+="px"),u.css(d))}},w.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){w.offset.setOffset(this,t,e)});var e,n,i=this[0];return i?i.getClientRects().length?(e=i.getBoundingClientRect(),n=i.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,n,i=this[0],o={top:0,left:0};if("fixed"===w.css(i,"position"))e=i.getBoundingClientRect();else{for(e=this.offset(),n=i.ownerDocument,t=i.offsetParent||n.documentElement;t&&(t===n.body||t===n.documentElement)&&"static"===w.css(t,"position");)t=t.parentNode;t&&t!==i&&1===t.nodeType&&((o=w(t).offset()).top+=w.css(t,"borderTopWidth",!0),o.left+=w.css(t,"borderLeftWidth",!0))}return{top:e.top-o.top-w.css(i,"marginTop",!0),left:e.left-o.left-w.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&"static"===w.css(t,"position");)t=t.offsetParent;return t||it})}}),w.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,e){var n="pageYOffset"===e;w.fn[t]=function(i){return W(this,function(t,i,o){var r;if(g(t)?r=t:9===t.nodeType&&(r=t.defaultView),void 0===o)return r?r[e]:t[i];r?r.scrollTo(n?r.pageXOffset:o,n?o:r.pageYOffset):t[i]=o},t,i,arguments.length)}}),w.each(["top","left"],function(t,e){w.cssHooks[e]=Wt(p.pixelPosition,function(t,n){if(n)return n=_t(t,e),Pt.test(n)?w(t).position()[e]+"px":n})}),w.each({Height:"height",Width:"width"},function(t,e){w.each({padding:"inner"+t,content:e,"":"outer"+t},function(n,i){w.fn[i]=function(o,r){var s=arguments.length&&(n||"boolean"!=typeof o),a=n||(!0===o||!0===r?"margin":"border");return W(this,function(e,n,o){var r;return g(e)?0===i.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+t],r["scroll"+t],e.body["offset"+t],r["offset"+t],r["client"+t])):void 0===o?w.css(e,n,a):w.style(e,n,o,a)},e,s?o:void 0,s)}})}),w.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){w.fn[e]=function(t){return this.on(e,t)}}),w.fn.extend({bind:function(t,e,n){return this.on(t,null,e,n)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,n,i){return this.on(e,t,n,i)},undelegate:function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)},hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)}}),w.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(t,e){w.fn[e]=function(t,n){return 0<arguments.length?this.on(e,null,t,n):this.trigger(e)}});var Qe=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;w.proxy=function(t,e){var n,i,r;if("string"==typeof e&&(n=t[e],e=t,t=n),h(t))return i=o.call(arguments,2),(r=function(){return t.apply(e||this,i.concat(o.call(arguments)))}).guid=t.guid=t.guid||w.guid++,r},w.holdReady=function(t){t?w.readyWait++:w.ready(!0)},w.isArray=Array.isArray,w.parseJSON=JSON.parse,w.nodeName=A,w.isFunction=h,w.isWindow=g,w.camelCase=V,w.type=b,w.now=Date.now,w.isNumeric=function(t){var e=w.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},w.trim=function(t){return null==t?"":(t+"").replace(Qe,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return w});var Ye=t.jQuery,Ge=t.$;return w.noConflict=function(e){return t.$===w&&(t.$=Ge),e&&t.jQuery===w&&(t.jQuery=Ye),w},void 0===e&&(t.jQuery=t.$=w),w}),"function"!=typeof Object.create&&(Object.create=function(t){function e(){}return e.prototype=t,new e}),function(t,e,n,i){"use strict";var o={_positionClasses:["bottom-left","bottom-right","top-right","top-left","bottom-center","top-center","mid-center"],_defaultIcons:["success","error","info","warning"],init:function(e,n){this.prepareOptions(e,t.toast.options),this.process()},prepareOptions:function(e,n){var i={};"string"==typeof e||e instanceof Array?i.text=e:i=e,this.options=t.extend({},n,i)},process:function(){this.setup(),this.addToDom(),this.position(),this.bindToast(),this.animate()},setup:function(){var e="";if(this._toastEl=this._toastEl||t("<div></div>",{class:"jq-toast-single"}),e+='<span class="jq-toast-loader"></span>',this.options.allowToastClose&&(e+='<span class="close-jq-toast-single">&times;</span>'),this.options.text instanceof Array){this.options.heading&&(e+='<h2 class="jq-toast-heading">'+this.options.heading+"</h2>"),e+='<ul class="jq-toast-ul">';for(var n=0;n<this.options.text.length;n++)e+='<li class="jq-toast-li" id="jq-toast-item-'+n+'">'+this.options.text[n]+"</li>";e+="</ul>"}else this.options.heading&&(e+='<h2 class="jq-toast-heading">'+this.options.heading+"</h2>"),e+=this.options.text;this._toastEl.html(e),!1!==this.options.bgColor&&this._toastEl.css("background-color",this.options.bgColor),!1!==this.options.textColor&&this._toastEl.css("color",this.options.textColor),this.options.textAlign&&this._toastEl.css("text-align",this.options.textAlign),!1!==this.options.icon&&(this._toastEl.addClass("jq-has-icon"),-1!==t.inArray(this.options.icon,this._defaultIcons)&&this._toastEl.addClass("jq-icon-"+this.options.icon)),!1!==this.options.class&&this._toastEl.addClass(this.options.class)},position:function(){"string"==typeof this.options.position&&-1!==t.inArray(this.options.position,this._positionClasses)?"bottom-center"===this.options.position?this._container.css({left:t(e).outerWidth()/2-this._container.outerWidth()/2,bottom:20}):"top-center"===this.options.position?this._container.css({left:t(e).outerWidth()/2-this._container.outerWidth()/2,top:20}):"mid-center"===this.options.position?this._container.css({left:t(e).outerWidth()/2-this._container.outerWidth()/2,top:t(e).outerHeight()/2-this._container.outerHeight()/2}):this._container.addClass(this.options.position):"object"==typeof this.options.position?this._container.css({top:this.options.position.top?this.options.position.top:"auto",bottom:this.options.position.bottom?this.options.position.bottom:"auto",left:this.options.position.left?this.options.position.left:"auto",right:this.options.position.right?this.options.position.right:"auto"}):this._container.addClass("bottom-left")},bindToast:function(){var t=this;this._toastEl.on("afterShown",function(){t.processLoader()}),this._toastEl.find(".close-jq-toast-single").on("click",function(e){e.preventDefault(),"fade"===t.options.showHideTransition?(t._toastEl.trigger("beforeHide"),t._toastEl.fadeOut(function(){t._toastEl.trigger("afterHidden")})):"slide"===t.options.showHideTransition?(t._toastEl.trigger("beforeHide"),t._toastEl.slideUp(function(){t._toastEl.trigger("afterHidden")})):(t._toastEl.trigger("beforeHide"),t._toastEl.hide(function(){t._toastEl.trigger("afterHidden")}))}),"function"==typeof this.options.beforeShow&&this._toastEl.on("beforeShow",function(){t.options.beforeShow()}),"function"==typeof this.options.afterShown&&this._toastEl.on("afterShown",function(){t.options.afterShown()}),"function"==typeof this.options.beforeHide&&this._toastEl.on("beforeHide",function(){t.options.beforeHide()}),"function"==typeof this.options.afterHidden&&this._toastEl.on("afterHidden",function(){t.options.afterHidden()})},addToDom:function(){var e=t(".jq-toast-wrap");if(0===e.length?(e=t("<div></div>",{class:"jq-toast-wrap"}),t("body").append(e)):(!this.options.stack||isNaN(parseInt(this.options.stack,10)))&&e.empty(),e.find(".jq-toast-single:hidden").remove(),e.append(this._toastEl),this.options.stack&&!isNaN(parseInt(this.options.stack),10)){var n=e.find(".jq-toast-single").length-this.options.stack;n>0&&t(".jq-toast-wrap").find(".jq-toast-single").slice(0,n).remove()}this._container=e},canAutoHide:function(){return!1!==this.options.hideAfter&&!isNaN(parseInt(this.options.hideAfter,10))},processLoader:function(){if(!this.canAutoHide()||!1===this.options.loader)return!1;var t=this._toastEl.find(".jq-toast-loader"),e=(this.options.hideAfter-400)/1e3+"s",n=this.options.loaderBg,i=t.attr("style")||"";i=i.substring(0,i.indexOf("-webkit-transition")),i+="-webkit-transition: width "+e+" ease-in;                       -o-transition: width "+e+" ease-in;                       transition: width "+e+" ease-in;                       background-color: "+n+";",t.attr("style",i).addClass("jq-toast-loaded")},animate:function(){var t=this;if(this._toastEl.hide(),this._toastEl.trigger("beforeShow"),"fade"===this.options.showHideTransition.toLowerCase()?this._toastEl.fadeIn(function(){t._toastEl.trigger("afterShown")}):"slide"===this.options.showHideTransition.toLowerCase()?this._toastEl.slideDown(function(){t._toastEl.trigger("afterShown")}):this._toastEl.show(function(){t._toastEl.trigger("afterShown")}),this.canAutoHide()){t=this;e.setTimeout(function(){"fade"===t.options.showHideTransition.toLowerCase()?(t._toastEl.trigger("beforeHide"),t._toastEl.fadeOut(function(){t._toastEl.trigger("afterHidden")})):"slide"===t.options.showHideTransition.toLowerCase()?(t._toastEl.trigger("beforeHide"),t._toastEl.slideUp(function(){t._toastEl.trigger("afterHidden")})):(t._toastEl.trigger("beforeHide"),t._toastEl.hide(function(){t._toastEl.trigger("afterHidden")}))},this.options.hideAfter)}},reset:function(e){"all"===e?t(".jq-toast-wrap").remove():this._toastEl.remove()},update:function(t){this.prepareOptions(t,this.options),this.setup(),this.bindToast()}};t.toast=function(t){var e=Object.create(o);return e.init(t,this),{reset:function(t){e.reset(t)},update:function(t){e.update(t)}}},t.toast.options={text:"",heading:"",showHideTransition:"fade",allowToastClose:!0,hideAfter:3e3,loader:!0,loaderBg:"#9EC600",stack:5,position:"bottom-left",bgColor:!1,textColor:!1,textAlign:"left",icon:!1,beforeShow:function(){},afterShown:function(){},beforeHide:function(){},afterHidden:function(){}}}(jQuery,window,document),"undefined"==typeof jQuery)throw new Error("Bootstrap's JavaScript requires jQuery");!function(t){"use strict";t.fn.emulateTransitionEnd=function(e){var n=!1,i=this;t(this).one("bsTransitionEnd",function(){n=!0});return setTimeout(function(){n||t(i).trigger(t.support.transition.end)},e),this},t(function(){t.support.transition=function(){var t=document.createElement("bootstrap"),e={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var n in e)if(void 0!==t.style[n])return{end:e[n]};return!1}(),t.support.transition&&(t.event.special.bsTransitionEnd={bindType:t.support.transition.end,delegateType:t.support.transition.end,handle:function(e){return t(e.target).is(this)?e.handleObj.handler.apply(this,arguments):void 0}})})}(jQuery),function(t){"use strict";var e='[data-dismiss="alert"]',n=function(n){t(n).on("click",e,this.close)};n.VERSION="3.2.0",n.prototype.close=function(e){function n(){r.detach().trigger("closed.bs.alert").remove()}var i=t(this),o=i.attr("data-target");o||(o=(o=i.attr("href"))&&o.replace(/.*(?=#[^\s]*$)/,""));var r=t(o);e&&e.preventDefault(),r.length||(r=i.hasClass("alert")?i:i.parent()),r.trigger(e=t.Event("close.bs.alert")),e.isDefaultPrevented()||(r.removeClass("in"),t.support.transition&&r.hasClass("fade")?r.one("bsTransitionEnd",n).emulateTransitionEnd(150):n())};var i=t.fn.alert;t.fn.alert=function(e){return this.each(function(){var i=t(this),o=i.data("bs.alert");o||i.data("bs.alert",o=new n(this)),"string"==typeof e&&o[e].call(i)})},t.fn.alert.Constructor=n,t.fn.alert.noConflict=function(){return t.fn.alert=i,this},t(document).on("click.bs.alert.data-api",e,n.prototype.close)}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),o=i.data("bs.button"),r="object"==typeof e&&e;o||i.data("bs.button",o=new n(this,r)),"toggle"==e?o.toggle():e&&o.setState(e)})}var n=function(e,i){this.$element=t(e),this.options=t.extend({},n.DEFAULTS,i),this.isLoading=!1};n.VERSION="3.2.0",n.DEFAULTS={loadingText:"loading..."},n.prototype.setState=function(e){var n="disabled",i=this.$element,o=i.is("input")?"val":"html",r=i.data();e+="Text",null==r.resetText&&i.data("resetText",i[o]()),i[o](null==r[e]?this.options[e]:r[e]),setTimeout(t.proxy(function(){"loadingText"==e?(this.isLoading=!0,i.addClass(n).attr(n,n)):this.isLoading&&(this.isLoading=!1,i.removeClass(n).removeAttr(n))},this),0)},n.prototype.toggle=function(){var t=!0,e=this.$element.closest('[data-toggle="buttons"]');if(e.length){var n=this.$element.find("input");"radio"==n.prop("type")&&(n.prop("checked")&&this.$element.hasClass("active")?t=!1:e.find(".active").removeClass("active")),t&&n.prop("checked",!this.$element.hasClass("active")).trigger("change")}t&&this.$element.toggleClass("active")};var i=t.fn.button;t.fn.button=e,t.fn.button.Constructor=n,t.fn.button.noConflict=function(){return t.fn.button=i,this},t(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(n){var i=t(n.target);i.hasClass("btn")||(i=i.closest(".btn")),e.call(i,"toggle"),n.preventDefault()})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),o=i.data("bs.carousel"),r=t.extend({},n.DEFAULTS,i.data(),"object"==typeof e&&e),s="string"==typeof e?e:r.slide;o||i.data("bs.carousel",o=new n(this,r)),"number"==typeof e?o.to(e):s?o[s]():r.interval&&o.pause().cycle()})}var n=function(e,n){this.$element=t(e).on("keydown.bs.carousel",t.proxy(this.keydown,this)),this.$indicators=this.$element.find(".carousel-indicators"),this.options=n,this.paused=this.sliding=this.interval=this.$active=this.$items=null,"hover"==this.options.pause&&this.$element.on("mouseenter.bs.carousel",t.proxy(this.pause,this)).on("mouseleave.bs.carousel",t.proxy(this.cycle,this))};n.VERSION="3.2.0",n.DEFAULTS={interval:5e3,pause:"hover",wrap:!0},n.prototype.keydown=function(t){switch(t.which){case 37:this.prev();break;case 39:this.next();break;default:return}t.preventDefault()},n.prototype.cycle=function(e){return e||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(t.proxy(this.next,this),this.options.interval)),this},n.prototype.getItemIndex=function(t){return this.$items=t.parent().children(".item"),this.$items.index(t||this.$active)},n.prototype.to=function(e){var n=this,i=this.getItemIndex(this.$active=this.$element.find(".item.active"));return e>this.$items.length-1||0>e?void 0:this.sliding?this.$element.one("slid.bs.carousel",function(){n.to(e)}):i==e?this.pause().cycle():this.slide(e>i?"next":"prev",t(this.$items[e]))},n.prototype.pause=function(e){return e||(this.paused=!0),this.$element.find(".next, .prev").length&&t.support.transition&&(this.$element.trigger(t.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this},n.prototype.next=function(){return this.sliding?void 0:this.slide("next")},n.prototype.prev=function(){return this.sliding?void 0:this.slide("prev")},n.prototype.slide=function(e,n){var i=this.$element.find(".item.active"),o=n||i[e](),r=this.interval,s="next"==e?"left":"right",a="next"==e?"first":"last",l=this;if(!o.length){if(!this.options.wrap)return;o=this.$element.find(".item")[a]()}if(o.hasClass("active"))return this.sliding=!1;var c=o[0],u=t.Event("slide.bs.carousel",{relatedTarget:c,direction:s});if(this.$element.trigger(u),!u.isDefaultPrevented()){if(this.sliding=!0,r&&this.pause(),this.$indicators.length){this.$indicators.find(".active").removeClass("active");var d=t(this.$indicators.children()[this.getItemIndex(o)]);d&&d.addClass("active")}var f=t.Event("slid.bs.carousel",{relatedTarget:c,direction:s});return t.support.transition&&this.$element.hasClass("slide")?(o.addClass(e),o[0].offsetWidth,i.addClass(s),o.addClass(s),i.one("bsTransitionEnd",function(){o.removeClass([e,s].join(" ")).addClass("active"),i.removeClass(["active",s].join(" ")),l.sliding=!1,setTimeout(function(){l.$element.trigger(f)},0)}).emulateTransitionEnd(1e3*i.css("transition-duration").slice(0,-1))):(i.removeClass("active"),o.addClass("active"),this.sliding=!1,this.$element.trigger(f)),r&&this.cycle(),this}};var i=t.fn.carousel;t.fn.carousel=e,t.fn.carousel.Constructor=n,t.fn.carousel.noConflict=function(){return t.fn.carousel=i,this},t(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",function(n){var i,o=t(this),r=t(o.attr("data-target")||(i=o.attr("href"))&&i.replace(/.*(?=#[^\s]+$)/,""));if(r.hasClass("carousel")){var s=t.extend({},r.data(),o.data()),a=o.attr("data-slide-to");a&&(s.interval=!1),e.call(r,s),a&&r.data("bs.carousel").to(a),n.preventDefault()}}),t(window).on("load",function(){t('[data-ride="carousel"]').each(function(){var n=t(this);e.call(n,n.data())})})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),o=i.data("bs.collapse"),r=t.extend({},n.DEFAULTS,i.data(),"object"==typeof e&&e);!o&&r.toggle&&"show"==e&&(e=!e),o||i.data("bs.collapse",o=new n(this,r)),"string"==typeof e&&o[e]()})}var n=function(e,i){this.$element=t(e),this.options=t.extend({},n.DEFAULTS,i),this.transitioning=null,this.options.parent&&(this.$parent=t(this.options.parent)),this.options.toggle&&this.toggle()};n.VERSION="3.2.0",n.DEFAULTS={toggle:!0},n.prototype.dimension=function(){return this.$element.hasClass("width")?"width":"height"},n.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var n=t.Event("show.bs.collapse");if(this.$element.trigger(n),!n.isDefaultPrevented()){var i=this.$parent&&this.$parent.find("> .panel > .in");if(i&&i.length){var o=i.data("bs.collapse");if(o&&o.transitioning)return;e.call(i,"hide"),o||i.data("bs.collapse",null)}var r=this.dimension();this.$element.removeClass("collapse").addClass("collapsing")[r](0),this.transitioning=1;var s=function(){this.$element.removeClass("collapsing").addClass("collapse in")[r](""),this.transitioning=0,this.$element.trigger("shown.bs.collapse")};if(!t.support.transition)return s.call(this);var a=t.camelCase(["scroll",r].join("-"));this.$element.one("bsTransitionEnd",t.proxy(s,this)).emulateTransitionEnd(350)[r](this.$element[0][a])}}},n.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var e=t.Event("hide.bs.collapse");if(this.$element.trigger(e),!e.isDefaultPrevented()){var n=this.dimension();this.$element[n](this.$element[n]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse").removeClass("in"),this.transitioning=1;var i=function(){this.transitioning=0,this.$element.trigger("hidden.bs.collapse").removeClass("collapsing").addClass("collapse")};return t.support.transition?void this.$element[n](0).one("bsTransitionEnd",t.proxy(i,this)).emulateTransitionEnd(350):i.call(this)}}},n.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()};var i=t.fn.collapse;t.fn.collapse=e,t.fn.collapse.Constructor=n,t.fn.collapse.noConflict=function(){return t.fn.collapse=i,this},t(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(n){var i,o=t(this),r=o.attr("data-target")||n.preventDefault()||(i=o.attr("href"))&&i.replace(/.*(?=#[^\s]+$)/,""),s=t(r),a=s.data("bs.collapse"),l=a?"toggle":o.data(),c=o.attr("data-parent"),u=c&&t(c);a&&a.transitioning||(u&&u.find('[data-toggle="collapse"][data-parent="'+c+'"]').not(o).addClass("collapsed"),o[s.hasClass("in")?"addClass":"removeClass"]("collapsed")),e.call(s,l)})}(jQuery),function(t){"use strict";function e(e){e&&3===e.which||(t(i).remove(),t(o).each(function(){var i=n(t(this)),o={relatedTarget:this};i.hasClass("open")&&(i.trigger(e=t.Event("hide.bs.dropdown",o)),e.isDefaultPrevented()||i.removeClass("open").trigger("hidden.bs.dropdown",o))}))}function n(e){var n=e.attr("data-target");n||(n=(n=e.attr("href"))&&/#[A-Za-z]/.test(n)&&n.replace(/.*(?=#[^\s]*$)/,""));var i=n&&t(n);return i&&i.length?i:e.parent()}var i=".dropdown-backdrop",o='[data-toggle="dropdown"]',r=function(e){t(e).on("click.bs.dropdown",this.toggle)};r.VERSION="3.2.0",r.prototype.toggle=function(i){var o=t(this);if(!o.is(".disabled, :disabled")){var r=n(o),s=r.hasClass("open");if(e(),!s){"ontouchstart"in document.documentElement&&!r.closest(".navbar-nav").length&&t('<div class="dropdown-backdrop"/>').insertAfter(t(this)).on("click",e);var a={relatedTarget:this};if(r.trigger(i=t.Event("show.bs.dropdown",a)),i.isDefaultPrevented())return;o.trigger("focus"),r.toggleClass("open").trigger("shown.bs.dropdown",a)}return!1}},r.prototype.keydown=function(e){if(/(38|40|27)/.test(e.keyCode)){var i=t(this);if(e.preventDefault(),e.stopPropagation(),!i.is(".disabled, :disabled")){var r=n(i),s=r.hasClass("open");if(!s||s&&27==e.keyCode)return 27==e.which&&r.find(o).trigger("focus"),i.trigger("click");var a=" li:not(.divider):visible a",l=r.find('[role="menu"]'+a+', [role="listbox"]'+a);if(l.length){var c=l.index(l.filter(":focus"));38==e.keyCode&&c>0&&c--,40==e.keyCode&&c<l.length-1&&c++,~c||(c=0),l.eq(c).trigger("focus")}}}};var s=t.fn.dropdown;t.fn.dropdown=function(e){return this.each(function(){var n=t(this),i=n.data("bs.dropdown");i||n.data("bs.dropdown",i=new r(this)),"string"==typeof e&&i[e].call(n)})},t.fn.dropdown.Constructor=r,t.fn.dropdown.noConflict=function(){return t.fn.dropdown=s,this},t(document).on("click.bs.dropdown.data-api",e).on("click.bs.dropdown.data-api",".dropdown form",function(t){t.stopPropagation()}).on("click.bs.dropdown.data-api",o,r.prototype.toggle).on("keydown.bs.dropdown.data-api",o+', [role="menu"], [role="listbox"]',r.prototype.keydown)}(jQuery),function(t){"use strict";function e(e,i){return this.each(function(){var o=t(this),r=o.data("bs.modal"),s=t.extend({},n.DEFAULTS,o.data(),"object"==typeof e&&e);r||o.data("bs.modal",r=new n(this,s)),"string"==typeof e?r[e](i):s.show&&r.show(i)})}var n=function(e,n){this.options=n,this.$body=t(document.body),this.$element=t(e),this.$backdrop=this.isShown=null,this.scrollbarWidth=0,this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,t.proxy(function(){this.$element.trigger("loaded.bs.modal")},this))};n.VERSION="3.2.0",n.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},n.prototype.toggle=function(t){return this.isShown?this.hide():this.show(t)},n.prototype.show=function(e){var n=this,i=t.Event("show.bs.modal",{relatedTarget:e});this.$element.trigger(i),this.isShown||i.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.$body.addClass("modal-open"),this.setScrollbar(),this.escape(),this.$element.on("click.dismiss.bs.modal",'[data-dismiss="modal"]',t.proxy(this.hide,this)),this.backdrop(function(){var i=t.support.transition&&n.$element.hasClass("fade");n.$element.parent().length||n.$element.appendTo(n.$body),n.$element.show().scrollTop(0),i&&n.$element[0].offsetWidth,n.$element.addClass("in").attr("aria-hidden",!1),n.enforceFocus();var o=t.Event("shown.bs.modal",{relatedTarget:e});i?n.$element.find(".modal-dialog").one("bsTransitionEnd",function(){n.$element.trigger("focus").trigger(o)}).emulateTransitionEnd(300):n.$element.trigger("focus").trigger(o)}))},n.prototype.hide=function(e){e&&e.preventDefault(),e=t.Event("hide.bs.modal"),this.$element.trigger(e),this.isShown&&!e.isDefaultPrevented()&&(this.isShown=!1,this.$body.removeClass("modal-open"),this.resetScrollbar(),this.escape(),t(document).off("focusin.bs.modal"),this.$element.removeClass("in").attr("aria-hidden",!0).off("click.dismiss.bs.modal"),t.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",t.proxy(this.hideModal,this)).emulateTransitionEnd(300):this.hideModal())},n.prototype.enforceFocus=function(){t(document).off("focusin.bs.modal").on("focusin.bs.modal",t.proxy(function(t){this.$element[0]===t.target||this.$element.has(t.target).length||this.$element.trigger("focus")},this))},n.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keyup.dismiss.bs.modal",t.proxy(function(t){27==t.which&&this.hide()},this)):this.isShown||this.$element.off("keyup.dismiss.bs.modal")},n.prototype.hideModal=function(){var t=this;this.$element.hide(),this.backdrop(function(){t.$element.trigger("hidden.bs.modal")})},n.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},n.prototype.backdrop=function(e){var n=this,i=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var o=t.support.transition&&i;if(this.$backdrop=t('<div class="modal-backdrop '+i+'" />').appendTo(this.$body),this.$element.on("click.dismiss.bs.modal",t.proxy(function(t){t.target===t.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus.call(this.$element[0]):this.hide.call(this))},this)),o&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!e)return;o?this.$backdrop.one("bsTransitionEnd",e).emulateTransitionEnd(150):e()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var r=function(){n.removeBackdrop(),e&&e()};t.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",r).emulateTransitionEnd(150):r()}else e&&e()},n.prototype.checkScrollbar=function(){document.body.clientWidth>=window.innerWidth||(this.scrollbarWidth=this.scrollbarWidth||this.measureScrollbar())},n.prototype.setScrollbar=function(){var t=parseInt(this.$body.css("padding-right")||0,10);this.scrollbarWidth&&this.$body.css("padding-right",t+this.scrollbarWidth)},n.prototype.resetScrollbar=function(){this.$body.css("padding-right","")},n.prototype.measureScrollbar=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",this.$body.append(t);var e=t.offsetWidth-t.clientWidth;return this.$body[0].removeChild(t),e};var i=t.fn.modal;t.fn.modal=e,t.fn.modal.Constructor=n,t.fn.modal.noConflict=function(){return t.fn.modal=i,this},t(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(n){var i=t(this),o=i.attr("href"),r=t(i.attr("data-target")||o&&o.replace(/.*(?=#[^\s]+$)/,"")),s=r.data("bs.modal")?"toggle":t.extend({remote:!/#/.test(o)&&o},r.data(),i.data());i.is("a")&&n.preventDefault(),r.one("show.bs.modal",function(t){t.isDefaultPrevented()||r.one("hidden.bs.modal",function(){i.is(":visible")&&i.trigger("focus")})}),e.call(r,s,this)})}(jQuery),function(t){"use strict";var e=function(t,e){this.type=this.options=this.enabled=this.timeout=this.hoverState=this.$element=null,this.init("tooltip",t,e)};e.VERSION="3.2.0",e.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0}},e.prototype.init=function(e,n,i){this.enabled=!0,this.type=e,this.$element=t(n),this.options=this.getOptions(i),this.$viewport=this.options.viewport&&t(this.options.viewport.selector||this.options.viewport);for(var o=this.options.trigger.split(" "),r=o.length;r--;){var s=o[r];if("click"==s)this.$element.on("click."+this.type,this.options.selector,t.proxy(this.toggle,this));else if("manual"!=s){var a="hover"==s?"mouseenter":"focusin",l="hover"==s?"mouseleave":"focusout";this.$element.on(a+"."+this.type,this.options.selector,t.proxy(this.enter,this)),this.$element.on(l+"."+this.type,this.options.selector,t.proxy(this.leave,this))}}this.options.selector?this._options=t.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},e.prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.getOptions=function(e){return(e=t.extend({},this.getDefaults(),this.$element.data(),e)).delay&&"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),e},e.prototype.getDelegateOptions=function(){var e={},n=this.getDefaults();return this._options&&t.each(this._options,function(t,i){n[t]!=i&&(e[t]=i)}),e},e.prototype.enter=function(e){var n=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);return n||(n=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,n)),clearTimeout(n.timeout),n.hoverState="in",n.options.delay&&n.options.delay.show?void(n.timeout=setTimeout(function(){"in"==n.hoverState&&n.show()},n.options.delay.show)):n.show()},e.prototype.leave=function(e){var n=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);return n||(n=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,n)),clearTimeout(n.timeout),n.hoverState="out",n.options.delay&&n.options.delay.hide?void(n.timeout=setTimeout(function(){"out"==n.hoverState&&n.hide()},n.options.delay.hide)):n.hide()},e.prototype.show=function(){var e=t.Event("show.bs."+this.type);if(this.hasContent()&&this.enabled){this.$element.trigger(e);var n=t.contains(document.documentElement,this.$element[0]);if(e.isDefaultPrevented()||!n)return;var i=this,o=this.tip(),r=this.getUID(this.type);this.setContent(),o.attr("id",r),this.$element.attr("aria-describedby",r),this.options.animation&&o.addClass("fade");var s="function"==typeof this.options.placement?this.options.placement.call(this,o[0],this.$element[0]):this.options.placement,a=/\s?auto?\s?/i,l=a.test(s);l&&(s=s.replace(a,"")||"top"),o.detach().css({top:0,left:0,display:"block"}).addClass(s).data("bs."+this.type,this),this.options.container?o.appendTo(this.options.container):o.insertAfter(this.$element);var c=this.getPosition(),u=o[0].offsetWidth,d=o[0].offsetHeight;if(l){var f=s,p=this.$element.parent(),h=this.getPosition(p);s="bottom"==s&&c.top+c.height+d-h.scroll>h.height?"top":"top"==s&&c.top-h.scroll-d<0?"bottom":"right"==s&&c.right+u>h.width?"left":"left"==s&&c.left-u<h.left?"right":s,o.removeClass(f).addClass(s)}var g=this.getCalculatedOffset(s,c,u,d);this.applyPlacement(g,s);var m=function(){i.$element.trigger("shown.bs."+i.type),i.hoverState=null};t.support.transition&&this.$tip.hasClass("fade")?o.one("bsTransitionEnd",m).emulateTransitionEnd(150):m()}},e.prototype.applyPlacement=function(e,n){var i=this.tip(),o=i[0].offsetWidth,r=i[0].offsetHeight,s=parseInt(i.css("margin-top"),10),a=parseInt(i.css("margin-left"),10);isNaN(s)&&(s=0),isNaN(a)&&(a=0),e.top=e.top+s,e.left=e.left+a,t.offset.setOffset(i[0],t.extend({using:function(t){i.css({top:Math.round(t.top),left:Math.round(t.left)})}},e),0),i.addClass("in");var l=i[0].offsetWidth,c=i[0].offsetHeight;"top"==n&&c!=r&&(e.top=e.top+r-c);var u=this.getViewportAdjustedDelta(n,e,l,c);u.left?e.left+=u.left:e.top+=u.top;var d=u.left?2*u.left-o+l:2*u.top-r+c,f=u.left?"left":"top",p=u.left?"offsetWidth":"offsetHeight";i.offset(e),this.replaceArrow(d,i[0][p],f)},e.prototype.replaceArrow=function(t,e,n){this.arrow().css(n,t?50*(1-t/e)+"%":"")},e.prototype.setContent=function(){var t=this.tip(),e=this.getTitle();t.find(".tooltip-inner")[this.options.html?"html":"text"](e),t.removeClass("fade in top bottom left right")},e.prototype.hide=function(){function e(){"in"!=n.hoverState&&i.detach(),n.$element.trigger("hidden.bs."+n.type)}var n=this,i=this.tip(),o=t.Event("hide.bs."+this.type);return this.$element.removeAttr("aria-describedby"),this.$element.trigger(o),o.isDefaultPrevented()?void 0:(i.removeClass("in"),t.support.transition&&this.$tip.hasClass("fade")?i.one("bsTransitionEnd",e).emulateTransitionEnd(150):e(),this.hoverState=null,this)},e.prototype.fixTitle=function(){var t=this.$element;(t.attr("title")||"string"!=typeof t.attr("data-original-title"))&&t.attr("data-original-title",t.attr("title")||"").attr("title","")},e.prototype.hasContent=function(){return this.getTitle()},e.prototype.getPosition=function(e){var n=(e=e||this.$element)[0],i="BODY"==n.tagName;return t.extend({},"function"==typeof n.getBoundingClientRect?n.getBoundingClientRect():null,{scroll:i?document.documentElement.scrollTop||document.body.scrollTop:e.scrollTop(),width:i?t(window).width():e.outerWidth(),height:i?t(window).height():e.outerHeight()},i?{top:0,left:0}:e.offset())},e.prototype.getCalculatedOffset=function(t,e,n,i){return"bottom"==t?{top:e.top+e.height,left:e.left+e.width/2-n/2}:"top"==t?{top:e.top-i,left:e.left+e.width/2-n/2}:"left"==t?{top:e.top+e.height/2-i/2,left:e.left-n}:{top:e.top+e.height/2-i/2,left:e.left+e.width}},e.prototype.getViewportAdjustedDelta=function(t,e,n,i){var o={top:0,left:0};if(!this.$viewport)return o;var r=this.options.viewport&&this.options.viewport.padding||0,s=this.getPosition(this.$viewport);if(/right|left/.test(t)){var a=e.top-r-s.scroll,l=e.top+r-s.scroll+i;a<s.top?o.top=s.top-a:l>s.top+s.height&&(o.top=s.top+s.height-l)}else{var c=e.left-r,u=e.left+r+n;c<s.left?o.left=s.left-c:u>s.width&&(o.left=s.left+s.width-u)}return o},e.prototype.getTitle=function(){var t=this.$element,e=this.options;return t.attr("data-original-title")||("function"==typeof e.title?e.title.call(t[0]):e.title)},e.prototype.getUID=function(t){do{t+=~~(1e6*Math.random())}while(document.getElementById(t));return t},e.prototype.tip=function(){return this.$tip=this.$tip||t(this.options.template)},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},e.prototype.validate=function(){this.$element[0].parentNode||(this.hide(),this.$element=null,this.options=null)},e.prototype.enable=function(){this.enabled=!0},e.prototype.disable=function(){this.enabled=!1},e.prototype.toggleEnabled=function(){this.enabled=!this.enabled},e.prototype.toggle=function(e){var n=this;e&&((n=t(e.currentTarget).data("bs."+this.type))||(n=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,n))),n.tip().hasClass("in")?n.leave(n):n.enter(n)},e.prototype.destroy=function(){clearTimeout(this.timeout),this.hide().$element.off("."+this.type).removeData("bs."+this.type)};var n=t.fn.tooltip;t.fn.tooltip=function(n){return this.each(function(){var i=t(this),o=i.data("bs.tooltip"),r="object"==typeof n&&n;(o||"destroy"!=n)&&(o||i.data("bs.tooltip",o=new e(this,r)),"string"==typeof n&&o[n]())})},t.fn.tooltip.Constructor=e,t.fn.tooltip.noConflict=function(){return t.fn.tooltip=n,this}}(jQuery),function(t){"use strict";var e=function(t,e){this.init("popover",t,e)};if(!t.fn.tooltip)throw new Error("Popover requires tooltip.js");e.VERSION="3.2.0",e.DEFAULTS=t.extend({},t.fn.tooltip.Constructor.DEFAULTS,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),e.prototype=t.extend({},t.fn.tooltip.Constructor.prototype),e.prototype.constructor=e,e.prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.setContent=function(){var t=this.tip(),e=this.getTitle(),n=this.getContent();t.find(".popover-title")[this.options.html?"html":"text"](e),t.find(".popover-content").empty()[this.options.html?"string"==typeof n?"html":"append":"text"](n),t.removeClass("fade top bottom left right in"),t.find(".popover-title").html()||t.find(".popover-title").hide()},e.prototype.hasContent=function(){return this.getTitle()||this.getContent()},e.prototype.getContent=function(){var t=this.$element,e=this.options;return t.attr("data-content")||("function"==typeof e.content?e.content.call(t[0]):e.content)},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".arrow")},e.prototype.tip=function(){return this.$tip||(this.$tip=t(this.options.template)),this.$tip};var n=t.fn.popover;t.fn.popover=function(n){return this.each(function(){var i=t(this),o=i.data("bs.popover"),r="object"==typeof n&&n;(o||"destroy"!=n)&&(o||i.data("bs.popover",o=new e(this,r)),"string"==typeof n&&o[n]())})},t.fn.popover.Constructor=e,t.fn.popover.noConflict=function(){return t.fn.popover=n,this}}(jQuery),function(t){"use strict";function e(n,i){var o=t.proxy(this.process,this);this.$body=t("body"),this.$scrollElement=t(t(n).is("body")?window:n),this.options=t.extend({},e.DEFAULTS,i),this.selector=(this.options.target||"")+" .nav li > a",this.offsets=[],this.targets=[],this.activeTarget=null,this.scrollHeight=0,this.$scrollElement.on("scroll.bs.scrollspy",o),this.refresh(),this.process()}function n(n){return this.each(function(){var i=t(this),o=i.data("bs.scrollspy"),r="object"==typeof n&&n;o||i.data("bs.scrollspy",o=new e(this,r)),"string"==typeof n&&o[n]()})}e.VERSION="3.2.0",e.DEFAULTS={offset:10},e.prototype.getScrollHeight=function(){return this.$scrollElement[0].scrollHeight||Math.max(this.$body[0].scrollHeight,document.documentElement.scrollHeight)},e.prototype.refresh=function(){var e="offset",n=0;t.isWindow(this.$scrollElement[0])||(e="position",n=this.$scrollElement.scrollTop()),this.offsets=[],this.targets=[],this.scrollHeight=this.getScrollHeight();var i=this;this.$body.find(this.selector).map(function(){var i=t(this),o=i.data("target")||i.attr("href"),r=/^#./.test(o)&&t(o);return r&&r.length&&r.is(":visible")&&[[r[e]().top+n,o]]||null}).sort(function(t,e){return t[0]-e[0]}).each(function(){i.offsets.push(this[0]),i.targets.push(this[1])})},e.prototype.process=function(){var t,e=this.$scrollElement.scrollTop()+this.options.offset,n=this.getScrollHeight(),i=this.options.offset+n-this.$scrollElement.height(),o=this.offsets,r=this.targets,s=this.activeTarget;if(this.scrollHeight!=n&&this.refresh(),e>=i)return s!=(t=r[r.length-1])&&this.activate(t);if(s&&e<=o[0])return s!=(t=r[0])&&this.activate(t);for(t=o.length;t--;)s!=r[t]&&e>=o[t]&&(!o[t+1]||e<=o[t+1])&&this.activate(r[t])},e.prototype.activate=function(e){this.activeTarget=e,t(this.selector).parentsUntil(this.options.target,".active").removeClass("active");var n=this.selector+'[data-target="'+e+'"],'+this.selector+'[href="'+e+'"]',i=t(n).parents("li").addClass("active");i.parent(".dropdown-menu").length&&(i=i.closest("li.dropdown").addClass("active")),i.trigger("activate.bs.scrollspy")};var i=t.fn.scrollspy;t.fn.scrollspy=n,t.fn.scrollspy.Constructor=e,t.fn.scrollspy.noConflict=function(){return t.fn.scrollspy=i,this},t(window).on("load.bs.scrollspy.data-api",function(){t('[data-spy="scroll"]').each(function(){var e=t(this);n.call(e,e.data())})})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),o=i.data("bs.tab");o||i.data("bs.tab",o=new n(this)),"string"==typeof e&&o[e]()})}var n=function(e){this.element=t(e)};n.VERSION="3.2.0",n.prototype.show=function(){var e=this.element,n=e.closest("ul:not(.dropdown-menu)"),i=e.data("target");if(i||(i=(i=e.attr("href"))&&i.replace(/.*(?=#[^\s]*$)/,"")),!e.parent("li").hasClass("active")){var o=n.find(".active:last a")[0],r=t.Event("show.bs.tab",{relatedTarget:o});if(e.trigger(r),!r.isDefaultPrevented()){var s=t(i);this.activate(e.closest("li"),n),this.activate(s,s.parent(),function(){e.trigger({type:"shown.bs.tab",relatedTarget:o})})}}},n.prototype.activate=function(e,n,i){function o(){r.removeClass("active").find("> .dropdown-menu > .active").removeClass("active"),e.addClass("active"),s?(e[0].offsetWidth,e.addClass("in")):e.removeClass("fade"),e.parent(".dropdown-menu")&&e.closest("li.dropdown").addClass("active"),i&&i()}var r=n.find("> .active"),s=i&&t.support.transition&&r.hasClass("fade");s?r.one("bsTransitionEnd",o).emulateTransitionEnd(150):o(),r.removeClass("in")};var i=t.fn.tab;t.fn.tab=e,t.fn.tab.Constructor=n,t.fn.tab.noConflict=function(){return t.fn.tab=i,this},t(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"]',function(n){n.preventDefault(),e.call(t(this),"show")})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),o=i.data("bs.affix"),r="object"==typeof e&&e;o||i.data("bs.affix",o=new n(this,r)),"string"==typeof e&&o[e]()})}var n=function(e,i){this.options=t.extend({},n.DEFAULTS,i),this.$target=t(this.options.target).on("scroll.bs.affix.data-api",t.proxy(this.checkPosition,this)).on("click.bs.affix.data-api",t.proxy(this.checkPositionWithEventLoop,this)),this.$element=t(e),this.affixed=this.unpin=this.pinnedOffset=null,this.checkPosition()};n.VERSION="3.2.0",n.RESET="affix affix-top affix-bottom",n.DEFAULTS={offset:0,target:window},n.prototype.getPinnedOffset=function(){if(this.pinnedOffset)return this.pinnedOffset;this.$element.removeClass(n.RESET).addClass("affix");var t=this.$target.scrollTop(),e=this.$element.offset();return this.pinnedOffset=e.top-t},n.prototype.checkPositionWithEventLoop=function(){setTimeout(t.proxy(this.checkPosition,this),1)},n.prototype.checkPosition=function(){if(this.$element.is(":visible")){var e=t(document).height(),i=this.$target.scrollTop(),o=this.$element.offset(),r=this.options.offset,s=r.top,a=r.bottom;"object"!=typeof r&&(a=s=r),"function"==typeof s&&(s=r.top(this.$element)),"function"==typeof a&&(a=r.bottom(this.$element));var l=!(null!=this.unpin&&i+this.unpin<=o.top)&&(null!=a&&o.top+this.$element.height()>=e-a?"bottom":null!=s&&s>=i&&"top");if(this.affixed!==l){null!=this.unpin&&this.$element.css("top","");var c="affix"+(l?"-"+l:""),u=t.Event(c+".bs.affix");this.$element.trigger(u),u.isDefaultPrevented()||(this.affixed=l,this.unpin="bottom"==l?this.getPinnedOffset():null,this.$element.removeClass(n.RESET).addClass(c).trigger(t.Event(c.replace("affix","affixed"))),"bottom"==l&&this.$element.offset({top:e-this.$element.height()-a}))}}};var i=t.fn.affix;t.fn.affix=e,t.fn.affix.Constructor=n,t.fn.affix.noConflict=function(){return t.fn.affix=i,this},t(window).on("load",function(){t('[data-spy="affix"]').each(function(){var n=t(this),i=n.data();i.offset=i.offset||{},i.offsetBottom&&(i.offset.bottom=i.offsetBottom),i.offsetTop&&(i.offset.top=i.offsetTop),e.call(n,i)})})}(jQuery);const Common={redirectToLoginPage:function(){window.location.replace("index.html")},trimEnd:function(t,e){for(;t.charAt(t.length-1)===e;)t=t.substring(0,t.length-1);return t},get:function(t,e,n,i){var o=new XMLHttpRequest;o.onreadystatechange=function(){if(4===this.readyState&&200===this.status&&e)if(""!==this.responseText){var t=JSON.parse(this.responseText);e(t)}else e()},o.onerror=function(){n&&n()},o.open("GET",t,!0),i&&o.setRequestHeader("Authorization",i),o.send()},post:function(t,e,n,i,o,r){var s=new XMLHttpRequest;s.onreadystatechange=function(){if(4===this.readyState&&200===this.status&&e)if(""!==this.responseText){var t=JSON.parse(this.responseText);e(t)}else e()},s.onerror=function(){n&&n()},s.open("POST",t,!0),o&&s.setRequestHeader("Authorization",o),!0===r?s.send(i):s.send(JSON.stringify(i))},launchType:function(t){switch(t){case 0:return"Startup";case 1:return"Trigger";case 2:return"Periodic";case 3:return"Cron";default:return""}},status:function(t,e){switch(e){case 0:return"<img src='images/pending-small.png' /> <span class='st-pending'>"+t.get("status-pending-label")+"</span>";case 1:return"<img src='images/running-small.png' /> <span class='st-running'>"+t.get("status-running-label")+"</span>";case 2:return"<img src='images/done-small.png' /> <span class='st-done'>"+t.get("status-done-label")+"</span>";case 3:return"<img src='images/failed-small.png' /> <span class='st-failed'>"+t.get("status-failed-label")+"</span>";case 4:return"<img src='images/warning-small.png' /> <span class='st-warning'>"+t.get("status-warning-label")+"</span>";case 6:return"<img src='images/stopped-small.png' /> <span class='st-stopped'>"+t.get("status-stopped-label")+"</span>";case 7:return"<img src='images/disapproved-small.png' /> <span class='st-rejected'>"+t.get("status-disapproved-label")+"</span>";default:return""}},disableButton:function(t,e){t.disabled=e},formatDate:function(t){return("0"+t.getDate()).slice(-2)+"-"+("0"+(t.getMonth()+1)).slice(-2)+"-"+t.getFullYear()+" "+("0"+t.getHours()).slice(-2)+":"+("0"+t.getMinutes()).slice(-2)+":"+("0"+t.getSeconds()).slice(-2)},os:function(){var t="Unknown";return-1!==window.navigator.userAgent.indexOf("Windows NT 10.0")&&(t="Windows 10"),-1!==window.navigator.userAgent.indexOf("Windows NT 6.2")&&(t="Windows 8"),-1!==window.navigator.userAgent.indexOf("Windows NT 6.1")&&(t="Windows 7"),-1!==window.navigator.userAgent.indexOf("Windows NT 6.0")&&(t="Windows Vista"),-1!==window.navigator.userAgent.indexOf("Windows NT 5.1")&&(t="Windows XP"),-1!==window.navigator.userAgent.indexOf("Windows NT 5.0")&&(t="Windows 2000"),-1!==window.navigator.userAgent.indexOf("Mac")&&(t="Mac/iOS"),-1!==window.navigator.userAgent.indexOf("X11")&&(t="UNIX"),-1!==window.navigator.userAgent.indexOf("Linux")&&(t="Linux"),t},toastInfo:function(t){$.toast({heading:"Information",text:t,hideAfter:5e3,icon:"info"})},toastSuccess:function(t){$.toast({heading:"Success",text:t,hideAfter:5e3,icon:"success"})},toastError:function(t){$.toast({heading:"Error",text:t,hideAfter:5e3,icon:"error"})},escape:function(t){return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")},removeItemOnce:function(t,e){var n=t.indexOf(e);return n>-1&&t.splice(n,1),t}};function authorize(t,e,n){set("authorize",'{"Username": "'+t+'", "Password":"'+e+'","UserProfile":'+n+"}")}function getUser(){return get("authorize")}function deleteUser(){remove("authorize")}function set(t,e){isIE()?setCookie(t,e,365):window.localStorage.setItem(t,e)}function get(t){return isIE()?getCookie(t):window.localStorage.getItem(t)}function remove(t){isIE()?setCookie(t,"",-365):window.localStorage.removeItem(t)}function setCookie(t,e,n){let i=new Date;i.setTime(i.getTime()+24*n*60*60*1e3);let o="expires="+i.toUTCString();document.cookie=t+"="+e+";"+o+";path=/"}function getCookie(t){let e=t+"=",n=document.cookie.split(";");for(let t=0;t<n.length;t++){let i=n[t];for(;" "===i.charAt(0);)i=i.substring(1);if(0===i.indexOf(e))return i.substring(e.length,i.length)}return""}function isIE(){let t=navigator.userAgent;return t.indexOf("MSIE ")>-1||t.indexOf("Trident/")>-1}const Language=function(t,e){"use strict";let n=this;function i(){let t=navigator.userAgent;return t.indexOf("MSIE ")>-1||t.indexOf("Trident/")>-1}this.get=function(t){return languageModule.languages[n.getLanguage()][t]||languageModule.languages.en[t]},this.setLanguage=function(t){var e,n;e="language",n=t,i()?function(t,e,n){let i=new Date;i.setTime(i.getTime()+24*n*60*60*1e3);let o="expires="+i.toUTCString();document.cookie=t+"="+e+";"+o+";path=/"}(e,n,365):window.localStorage.setItem(e,n)},this.getLanguage=function(){let t=(e="language",i()?function(t){let e=t+"=",n=document.cookie.split(";");for(let t=0;t<n.length;t++){let i=n[t];for(;" "===i.charAt(0);)i=i.substring(1);if(0===i.indexOf(e))return i.substring(e.length,i.length)}return""}(e):window.localStorage.getItem(e));var e;return t||"en"},this.init=function(){let i=function(){let t=8-n.get("language").length,e="";if(t>0)for(let n=0;n<t;n++)e+="&nbsp;&nbsp;";return e},o='<button class="btn btn-default btn-xs dropdown-toggle" type="button" data-toggle="dropdown"><span id="lang-label">'+n.get("language")+i()+'</span> <span class="caret"></span></button><ul class="dropdown-menu" role="menu">';for(let t=0;t<languageModule.codes.length;t++)o+='<li><div class="lang"><img src="'+languageModule.codes[t].Icon+'" alt="">&nbsp;'+languageModule.codes[t].Name+'<input type="hidden" class="lang-code" value="'+languageModule.codes[t].Code+'" /></div></li>';o+=" </ul>";let r=document.getElementById(t);r.classList.add("btn-group"),r.innerHTML=o;let s=document.getElementsByClassName("lang");for(let t=0;t<s.length;t++)s[t].onclick=function(){let t=this.querySelector(".lang-code").value;n.setLanguage(t),e(n),document.getElementById("lang-label").innerHTML=n.get("language")+i()};e(n)}};window.onload=function(){"use strict";let t=function(){document.getElementById("th-assigned-by")&&(document.getElementById("th-assigned-by").innerHTML=e.get("th-assigned-by")),document.getElementById("th-assigned-on")&&(document.getElementById("th-assigned-on").innerHTML=e.get("th-assigned-on")),document.getElementById("th-message")&&(document.getElementById("th-message").innerHTML=e.get("th-message"))},e=new Language("lang",function(e){document.getElementById("lnk-records").innerHTML=e.get("lnk-records"),document.getElementById("lnk-approval").innerHTML=e.get("lnk-approval"),document.getElementById("lnk-dashboard").innerHTML=e.get("lnk-dashboard"),document.getElementById("lnk-manager").innerHTML=e.get("lnk-manager"),document.getElementById("lnk-designer").innerHTML=e.get("lnk-designer"),document.getElementById("lnk-history").innerHTML=e.get("lnk-history"),document.getElementById("lnk-users").innerHTML=e.get("lnk-users"),document.getElementById("lnk-profiles").innerHTML=e.get("lnk-profiles"),document.getElementById("spn-logout").innerHTML=e.get("spn-logout"),document.getElementById("search-notifications").placeholder=e.get("search-notifications"),document.getElementById("btn-delete").innerHTML=e.get("btn-delete-notification"),document.getElementById("btn-mark-as-unread").innerHTML=e.get("btn-mark-as-unread"),document.getElementById("btn-mark-as-read").innerHTML=e.get("btn-mark-as-read"),t()});e.init();let n=Common.trimEnd(Settings.Uri,"/"),i=document.getElementById("lnk-records"),o=document.getElementById("lnk-manager"),r=document.getElementById("lnk-designer"),s=document.getElementById("lnk-approval"),a=document.getElementById("lnk-users"),l=document.getElementById("lnk-profiles"),c=document.getElementById("lnk-notifications"),u=document.getElementById("img-notifications"),d=this.document.getElementById("search-notifications"),f=null,p="",h="",g=getUser();if(null===g||""===g)Common.redirectToLoginPage();else{p=(f=JSON.parse(g)).Username;let v=f.Password;function m(){Common.get(n+"/searchNotifications?a="+encodeURIComponent(f.Username)+"&s="+encodeURIComponent(d.value),function(i){let o=[];for(let t=0;t<i.length;t++){let e=i[t];o.push("<tr><td class='check'><input type='checkbox'></td><td class='id'>"+e.Id+"</td><td class='assigned-by "+(!1===e.IsRead?"bold":"")+"'>"+e.AssignedBy+"</td><td class='assigned-on "+(!1===e.IsRead?"bold":"")+"'>"+e.AssignedOn+"</td><td class='message "+(!1===e.IsRead?"bold":"")+"'>"+e.Message+"</td></tr>")}let r="<table id='notifications-table' class='table'><thead class='thead-dark'><tr><th class='check'><input id='check-all' type='checkbox'></th><th class='id'></th><th id='th-assigned-by' class='assigned-by'>Assigned by</th><th id='th-assigned-on' class='assigned-on'>Assigned on</th><th id='th-message' class='message'>Message</th></tr></thead><tbody>"+o.join("")+"</tbody></table>";document.getElementById("content").innerHTML=r,t();let s=document.getElementById("notifications-table").getElementsByTagName("tbody")[0].getElementsByTagName("tr"),a=[];for(let t=0;t<s.length;t++){s[t].getElementsByClassName("check")[0].firstChild.onchange=function(){let t=this.parentElement.parentElement.getElementsByClassName("id")[0].innerHTML;!0===this.checked?a.push(t):a=Common.removeItemOnce(a,t)}}document.getElementById("btn-mark-as-read").onclick=function(){Common.post(n+"/markNotificationsAsRead",function(t){!0===t&&Common.get(n+"/hasNotifications?a="+encodeURIComponent(f.Username),function(t){for(let e=0;e<a.length;e++){let n=a[e];for(let t=0;t<s.length;t++){let e=s[t];n===e.getElementsByClassName("id")[0].innerHTML&&(e.getElementsByClassName("assigned-by")[0].classList.remove("bold"),e.getElementsByClassName("assigned-on")[0].classList.remove("bold"),e.getElementsByClassName("message")[0].classList.remove("bold"))}u.src=!0===t?"images/notification-active.png":"images/notification.png"}},function(){},h)},function(){},a,h)},document.getElementById("btn-mark-as-unread").onclick=function(){Common.post(n+"/markNotificationsAsUnread",function(t){!0===t&&Common.get(n+"/hasNotifications?a="+encodeURIComponent(f.Username),function(t){for(let e=0;e<a.length;e++){let n=a[e];for(let t=0;t<s.length;t++){let e=s[t];n===e.getElementsByClassName("id")[0].innerHTML&&(!1===e.getElementsByClassName("assigned-by")[0].classList.contains("bold")&&e.getElementsByClassName("assigned-by")[0].classList.add("bold"),!1===e.getElementsByClassName("assigned-on")[0].classList.contains("bold")&&e.getElementsByClassName("assigned-on")[0].classList.add("bold"),!1===e.getElementsByClassName("message")[0].classList.contains("bold")&&e.getElementsByClassName("message")[0].classList.add("bold"))}u.src=!0===t?"images/notification-active.png":"images/notification.png"}},function(){},h)},function(){},a,h)},document.getElementById("btn-delete").onclick=function(){if(0===a.length)Common.toastInfo(e.get("toast-select-notifications"));else{!0===confirm(1==a.length?e.get("confirm-delete-notification"):e.get("confirm-delete-notifications"))&&Common.post(n+"/deleteNotifications",function(t){if(!0===t)for(let t=a.length-1;t>=0;t--){let e=a[t];for(let t=0;t<s.length;t++){let n=s[t];e===n.getElementsByClassName("id")[0].innerHTML&&(a=Common.removeItemOnce(a,e),n.remove())}}},function(){},a,h)}},document.getElementById("check-all").onchange=function(){for(let t=0;t<s.length;t++){let e=s[t],n=e.getElementsByClassName("check")[0].firstChild,i=e.getElementsByClassName("id")[0].innerHTML;!0===n.checked?(n.checked=!1,a=Common.removeItemOnce(a,i)):(n.checked=!0,a.push(i))}}},function(){},h)}h="Basic "+btoa(p+":"+v),Common.get(n+"/user?username="+encodeURIComponent(f.Username),function(t){f.Password!==t.Password?Common.redirectToLoginPage():0===t.UserProfile||1===t.UserProfile?Common.get(n+"/hasNotifications?a="+encodeURIComponent(f.Username),function(e){i.style.display="inline",o.style.display="inline",r.style.display="inline",s.style.display="inline",a.style.display="inline",c.style.display="inline",0===t.UserProfile&&(l.style.display="inline"),u.src=!0===e?"images/notification-active.png":"images/notification.png";let n=document.getElementById("btn-logout");document.getElementById("navigation").style.display="block",document.getElementById("content").style.display="block",n.onclick=function(){deleteUser(),Common.redirectToLoginPage()},document.getElementById("spn-username").innerHTML=" ("+t.Username+")",d.onkeyup=function(t){return t.preventDefault(),13===t.keyCode&&m(),!1},m()},function(){},h):Common.redirectToLoginPage()},function(){},h)}};