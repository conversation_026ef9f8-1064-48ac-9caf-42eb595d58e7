﻿(function () { var s, o, l, r, h, e, t, i = function () { return this }(); i || "undefined" == typeof window || (i = window), "undefined" == typeof requirejs && ((s = function (e, t, i) { "string" != typeof e ? s.original ? s.original.apply(this, arguments) : (console.error("dropping module because define wasn't a string."), console.trace()) : (2 == arguments.length && (i = t), s.modules[e] || (s.payloads[e] = i, s.modules[e] = null)) }).modules = {}, s.payloads = {}, o = function (e, t, i) { if ("string" == typeof t) { var n = h(e, t); if (null != n) return i && i(), n } else if ("[object Array]" === Object.prototype.toString.call(t)) { for (var s = [], o = 0, r = t.length; o < r; ++o) { var a = h(e, t[o]); if (null == a && l.original) return; s.push(a) } return i && i.apply(null, s) || !0 } }, l = function (e, t) { var i = o("", e, t); return null == i && l.original ? l.original.apply(this, arguments) : i }, r = function (e, t) { var i; if (-1 !== t.indexOf("!")) return i = t.split("!"), r(e, i[0]) + "!" + r(e, i[1]); if ("." == t.charAt(0)) for (t = e.split("/").slice(0, -1).join("/") + "/" + t; -1 !== t.indexOf(".") && n != t;) { var n = t; t = t.replace(/\/\.\//, "/").replace(/[^\/]+\/\.\.\//, "") } return t }, h = function (e, i) { i = r(e, i); var t, n, e = s.modules[i]; return e || ("function" == typeof (e = s.payloads[i]) && (t = { id: i, uri: "", exports: n = {}, packaged: !0 }, n = e(function (e, t) { return o(i, e, t) }, n, t) || t.exports, s.modules[i] = n, delete s.payloads[i]), e = s.modules[i] = n || e), e }, t = i, (e = "") && (i[e] || (i[e] = {}), t = i[e]), t.define && t.define.packaged || (s.original = t.define, t.define = s, t.define.packaged = !0), t.require && t.require.packaged || (l.original = t.require, t.require = l, t.require.packaged = !0)) })(), define("ace/lib/es6-shim", ["require", "exports", "module"], function (e, t, i) { function n(e, t, i) { Object.defineProperty(e, t, { value: i, enumerable: !1, writable: !0, configurable: !0 }) } String.prototype.startsWith || n(String.prototype, "startsWith", function (e, t) { return this.lastIndexOf(e, t = t || 0) === t }), String.prototype.endsWith || n(String.prototype, "endsWith", function (e, t) { var i = this, i = ((void 0 === t || t > i.length) && (t = i.length), t -= e.length, i.indexOf(e, t)); return -1 !== i && i === t }), String.prototype.repeat || n(String.prototype, "repeat", function (e) { for (var t = "", i = this; 0 < e;)1 & e && (t += i), (e >>= 1) && (i += i); return t }), String.prototype.includes || n(String.prototype, "includes", function (e, t) { return -1 != this.indexOf(e, t) }), Object.assign || (Object.assign = function (e) { if (null == e) throw new TypeError("Cannot convert undefined or null to object"); for (var t = Object(e), i = 1; i < arguments.length; i++) { var n = arguments[i]; null != n && Object.keys(n).forEach(function (e) { t[e] = n[e] }) } return t }), Object.values || (Object.values = function (t) { return Object.keys(t).map(function (e) { return t[e] }) }), Array.prototype.find || n(Array.prototype, "find", function (e) { for (var t = this.length, i = arguments[1], n = 0; n < t; n++) { var s = this[n]; if (e.call(i, s, n, this)) return s } }), Array.prototype.findIndex || n(Array.prototype, "findIndex", function (e) { for (var t = this.length, i = arguments[1], n = 0; n < t; n++) { var s = this[n]; if (e.call(i, s, n, this)) return n } }), Array.prototype.includes || n(Array.prototype, "includes", function (e, t) { return -1 != this.indexOf(e, t) }), Array.prototype.fill || n(Array.prototype, "fill", function (e) { for (var t = this.length >>> 0, i = arguments[1] >> 0, n = i < 0 ? Math.max(t + i, 0) : Math.min(i, t), i = arguments[2], i = void 0 === i ? t : i >> 0, s = i < 0 ? Math.max(t + i, 0) : Math.min(i, t); n < s;)this[n] = e, n++; return this }), Array.of || n(Array, "of", function () { return Array.prototype.slice.call(arguments) }) }), define("ace/lib/fixoldbrowsers", ["require", "exports", "module", "ace/lib/es6-shim"], function (e, t, i) { "use strict"; e("./es6-shim") }), define("ace/lib/lang", ["require", "exports", "module"], function (e, t, i) { "use strict"; t.last = function (e) { return e[e.length - 1] }, t.stringReverse = function (e) { return e.split("").reverse().join("") }, t.stringRepeat = function (e, t) { for (var i = ""; 0 < t;)1 & t && (i += e), (t >>= 1) && (e += e); return i }; var n = /^\s\s*/, s = /\s\s*$/; t.stringTrimLeft = function (e) { return e.replace(n, "") }, t.stringTrimRight = function (e) { return e.replace(s, "") }, t.copyObject = function (e) { var t, i = {}; for (t in e) i[t] = e[t]; return i }, t.copyArray = function (e) { for (var t = [], i = 0, n = e.length; i < n; i++)e[i] && "object" == typeof e[i] ? t[i] = this.copyObject(e[i]) : t[i] = e[i]; return t }, t.deepCopy = function e(t) { if ("object" != typeof t || !t) return t; if (Array.isArray(t)) { for (var i = [], n = 0; n < t.length; n++)i[n] = e(t[n]); return i } if ("[object Object]" !== Object.prototype.toString.call(t)) return t; for (n in i = {}, t) i[n] = e(t[n]); return i }, t.arrayToMap = function (e) { for (var t = {}, i = 0; i < e.length; i++)t[e[i]] = 1; return t }, t.createMap = function (e) { var t, i = Object.create(null); for (t in e) i[t] = e[t]; return i }, t.arrayRemove = function (e, t) { for (var i = 0; i <= e.length; i++)t === e[i] && e.splice(i, 1) }, t.escapeRegExp = function (e) { return e.replace(/([.*+?^${}()|[\]\/\\])/g, "\\$1") }, t.escapeHTML = function (e) { return ("" + e).replace(/&/g, "&#38;").replace(/"/g, "&#34;").replace(/'/g, "&#39;").replace(/</g, "&#60;") }, t.getMatchOffsets = function (e, t) { var i = []; return e.replace(t, function (e) { i.push({ offset: arguments[arguments.length - 2], length: e.length }) }), i }, t.deferredCall = function (e) { function t() { n = null, e() } function i(e) { return i.cancel(), n = setTimeout(t, e || 0), i } var n = null; return (i.schedule = i).call = function () { return this.cancel(), e(), i }, i.cancel = function () { return clearTimeout(n), n = null, i }, i.isPending = function () { return n }, i }, t.delayedCall = function (e, t) { function i() { s = null, e() } function n(e) { null == s && (s = setTimeout(i, e || t)) } var s = null; return n.delay = function (e) { s && clearTimeout(s), s = setTimeout(i, e || t) }, (n.schedule = n).call = function () { this.cancel(), e() }, n.cancel = function () { s && clearTimeout(s), s = null }, n.isPending = function () { return s }, n } }), define("ace/lib/oop", ["require", "exports", "module"], function (e, i, t) { "use strict"; i.inherits = function (e, t) { e.super_ = t, e.prototype = Object.create(t.prototype, { constructor: { value: e, enumerable: !1, writable: !0, configurable: !0 } }) }, i.mixin = function (e, t) { for (var i in t) e[i] = t[i]; return e }, i.implement = function (e, t) { i.mixin(e, t) } }), define("ace/lib/useragent", ["require", "exports", "module"], function (e, t, i) { "use strict"; t.OS = { LINUX: "LINUX", MAC: "MAC", WINDOWS: "WINDOWS" }, t.getOS = function () { return t.isMac ? t.OS.MAC : t.isLinux ? t.OS.LINUX : t.OS.WINDOWS }; var n = "object" == typeof navigator ? navigator : {}, s = (/mac|win|linux/i.exec(n.platform) || ["other"])[0].toLowerCase(), o = n.userAgent || "", n = n.appName || ""; t.isWin = "win" == s, t.isMac = "mac" == s, t.isLinux = "linux" == s, t.isIE = "Microsoft Internet Explorer" == n || 0 <= n.indexOf("MSAppHost") ? parseFloat((o.match(/(?:MSIE |Trident\/[0-9]+[\.0-9]+;.*rv:)([0-9]+[\.0-9]+)/) || [])[1]) : parseFloat((o.match(/(?:Trident\/[0-9]+[\.0-9]+;.*rv:)([0-9]+[\.0-9]+)/) || [])[1]), t.isOldIE = t.isIE && t.isIE < 9, t.isGecko = t.isMozilla = o.match(/ Gecko\/\d+/), t.isOpera = "object" == typeof opera && "[object Opera]" == Object.prototype.toString.call(window.opera), t.isWebKit = parseFloat(o.split("WebKit/")[1]) || void 0, t.isChrome = parseFloat(o.split(" Chrome/")[1]) || void 0, t.isEdge = parseFloat(o.split(" Edge/")[1]) || void 0, t.isAIR = 0 <= o.indexOf("AdobeAIR"), t.isAndroid = 0 <= o.indexOf("Android"), t.isChromeOS = 0 <= o.indexOf(" CrOS "), t.isIOS = /iPad|iPhone|iPod/.test(o) && !window.MSStream, t.isIOS && (t.isMac = !0), t.isMobile = t.isIOS || t.isAndroid }), define("ace/lib/dom", ["require", "exports", "module", "ace/lib/useragent"], function (e, o, t) { "use strict"; function r() { var e = l; l = null, e && e.forEach(function (e) { i(e[0], e[1]) }) } function i(e, t, i) { if ("undefined" != typeof document) { if (l) if (i) r(); else if (!1 === i) return l.push([e, t]); if (!a) { var n = i, i = (n = i && i.getRootNode && (n = i.getRootNode()) && n != i ? n : document).ownerDocument || n; if (t && o.hasCssString(t, n)) return null; t && (e += "\n/*# sourceURL=ace/css/" + t + " */"); var s = o.createElement("style"); s.appendChild(i.createTextNode(e)), t && (s.id = t), (n = n == i ? o.getDocumentHead(i) : n).insertBefore(s, n.firstChild) } } } e = e("./useragent"); o.buildDom = function e(t, i, n) { if ("string" == typeof t && t) return h = document.createTextNode(t), i && i.appendChild(h), h; if (!Array.isArray(t)) return t && t.appendChild && i && i.appendChild(t), t; if ("string" != typeof t[0] || !t[0]) { for (var s = [], o = 0; o < t.length; o++) { var r = e(t[o], i, n); r && s.push(r) } return s } for (var a = document.createElement(t[0]), l = t[1], h = 1, o = h = l && "object" == typeof l && !Array.isArray(l) ? 2 : h; o < t.length; o++)e(t[o], a, n); return 2 == h && Object.keys(l).forEach(function (e) { var t = l[e]; "class" === e ? a.className = Array.isArray(t) ? t.join(" ") : t : "function" == typeof t || "value" == e || "$" == e[0] ? a[e] = t : "ref" === e ? n && (n[t] = a) : "style" === e ? "string" == typeof t && (a.style.cssText = t) : null != t && a.setAttribute(e, t) }), i && i.appendChild(a), a }, o.getDocumentHead = function (e) { return (e = e || document).head || e.getElementsByTagName("head")[0] || e.documentElement }, o.createElement = function (e, t) { return document.createElementNS ? document.createElementNS(t || "http://www.w3.org/1999/xhtml", e) : document.createElement(e) }, o.removeChildren = function (e) { e.innerHTML = "" }, o.createTextNode = function (e, t) { return (t ? t.ownerDocument : document).createTextNode(e) }, o.createFragment = function (e) { return (e ? e.ownerDocument : document).createDocumentFragment() }, o.hasCssClass = function (e, t) { return -1 !== (e.className + "").split(/\s+/g).indexOf(t) }, o.addCssClass = function (e, t) { o.hasCssClass(e, t) || (e.className += " " + t) }, o.removeCssClass = function (e, t) { for (var i = e.className.split(/\s+/g); ;) { var n = i.indexOf(t); if (-1 == n) break; i.splice(n, 1) } e.className = i.join(" ") }, o.toggleCssClass = function (e, t) { for (var i = e.className.split(/\s+/g), n = !0; ;) { var s = i.indexOf(t); if (-1 == s) break; n = !1, i.splice(s, 1) } return n && i.push(t), e.className = i.join(" "), n }, o.setCssClass = function (e, t, i) { i ? o.addCssClass(e, t) : o.removeCssClass(e, t) }, o.hasCssString = function (e, t) { var i, n = 0; if (i = (t = t || document).querySelectorAll("style")) for (; n < i.length;)if (i[n++].id === e) return !0 }, o.removeElementById = function (e, t) { (t = t || document).getElementById(e) && t.getElementById(e).remove() }; var a, n, l = []; o.useStrictCSP = function (e) { 0 == (a = e) ? r() : l = l || [] }, o.importCssString = i, o.importCssStylsheet = function (e, t) { o.buildDom(["link", { rel: "stylesheet", href: e }], o.getDocumentHead(t)) }, o.scrollbarWidth = function (e) { var t = o.createElement("ace_inner"), i = (t.style.width = "100%", t.style.minWidth = "0px", t.style.height = "200px", t.style.display = "block", o.createElement("ace_outer")), n = i.style, e = (n.position = "absolute", n.left = "-10000px", n.overflow = "hidden", n.width = "200px", n.minWidth = "0px", n.height = "150px", n.display = "block", i.appendChild(t), e && e.documentElement || document && document.documentElement); if (!e) return 0; e.appendChild(i); var s = t.offsetWidth, n = (n.overflow = "scroll", t.offsetWidth); return s === n && (n = i.clientWidth), e.removeChild(i), s - n }, o.computedStyle = function (e, t) { return window.getComputedStyle(e, "") || {} }, o.setStyle = function (e, t, i) { e[t] !== i && (e[t] = i) }, o.HAS_CSS_ANIMATION = !1, o.HAS_CSS_TRANSFORMS = !1, o.HI_DPI = !e.isWin || "undefined" != typeof window && 1.5 <= window.devicePixelRatio, e.isChromeOS && (o.HI_DPI = !1), "undefined" != typeof document && (n = document.createElement("div"), o.HI_DPI && void 0 !== n.style.transform && (o.HAS_CSS_TRANSFORMS = !0), e.isEdge || void 0 === n.style.animationName || (o.HAS_CSS_ANIMATION = !0), n = null), o.HAS_CSS_TRANSFORMS ? o.translate = function (e, t, i) { e.style.transform = "translate(" + Math.round(t) + "px, " + Math.round(i) + "px)" } : o.translate = function (e, t, i) { e.style.top = Math.round(i) + "px", e.style.left = Math.round(t) + "px" } }), define("ace/lib/net", ["require", "exports", "module", "ace/lib/dom"], function (e, t, i) { "use strict"; var s = e("./dom"); t.get = function (e, t) { var i = new XMLHttpRequest; i.open("GET", e, !0), i.onreadystatechange = function () { 4 === i.readyState && t(i.responseText) }, i.send(null) }, t.loadScript = function (e, i) { var t = s.getDocumentHead(), n = document.createElement("script"); n.src = e, t.appendChild(n), n.onload = n.onreadystatechange = function (e, t) { !t && n.readyState && "loaded" != n.readyState && "complete" != n.readyState || (n = n.onload = n.onreadystatechange = null, t || i()) } }, t.qualifyURL = function (e) { var t = document.createElement("a"); return t.href = e, t.href } }), define("ace/lib/event_emitter", ["require", "exports", "module"], function (e, t, i) { "use strict"; function o() { this.propagationStopped = !0 } function r() { this.defaultPrevented = !0 } var n = {}; n._emit = n._dispatchEvent = function (e, t) { this._eventRegistry || (this._eventRegistry = {}), this._defaultHandlers || (this._defaultHandlers = {}); var i = this._eventRegistry[e] || [], n = this._defaultHandlers[e]; if (i.length || n) { (t = "object" == typeof t && t ? t : {}).type || (t.type = e), t.stopPropagation || (t.stopPropagation = o), t.preventDefault || (t.preventDefault = r); for (var i = i.slice(), s = 0; s < i.length && (i[s](t, this), !t.propagationStopped); s++); return n && !t.defaultPrevented ? n(t, this) : void 0 } }, n._signal = function (e, t) { if (i = (this._eventRegistry || {})[e]) for (var i = i.slice(), n = 0; n < i.length; n++)i[n](t, this) }, n.once = function (t, i) { var n = this; if (this.on(t, function e() { n.off(t, e), i.apply(null, arguments) }), !i) return new Promise(function (e) { i = e }) }, n.setDefaultHandler = function (e, t) { var i, n, s = this._defaultHandlers; (s = s || (this._defaultHandlers = { _disabled_: {} }))[e] && (n = s[e], (i = s._disabled_[e]) || (s._disabled_[e] = i = []), i.push(n), -1 != (n = i.indexOf(t)) && i.splice(n, 1)), s[e] = t }, n.removeDefaultHandler = function (e, t) { var i, n = this._defaultHandlers; n && (i = n._disabled_[e], n[e] == t ? i && this.setDefaultHandler(e, i.pop()) : i && -1 != (n = i.indexOf(t)) && i.splice(n, 1)) }, n.on = n.addEventListener = function (e, t, i) { this._eventRegistry = this._eventRegistry || {}; var n = this._eventRegistry[e]; return -1 == (n = n || (this._eventRegistry[e] = [])).indexOf(t) && n[i ? "unshift" : "push"](t), t }, n.off = n.removeListener = n.removeEventListener = function (e, t) { this._eventRegistry = this._eventRegistry || {}; e = this._eventRegistry[e]; !e || -1 !== (t = e.indexOf(t)) && e.splice(t, 1) }, n.removeAllListeners = function (e) { e || (this._eventRegistry = this._defaultHandlers = void 0), this._eventRegistry && (this._eventRegistry[e] = void 0), this._defaultHandlers && (this._defaultHandlers[e] = void 0) }, t.EventEmitter = n }), define("ace/lib/app_config", ["require", "exports", "module", "ace/lib/oop", "ace/lib/event_emitter"], function (e, t, i) { function n(e) { "undefined" != typeof console && console.warn && console.warn.apply(console, arguments) } function s(e, t) { var i = new Error(e); i.data = t, "object" == typeof console && console.error && console.error(i), setTimeout(function () { throw i }) } function o() { this.$defaultOptions = {} } var r = e("./oop"), a = e("./event_emitter").EventEmitter, l = { setOptions: function (t) { Object.keys(t).forEach(function (e) { this.setOption(e, t[e]) }, this) }, getOptions: function (e) { var t, i = {}; return e ? Array.isArray(e) || (i = e, e = Object.keys(i)) : (t = this.$options, e = Object.keys(t).filter(function (e) { return !t[e].hidden })), e.forEach(function (e) { i[e] = this.getOption(e) }, this), i }, setOption: function (e, t) { var i; if (this["$" + e] !== t) return i = this.$options[e], i ? i.forwardTo ? this[i.forwardTo] && this[i.forwardTo].setOption(e, t) : (i.handlesSet || (this["$" + e] = t), void (i && i.set && i.set.call(this, t))) : n('misspelled option "' + e + '"') }, getOption: function (e) { var t = this.$options[e]; return t ? t.forwardTo ? this[t.forwardTo] && this[t.forwardTo].getOption(e) : t && t.get ? t.get.call(this) : this["$" + e] : n('misspelled option "' + e + '"') } }; (function () { r.implement(this, a), this.defineOptions = function (i, e, n) { return i.$options || (this.$defaultOptions[e] = i.$options = {}), Object.keys(n).forEach(function (e) { var t = n[e]; (t = "string" == typeof t ? { forwardTo: t } : t).name || (t.name = e), "initialValue" in (i.$options[t.name] = t) && (i["$" + t.name] = t.initialValue) }), r.implement(i, l), this }, this.resetOptions = function (i) { Object.keys(i.$options).forEach(function (e) { var t = i.$options[e]; "value" in t && i.setOption(e, t.value) }) }, this.setDefaultValue = function (e, t, i) { if (!e) { for (e in this.$defaultOptions) if (this.$defaultOptions[e][t]) break; if (!this.$defaultOptions[e][t]) return !1 } var n = this.$defaultOptions[e] || (this.$defaultOptions[e] = {}); n[t] && (n.forwardTo ? this.setDefaultValue(n.forwardTo, t, i) : n[t].value = i) }, this.setDefaultValues = function (t, i) { Object.keys(i).forEach(function (e) { this.setDefaultValue(t, e, i[e]) }, this) }, this.warn = n, this.reportError = s }).call(o.prototype), t.AppConfig = o }), define("ace/theme/textmate.css", ["require", "exports", "module"], function (e, t, i) { i.exports = '.ace-tm .ace_gutter {\n  background: #f0f0f0;\n  color: #333;\n}\n\n.ace-tm .ace_print-margin {\n  width: 1px;\n  background: #e8e8e8;\n}\n\n.ace-tm .ace_fold {\n    background-color: #6B72E6;\n}\n\n.ace-tm {\n  background-color: #FFFFFF;\n  color: black;\n}\n\n.ace-tm .ace_cursor {\n  color: black;\n}\n        \n.ace-tm .ace_invisible {\n  color: rgb(191, 191, 191);\n}\n\n.ace-tm .ace_storage,\n.ace-tm .ace_keyword {\n  color: blue;\n}\n\n.ace-tm .ace_constant {\n  color: rgb(197, 6, 11);\n}\n\n.ace-tm .ace_constant.ace_buildin {\n  color: rgb(88, 72, 246);\n}\n\n.ace-tm .ace_constant.ace_language {\n  color: rgb(88, 92, 246);\n}\n\n.ace-tm .ace_constant.ace_library {\n  color: rgb(6, 150, 14);\n}\n\n.ace-tm .ace_invalid {\n  background-color: rgba(255, 0, 0, 0.1);\n  color: red;\n}\n\n.ace-tm .ace_support.ace_function {\n  color: rgb(60, 76, 114);\n}\n\n.ace-tm .ace_support.ace_constant {\n  color: rgb(6, 150, 14);\n}\n\n.ace-tm .ace_support.ace_type,\n.ace-tm .ace_support.ace_class {\n  color: rgb(109, 121, 222);\n}\n\n.ace-tm .ace_keyword.ace_operator {\n  color: rgb(104, 118, 135);\n}\n\n.ace-tm .ace_string {\n  color: rgb(3, 106, 7);\n}\n\n.ace-tm .ace_comment {\n  color: rgb(76, 136, 107);\n}\n\n.ace-tm .ace_comment.ace_doc {\n  color: rgb(0, 102, 255);\n}\n\n.ace-tm .ace_comment.ace_doc.ace_tag {\n  color: rgb(128, 159, 191);\n}\n\n.ace-tm .ace_constant.ace_numeric {\n  color: rgb(0, 0, 205);\n}\n\n.ace-tm .ace_variable {\n  color: rgb(49, 132, 149);\n}\n\n.ace-tm .ace_xml-pe {\n  color: rgb(104, 104, 91);\n}\n\n.ace-tm .ace_entity.ace_name.ace_function {\n  color: #0000A2;\n}\n\n\n.ace-tm .ace_heading {\n  color: rgb(12, 7, 255);\n}\n\n.ace-tm .ace_list {\n  color:rgb(185, 6, 144);\n}\n\n.ace-tm .ace_meta.ace_tag {\n  color:rgb(0, 22, 142);\n}\n\n.ace-tm .ace_string.ace_regex {\n  color: rgb(255, 0, 0)\n}\n\n.ace-tm .ace_marker-layer .ace_selection {\n  background: rgb(181, 213, 255);\n}\n.ace-tm.ace_multiselect .ace_selection.ace_start {\n  box-shadow: 0 0 3px 0px white;\n}\n.ace-tm .ace_marker-layer .ace_step {\n  background: rgb(252, 255, 0);\n}\n\n.ace-tm .ace_marker-layer .ace_stack {\n  background: rgb(164, 229, 101);\n}\n\n.ace-tm .ace_marker-layer .ace_bracket {\n  margin: -1px 0 0 -1px;\n  border: 1px solid rgb(192, 192, 192);\n}\n\n.ace-tm .ace_marker-layer .ace_active-line {\n  background: rgba(0, 0, 0, 0.07);\n}\n\n.ace-tm .ace_gutter-active-line {\n    background-color : #dcdcdc;\n}\n\n.ace-tm .ace_marker-layer .ace_selected-word {\n  background: rgb(250, 250, 255);\n  border: 1px solid rgb(200, 200, 250);\n}\n\n.ace-tm .ace_indent-guide {\n  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAAE0lEQVQImWP4////f4bLly//BwAmVgd1/w11/gAAAABJRU5ErkJggg==") right repeat-y;\n}\n\n.ace-tm .ace_indent-guide-active {\n  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAAZSURBVHjaYvj///9/hivKyv8BAAAA//8DACLqBhbvk+/eAAAAAElFTkSuQmCC") right repeat-y;\n}\n' }), define("ace/theme/textmate", ["require", "exports", "module", "ace/theme/textmate.css", "ace/lib/dom"], function (e, t, i) { "use strict"; t.isDark = !1, t.cssClass = "ace-tm", t.cssText = e("./textmate.css"), t.$id = "ace/theme/textmate", e("../lib/dom").importCssString(t.cssText, t.cssClass, !1) }), define("ace/config", ["require", "exports", "module", "ace/lib/lang", "ace/lib/oop", "ace/lib/net", "ace/lib/dom", "ace/lib/app_config", "ace/theme/textmate"], function (s, o, e) { var t = s("./lib/lang"), r = (s("./lib/oop"), s("./lib/net")), i = s("./lib/dom"), n = s("./lib/app_config").AppConfig, a = (e.exports = o = new n, { packaged: !1, workerPath: null, modePath: null, themePath: null, basePath: "", suffix: ".js", $moduleUrls: {}, loadWorkerFromBlob: !0, sharedPopups: !1, useStrictCSP: null }), l = (o.get = function (e) { if (a.hasOwnProperty(e)) return a[e]; throw new Error("Unknown config key: " + e) }, o.set = function (e, t) { if (a.hasOwnProperty(e)) a[e] = t; else if (0 == this.setDefaultValue("", e, t)) throw new Error("Unknown config key: " + e); "useStrictCSP" == e && i.useStrictCSP(t) }, o.all = function () { return t.copyObject(a) }, o.$modes = {}, o.moduleUrl = function (e, t) { if (a.$moduleUrls[e]) return a.$moduleUrls[e]; var e = e.split("/"), i = "snippets" == (t = t || e[e.length - 2] || "") ? "/" : "-", n = e[e.length - 1], s = ("worker" == t && "-" == i && (s = new RegExp("^" + t + "[\\-_]|[\\-_]" + t + "$", "g"), n = n.replace(s, "")), (!n || n == t) && 1 < e.length && (n = e[e.length - 2]), a[t + "Path"]); return null == s ? s = a.basePath : "/" == i && (t = i = ""), s && "/" != s.slice(-1) && (s += "/"), s + t + i + n + this.get("suffix") }, o.setModuleUrl = function (e, t) { return a.$moduleUrls[e] = t }, function (e, t) { return "ace/theme/textmate" == e ? t(null, s("./theme/textmate")) : console.error("loader is not configured") }), h = (o.setLoader = function (e) { l = e }, o.$loading = {}, o.loadModule = function (n, e) { var t, i; Array.isArray(n) && (t = n[0], n = n[1]); try { i = s(n) } catch (e) { } if (i && !o.$loading[n]) return e && e(i); if (o.$loading[n] || (o.$loading[n] = []), o.$loading[n].push(e), !(1 < o.$loading[n].length)) return i = function () { l(n, function (e, t) { o._emit("load.module", { name: n, module: t }); var i = o.$loading[n]; o.$loading[n] = null, i.forEach(function (e) { e && e(t) }) }) }, o.get("packaged") ? (r.loadScript(o.moduleUrl(n, t), i), void h()) : i() }, function () { a.basePath || a.workerPath || a.modePath || a.themePath || Object.keys(a.$moduleUrls).length || (console.error("Unable to infer path to ace from script src,", "use ace.config.set('basePath', 'path') to enable dynamic loading of modes and themes", "or with webpack use ace/webpack-resolver"), h = function () { }) }); o.version = "1.13.1" }), define("ace/loader_build", ["require", "exports", "module", "ace/lib/fixoldbrowsers", "ace/config"], function (d, e, g) { "use strict"; function t(e) { if (m && m.document) { f.set("packaged", e || d.packaged || g.packaged || m.define && define.packaged); for (var t, i = {}, n = "", e = document.currentScript || document._currentScript, s = (e && e.ownerDocument || document).getElementsByTagName("script"), o = 0; o < s.length; o++) { var r = s[o], a = r.src || r.getAttribute("src"); if (a) { for (var l = r.attributes, h = 0, c = l.length; h < c; h++) { var u = l[h]; 0 === u.name.indexOf("data-ace-") && (i[u.name.replace(/^data-ace-/, "").replace(/-(.)/g, function (e, t) { return t.toUpperCase() })] = u.value) } r = a.match(/^(.*)\/ace(\-\w+)?\.js(\?|$)/); r && (n = r[1]) } } for (t in n && (i.base = i.base || n, i.packaged = !0), i.basePath = i.base, i.workerPath = i.workerPath || i.base, i.modePath = i.modePath || i.base, i.themePath = i.themePath || i.base, delete i.base, i) void 0 !== i[t] && f.set(t, i[t]) } } d("./lib/fixoldbrowsers"); var f = d("./config"), m = (f.setLoader(function (e, t) { d([e], function (e) { t(null, e) }) }), function () { return this || "undefined" != typeof window && window }()); g.exports = function (e) { f.init = t, e.require = d, "function" == typeof define && (e.define = define) } }), define("ace/lib/keys", ["require", "exports", "module", "ace/lib/oop"], function (e, t, i) { "use strict"; var o = e("./oop"), n = function () { var e, t, i = { MODIFIER_KEYS: { 16: "Shift", 17: "Ctrl", 18: "Alt", 224: "Meta", 91: "MetaLeft", 92: "MetaRight", 93: "ContextMenu" }, KEY_MODS: { ctrl: 1, alt: 2, option: 2, shift: 4, super: 8, meta: 8, command: 8, cmd: 8, control: 1 }, FUNCTION_KEYS: { 8: "Backspace", 9: "Tab", 13: "Return", 19: "Pause", 27: "Esc", 32: "Space", 33: "PageUp", 34: "PageDown", 35: "End", 36: "Home", 37: "Left", 38: "Up", 39: "Right", 40: "Down", 44: "Print", 45: "Insert", 46: "Delete", 96: "Numpad0", 97: "Numpad1", 98: "Numpad2", 99: "Numpad3", 100: "Numpad4", 101: "Numpad5", 102: "Numpad6", 103: "Numpad7", 104: "Numpad8", 105: "Numpad9", "-13": "NumpadEnter", 112: "F1", 113: "F2", 114: "F3", 115: "F4", 116: "F5", 117: "F6", 118: "F7", 119: "F8", 120: "F9", 121: "F10", 122: "F11", 123: "F12", 144: "Numlock", 145: "Scrolllock" }, PRINTABLE_KEYS: { 32: " ", 48: "0", 49: "1", 50: "2", 51: "3", 52: "4", 53: "5", 54: "6", 55: "7", 56: "8", 57: "9", 59: ";", 61: "=", 65: "a", 66: "b", 67: "c", 68: "d", 69: "e", 70: "f", 71: "g", 72: "h", 73: "i", 74: "j", 75: "k", 76: "l", 77: "m", 78: "n", 79: "o", 80: "p", 81: "q", 82: "r", 83: "s", 84: "t", 85: "u", 86: "v", 87: "w", 88: "x", 89: "y", 90: "z", 107: "+", 109: "-", 110: ".", 186: ";", 187: "=", 188: ",", 189: "-", 190: ".", 191: "/", 192: "`", 219: "[", 220: "\\", 221: "]", 222: "'", 111: "/", 106: "*" } }; for (t in i.PRINTABLE_KEYS[173] = "-", i.FUNCTION_KEYS) e = i.FUNCTION_KEYS[t].toLowerCase(), i[e] = parseInt(t, 10); for (t in i.PRINTABLE_KEYS) e = i.PRINTABLE_KEYS[t].toLowerCase(), i[e] = parseInt(t, 10); o.mixin(i, i.MODIFIER_KEYS), o.mixin(i, i.PRINTABLE_KEYS), o.mixin(i, i.FUNCTION_KEYS), i.enter = i.return, i.escape = i.esc, i.del = i.delete; for (var n = ["cmd", "ctrl", "alt", "shift"], s = Math.pow(2, n.length); s--;)i.KEY_MODS[s] = n.filter(function (e) { return s & i.KEY_MODS[e] }).join("-") + "-"; return i.KEY_MODS[0] = "", i.KEY_MODS[-1] = "input-", i }(); o.mixin(t, n), t.keyCodeToString = function (e) { var t = n[e]; return (t = "string" != typeof t ? String.fromCharCode(e) : t).toLowerCase() } }), define("ace/lib/event", ["require", "exports", "module", "ace/lib/keys", "ace/lib/useragent"], function (e, u, t) { "use strict"; function s() { if (null == i) { i = !1; try { document.createComment("").addEventListener("test", function () { }, { get passive() { i = { passive: !1 } } }) } catch (e) { } } return i } function o(e, t, i) { this.elem = e, this.type = t, this.callback = i } function r(e, t, i) { var n = l(t); if (!d.isMac && g) { if (t.getModifierState && (t.getModifierState("OS") || t.getModifierState("Win")) && (n |= 8), g.altGr) { if (3 == (3 & n)) return; g.altGr = 0 } 18 !== i && 17 !== i || (s = "location" in t ? t.location : t.keyLocation, 17 === i && 1 === s ? 1 == g[i] && (f = t.timeStamp) : 18 === i && 3 === n && 2 === s && t.timeStamp - f < 50 && (g.altGr = !0)) } if (i in c.MODIFIER_KEYS && (i = -1), !n && 13 === i) { var s = "location" in t ? t.location : t.keyLocation; if (3 === s && (e(t, n, -i), t.defaultPrevented)) return } if (d.isChromeOS && 8 & n) { if (e(t, n, i), t.defaultPrevented) return; n &= -9 } return !!(n || i in c.FUNCTION_KEYS || i in c.PRINTABLE_KEYS) && e(t, n, i) } function a() { g = Object.create(null) } function l(e) { return 0 | (e.ctrlKey ? 1 : 0) | (e.altKey ? 2 : 0) | (e.shiftKey ? 4 : 0) | (e.metaKey ? 8 : 0) } var i, h, c = e("./keys"), d = e("./useragent"), g = null, f = 0, m = (o.prototype.destroy = function () { p(this.elem, this.type, this.callback), this.elem = this.type = this.callback = void 0 }, u.addListener = function (e, t, i, n) { e.addEventListener(t, i, s()), n && n.$toDestroy.push(new o(e, t, i)) }), p = u.removeListener = function (e, t, i) { e.removeEventListener(t, i, s()) }; u.stopEvent = function (e) { return u.stopPropagation(e), u.preventDefault(e), !1 }, u.stopPropagation = function (e) { e.stopPropagation && e.stopPropagation() }, u.preventDefault = function (e) { e.preventDefault && e.preventDefault() }, u.getButton = function (e) { return "dblclick" == e.type ? 0 : "contextmenu" == e.type || d.isMac && e.ctrlKey && !e.altKey && !e.shiftKey ? 2 : e.button }, u.capture = function (e, t, i) { function n(e) { t && t(e), i && i(e), p(s, "mousemove", t), p(s, "mouseup", n), p(s, "dragstart", n) } var s = e && e.ownerDocument || document; return m(s, "mousemove", t), m(s, "mouseup", n), m(s, "dragstart", n), n }, u.addMouseWheelListener = function (e, n, t) { m(e, "wheel", function (e) { var t = e.deltaX || 0, i = e.deltaY || 0; switch (e.deltaMode) { case e.DOM_DELTA_PIXEL: e.wheelX = .15 * t, e.wheelY = .15 * i; break; case e.DOM_DELTA_LINE: e.wheelX = 15 * t, e.wheelY = 15 * i; break; case e.DOM_DELTA_PAGE: e.wheelX = 150 * t, e.wheelY = 150 * i }n(e) }, t) }, u.addMultiMouseDownListener = function (e, i, n, s, t) { function o(e) { var t; if (0 !== u.getButton(e) ? h = 0 : 1 < e.detail ? 4 < ++h && (h = 1) : h = 1, d.isIE && (t = 5 < Math.abs(e.clientX - r) || 5 < Math.abs(e.clientY - a), l && !t || (h = 1), l && clearTimeout(l), l = setTimeout(function () { l = null }, i[h - 1] || 600), 1 == h && (r = e.clientX, a = e.clientY)), e._clicks = h, n[s]("mousedown", e), 4 < h) h = 0; else if (1 < h) return n[s](c[h], e) } var r, a, l, h = 0, c = { 2: "dblclick", 3: "tripleclick", 4: "quadclick" }; (e = Array.isArray(e) ? e : [e]).forEach(function (e) { m(e, "mousedown", o, t) }) }; u.getModifierString = function (e) { return c.KEY_MODS[l(e)] }, u.addCommandKeyListener = function (e, i, t) { var n, s; d.isOldGecko || d.isOpera && !("KeyboardEvent" in window) ? (n = null, m(e, "keydown", function (e) { n = e.keyCode }, t), m(e, "keypress", function (e) { return r(i, e, n) }, t)) : (s = null, m(e, "keydown", function (e) { g[e.keyCode] = (g[e.keyCode] || 0) + 1; var t = r(i, e, e.keyCode); return s = e.defaultPrevented, t }, t), m(e, "keypress", function (e) { s && (e.ctrlKey || e.altKey || e.shiftKey || e.metaKey) && (u.stopEvent(e), s = null) }, t), m(e, "keyup", function (e) { g[e.keyCode] = null }, t), g || (a(), m(window, "focus", a))) }, "object" == typeof window && window.postMessage && !d.isOldIE && (h = 1, u.nextTick = function (t, i) { i = i || window; function n(e) { e.data == s && (u.stopPropagation(e), p(i, "message", n), t()) } var s = "zero-timeout-message-" + h++; m(i, "message", n), i.postMessage(s, "*") }), u.$idleBlocked = !1, u.onIdle = function (t, e) { return setTimeout(function e() { u.$idleBlocked ? setTimeout(e, 100) : t() }, e) }, u.$idleBlockId = null, u.blockIdle = function (e) { u.$idleBlockId && clearTimeout(u.$idleBlockId), u.$idleBlocked = !0, u.$idleBlockId = setTimeout(function () { u.$idleBlocked = !1 }, e || 100) }, u.nextFrame = "object" == typeof window && (window.requestAnimationFrame || window.mozRequestAnimationFrame || window.webkitRequestAnimationFrame || window.msRequestAnimationFrame || window.oRequestAnimationFrame), u.nextFrame ? u.nextFrame = u.nextFrame.bind(window) : u.nextFrame = function (e) { setTimeout(e, 17) } }), define("ace/range", ["require", "exports", "module"], function (e, t, i) { "use strict"; function o(e, t, i, n) { this.start = { row: e, column: t }, this.end = { row: i, column: n } } (function () { this.isEqual = function (e) { return this.start.row === e.start.row && this.end.row === e.end.row && this.start.column === e.start.column && this.end.column === e.end.column }, this.toString = function () { return "Range: [" + this.start.row + "/" + this.start.column + "] -> [" + this.end.row + "/" + this.end.column + "]" }, this.contains = function (e, t) { return 0 == this.compare(e, t) }, this.compareRange = function (e) { var t = e.end, e = e.start, t = this.compare(t.row, t.column); return 1 == t ? 1 == (t = this.compare(e.row, e.column)) ? 2 : 0 == t ? 1 : 0 : -1 == t ? -2 : -1 == (t = this.compare(e.row, e.column)) ? -1 : 1 == t ? 42 : 0 }, this.comparePoint = function (e) { return this.compare(e.row, e.column) }, this.containsRange = function (e) { return 0 == this.comparePoint(e.start) && 0 == this.comparePoint(e.end) }, this.intersects = function (e) { e = this.compareRange(e); return -1 == e || 0 == e || 1 == e }, this.isEnd = function (e, t) { return this.end.row == e && this.end.column == t }, this.isStart = function (e, t) { return this.start.row == e && this.start.column == t }, this.setStart = function (e, t) { "object" == typeof e ? (this.start.column = e.column, this.start.row = e.row) : (this.start.row = e, this.start.column = t) }, this.setEnd = function (e, t) { "object" == typeof e ? (this.end.column = e.column, this.end.row = e.row) : (this.end.row = e, this.end.column = t) }, this.inside = function (e, t) { return 0 == this.compare(e, t) && (!this.isEnd(e, t) && !this.isStart(e, t)) }, this.insideStart = function (e, t) { return 0 == this.compare(e, t) && !this.isEnd(e, t) }, this.insideEnd = function (e, t) { return 0 == this.compare(e, t) && !this.isStart(e, t) }, this.compare = function (e, t) { return this.isMultiLine() || e !== this.start.row ? e < this.start.row ? -1 : e > this.end.row ? 1 : this.start.row === e ? t >= this.start.column ? 0 : -1 : this.end.row !== e || t <= this.end.column ? 0 : 1 : t < this.start.column ? -1 : t > this.end.column ? 1 : 0 }, this.compareStart = function (e, t) { return this.start.row == e && this.start.column == t ? -1 : this.compare(e, t) }, this.compareEnd = function (e, t) { return this.end.row == e && this.end.column == t ? 1 : this.compare(e, t) }, this.compareInside = function (e, t) { return this.end.row == e && this.end.column == t ? 1 : this.start.row == e && this.start.column == t ? -1 : this.compare(e, t) }, this.clipRows = function (e, t) { var i, n; return this.end.row > t ? i = { row: t + 1, column: 0 } : this.end.row < e && (i = { row: e, column: 0 }), this.start.row > t ? n = { row: t + 1, column: 0 } : this.start.row < e && (n = { row: e, column: 0 }), o.fromPoints(n || this.start, i || this.end) }, this.extend = function (e, t) { var i, n, s = this.compare(e, t); return 0 == s ? this : (-1 == s ? i = { row: e, column: t } : n = { row: e, column: t }, o.fromPoints(i || this.start, n || this.end)) }, this.isEmpty = function () { return this.start.row === this.end.row && this.start.column === this.end.column }, this.isMultiLine = function () { return this.start.row !== this.end.row }, this.clone = function () { return o.fromPoints(this.start, this.end) }, this.collapseRows = function () { return 0 == this.end.column ? new o(this.start.row, 0, Math.max(this.start.row, this.end.row - 1), 0) : new o(this.start.row, 0, this.end.row, 0) }, this.toScreenRange = function (e) { var t = e.documentToScreenPosition(this.start), e = e.documentToScreenPosition(this.end); return new o(t.row, t.column, e.row, e.column) }, this.moveBy = function (e, t) { this.start.row += e, this.start.column += t, this.end.row += e, this.end.column += t } }).call(o.prototype), o.fromPoints = function (e, t) { return new o(e.row, e.column, t.row, t.column) }, o.comparePoints = function (e, t) { return e.row - t.row || e.column - t.column }, o.comparePoints = function (e, t) { return e.row - t.row || e.column - t.column }, t.Range = o }), define("ace/clipboard", ["require", "exports", "module"], function (e, t, i) { "use strict"; var n; i.exports = { lineMode: !1, pasteCancelled: function () { return !!(n && n > Date.now() - 50) || (n = !1) }, cancel: function () { n = Date.now() } } }), define("ace/keyboard/textinput", ["require", "exports", "module", "ace/lib/event", "ace/lib/useragent", "ace/lib/dom", "ace/lib/lang", "ace/clipboard", "ace/lib/keys"], function (e, t, i) { "use strict"; var D = e("../lib/event"), B = e("../lib/useragent"), P = e("../lib/dom"), N = e("../lib/lang"), V = e("../clipboard"), z = B.isChrome < 18, U = B.isIE, G = 63 < B.isChrome, K = e("../lib/keys"), j = K.KEY_MODS, q = B.isIOS, Y = q ? /\s/ : /\n/, X = B.isMobile; t.TextInput = function (e, u) { function t() { i = !0, d.blur(), d.focus(), i = !1 } function a() { clearTimeout(E), E = setTimeout(function () { l && (d.style.cssText = l, l = ""), u.renderer.$isMousePressed = !1, u.renderer.$keepTextAreaAtCursor && u.renderer.$moveTextAreaToCursor() }, 0) } var d = P.createElement("textarea"), o = (d.className = "ace_text-input", d.setAttribute("wrap", "off"), d.setAttribute("autocorrect", "off"), d.setAttribute("autocapitalize", "off"), d.setAttribute("spellcheck", !1), d.style.opacity = "0", e.insertBefore(d, e.firstChild), !1), g = !1, r = !1, f = !1, l = "", m = (X || (d.style.fontSize = "1px"), !1), i = !1, p = "", w = 0, v = 0, $ = 0; try { var h = document.activeElement === d } catch (e) { } D.addListener(d, "blur", function (e) { i || (u.onBlur(e), h = !1) }, u), D.addListener(d, "focus", function (e) { if (!i) { if (h = !0, B.isEdge) try { if (!document.hasFocus()) return } catch (e) { } u.onFocus(e), B.isEdge ? setTimeout(b) : b() } }, u), this.$focusScroll = !1, this.focus = function () { if (l || G || "browser" == this.$focusScroll) return d.focus({ preventScroll: !0 }); var e = d.style.top; d.style.position = "fixed", d.style.top = "0px"; try { var t = 0 != d.getBoundingClientRect().top } catch (e) { return } var i = []; if (t) for (var n = d.parentElement; n && 1 == n.nodeType;)i.push(n), n.setAttribute("ace_nocontext", !0), n = !n.parentElement && n.getRootNode ? n.getRootNode().host : n.parentElement; d.focus({ preventScroll: !0 }), t && i.forEach(function (e) { e.removeAttribute("ace_nocontext") }), setTimeout(function () { d.style.position = "", "0px" == d.style.top && (d.style.top = e) }, 0) }, this.blur = function () { d.blur() }, this.isFocused = function () { return h }, u.on("beforeEndOperation", function () { var e = u.curOp, t = e && e.command && e.command.name; "insertstring" != t && (t = t && (e.docChanged || e.selectionChanged), r && t && (p = d.value = "", R()), b()) }); function n(e) { A(e, !0) } function s(e) { A(e, !1) } function c(e) { var t = x(e); V.pasteCancelled() || ("string" == typeof t ? (t && u.onPaste(t, e), B.isIE && setTimeout(b), D.preventDefault(e)) : (d.value = "", g = !0)) } var b = q ? function (e) { var t; !h || o && !e || f || ((t = "\n ab" + (e = e || "") + "cde fg\n") != d.value && (d.value = p = t), t = 4 + (e.length || (u.selection.isEmpty() ? 0 : 1)), 4 == w && v == t || d.setSelectionRange(4, t), w = 4, v = t) } : function () { if (!r && !f && (h || C)) { r = !0; var e, t, i = 0, n = 0, s = "", o = (u.session && (o = (e = u.selection).getRange(), e = e.cursor.row, i = o.start.column, n = o.end.column, s = u.session.getLine(e), o.start.row != e ? (t = u.session.getLine(e - 1), i = o.start.row < e - 1 ? 0 : i, n += t.length + 1, s = t + "\n" + s) : o.end.row != e ? (t = u.session.getLine(e + 1), n = o.end.row > e + 1 ? t.length : n, n += s.length + 1, s = s + "\n" + t) : X && 0 < e && (s = "\n" + s, n += 1, i += 1), 400 < s.length && (i < 400 && n < 400 ? s = s.slice(0, 400) : (s = "\n", i == n ? i = n = 0 : (i = 0, n = 1)))), s + "\n\n"); if (o != p && (d.value = p = o, w = v = o.length), C && (w = d.selectionStart, v = d.selectionEnd), v != n || w != i || d.selectionEnd != v) try { d.setSelectionRange(i, n), w = i, v = n } catch (e) { } r = !1 } }, O = (this.resetSelection = b, h && u.onFocus(), function (e) { return 0 === e.selectionStart && e.selectionEnd >= p.length && e.value === p && p && e.selectionEnd !== v }), y = null, C = (this.setInputHandler = function (e) { y = e }, !(this.getInputHandler = function () { return y })), S = function (e, t) { if (C = C && !1, g) return b(), e && u.onPaste(e), g = !1, ""; for (var i = d.selectionStart, n = d.selectionEnd, s = w, o = p.length - v, r = e, a = e.length - i, l = e.length - n, h = 0; 0 < s && p[h] == e[h];)h++, s--; for (r = r.slice(h), h = 1; 0 < o && p.length - h > w - 1 && p[p.length - h] == e[e.length - h];)h++, o--; a -= h - 1, l -= h - 1; var c = r.length - h + 1; if (c < 0 && (s = -c, c = 0), r = r.slice(0, c), !(t || r || a || s || o || l)) return ""; c = !(f = !0); return B.isAndroid && ". " == r && (r = "  ", c = !0), r && !s && !o && !a && !l || m ? u.onTextInput(r) : u.onTextInput(r, { extendLeft: s, extendRight: o, restoreStart: a, restoreEnd: l }), f = !1, p = e, w = i, v = n, $ = l, c ? "\n" : r }, k = function (e) { if (r) return L(); if (e && e.inputType) { if ("historyUndo" == e.inputType) return u.execCommand("undo"); if ("historyRedo" == e.inputType) return u.execCommand("redo") } var e = d.value, t = S(e, !0); (500 < e.length || Y.test(t) || X && w < 1 && w == v) && b() }, x = function (e, t, i) { var n = e.clipboardData || window.clipboardData; if (n && !z) { var s = U || i ? "Text" : "text/plain"; try { return t ? !1 !== n.setData(s, t) : n.getData(s) } catch (e) { if (!i) return x(e, t, !0) } } }, A = function (e, t) { var i = u.getCopyText(); if (!i) return D.preventDefault(e); x(e, i) ? (q && (b(i), o = i, setTimeout(function () { o = !1 }, 10)), t ? u.onCut() : u.onCopy(), D.preventDefault(e)) : (o = !0, d.value = i, d.select(), setTimeout(function () { o = !1, b(), t ? u.onCut() : u.onCopy() })) }, L = (D.addCommandKeyListener(d, u.onCommandKey.bind(u), u), D.addListener(d, "select", function (e) { r || (o ? o = !1 : O(d) ? (u.selectAll(), b()) : X && d.selectionStart != w && b()) }, u), D.addListener(d, "input", k, u), D.addListener(d, "cut", n, u), D.addListener(d, "copy", s, u), D.addListener(d, "paste", c, u), "oncut" in d && "oncopy" in d && "onpaste" in d || D.addListener(e, "keydown", function (e) { if ((!B.isMac || e.metaKey) && e.ctrlKey) switch (e.keyCode) { case 67: s(e); break; case 86: c(e); break; case 88: n(e) } }, u), function () { var e; if (r && u.onCompositionUpdate && !u.$readOnly) return m ? t() : void (r.useTextareaForIME ? u.onCompositionUpdate(d.value) : (e = d.value, S(e), r.markerRange && (r.context && (r.markerRange.start.column = r.selectionStart = r.context.compositionStartOffset), r.markerRange.end.column = r.markerRange.start.column + v - r.selectionStart + $))) }), R = function (e) { u.onCompositionEnd && !u.$readOnly && (r = !1, u.onCompositionEnd(), u.off("mousedown", t), e && k()) }, M = N.delayedCall(L, 50).schedule.bind(null, null); D.addListener(d, "compositionstart", function (e) { r || !u.onCompositionStart || u.$readOnly || (r = {}, m || (e.data && (r.useTextareaForIME = !1), setTimeout(L, 0), u._signal("compositionStart"), u.on("mousedown", t), (e = u.getSelectionRange()).end.row = e.start.row, e.end.column = e.start.column, r.markerRange = e, r.selectionStart = w, u.onCompositionStart(r), r.useTextareaForIME ? (p = d.value = "", v = w = 0) : (d.msGetInputContext && (r.context = d.msGetInputContext()), d.getInputContext && (r.context = d.getInputContext())))) }, u), D.addListener(d, "compositionupdate", L, u), D.addListener(d, "keyup", function (e) { 27 == e.keyCode && d.value.length < d.selectionStart && (r || (p = d.value), w = v = -1, b()), M() }, u), D.addListener(d, "keydown", M, u), D.addListener(d, "compositionend", R, u), this.getElement = function () { return d }, this.setCommandMode = function (e) { m = e, d.readOnly = !1 }, this.setReadOnly = function (e) { m || (d.readOnly = e) }, this.setCopyWithEmptySelection = function (e) { }, this.onContextMenu = function (e) { C = !0, b(), u._emit("nativecontextmenu", { target: u, domEvent: e }), this.moveToMouse(e, !0) }, this.moveToMouse = function (e, t) { l = l || d.style.cssText, d.style.cssText = (t ? "z-index:100000;" : "") + (B.isIE ? "opacity:0.1;" : "") + "text-indent: -" + (w + v) * u.renderer.characterWidth * .5 + "px;"; function i(e) { P.translate(d, e.clientX - o - 2, Math.min(e.clientY - s - 2, r)) } var t = u.container.getBoundingClientRect(), n = P.computedStyle(u.container), s = t.top + (parseInt(n.borderTopWidth) || 0), o = t.left + (parseInt(t.borderLeftWidth) || 0), r = t.bottom - s - d.clientHeight - 2; i(e), "mousedown" == e.type && (u.renderer.$isMousePressed = !0, clearTimeout(E), B.isWin && D.capture(u.container, i, a)) }, this.onContextMenuClose = a; function T(e) { u.textInput.onContextMenu(e), a() } var E, _, I, F, W; function H(e) { var t, i, n, s; document.activeElement !== I || W || r || _.$mouseHandler.isMousePressed || o || (t = I.selectionStart, i = I.selectionEnd, n = null, (s = 0) == t ? n = K.up : 1 == t ? n = K.home : v < i && "\n" == p[i] ? n = K.end : t < w && " " == p[t - 1] ? (n = K.left, s = j.option) : t < w || t == w && v != w && t == i ? n = K.left : v < i && 2 < p.slice(0, i).split("\n").length ? n = K.down : v < i && " " == p[i - 1] ? (n = K.right, s = j.option) : (v < i || i == v && v != w && t == i) && (n = K.right), t !== i && (s |= j.shift), n && (!_.onCommandKey({}, s, n) && _.commands && (n = K.keyCodeToString(n), (s = _.commands.findKeyCommand(s, n)) && _.execCommand(s)), w = t, v = i, b(""))) } D.addListener(d, "mouseup", T, u), D.addListener(d, "mousedown", function (e) { e.preventDefault(), a() }, u), D.addListener(u.renderer.scroller, "contextmenu", T, u), D.addListener(d, "contextmenu", T, u), q && (_ = u, F = null, W = !1, (I = d).addEventListener("keydown", function (e) { F && clearTimeout(F), W = !0 }, !0), I.addEventListener("keyup", function (e) { F = setTimeout(function () { W = !1 }, 100) }, !0), document.addEventListener("selectionchange", H), _.on("destroy", function () { document.removeEventListener("selectionchange", H) })), this.destroy = function () { d.parentElement && d.parentElement.removeChild(d) } }, t.$setUserAgentForTests = function (e, t) { X = e, q = t } }), define("ace/mouse/default_handlers", ["require", "exports", "module", "ace/lib/useragent"], function (e, t, i) { "use strict"; function n(t) { t.$clickSelection = null; var e = t.editor; e.setDefaultHandler("mousedown", this.onMouseDown.bind(t)), e.setDefaultHandler("dblclick", this.onDoubleClick.bind(t)), e.setDefaultHandler("tripleclick", this.onTripleClick.bind(t)), e.setDefaultHandler("quadclick", this.onQuadClick.bind(t)), e.setDefaultHandler("mousewheel", this.onMouseWheel.bind(t));["select", "startSelect", "selectEnd", "selectAllEnd", "selectByWordsEnd", "selectByLinesEnd", "dragWait", "dragWaitEnd", "focusWait"].forEach(function (e) { t[e] = this[e] }, this), t.selectByLines = this.extendSelectionBy.bind(t, "getLineRange"), t.selectByWords = this.extendSelectionBy.bind(t, "getWordRange") } function r(e, t) { return (e.start.row == e.end.row ? 2 * t.column - e.start.column - e.end.column : e.start.row != e.end.row - 1 || e.start.column || e.end.column ? 2 * t.row - e.start.row - e.end.row : t.column - 4) < 0 ? { cursor: e.start, anchor: e.end } : { cursor: e.end, anchor: e.start } } var o = e("../lib/useragent"); (function () { this.onMouseDown = function (e) { var t = e.inSelection(), i = e.getDocumentPosition(), n = (this.mousedownEvent = e, this.editor), s = e.getButton(); return 0 !== s ? (!n.getSelectionRange().isEmpty() && 1 != s || n.selection.moveToPosition(i), void (2 == s && (n.textInput.onContextMenu(e.domEvent), o.isMozilla || e.preventDefault()))) : (this.mousedownEvent.time = Date.now(), !t || n.isFocused() || (n.focus(), !this.$focusTimeout || this.$clickSelection || n.inMultiSelectMode) ? (this.captureMouse(e), this.startSelect(i, 1 < e.domEvent._clicks), e.preventDefault()) : (this.setState("focusWait"), void this.captureMouse(e))) }, this.startSelect = function (e, t) { e = e || this.editor.renderer.screenToTextCoordinates(this.x, this.y); var i = this.editor; this.mousedownEvent && (this.mousedownEvent.getShiftKey() ? i.selection.selectToPosition(e) : t || i.selection.moveToPosition(e), t || this.select(), i.renderer.scroller.setCapture && i.renderer.scroller.setCapture(), i.setStyle("ace_selecting"), this.setState("select")) }, this.select = function () { var e, t = this.editor, i = t.renderer.screenToTextCoordinates(this.x, this.y); this.$clickSelection && (e = -1 == (e = this.$clickSelection.comparePoint(i)) ? this.$clickSelection.end : 1 == e ? this.$clickSelection.start : (i = (e = r(this.$clickSelection, i)).cursor, e.anchor), t.selection.setSelectionAnchor(e.row, e.column)), t.selection.selectToPosition(i), t.renderer.scrollCursorIntoView() }, this.extendSelectionBy = function (e) { var t, i, n, s = this.editor, o = s.renderer.screenToTextCoordinates(this.x, this.y), e = s.selection[e](o.row, o.column); this.$clickSelection && (i = this.$clickSelection.comparePoint(e.start), t = this.$clickSelection.comparePoint(e.end), -1 == i && t <= 0 ? (n = this.$clickSelection.end, e.end.row == o.row && e.end.column == o.column || (o = e.start)) : 1 == t && 0 <= i ? (n = this.$clickSelection.start, e.start.row == o.row && e.start.column == o.column || (o = e.end)) : n = -1 == i && 1 == t ? (o = e.end, e.start) : (o = (i = r(this.$clickSelection, o)).cursor, i.anchor), s.selection.setSelectionAnchor(n.row, n.column)), s.selection.selectToPosition(o), s.renderer.scrollCursorIntoView() }, this.selectEnd = this.selectAllEnd = this.selectByWordsEnd = this.selectByLinesEnd = function () { this.$clickSelection = null, this.editor.unsetStyle("ace_selecting"), this.editor.renderer.scroller.releaseCapture && this.editor.renderer.scroller.releaseCapture() }, this.focusWait = function () { n = this.mousedownEvent.x, e = this.mousedownEvent.y, i = this.x, t = this.y; var e, t, i = Math.sqrt(Math.pow(i - n, 2) + Math.pow(t - e, 2)), n = Date.now(); (0 < i || n - this.mousedownEvent.time > this.$focusTimeout) && this.startSelect(this.mousedownEvent.getDocumentPosition()) }, this.onDoubleClick = function (e) { var e = e.getDocumentPosition(), t = this.editor, i = t.session.getBracketRange(e); i ? (i.isEmpty() && (i.start.column--, i.end.column++), this.setState("select")) : (i = t.selection.getWordRange(e.row, e.column), this.setState("selectByWords")), this.$clickSelection = i, this.select() }, this.onTripleClick = function (e) { var e = e.getDocumentPosition(), t = this.editor, i = (this.setState("selectByLines"), t.getSelectionRange()); i.isMultiLine() && i.contains(e.row, e.column) ? (this.$clickSelection = t.selection.getLineRange(i.start.row), this.$clickSelection.end = t.selection.getLineRange(i.end.row).end) : this.$clickSelection = t.selection.getLineRange(e.row), this.select() }, this.onQuadClick = function (e) { var t = this.editor; t.selectAll(), this.$clickSelection = t.getSelectionRange(), this.setState("selectAll") }, this.onMouseWheel = function (e) { var t, i, n, s, o, r, a; if (!e.getAccelKey()) return e.getShiftKey() && e.wheelY && !e.wheelX && (e.wheelX = e.wheelY, e.wheelY = 0), t = this.editor, this.$lastScroll || (this.$lastScroll = { t: 0, vx: 0, vy: 0, allowed: 0 }), i = this.$lastScroll, n = e.domEvent.timeStamp, r = n - i.t, s = r ? e.wheelX / r : i.vx, o = r ? e.wheelY / r : i.vy, r < 550 && (s = (s + i.vx) / 2, o = (o + i.vy) / 2), r = Math.abs(s / o), a = !1, 1 <= r && t.renderer.isScrollableBy(e.wheelX * e.speed, 0) && (a = !0), (a = r <= 1 && t.renderer.isScrollableBy(0, e.wheelY * e.speed) ? !0 : a) ? i.allowed = n : n - i.allowed < 550 && (Math.abs(s) <= 1.5 * Math.abs(i.vx) && Math.abs(o) <= 1.5 * Math.abs(i.vy) ? (a = !0, i.allowed = n) : i.allowed = 0), i.t = n, i.vx = s, i.vy = o, a ? (t.renderer.scrollBy(e.wheelX * e.speed, e.wheelY * e.speed), e.stop()) : void 0 } }).call(n.prototype), t.DefaultHandlers = n }), define("ace/tooltip", ["require", "exports", "module", "ace/lib/oop", "ace/lib/dom"], function (e, t, i) { "use strict"; function n(e) { this.isOpen = !1, this.$element = null, this.$parentNode = e } e("./lib/oop"); var s = e("./lib/dom"), o = "ace_tooltip"; (function () { this.$init = function () { return this.$element = s.createElement("div"), this.$element.className = o, this.$element.style.display = "none", this.$parentNode.appendChild(this.$element), this.$element }, this.getElement = function () { return this.$element || this.$init() }, this.setText = function (e) { this.getElement().textContent = e }, this.setHtml = function (e) { this.getElement().innerHTML = e }, this.setPosition = function (e, t) { this.getElement().style.left = e + "px", this.getElement().style.top = t + "px" }, this.setClassName = function (e) { s.addCssClass(this.getElement(), e) }, this.show = function (e, t, i) { null != e && this.setText(e), null != t && null != i && this.setPosition(t, i), this.isOpen || (this.getElement().style.display = "block", this.isOpen = !0) }, this.hide = function () { this.isOpen && (this.getElement().style.display = "none", this.getElement().className = o, this.isOpen = !1) }, this.getHeight = function () { return this.getElement().offsetHeight }, this.getWidth = function () { return this.getElement().offsetWidth }, this.destroy = function () { this.isOpen = !1, this.$element && this.$element.parentNode && this.$element.parentNode.removeChild(this.$element) } }).call(n.prototype), t.Tooltip = n }), define("ace/mouse/default_gutter_handler", ["require", "exports", "module", "ace/lib/dom", "ace/lib/oop", "ace/lib/event", "ace/tooltip"], function (e, t, i) { "use strict"; function u(e) { r.call(this, e) } var d = e("../lib/dom"), n = e("../lib/oop"), g = e("../lib/event"), r = e("../tooltip").Tooltip; n.inherits(u, r), function () { this.setPosition = function (e, t) { var i = window.innerWidth || document.documentElement.clientWidth, n = window.innerHeight || document.documentElement.clientHeight, s = this.getWidth(), o = this.getHeight(); i < (e += 15) + s && (e -= e + s - i), n < (t += 15) + o && (t -= 20 + o), r.prototype.setPosition.call(this, e, t) } }.call(u.prototype), t.GutterHandler = function (n) { function s() { i = i && clearTimeout(i), a && (c.hide(), a = null, l._signal("hideGutterTooltip", c), l.off("mousewheel", s)) } function o(e) { c.setPosition(e.x, e.y) } var i, r, a, l = n.editor, h = l.renderer.$gutterLayer, c = new u(l.container); n.editor.setDefaultHandler("guttermousedown", function (e) { if (l.isFocused() && 0 == e.getButton()) { var t = h.getRegion(e); if ("foldWidgets" != t) { var t = e.getDocumentPosition().row, i = l.session.selection; if (e.getShiftKey()) i.selectTo(t, 0); else { if (2 == e.domEvent.detail) return l.selectAll(), e.preventDefault(); n.$clickSelection = l.selection.getLineRange(t) } return n.setState("selectByLines"), n.captureMouse(e), e.preventDefault() } } }), n.editor.setDefaultHandler("guttermousemove", function (e) { var t = e.domEvent.target || e.domEvent.srcElement; if (d.hasCssClass(t, "ace_fold-widget")) return s(); a && n.$tooltipFollowsMouse && o(e), r = e, i = i || setTimeout(function () { i = null, (r && !n.isMousePressed ? function () { var e = r.getDocumentPosition().row, t = h.$annotations[e]; if (!t) return s(); if (e == l.session.getLength()) { var e = l.renderer.pixelToScreenCoordinates(0, r.y).row, i = r.$pos; if (e > l.session.documentToScreenRow(i.row, i.column)) return s() } a != t && (a = t.text.join("<br/>"), c.setHtml(a), (e = t.className) && c.setClassName(e.trim()), c.show(), l._signal("showGutterTooltip", c), l.on("mousewheel", s), n.$tooltipFollowsMouse ? o(r) : (i = r.domEvent.target.getBoundingClientRect(), (t = c.getElement().style).left = i.right + "px", t.top = i.bottom + "px")) } : s)() }, 50) }), g.addListener(l.renderer.$gutter, "mouseout", function (e) { r = null, a && !i && (i = setTimeout(function () { i = null, s() }, 50)) }, l), l.on("changeSession", s) } }), define("ace/mouse/mouse_event", ["require", "exports", "module", "ace/lib/event", "ace/lib/useragent"], function (e, t, i) { "use strict"; var n = e("../lib/event"), s = e("../lib/useragent"), e = t.MouseEvent = function (e, t) { this.domEvent = e, this.editor = t, this.x = this.clientX = e.clientX, this.y = this.clientY = e.clientY, this.$pos = null, this.$inSelection = null, this.propagationStopped = !1, this.defaultPrevented = !1 }; !function () { this.stopPropagation = function () { n.stopPropagation(this.domEvent), this.propagationStopped = !0 }, this.preventDefault = function () { n.preventDefault(this.domEvent), this.defaultPrevented = !0 }, this.stop = function () { this.stopPropagation(), this.preventDefault() }, this.getDocumentPosition = function () { return this.$pos || (this.$pos = this.editor.renderer.screenToTextCoordinates(this.clientX, this.clientY), this.$pos) }, this.inSelection = function () { if (null !== this.$inSelection) return this.$inSelection; var e, t = this.editor.getSelectionRange(); return t.isEmpty() ? this.$inSelection = !1 : (e = this.getDocumentPosition(), this.$inSelection = t.contains(e.row, e.column)), this.$inSelection }, this.getButton = function () { return n.getButton(this.domEvent) }, this.getShiftKey = function () { return this.domEvent.shiftKey }, this.getAccelKey = s.isMac ? function () { return this.domEvent.metaKey } : function () { return this.domEvent.ctrlKey } }.call(e.prototype) }), define("ace/mouse/dragdrop_handler", ["require", "exports", "module", "ace/lib/dom", "ace/lib/event", "ace/lib/useragent"], function (e, t, i) { "use strict"; function n(t) { function e() { var e, t, i, n, s, o, r, a, l = d; d = v.renderer.screenToTextCoordinates(h, c), s = d, o = l, r = Date.now(), a = !o || s.row != o.row, o = !o || s.column != o.column, !p || a || o ? (v.moveCursorToPosition(s), p = r, w = { x: h, y: c }) : 5 < S(w.x, w.y, h, c) ? p = null : 200 <= r - p && (v.renderer.scrollCursorIntoView(), p = null), a = d, o = l, s = Date.now(), r = v.renderer.layerConfig.lineHeight, l = v.renderer.layerConfig.characterWidth, e = v.renderer.scroller.getBoundingClientRect(), e = { x: { left: h - e.left, right: e.right - h }, y: { top: c - e.top, bottom: e.bottom - c } }, t = Math.min(e.x.left, e.x.right), i = Math.min(e.y.top, e.y.bottom), n = { row: a.row, column: a.column }, t / l <= 2 && (n.column += e.x.left < e.x.right ? -3 : 2), i / r <= 1 && (n.row += e.y.top < e.y.bottom ? -1 : 1), t = a.row != n.row, l = a.column != n.column, i = !o || a.row != o.row, t || l && !i ? m ? 200 <= s - m && v.renderer.scrollCursorIntoView(n) : m = s : m = null } function i() { u = v.selection.toOrientedRange(), a = v.session.addMarker(u, "ace_selection", v.getSelectionStyle()), v.clearSelection(), v.isFocused() && v.renderer.$cursorLayer.setBlinking(!1), clearInterval(l), e(), l = setInterval(e, 20), y = 0, x.addListener(document, "mousemove", s) } function n() { clearInterval(l), v.session.removeMarker(a), a = null, v.selection.fromOrientedRange(u), v.isFocused() && !f && v.$resetCursorStyle(), y = 0, p = m = d = u = null, x.removeListener(document, "mousemove", s) } function s() { null == C && (C = setTimeout(function () { null != C && a && n() }, 20)) } function o(e) { e = e.types; return !e || Array.prototype.some.call(e, function (e) { return "text/plain" == e || "Text" == e }) } function r(e) { var t = ["copy", "copymove", "all", "uninitialized"], i = A.isMac ? e.altKey : e.ctrlKey, n = "uninitialized"; try { n = e.dataTransfer.effectAllowed.toLowerCase() } catch (e) { } var s = "none"; return i && 0 <= t.indexOf(n) ? s = "copy" : 0 <= ["move", "copymove", "linkmove", "all", "uninitialized"].indexOf(n) ? s = "move" : 0 <= t.indexOf(n) && (s = "copy"), s } var a, h, c, l, u, d, g, f, m, p, w, v = t.editor, $ = k.createElement("div"), b = ($.style.cssText = "top:-100px;position:absolute;z-index:2147483647;opacity:0.5", $.textContent = " ", ["dragWait", "dragWaitEnd", "startDrag", "dragReadyEnd", "onMouseDrag"].forEach(function (e) { t[e] = this[e] }, this), v.on("mousedown", this.onMouseDown.bind(t)), v.container), y = 0, C = (this.onDragStart = function (e) { var t; if (this.cancelDrag || !b.draggable) return t = this, setTimeout(function () { t.startSelect(), t.captureMouse(e) }, 0), e.preventDefault(); u = v.getSelectionRange(); var i = e.dataTransfer; i.effectAllowed = v.getReadOnly() ? "copy" : "copyMove", v.container.appendChild($), i.setDragImage && i.setDragImage($, 0, 0), setTimeout(function () { v.container.removeChild($) }), i.clearData(), i.setData("Text", v.session.getTextRange()), f = !0, this.setState("drag") }, this.onDragEnd = function (e) { b.draggable = !1, f = !1, this.setState(null), v.getReadOnly() || (e = e.dataTransfer.dropEffect, g || "move" != e || v.session.remove(v.getSelectionRange()), v.$resetCursorStyle()), this.editor.unsetStyle("ace_dragging"), this.editor.renderer.setCursorStyle("") }, this.onDragEnter = function (e) { if (!v.getReadOnly() && o(e.dataTransfer)) return h = e.clientX, c = e.clientY, a || i(), y++, e.dataTransfer.dropEffect = g = r(e), x.preventDefault(e) }, this.onDragOver = function (e) { if (!v.getReadOnly() && o(e.dataTransfer)) return h = e.clientX, c = e.clientY, a || (i(), y++), null !== C && (C = null), e.dataTransfer.dropEffect = g = r(e), x.preventDefault(e) }, this.onDragLeave = function (e) { if (--y <= 0 && a) return n(), g = null, x.preventDefault(e) }, this.onDrop = function (e) { if (d) { var t = e.dataTransfer; if (f) switch (g) { case "move": u = u.contains(d.row, d.column) ? { start: d, end: d } : v.moveText(u, d); break; case "copy": u = v.moveText(u, d, !0) } else { t = t.getData("Text"); u = { start: d, end: v.session.insert(d, t) }, v.focus(), g = null } return n(), x.preventDefault(e) } }, x.addListener(b, "dragstart", this.onDragStart.bind(t), v), x.addListener(b, "dragend", this.onDragEnd.bind(t), v), x.addListener(b, "dragenter", this.onDragEnter.bind(t), v), x.addListener(b, "dragover", this.onDragOver.bind(t), v), x.addListener(b, "dragleave", this.onDragLeave.bind(t), v), x.addListener(b, "drop", this.onDrop.bind(t), v), null) } function S(e, t, i, n) { return Math.sqrt(Math.pow(i - e, 2) + Math.pow(n - t, 2)) } var k = e("../lib/dom"), x = e("../lib/event"), A = e("../lib/useragent"); (function () { this.dragWait = function () { Date.now() - this.mousedownEvent.time > this.editor.getDragDelay() && this.startDrag() }, this.dragWaitEnd = function () { this.editor.container.draggable = !1, this.startSelect(this.mousedownEvent.getDocumentPosition()), this.selectEnd() }, this.dragReadyEnd = function (e) { this.editor.$resetCursorStyle(), this.editor.unsetStyle("ace_dragging"), this.editor.renderer.setCursorStyle(""), this.dragWaitEnd() }, this.startDrag = function () { this.cancelDrag = !1; var e = this.editor, t = (e.container.draggable = !0, e.renderer.$cursorLayer.setBlinking(!1), e.setStyle("ace_dragging"), A.isWin ? "default" : "move"); e.renderer.setCursorStyle(t), this.setState("dragReady") }, this.onMouseDrag = function (e) { var t = this.editor.container; A.isIE && "dragReady" == this.state && 3 < S(this.mousedownEvent.x, this.mousedownEvent.y, this.x, this.y) && t.dragDrop(), "dragWait" === this.state && 0 < S(this.mousedownEvent.x, this.mousedownEvent.y, this.x, this.y) && (t.draggable = !1, this.startSelect(this.mousedownEvent.getDocumentPosition())) }, this.onMouseDown = function (e) { var t, i, n; this.$dragEnabled && (this.mousedownEvent = e, t = this.editor, i = e.inSelection(), n = e.getButton(), 1 !== (e.domEvent.detail || 1) || 0 !== n || !i || e.editor.inMultiSelectMode && (e.getAccelKey() || e.getShiftKey()) || (this.mousedownEvent.time = Date.now(), "unselectable" in (n = e.domEvent.target || e.domEvent.srcElement) && (n.unselectable = "on"), t.getDragDelay() ? (A.isWebKit && (this.cancelDrag = !0, t.container.draggable = !0), this.setState("dragWait")) : this.startDrag(), this.captureMouse(e, this.onMouseDrag.bind(this)), e.defaultPrevented = !0)) } }).call(n.prototype), t.DragdropHandler = n }), define("ace/mouse/touch_handler", ["require", "exports", "module", "ace/mouse/mouse_event", "ace/lib/event", "ace/lib/dom"], function (e, t, i) { "use strict"; var k = e("./mouse_event").MouseEvent, n = e("../lib/event"), a = e("../lib/dom"); t.addTouchListeners = function (e, c) { function s() { function t(e) { var t, i = e.target.getAttribute("action"); if ("more" == i || !s) return s = !s, e = c.getCopyText(), t = c.session.getUndoManager().hasUndo(), void r.replaceChild(a.buildDom(s ? ["span", !e && ["span", { class: "ace_mobile-button", action: "selectall" }, "Select All"], e && ["span", { class: "ace_mobile-button", action: "copy" }, "Copy"], e && ["span", { class: "ace_mobile-button", action: "cut" }, "Cut"], n && ["span", { class: "ace_mobile-button", action: "paste" }, "Paste"], t && ["span", { class: "ace_mobile-button", action: "undo" }, "Undo"], ["span", { class: "ace_mobile-button", action: "find" }, "Find"], ["span", { class: "ace_mobile-button", action: "openCommandPallete" }, "Palette"]] : ["span"]), r.firstChild); "paste" == i ? n.readText().then(function (e) { c.execCommand(i, e) }) : i && ("cut" != i && "copy" != i || (n ? n.writeText(c.getCopyText()) : document.execCommand("copy")), c.execCommand(i)), r.firstChild.style.display = "none", s = !1, "openCommandPallete" != i && c.focus() } var n = window.navigator && window.navigator.clipboard, s = !1; r = a.buildDom(["div", { class: "ace_mobile-menu", ontouchstart: function (e) { $ = "menu", e.stopPropagation(), e.preventDefault(), c.textInput.focus() }, ontouchend: function (e) { e.stopPropagation(), e.preventDefault(), t(e) }, onclick: t }, ["span"], ["span", { class: "ace_mobile-button", action: "more" }, "..."]], c.container) } function i() { r || s(); var e = c.selection.cursor, e = c.renderer.textToScreenCoordinates(e.row, e.column), t = c.renderer.textToScreenCoordinates(0, 0).pageX, i = c.renderer.scrollLeft, n = c.container.getBoundingClientRect(); r.style.top = e.pageY - n.top - 3 + "px", e.pageX - n.left < n.width - 70 ? (r.style.left = "", r.style.right = "10px") : (r.style.right = "", r.style.left = t + i - n.left + "px"), r.style.display = "", r.firstChild.style.display = "none", c.on("input", o) } function o(e) { r && (r.style.display = "none"), c.off("input", o) } function u() { p = null, clearTimeout(p); var e = c.selection.getRange(), t = e.contains(w.row, w.column); !e.isEmpty() && t || (c.selection.moveToPosition(w), c.selection.selectWord()), $ = "wait", i() } var d, g, f, m, p, t, w, v, r, $ = "scroll", b = 0, y = 0, C = 0, S = 0; n.addListener(e, "contextmenu", function (e) { v && c.textInput.getElement().focus() }, c), n.addListener(e, "touchstart", function (e) { var t = e.touches; if (p || 1 < t.length) return clearTimeout(p), p = null, f = -1, void ($ = "zoom"); v = c.$mouseHandler.isMousePressed = !0; var i = c.renderer.layerConfig.lineHeight, n = c.renderer.layerConfig.lineHeight, s = e.timeStamp, o = (m = s, t[0]), r = o.clientX, o = o.clientY, r = (Math.abs(d - r) + Math.abs(g - o) > i && (f = -1), d = e.clientX = r, g = e.clientY = o, C = S = 0, new k(e, c)); if (w = r.getDocumentPosition(), s - f < 500 && 1 == t.length && !b) y++, e.preventDefault(), e.button = 0, p = null, clearTimeout(p), c.selection.moveToPosition(w), (o = 2 <= y ? c.selection.getLineRange(w.row) : c.session.getBracketRange(w)) && !o.isEmpty() ? c.selection.setRange(o) : c.selection.selectWord(), $ = "wait"; else { y = 0; var r = c.selection.cursor, t = c.selection.isEmpty() ? r : c.selection.anchor, o = c.renderer.$cursorLayer.getPixelPosition(r, !0), r = c.renderer.$cursorLayer.getPixelPosition(t, !0), t = c.renderer.scroller.getBoundingClientRect(), a = c.renderer.layerConfig.offset, l = c.renderer.scrollLeft, h = function (e, t) { return (e /= n) * e + (t = t / i - .75) * t }; if (e.clientX < t.left) return void ($ = "zoom"); o = h(e.clientX - t.left - o.left + l, e.clientY - t.top - o.top + a), h = h(e.clientX - t.left - r.left + l, e.clientY - t.top - r.top + a); o < 3.5 && h < 3.5 && ($ = h < o ? "cursor" : "anchor"), $ = h < 3.5 ? "anchor" : o < 3.5 ? "cursor" : "scroll", p = setTimeout(u, 450) } f = s }, c), n.addListener(e, "touchend", function (e) { v = c.$mouseHandler.isMousePressed = !1, t && clearInterval(t), "zoom" == $ ? ($ = "", b = 0) : p ? (c.selection.moveToPosition(w), b = 0, i()) : "scroll" == $ ? (b += 60, t = setInterval(function () { b-- <= 0 && (clearInterval(t), t = null), Math.abs(C) < .01 && (C = 0), Math.abs(S) < .01 && (S = 0), b < 20 && (C *= .9), b < 20 && (S *= .9); var e = c.session.getScrollTop(); c.renderer.scrollBy(10 * C, 10 * S), e == c.session.getScrollTop() && (b = 0) }, 10), o()) : i(), clearTimeout(p), p = null }, c), n.addListener(e, "touchmove", function (e) { p && (clearTimeout(p), p = null); var t = e.touches; if (!(1 < t.length || "zoom" == $)) { var t = t[0], i = d - t.clientX, n = g - t.clientY; if ("wait" == $) { if (!(4 < i * i + n * n)) return e.preventDefault(); $ = "cursor" } d = t.clientX, g = t.clientY, e.clientX = t.clientX, e.clientY = t.clientY; var t = e.timeStamp, s = t - m; m = t, "scroll" == $ ? ((t = new k(e, c)).speed = 1, t.wheelX = i, t.wheelY = n, 10 * Math.abs(i) < Math.abs(n) && (i = 0), 10 * Math.abs(n) < Math.abs(i) && (n = 0), 0 != s && (C = i / s, S = n / s), c._emit("mousewheel", t), t.propagationStopped || (C = S = 0)) : (i = new k(e, c).getDocumentPosition(), "cursor" == $ ? c.selection.moveCursorToPosition(i) : "anchor" == $ && c.selection.setSelectionAnchor(i.row, i.column), c.renderer.scrollCursorIntoView(i), e.preventDefault()) } }, c) } }), define("ace/mouse/mouse_handler", ["require", "exports", "module", "ace/lib/event", "ace/lib/useragent", "ace/mouse/default_handlers", "ace/mouse/default_gutter_handler", "ace/mouse/mouse_event", "ace/mouse/dragdrop_handler", "ace/mouse/touch_handler", "ace/config"], function (e, t, i) { "use strict"; function n(n) { function e(e) { document.hasFocus && document.hasFocus() && (n.isFocused() || document.activeElement != (n.textInput && n.textInput.getElement())) || window.focus(), n.focus(), setTimeout(function () { n.isFocused() || n.focus() }) } var s = this, t = (this.editor = n, new o(this), new r(this), new a(this), n.renderer.getMouseEventTarget()), i = (c.addListener(t, "click", this.onMouseEvent.bind(this, "click"), n), c.addListener(t, "mousemove", this.onMouseMove.bind(this, "mousemove"), n), c.addMultiMouseDownListener([t, n.renderer.scrollBarV && n.renderer.scrollBarV.inner, n.renderer.scrollBarH && n.renderer.scrollBarH.inner, n.textInput && n.textInput.getElement()].filter(Boolean), [400, 300, 250], this, "onMouseEvent", n), c.addMouseWheelListener(n.container, this.onMouseWheel.bind(this, "mousewheel"), n), l(n.container, n), n.renderer.$gutter); c.addListener(i, "mousedown", this.onMouseEvent.bind(this, "guttermousedown"), n), c.addListener(i, "click", this.onMouseEvent.bind(this, "gutterclick"), n), c.addListener(i, "dblclick", this.onMouseEvent.bind(this, "gutterdblclick"), n), c.addListener(i, "mousemove", this.onMouseEvent.bind(this, "guttermousemove"), n), c.addListener(t, "mousedown", e, n), c.addListener(i, "mousedown", e, n), u.isIE && n.renderer.scrollBarV && (c.addListener(n.renderer.scrollBarV.element, "mousedown", e, n), c.addListener(n.renderer.scrollBarH.element, "mousedown", e, n)), n.on("mousemove", function (e) { var t, i; s.state || s.$dragDelay || !s.$dragEnabled || (e = n.renderer.screenToTextCoordinates(e.x, e.y), t = n.session.selection.getRange(), i = n.renderer, !t.isEmpty() && t.insideStart(e.row, e.column) ? i.setCursorStyle("default") : i.setCursorStyle("")) }, n) } var c = e("../lib/event"), u = e("../lib/useragent"), o = e("./default_handlers").DefaultHandlers, r = e("./default_gutter_handler").GutterHandler, d = e("./mouse_event").MouseEvent, a = e("./dragdrop_handler").DragdropHandler, l = e("./touch_handler").addTouchListeners, e = e("../config"); (function () { this.onMouseEvent = function (e, t) { this.editor.session && this.editor._emit(e, new d(t, this.editor)) }, this.onMouseMove = function (e, t) { var i = this.editor._eventRegistry && this.editor._eventRegistry.mousemove; i && i.length && this.editor._emit(e, new d(t, this.editor)) }, this.onMouseWheel = function (e, t) { var i = new d(t, this.editor); i.speed = 2 * this.$scrollSpeed, i.wheelX = t.wheelX, i.wheelY = t.wheelY, this.editor._emit(e, i) }, this.setState = function (e) { this.state = e }, this.captureMouse = function (e, t) { this.x = e.x, this.y = e.y, this.isMousePressed = !0; function i(e) { if (e) return u.isWebKit && !e.which && a.releaseMouse ? a.releaseMouse() : (a.x = e.clientX, a.y = e.clientY, t && t(e), a.mouseEvent = new d(e, a.editor), void (a.$mouseMoved = !0)) } function n(e) { o.off("beforeEndOperation", l), clearInterval(h), o.session && s(), a[a.state + "End"] && a[a.state + "End"](e), a.state = "", a.isMousePressed = r.$isMousePressed = !1, r.$keepTextAreaAtCursor && r.$moveTextAreaToCursor(), a.$onCaptureMouseMove = a.releaseMouse = null, e && a.onMouseEvent("mouseup", e), o.endOperation() } function s() { a[a.state] && a[a.state](), a.$mouseMoved = !1 } var o = this.editor, r = this.editor.renderer, a = (r.$isMousePressed = !0, this); if (u.isOldIE && "dblclick" == e.domEvent.type) return setTimeout(function () { n(e) }); var l = function (e) { a.releaseMouse && o.curOp.command.name && o.curOp.selectionChanged && (a[a.state + "End"] && a[a.state + "End"](), a.state = "", a.releaseMouse()) }, h = (o.on("beforeEndOperation", l), o.startOperation({ command: { name: "mouse" } }), a.$onCaptureMouseMove = i, a.releaseMouse = c.capture(this.editor.container, i, n), setInterval(s, 20)) }, this.releaseMouse = null, this.cancelContextMenu = function () { var t = function (e) { e && e.domEvent && "contextmenu" != e.domEvent.type || (this.editor.off("nativecontextmenu", t), e && e.domEvent && c.stopEvent(e.domEvent)) }.bind(this); setTimeout(t, 10), this.editor.on("nativecontextmenu", t) }, this.destroy = function () { this.releaseMouse && this.releaseMouse() } }).call(n.prototype), e.defineOptions(n.prototype, "mouseHandler", { scrollSpeed: { initialValue: 2 }, dragDelay: { initialValue: u.isMac ? 150 : 0 }, dragEnabled: { initialValue: !0 }, focusTimeout: { initialValue: 0 }, tooltipFollowsMouse: { initialValue: !0 } }), t.MouseHandler = n }), define("ace/mouse/fold_handler", ["require", "exports", "module", "ace/lib/dom"], function (e, t, i) { "use strict"; var n = e("../lib/dom"); t.FoldHandler = function (s) { s.on("click", function (e) { var t = e.getDocumentPosition(), i = s.session, t = i.getFoldAt(t.row, t.column, 1), t = (t && (e.getAccelKey() ? i.removeFold(t) : i.expandFold(t), e.stop()), e.domEvent && e.domEvent.target); t && n.hasCssClass(t, "ace_inline_button") && n.hasCssClass(t, "ace_toggle_wrap") && (i.setOption("wrap", !i.getUseWrapMode()), s.renderer.scrollCursorIntoView()) }), s.on("gutterclick", function (e) { var t, i; "foldWidgets" == s.renderer.$gutterLayer.getRegion(e) && (t = e.getDocumentPosition().row, (i = s.session).foldWidgets && i.foldWidgets[t] && s.session.onFoldWidgetClick(t, e), s.isFocused() || s.focus(), e.stop()) }), s.on("gutterdblclick", function (e) { var t, i, n; "foldWidgets" == s.renderer.$gutterLayer.getRegion(e) && (n = e.getDocumentPosition().row, (i = (i = (t = s.session).getParentFoldRangeData(n, !0)).range || i.firstRange) && (n = i.start.row, (n = t.getFoldAt(n, t.getLine(n).length, 1)) ? t.removeFold(n) : (t.addFold("...", i), s.renderer.scrollCursorIntoView({ row: i.start.row, column: 0 }))), e.stop()) }) } }), define("ace/keyboard/keybinding", ["require", "exports", "module", "ace/lib/keys", "ace/lib/event"], function (e, t, i) { "use strict"; function n(e) { this.$editor = e, this.$data = { editor: e }, this.$handlers = [], this.setDefaultHandler(e.commands) } var s = e("../lib/keys"), l = e("../lib/event"); (function () { this.setDefaultHandler = function (e) { this.removeKeyboardHandler(this.$defaultHandler), this.$defaultHandler = e, this.addKeyboardHandler(e, 0) }, this.setKeyboardHandler = function (e) { var t = this.$handlers; if (t[t.length - 1] != e) { for (; t[t.length - 1] && t[t.length - 1] != this.$defaultHandler;)this.removeKeyboardHandler(t[t.length - 1]); this.addKeyboardHandler(e, 1) } }, this.addKeyboardHandler = function (e, t) { var i; e && ("function" != typeof e || e.handleKeyboard || (e.handleKeyboard = e), -1 != (i = this.$handlers.indexOf(e)) && this.$handlers.splice(i, 1), null == t ? this.$handlers.push(e) : this.$handlers.splice(t, 0, e), -1 == i && e.attach && e.attach(this.$editor)) }, this.removeKeyboardHandler = function (e) { var t = this.$handlers.indexOf(e); return -1 != t && (this.$handlers.splice(t, 1), e.detach && e.detach(this.$editor), !0) }, this.getKeyboardHandler = function () { return this.$handlers[this.$handlers.length - 1] }, this.getStatusText = function () { var t = this.$data, i = t.editor; return this.$handlers.map(function (e) { return e.getStatusText && e.getStatusText(i, t) || "" }).filter(Boolean).join(" ") }, this.$callKeyboardHandlers = function (e, t, i, n) { for (var s, o = !1, r = this.$editor.commands, a = this.$handlers.length; a-- && !((s = this.$handlers[a].handleKeyboard(this.$data, e, t, i, n)) && s.command && ((o = "null" == s.command || r.exec(s.command, this.$editor, s.args, n)) && n && -1 != e && 1 != s.passEvent && 1 != s.command.passEvent && l.stopEvent(n), o));); return o || -1 != e || (s = { command: "insertstring" }, o = r.exec("insertstring", this.$editor, t)), o && this.$editor._signal && this.$editor._signal("keyboardActivity", s), o }, this.onCommandKey = function (e, t, i) { var n = s.keyCodeToString(i); return this.$callKeyboardHandlers(t, n, i, e) }, this.onTextInput = function (e) { return this.$callKeyboardHandlers(-1, e) } }).call(n.prototype), t.KeyBinding = n }), define("ace/lib/bidiutil", ["require", "exports", "module"], function (e, l, t) { "use strict"; function h(e, t, i, n) { var s, o, r, a = m ? y : b, l = 0, h = -1, c = null, u = null, d = []; if (!n) for (c = 0, n = []; c < i; c++)n[c] = f(e[c]); for (p = m, $ = v = w = !1, u = 0; u < i; u++) { if (r = l, d[u] = s = function (e, t, i, n) { var s, o, r, a = t[n]; switch (a) { case C: case S: w = !1; case A: case x: return a; case k: return w ? x : k; case M: return w = !0, S; case T: return A; case E: return n < 1 || n + 1 >= t.length || (s = i[n - 1]) != k && s != x || (l = t[n + 1]) != k && l != x ? A : (l = w ? x : l) == s ? l : A; case _: return (s = 0 < n ? i[n - 1] : L) == k && n + 1 < t.length && t[n + 1] == k ? k : A; case I: if (0 < n && i[n - 1] == k) return k; if (w) return A; for (r = n + 1, o = t.length; r < o && t[r] == I;)r++; return r < o && t[r] == k ? k : A; case F: for (o = t.length, r = n + 1; r < o && t[r] == F;)r++; if (r < o) { var l = e[n], l = 1425 <= l && l <= 2303 || 64286 == l; if (s = t[r], l && (s == S || s == M)) return S } return n < 1 || (s = t[n - 1]) == L ? A : i[n - 1]; case L: return v = !(w = !1), m; case R: return $ = !0, A; case W: case O: case D: case B: case H: w = !1; case P: return A } }(e, n, d, u), r = 240 & (l = a[r][s]), l &= 15, t[u] = o = a[l][5], 0 < r) if (16 == r) { for (c = h; c < u; c++)t[c] = 1; h = -1 } else h = -1; if (a[l][6]) -1 == h && (h = u); else if (-1 < h) { for (c = h; c < u; c++)t[c] = o; h = -1 } n[u] == L && (t[u] = 0), p |= o } if ($) for (c = 0; c < i; c++)if (n[c] == R) { t[c] = m; for (var g = c - 1; 0 <= g && n[g] == T; g--)t[g] = m } } function c(e, t, i) { if (!(p < e)) if (1 != e || 1 != m || v) for (var n, s, o, r, a = i.length, l = 0; l < a;) { if (t[l] >= e) { for (n = l + 1; n < a && t[n] >= e;)n++; for (s = l, o = n - 1; s < o; s++, o--)r = i[s], i[s] = i[o], i[o] = r; l = n } l++ } else i.reverse() } function f(e) { var t = e.charCodeAt(0), i = t >> 8; return 0 == i ? 191 < t ? C : n[t] : 5 == i ? /[\u0591-\u05f4]/.test(e) ? S : C : 6 == i ? /[\u0610-\u061a\u064b-\u065f\u06d6-\u06e4\u06e7-\u06ed]/.test(e) ? F : /[\u0660-\u0669\u066b-\u066c]/.test(e) ? x : 1642 == t ? I : /[\u06f0-\u06f9]/.test(e) ? k : M : 32 == i && t <= 8287 ? s[255 & t] : 254 == i && 65136 <= t ? M : A } var m = 0, p = 0, w = !1, v = !1, $ = !1, b = [[0, 3, 0, 1, 0, 0, 0], [0, 3, 0, 1, 2, 2, 0], [0, 3, 0, 17, 2, 0, 1], [0, 3, 5, 5, 4, 1, 0], [0, 3, 21, 21, 4, 0, 1], [0, 3, 5, 5, 4, 2, 0]], y = [[2, 0, 1, 1, 0, 1, 0], [2, 0, 1, 1, 0, 2, 0], [2, 0, 2, 1, 3, 2, 0], [2, 0, 2, 33, 3, 1, 1]], C = 0, S = 1, k = 2, x = 3, A = 4, L = 5, R = 6, M = 7, T = 8, E = 9, _ = 10, I = 11, F = 12, W = 13, O = 14, H = 15, D = 16, B = 17, P = 18, n = [P, P, P, P, P, P, P, P, P, R, L, R, T, L, P, P, P, P, P, P, P, P, P, P, P, P, P, P, L, L, L, R, T, A, A, I, I, I, A, A, A, A, A, _, E, _, E, E, k, k, k, k, k, k, k, k, k, k, E, A, A, A, A, A, A, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, A, A, A, A, A, A, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, C, A, A, A, A, P, P, P, P, P, P, L, P, P, P, P, P, P, P, P, P, P, P, P, P, P, P, P, P, P, P, P, P, P, P, P, P, P, E, A, I, I, I, I, A, A, A, A, C, A, A, P, A, A, I, I, k, k, A, C, A, A, A, k, C, A, A, A, A, A], s = [T, T, T, T, T, T, T, T, T, T, T, P, P, P, C, S, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, T, L, W, O, H, D, B, E, I, I, I, I, I, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, E, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, A, T]; l.L = C, l.R = S, l.EN = k, l.ON_R = 3, l.AN = 4, l.R_H = 5, l.B = 6, l.RLE = 7, l.DOT = "·", l.doBidiReorder = function (e, t, i) { if (e.length < 2) return {}; var n = e.split(""), s = new Array(n.length), o = new Array(n.length), r = []; m = i ? 1 : 0, h(n, r, n.length, t); for (var a = 0; a < s.length; s[a] = a, a++); c(2, r, s), c(1, r, s); for (a = 0; a < s.length - 1; a++)t[a] === x ? r[a] = l.AN : r[a] === S && (t[a] > M && t[a] < W || t[a] === A || t[a] === P) ? r[a] = l.ON_R : 0 < a && "ل" === n[a - 1] && /\u0622|\u0623|\u0625|\u0627/.test(n[a]) && (r[a - 1] = r[a] = l.R_H, a++); n[n.length - 1] === l.DOT && (r[n.length - 1] = l.B), "‫" === n[0] && (r[0] = l.RLE); for (a = 0; a < s.length; a++)o[a] = r[s[a]]; return { logicalFromVisual: s, bidiLevels: o } }, l.hasBidiCharacters = function (e, t) { for (var i = !1, n = 0; n < e.length; n++)t[n] = f(e.charAt(n)), i || t[n] != S && t[n] != M && t[n] != x || (i = !0); return i }, l.getVisualFromLogicalIdx = function (e, t) { for (var i = 0; i < t.logicalFromVisual.length; i++)if (t.logicalFromVisual[i] == e) return i; return 0 } }), define("ace/bidihandler", ["require", "exports", "module", "ace/lib/bidiutil", "ace/lib/lang"], function (e, t, i) { "use strict"; function n(e) { this.session = e, this.bidiMap = {}, this.currentRow = null, this.bidiUtil = a, this.charWidths = [], this.EOL = "¬", this.showInvisibles = !0, this.isRtlDir = !1, this.$isRtl = !1, this.line = "", this.wrapIndent = 0, this.EOF = "¶", this.RLE = "‫", this.contentWidth = 0, this.fontMetrics = null, this.rtlLineOffset = 0, this.wrapOffset = 0, this.isMoveLeftOperation = !1, this.seenBidi = s.test(e.getValue()) } var a = e("./lib/bidiutil"), r = e("./lib/lang"), s = /[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\u202B]/; (function () { this.isBidiRow = function (e, t, i) { return !!this.seenBidi && (e !== this.currentRow && (this.currentRow = e, this.updateRowLine(t, i), this.updateBidiMap()), this.bidiMap.bidiLevels) }, this.onChange = function (e) { this.seenBidi ? this.currentRow = null : "insert" == e.action && s.test(e.lines.join("\n")) && (this.seenBidi = !0, this.currentRow = null) }, this.getDocumentRow = function () { var e = 0, t = this.session.$screenRowCache; return e = t.length && 0 <= (t = this.session.$getRowCacheIndex(t, this.currentRow)) ? this.session.$docRowCache[t] : e }, this.getSplitIndex = function () { var e = 0, t = this.session.$screenRowCache; if (t.length) for (var i, n = this.session.$getRowCacheIndex(t, this.currentRow); 0 < this.currentRow - e && (i = this.session.$getRowCacheIndex(t, this.currentRow - e - 1)) === n;)n = i, e++; else e = this.currentRow; return e }, this.updateRowLine = function (e, t) { var i, n = (e = void 0 === e ? this.getDocumentRow() : e) === this.session.getLength() - 1 ? this.EOF : this.EOL, s = (this.wrapIndent = 0, this.line = this.session.getLine(e), this.isRtlDir = this.$isRtl || this.line.charAt(0) === this.RLE, this.session.$useWrapMode ? (e = this.session.$wrapData[e]) && (0 < (t = void 0 === t ? this.getSplitIndex() : t) && e.length ? (this.wrapIndent = e.indent, this.wrapOffset = this.wrapIndent * this.charWidths[a.L], this.line = t < e.length ? this.line.substring(e[t - 1], e[t]) : this.line.substring(e[e.length - 1])) : this.line = this.line.substring(0, e[t]), t == e.length && (this.line += this.showInvisibles ? n : a.DOT)) : this.line += this.showInvisibles ? n : a.DOT, this.session), o = 0; this.line = this.line.replace(/\t|[\u1100-\u2029, \u202F-\uFFE6]/g, function (e, t) { return "\t" === e || s.isFullWidth(e.charCodeAt(0)) ? (i = "\t" === e ? s.getScreenTabSize(t + o) : 2, o += i - 1, r.stringRepeat(a.DOT, i)) : e }), this.isRtlDir && (this.fontMetrics.$main.textContent = this.line.charAt(this.line.length - 1) == a.DOT ? this.line.substr(0, this.line.length - 1) : this.line, this.rtlLineOffset = this.contentWidth - this.fontMetrics.$main.getBoundingClientRect().width) }, this.updateBidiMap = function () { var e = []; a.hasBidiCharacters(this.line, e) || this.isRtlDir ? this.bidiMap = a.doBidiReorder(this.line, e, this.isRtlDir) : this.bidiMap = {} }, this.markAsDirty = function () { this.currentRow = null }, this.updateCharacterWidths = function (e) { var t; this.characterWidth !== e.$characterSize.width && (this.fontMetrics = e, t = this.characterWidth = e.$characterSize.width, e = e.$measureCharWidth("ה"), this.charWidths[a.L] = this.charWidths[a.EN] = this.charWidths[a.ON_R] = t, this.charWidths[a.R] = this.charWidths[a.AN] = e, this.charWidths[a.R_H] = .45 * e, this.charWidths[a.B] = this.charWidths[a.RLE] = 0, this.currentRow = null) }, this.setShowInvisibles = function (e) { this.showInvisibles = e, this.currentRow = null }, this.setEolChar = function (e) { this.EOL = e }, this.setContentWidth = function (e) { this.contentWidth = e }, this.isRtlLine = function (e) { return !!this.$isRtl || (null != e ? this.session.getLine(e).charAt(0) == this.RLE : this.isRtlDir) }, this.setRtlDirection = function (e, t) { for (var i = e.getCursorPosition(), n = e.selection.getSelectionAnchor().row; n <= i.row; n++)t || e.session.getLine(n).charAt(0) !== e.session.$bidiHandler.RLE ? t && e.session.getLine(n).charAt(0) !== e.session.$bidiHandler.RLE && e.session.doc.insert({ column: 0, row: n }, e.session.$bidiHandler.RLE) : e.session.doc.removeInLine(n, 0, 1) }, this.getPosLeft = function (e) { e -= this.wrapIndent; var t = this.line.charAt(0) === this.RLE ? 1 : 0, i = t < e ? this.session.getOverwrite() ? e : e - 1 : t, n = a.getVisualFromLogicalIdx(i, this.bidiMap), s = this.bidiMap.bidiLevels, o = 0; !this.session.getOverwrite() && e <= t && s[n] % 2 != 0 && n++; for (var r = 0; r < n; r++)o += this.charWidths[s[r]]; return !this.session.getOverwrite() && t < e && s[n] % 2 == 0 && (o += this.charWidths[s[n]]), this.wrapIndent && (o += this.isRtlDir ? -1 * this.wrapOffset : this.wrapOffset), this.isRtlDir && (o += this.rtlLineOffset), o }, this.getSelections = function (e, t) { var i, n = this.bidiMap, s = n.bidiLevels, o = [], r = 0, a = Math.min(e, t) - this.wrapIndent, l = Math.max(e, t) - this.wrapIndent, h = !1, c = !1, u = 0; this.wrapIndent && (r += this.isRtlDir ? -1 * this.wrapOffset : this.wrapOffset); for (var d, g = 0; g < s.length; g++)d = n.logicalFromVisual[g], i = s[g], (h = a <= d && d < l) && !c ? u = r : !h && c && o.push({ left: u, width: r - u }), r += this.charWidths[i], c = h; if (h && g === s.length && o.push({ left: u, width: r - u }), this.isRtlDir) for (var f = 0; f < o.length; f++)o[f].left += this.rtlLineOffset; return o }, this.offsetToCol = function (e) { this.isRtlDir && (e -= this.rtlLineOffset); var t = 0, e = Math.max(e, 0), i = 0, n = 0, s = this.bidiMap.bidiLevels, o = this.charWidths[s[n]]; for (this.wrapIndent && (e -= this.isRtlDir ? -1 * this.wrapOffset : this.wrapOffset); i + o / 2 < e;) { if (i += o, n === s.length - 1) { o = 0; break } o = this.charWidths[s[++n]] } return 0 === (t = 0 < n && s[n - 1] % 2 != 0 && s[n] % 2 == 0 ? (e < i && n--, this.bidiMap.logicalFromVisual[n]) : 0 < n && s[n - 1] % 2 == 0 && s[n] % 2 != 0 ? 1 + (i < e ? this.bidiMap.logicalFromVisual[n] : this.bidiMap.logicalFromVisual[n - 1]) : this.isRtlDir && n === s.length - 1 && 0 === o && s[n - 1] % 2 == 0 || !this.isRtlDir && 0 === n && s[n] % 2 != 0 ? 1 + this.bidiMap.logicalFromVisual[n] : (0 < n && s[n - 1] % 2 != 0 && 0 !== o && n--, this.bidiMap.logicalFromVisual[n])) && this.isRtlDir && t++, t + this.wrapIndent } }).call(n.prototype), t.BidiHandler = n }), define("ace/selection", ["require", "exports", "module", "ace/lib/oop", "ace/lib/lang", "ace/lib/event_emitter", "ace/range"], function (e, t, i) { "use strict"; function n(e) { this.session = e, this.doc = e.getDocument(), this.clearSelection(), this.cursor = this.lead = this.doc.createAnchor(0, 0), this.anchor = this.doc.createAnchor(0, 0), this.$silent = !1; var t = this; this.cursor.on("change", function (e) { t.$cursorChanged = !0, t.$silent || t._emit("changeCursor"), t.$isEmpty || t.$silent || t._emit("changeSelection"), t.$keepDesiredColumnOnChange || e.old.column == e.value.column || (t.$desiredColumn = null) }), this.anchor.on("change", function () { t.$anchorChanged = !0, t.$isEmpty || t.$silent || t._emit("changeSelection") }) } var s = e("./lib/oop"), o = e("./lib/lang"), r = e("./lib/event_emitter").EventEmitter, a = e("./range").Range; (function () { s.implement(this, r), this.isEmpty = function () { return this.$isEmpty || this.anchor.row == this.lead.row && this.anchor.column == this.lead.column }, this.isMultiLine = function () { return !this.$isEmpty && this.anchor.row != this.cursor.row }, this.getCursor = function () { return this.lead.getPosition() }, this.setSelectionAnchor = function (e, t) { this.$isEmpty = !1, this.anchor.setPosition(e, t) }, this.getAnchor = this.getSelectionAnchor = function () { return this.$isEmpty ? this.getSelectionLead() : this.anchor.getPosition() }, this.getSelectionLead = function () { return this.lead.getPosition() }, this.isBackwards = function () { var e = this.anchor, t = this.lead; return e.row > t.row || e.row == t.row && e.column > t.column }, this.getRange = function () { var e = this.anchor, t = this.lead; return this.$isEmpty ? a.fromPoints(t, t) : this.isBackwards() ? a.fromPoints(t, e) : a.fromPoints(e, t) }, this.clearSelection = function () { this.$isEmpty || (this.$isEmpty = !0, this._emit("changeSelection")) }, this.selectAll = function () { this.$setSelection(0, 0, Number.MAX_VALUE, Number.MAX_VALUE) }, this.setRange = this.setSelectionRange = function (e, t) { var i = t ? e.end : e.start, t = t ? e.start : e.end; this.$setSelection(i.row, i.column, t.row, t.column) }, this.$setSelection = function (e, t, i, n) { var s, o; this.$silent || (s = this.$isEmpty, o = this.inMultiSelectMode, this.$silent = !0, this.$cursorChanged = this.$anchorChanged = !1, this.anchor.setPosition(e, t), this.cursor.setPosition(i, n), this.$isEmpty = !a.comparePoints(this.anchor, this.cursor), this.$silent = !1, this.$cursorChanged && this._emit("changeCursor"), (this.$cursorChanged || this.$anchorChanged || s != this.$isEmpty || o) && this._emit("changeSelection")) }, this.$moveSelection = function (e) { var t = this.lead; this.$isEmpty && this.setSelectionAnchor(t.row, t.column), e.call(this) }, this.selectTo = function (e, t) { this.$moveSelection(function () { this.moveCursorTo(e, t) }) }, this.selectToPosition = function (e) { this.$moveSelection(function () { this.moveCursorToPosition(e) }) }, this.moveTo = function (e, t) { this.clearSelection(), this.moveCursorTo(e, t) }, this.moveToPosition = function (e) { this.clearSelection(), this.moveCursorToPosition(e) }, this.selectUp = function () { this.$moveSelection(this.moveCursorUp) }, this.selectDown = function () { this.$moveSelection(this.moveCursorDown) }, this.selectRight = function () { this.$moveSelection(this.moveCursorRight) }, this.selectLeft = function () { this.$moveSelection(this.moveCursorLeft) }, this.selectLineStart = function () { this.$moveSelection(this.moveCursorLineStart) }, this.selectLineEnd = function () { this.$moveSelection(this.moveCursorLineEnd) }, this.selectFileEnd = function () { this.$moveSelection(this.moveCursorFileEnd) }, this.selectFileStart = function () { this.$moveSelection(this.moveCursorFileStart) }, this.selectWordRight = function () { this.$moveSelection(this.moveCursorWordRight) }, this.selectWordLeft = function () { this.$moveSelection(this.moveCursorWordLeft) }, this.getWordRange = function (e, t) { var i; return void 0 === t && (e = (i = e || this.lead).row, t = i.column), this.session.getWordRange(e, t) }, this.selectWord = function () { this.setSelectionRange(this.getWordRange()) }, this.selectAWord = function () { var e = this.getCursor(), e = this.session.getAWordRange(e.row, e.column); this.setSelectionRange(e) }, this.getLineRange = function (e, t) { var e = "number" == typeof e ? e : this.lead.row, i = this.session.getFoldLine(e), i = i ? (e = i.start.row, i.end.row) : e; return !0 === t ? new a(e, 0, i, this.session.getLine(i).length) : new a(e, 0, i + 1, 0) }, this.selectLine = function () { this.setSelectionRange(this.getLineRange()) }, this.moveCursorUp = function () { this.moveCursorBy(-1, 0) }, this.moveCursorDown = function () { this.moveCursorBy(1, 0) }, this.wouldMoveIntoSoftTab = function (e, t, i) { var n = e.column, s = e.column + t; return i < 0 && (n = e.column - t, s = e.column), this.session.isTabStop(e) && this.doc.getLine(e.row).slice(n, s).split(" ").length - 1 == t }, this.moveCursorLeft = function () { var e, t = this.lead.getPosition(); (e = this.session.getFoldAt(t.row, t.column, -1)) ? this.moveCursorTo(e.start.row, e.start.column) : 0 === t.column ? 0 < t.row && this.moveCursorTo(t.row - 1, this.doc.getLine(t.row - 1).length) : (e = this.session.getTabSize(), this.wouldMoveIntoSoftTab(t, e, -1) && !this.session.getNavigateWithinSoftTabs() ? this.moveCursorBy(0, -e) : this.moveCursorBy(0, -1)) }, this.moveCursorRight = function () { var e, t = this.lead.getPosition(); (e = this.session.getFoldAt(t.row, t.column, 1)) ? this.moveCursorTo(e.end.row, e.end.column) : this.lead.column == this.doc.getLine(this.lead.row).length ? this.lead.row < this.doc.getLength() - 1 && this.moveCursorTo(this.lead.row + 1, 0) : (e = this.session.getTabSize(), t = this.lead, this.wouldMoveIntoSoftTab(t, e, 1) && !this.session.getNavigateWithinSoftTabs() ? this.moveCursorBy(0, e) : this.moveCursorBy(0, 1)) }, this.moveCursorLineStart = function () { var e = this.lead.row, t = this.lead.column, i = this.session.documentToScreenRow(e, t), i = this.session.screenToDocumentPosition(i, 0), e = this.session.getDisplayLine(e, null, i.row, i.column).match(/^\s*/); e[0].length == t || this.session.$useEmacsStyleLineStart || (i.column += e[0].length), this.moveCursorToPosition(i) }, this.moveCursorLineEnd = function () { var e, t = this.lead, t = this.session.getDocumentLastRowColumnPosition(t.row, t.column); this.lead.column == t.column && (e = this.session.getLine(t.row), t.column == e.length && 0 < (e = e.search(/\s+$/)) && (t.column = e)), this.moveCursorTo(t.row, t.column) }, this.moveCursorFileEnd = function () { var e = this.doc.getLength() - 1, t = this.doc.getLine(e).length; this.moveCursorTo(e, t) }, this.moveCursorFileStart = function () { this.moveCursorTo(0, 0) }, this.moveCursorLongWordRight = function () { var e = this.lead.row, t = this.lead.column, i = this.doc.getLine(e), n = i.substring(t), s = (this.session.nonTokenRe.lastIndex = 0, this.session.tokenRe.lastIndex = 0, this.session.getFoldAt(e, t, 1)); if (s) this.moveCursorTo(s.end.row, s.end.column); else { if (this.session.nonTokenRe.exec(n) && (t += this.session.nonTokenRe.lastIndex, this.session.nonTokenRe.lastIndex = 0, n = i.substring(t)), t >= i.length) return this.moveCursorTo(e, i.length), this.moveCursorRight(), void (e < this.doc.getLength() - 1 && this.moveCursorWordRight()); this.session.tokenRe.exec(n) && (t += this.session.tokenRe.lastIndex, this.session.tokenRe.lastIndex = 0), this.moveCursorTo(e, t) } }, this.moveCursorLongWordLeft = function () { var e = this.lead.row, t = this.lead.column; if (i = this.session.getFoldAt(e, t, -1)) this.moveCursorTo(i.start.row, i.start.column); else { var i = this.session.getFoldStringAt(e, t, -1), i = (null == i && (i = this.doc.getLine(e).substring(0, t)), o.stringReverse(i)); if (this.session.nonTokenRe.lastIndex = 0, this.session.tokenRe.lastIndex = 0, this.session.nonTokenRe.exec(i) && (t -= this.session.nonTokenRe.lastIndex, i = i.slice(this.session.nonTokenRe.lastIndex), this.session.nonTokenRe.lastIndex = 0), t <= 0) return this.moveCursorTo(e, 0), this.moveCursorLeft(), void (0 < e && this.moveCursorWordLeft()); this.session.tokenRe.exec(i) && (t -= this.session.tokenRe.lastIndex, this.session.tokenRe.lastIndex = 0), this.moveCursorTo(e, t) } }, this.$shortWordEndIndex = function (e) { var t, i = 0, n = /\s/, s = this.session.tokenRe; if (s.lastIndex = 0, this.session.tokenRe.exec(e)) i = this.session.tokenRe.lastIndex; else { for (; (t = e[i]) && n.test(t);)i++; if (i < 1) for (s.lastIndex = 0; (t = e[i]) && !s.test(t);)if (s.lastIndex = 0, i++, n.test(t)) { if (2 < i) { i--; break } for (; (t = e[i]) && n.test(t);)i++; if (2 < i) break } } return s.lastIndex = 0, i }, this.moveCursorShortWordRight = function () { var e = this.lead.row, t = this.lead.column, i = this.doc.getLine(e), n = i.substring(t), s = this.session.getFoldAt(e, t, 1); if (s) return this.moveCursorTo(s.end.row, s.end.column); if (t == i.length) { for (var o = this.doc.getLength(); n = this.doc.getLine(++e), e < o && /^\s*$/.test(n);); /^\s+/.test(n) || (n = ""), t = 0 } s = this.$shortWordEndIndex(n); this.moveCursorTo(e, t + s) }, this.moveCursorShortWordLeft = function () { var e = this.lead.row, t = this.lead.column; if (n = this.session.getFoldAt(e, t, -1)) return this.moveCursorTo(n.start.row, n.start.column); var i = this.session.getLine(e).substring(0, t); if (0 === t) { for (; i = this.doc.getLine(--e), 0 < e && /^\s*$/.test(i);); t = i.length, /\s+$/.test(i) || (i = "") } var n = o.stringReverse(i), n = this.$shortWordEndIndex(n); return this.moveCursorTo(e, t - n) }, this.moveCursorWordRight = function () { this.session.$selectLongWords ? this.moveCursorLongWordRight() : this.moveCursorShortWordRight() }, this.moveCursorWordLeft = function () { this.session.$selectLongWords ? this.moveCursorLongWordLeft() : this.moveCursorShortWordLeft() }, this.moveCursorBy = function (e, t) { var i, n = this.session.documentToScreenPosition(this.lead.row, this.lead.column), s = (0 === t && (0 !== e && (this.session.$bidiHandler.isBidiRow(n.row, this.lead.row) ? (i = this.session.$bidiHandler.getPosLeft(n.column), n.column = Math.round(i / this.session.$bidiHandler.charWidths[0])) : i = n.column * this.session.$bidiHandler.charWidths[0]), this.$desiredColumn ? n.column = this.$desiredColumn : this.$desiredColumn = n.column), 0 != e && this.session.lineWidgets && this.session.lineWidgets[this.lead.row] && (s = this.session.lineWidgets[this.lead.row], e < 0 ? e -= s.rowsAbove || 0 : 0 < e && (e += s.rowCount - (s.rowsAbove || 0))), this.session.screenToDocumentPosition(n.row + e, n.column, i)); 0 !== e && 0 === t && s.row === this.lead.row && (s.column, this.lead.column), this.moveCursorTo(s.row, s.column + t, 0 === t) }, this.moveCursorToPosition = function (e) { this.moveCursorTo(e.row, e.column) }, this.moveCursorTo = function (e, t, i) { var n = this.session.getFoldAt(e, t, 1), n = (n && (e = n.start.row, t = n.start.column), this.$keepDesiredColumnOnChange = !0, this.session.getLine(e)); /[\uDC00-\uDFFF]/.test(n.charAt(t)) && n.charAt(t - 1) && (this.lead.row == e && this.lead.column == t + 1 ? --t : t += 1), this.lead.setPosition(e, t), this.$keepDesiredColumnOnChange = !1, i || (this.$desiredColumn = null) }, this.moveCursorToScreen = function (e, t, i) { e = this.session.screenToDocumentPosition(e, t); this.moveCursorTo(e.row, e.column, i) }, this.detach = function () { this.lead.detach(), this.anchor.detach() }, this.fromOrientedRange = function (e) { this.setSelectionRange(e, e.cursor == e.start), this.$desiredColumn = e.desiredColumn || this.$desiredColumn }, this.toOrientedRange = function (e) { var t = this.getRange(); return e ? (e.start.column = t.start.column, e.start.row = t.start.row, e.end.column = t.end.column, e.end.row = t.end.row) : e = t, e.cursor = this.isBackwards() ? e.start : e.end, e.desiredColumn = this.$desiredColumn, e }, this.getRangeOfMovements = function (e) { var t = this.getCursor(); try { e(this); var i = this.getCursor(); return a.fromPoints(t, i) } catch (e) { return a.fromPoints(t, t) } finally { this.moveCursorToPosition(t) } }, this.toJSON = function () { var e; return this.rangeCount ? e = this.ranges.map(function (e) { var t = e.clone(); return t.isBackwards = e.cursor == e.start, t }) : (e = this.getRange()).isBackwards = this.isBackwards(), e }, this.fromJSON = function (e) { if (null == e.start) { if (this.rangeList && 1 < e.length) { this.toSingleRange(e[0]); for (var t = e.length; t--;) { var i = a.fromPoints(e[t].start, e[t].end); e[t].isBackwards && (i.cursor = i.start), this.addRange(i, !0) } return } e = e[0] } this.rangeList && this.toSingleRange(e), this.setSelectionRange(e, e.isBackwards) }, this.isEqual = function (e) { if ((e.length || this.rangeCount) && e.length != this.rangeCount) return !1; if (!e.length || !this.ranges) return this.getRange().isEqual(e); for (var t = this.ranges.length; t--;)if (!this.ranges[t].isEqual(e[t])) return !1; return !0 } }).call(n.prototype), t.Selection = n }), define("ace/tokenizer", ["require", "exports", "module", "ace/config"], function (e, t, i) { "use strict"; function n(e) { for (var t in this.states = e, this.regExps = {}, this.matchMappings = {}, this.states) { for (var i = this.states[t], n = [], s = 0, o = this.matchMappings[t] = { defaultToken: "text" }, r = "g", a = [], l = 0; l < i.length; l++) { var h, c, u = i[l]; u.defaultToken && (o.defaultToken = u.defaultToken), u.caseInsensitive && -1 === r.indexOf("i") && (r += "i"), u.unicode && -1 === r.indexOf("u") && (r += "u"), null != u.regex && (u.regex instanceof RegExp && (u.regex = u.regex.toString().slice(1, -1)), h = u.regex, c = new RegExp("(?:(" + h + ")|(.))").exec("a").length - 2, Array.isArray(u.token) ? 1 == u.token.length || 1 == c ? u.token = u.token[0] : c - 1 != u.token.length ? (this.reportError("number of classes and regexp groups doesn't match", { rule: u, groupCount: c - 1 }), u.token = u.token[0]) : (u.tokenArray = u.token, u.token = null, u.onMatch = this.$arrayTokens) : "function" != typeof u.token || u.onMatch || (u.onMatch = 1 < c ? this.$applyToken : u.token), 1 < c && (h = /\\\d/.test(u.regex) ? u.regex.replace(/\\([0-9]+)/g, function (e, t) { return "\\" + (parseInt(t, 10) + s + 1) }) : (c = 1, this.removeCapturingGroups(u.regex)), u.splitRegex || "string" == typeof u.token || a.push(u)), o[s] = l, s += c, n.push(h), u.onMatch || (u.onMatch = null)) } n.length || (o[0] = 0, n.push("$")), a.forEach(function (e) { e.splitRegex = this.createSplitterRegexp(e.regex, r) }, this), this.regExps[t] = new RegExp("(" + n.join(")|(") + ")|($)", r) } } var s = e("./config"), v = 2e3; (function () { this.$setMaxTokenCount = function (e) { v = 0 | e }, this.$applyToken = function (e) { var t = this.splitRegex.exec(e).slice(1), i = this.token.apply(this, t); if ("string" == typeof i) return [{ type: i, value: e }]; for (var n = [], s = 0, o = i.length; s < o; s++)t[s] && (n[n.length] = { type: i[s], value: t[s] }); return n }, this.$arrayTokens = function (e) { if (!e) return []; var t = this.splitRegex.exec(e); if (!t) return "text"; for (var i = [], n = this.tokenArray, s = 0, o = n.length; s < o; s++)t[s + 1] && (i[i.length] = { type: n[s], value: t[s + 1] }); return i }, this.removeCapturingGroups = function (e) { return e.replace(/\\.|\[(?:\\.|[^\\\]])*|\(\?[:=!<]|(\()/g, function (e, t) { return t ? "(?:" : e }) }, this.createSplitterRegexp = function (e, t) { var r, a, l; return -1 != e.indexOf("(?=") && (r = 0, a = !1, l = {}, e.replace(/(\\.)|(\((?:\?[=!])?)|(\))|([\[\]])/g, function (e, t, i, n, s, o) { return a ? a = "]" != s : s ? a = !0 : n ? (r == l.stack && (l.end = o + 1, l.stack = -1), r--) : i && (r++, 1 != i.length && (l.stack = r, l.start = o)), e }), null != l.end && /^\)*$/.test(e.substr(l.end)) && (e = e.substring(0, l.start) + e.substr(l.end))), "$" != (e = "^" != e.charAt(0) ? "^" + e : e).charAt(e.length - 1) && (e += "$"), new RegExp(e, (t || "").replace("g", "")) }, this.getLineTokens = function (e, t) { t && "string" != typeof t ? "#tmp" === (t = (i = t.slice(0))[0]) && (i.shift(), t = i.shift()) : i = []; for (var i, n, s = t || "start", o = this.states[s], r = (o || (o = this.states[s = "start"]), this.matchMappings[s]), a = this.regExps[s], l = [], h = a.lastIndex = 0, c = 0, u = { type: null, value: "" }; n = a.exec(e);) { var d, g = r.defaultToken, f = null, m = n[0], p = a.lastIndex; p - m.length > h && (d = e.substring(h, p - m.length), u.type == g ? u.value += d : (u.type && l.push(u), u = { type: g, value: d })); for (var w = 0; w < n.length - 2; w++)if (void 0 !== n[w + 1]) { g = (f = o[r[w]]).onMatch ? f.onMatch(m, s, i, e) : f.token, f.next && (s = "string" == typeof f.next ? f.next : f.next(s, i), (o = this.states[s]) || (this.reportError("state doesn't exist", s), o = this.states[s = "start"]), r = this.matchMappings[s], (a = this.regExps[s]).lastIndex = h = p), f.consumeLineEnd && (h = p); break } if (m) if ("string" == typeof g) f && !1 === f.merge || u.type !== g ? (u.type && l.push(u), u = { type: g, value: m }) : u.value += m; else if (g) { u.type && l.push(u); for (u = { type: null, value: "" }, w = 0; w < g.length; w++)l.push(g[w]) } if (h == e.length) break; if (h = p, c++ > v) { for (c > 2 * e.length && this.reportError("infinite loop with in ace tokenizer", { startState: t, line: e }); h < e.length;)u.type && l.push(u), u = { value: e.substring(h, h += 500), type: "overflow" }; s = "start", i = []; break } } return u.type && l.push(u), 1 < i.length && i[0] !== s && i.unshift("#tmp", s), { tokens: l, state: i.length ? i : s } }, this.reportError = s.reportError }).call(n.prototype), t.Tokenizer = n }), define("ace/mode/text_highlight_rules", ["require", "exports", "module", "ace/lib/lang"], function (e, t, i) { "use strict"; function n() { this.$rules = { start: [{ token: "empty_line", regex: "^$" }, { defaultToken: "text" }] } } var l = e("../lib/lang"); (function () { this.addRules = function (e, t) { if (t) for (var i in e) { for (var n = e[i], s = 0; s < n.length; s++) { var o = n[s]; (o.next || o.onMatch) && ("string" == typeof o.next && 0 !== o.next.indexOf(t) && (o.next = t + o.next), o.nextState && 0 !== o.nextState.indexOf(t) && (o.nextState = t + o.nextState)) } this.$rules[t + i] = n } else for (var i in e) this.$rules[i] = e[i] }, this.getRules = function () { return this.$rules }, this.embedRules = function (e, t, i, n, s) { e = "function" == typeof e ? (new e).getRules() : e; if (n) for (var o = 0; o < n.length; o++)n[o] = t + n[o]; else for (var r in n = [], e) n.push(t + r); if (this.addRules(e, t), i) for (var a = Array.prototype[s ? "push" : "unshift"], o = 0; o < n.length; o++)a.apply(this.$rules[n[o]], l.deepCopy(i)); this.$embeds || (this.$embeds = []), this.$embeds.push(t) }, this.getEmbeds = function () { return this.$embeds }; function u(e, t) { return "start" == e && !t.length || t.unshift(this.nextState, e), this.nextState } function d(e, t) { return t.shift(), t.shift() || "start" } this.normalizeRules = function () { var h = 0, c = this.$rules; Object.keys(c).forEach(function e(t) { var i = c[t]; i.processed = !0; for (var n = 0; n < i.length; n++) { var s = i[n], o = null, r = (Array.isArray(s) && (o = s, s = {}), !s.regex && s.start && (s.regex = s.start, s.next || (s.next = []), s.next.push({ defaultToken: s.token }, { token: s.token + ".end", regex: s.end || s.start, next: "pop" }), s.token = s.token + ".start", s.push = !0), s.next || s.push); if (r && Array.isArray(r) ? ((l = s.stateName) || ("string" != typeof (l = s.token) && (l = l[0] || ""), c[l] && (l += h++)), c[l] = r, e(s.next = l)) : "pop" == r && (s.next = d), s.push && (s.nextState = s.next || s.push, s.next = u, delete s.push), s.rules) for (var a in s.rules) c[a] ? c[a].push && c[a].push.apply(c[a], s.rules[a]) : c[a] = s.rules[a]; var l = "string" == typeof s ? s : s.include; (o = l ? Array.isArray(l) ? l.map(function (e) { return c[e] }) : c[l] : o) && (r = [n, 1].concat(o), s.noEscape && (r = r.filter(function (e) { return !e.next })), i.splice.apply(i, r), n--), s.keywordMap && (s.token = this.createKeywordMapper(s.keywordMap, s.defaultToken || "text", s.caseInsensitive), delete s.defaultToken) } }, this) }, this.createKeywordMapper = function (s, t, o, r) { var a = Object.create(null); return this.$keywordList = [], Object.keys(s).forEach(function (e) { for (var t = s[e].split(r || "|"), i = t.length; i--;) { var n = t[i]; this.$keywordList.push(n), o && (n = n.toLowerCase()), a[n] = e } }, this), s = null, o ? function (e) { return a[e.toLowerCase()] || t } : function (e) { return a[e] || t } }, this.getKeywords = function () { return this.$keywords } }).call(n.prototype), t.TextHighlightRules = n }), define("ace/mode/behaviour", ["require", "exports", "module"], function (e, t, i) { "use strict"; function n() { this.$behaviours = {} } (function () { this.add = function (e, t, i) { switch (void 0) { case this.$behaviours: this.$behaviours = {}; case this.$behaviours[e]: this.$behaviours[e] = {} }this.$behaviours[e][t] = i }, this.addBehaviours = function (e) { for (var t in e) for (var i in e[t]) this.add(t, i, e[t][i]) }, this.remove = function (e) { this.$behaviours && this.$behaviours[e] && delete this.$behaviours[e] }, this.inherit = function (e, t) { e = ("function" == typeof e ? new e : e).getBehaviours(t), this.addBehaviours(e) }, this.getBehaviours = function (e) { if (!e) return this.$behaviours; for (var t = {}, i = 0; i < e.length; i++)this.$behaviours[e[i]] && (t[e[i]] = this.$behaviours[e[i]]); return t } }).call(n.prototype), t.Behaviour = n }), define("ace/token_iterator", ["require", "exports", "module", "ace/range"], function (e, t, i) { "use strict"; function n(e, t, i) { this.$session = e, this.$row = t, this.$rowTokens = e.getTokens(t), e = e.getTokenAt(t, i), this.$tokenIndex = e ? e.index : -1 } var s = e("./range").Range; (function () { this.stepBackward = function () { for (--this.$tokenIndex; this.$tokenIndex < 0;) { if (--this.$row, this.$row < 0) return this.$row = 0, null; this.$rowTokens = this.$session.getTokens(this.$row), this.$tokenIndex = this.$rowTokens.length - 1 } return this.$rowTokens[this.$tokenIndex] }, this.stepForward = function () { var e; for (this.$tokenIndex += 1; this.$tokenIndex >= this.$rowTokens.length;) { if (this.$row += 1, e = e || this.$session.getLength(), this.$row >= e) return this.$row = e - 1, null; this.$rowTokens = this.$session.getTokens(this.$row), this.$tokenIndex = 0 } return this.$rowTokens[this.$tokenIndex] }, this.getCurrentToken = function () { return this.$rowTokens[this.$tokenIndex] }, this.getCurrentTokenRow = function () { return this.$row }, this.getCurrentTokenColumn = function () { var e = this.$rowTokens, t = this.$tokenIndex, i = e[t].start; if (void 0 !== i) return i; for (i = 0; 0 < t;)i += e[--t].value.length; return i }, this.getCurrentTokenPosition = function () { return { row: this.$row, column: this.getCurrentTokenColumn() } }, this.getCurrentTokenRange = function () { var e = this.$rowTokens[this.$tokenIndex], t = this.getCurrentTokenColumn(); return new s(this.$row, t, this.$row, t + e.value.length) } }).call(n.prototype), t.TokenIterator = n }), define("ace/mode/behaviour/cstyle", ["require", "exports", "module", "ace/lib/oop", "ace/mode/behaviour", "ace/token_iterator", "ace/lib/lang"], function (e, t, i) { "use strict"; function d(e) { var t = -1; if (e.multiSelect && (t = e.selection.index, l.rangeCount != e.multiSelect.rangeCount && (l = { rangeCount: e.multiSelect.rangeCount })), l[t]) return m = l[t]; m = l[t] = { autoInsertedBrackets: 0, autoInsertedRow: -1, autoInsertedLineEnd: "", maybeInsertedBrackets: 0, maybeInsertedRow: -1, maybeInsertedLineStart: "", maybeInsertedLineEnd: "" } } function g(e, t, i, n) { var s = e.end.row - e.start.row; return { text: i + t + n, selection: [0, e.start.column + 1, s, e.end.column + (s ? 0 : 1)] } } function f(u) { this.add("braces", "insertion", function (e, t, i, n, s) { var o = i.getCursorPosition(), r = n.doc.getLine(o.row); if ("{" == s) return d(i), l = i.getSelectionRange(), "" !== (h = n.doc.getTextRange(l)) && "{" !== h && i.getWrapBehavioursEnabled() ? g(l, h, "{", "}") : f.isSaneInsertion(i, n) ? /[\]\}\)]/.test(r[o.column]) || i.inMultiSelectMode || u && u.braces ? (f.recordAutoInsert(i, n, "}"), { text: "{}", selection: [1, 1] }) : (f.recordMaybeInsert(i, n, "{"), { text: "{", selection: [1, 1] }) : void 0; if ("}" == s) { d(i); var a = r.substring(o.column, o.column + 1); if ("}" == a) if (null !== n.$findOpeningBracket("}", { column: o.column + 1, row: o.row }) && f.isAutoInsertedClosing(o, r, s)) return f.popAutoInsertedClosing(), { text: "", selection: [1, 1] } } else { if ("\n" == s || "\r\n" == s) { d(i); var l = ""; if (f.isMaybeInsertedClosing(o, r) && (l = p.stringRepeat("}", m.maybeInsertedBrackets), f.clearMaybeInsertedClosing()), "}" === (a = r.substring(o.column, o.column + 1))) { var h = n.findMatchingBracket({ row: o.row, column: o.column + 1 }, "}"); if (!h) return null; var c = this.$getIndent(n.getLine(h.row)) } else { if (!l) return void f.clearMaybeInsertedClosing(); c = this.$getIndent(r) } s = c + n.getTabString(); return { text: "\n" + s + "\n" + c + l, selection: [1, s.length, 1, s.length] } } f.clearMaybeInsertedClosing() } }), this.add("braces", "deletion", function (e, t, i, n, s) { var o = n.doc.getTextRange(s); if (!s.isMultiLine() && "{" == o) { if (d(i), "}" == n.doc.getLine(s.start.row).substring(s.end.column, s.end.column + 1)) return s.end.column++, s; m.maybeInsertedBrackets-- } }), this.add("parens", "insertion", function (e, t, i, n, s) { if ("(" == s) return d(i), o = i.getSelectionRange(), "" !== (r = n.doc.getTextRange(o)) && i.getWrapBehavioursEnabled() ? g(o, r, "(", ")") : f.isSaneInsertion(i, n) ? (f.recordAutoInsert(i, n, ")"), { text: "()", selection: [1, 1] }) : void 0; if (")" == s) { d(i); var o = i.getCursorPosition(), r = n.doc.getLine(o.row), i = r.substring(o.column, o.column + 1); if (")" == i) if (null !== n.$findOpeningBracket(")", { column: o.column + 1, row: o.row }) && f.isAutoInsertedClosing(o, r, s)) return f.popAutoInsertedClosing(), { text: "", selection: [1, 1] } } }), this.add("parens", "deletion", function (e, t, i, n, s) { var o = n.doc.getTextRange(s); if (!s.isMultiLine() && "(" == o && (d(i), ")" == n.doc.getLine(s.start.row).substring(s.start.column + 1, s.start.column + 2))) return s.end.column++, s }), this.add("brackets", "insertion", function (e, t, i, n, s) { if ("[" == s) return d(i), o = i.getSelectionRange(), "" !== (r = n.doc.getTextRange(o)) && i.getWrapBehavioursEnabled() ? g(o, r, "[", "]") : f.isSaneInsertion(i, n) ? (f.recordAutoInsert(i, n, "]"), { text: "[]", selection: [1, 1] }) : void 0; if ("]" == s) { d(i); var o = i.getCursorPosition(), r = n.doc.getLine(o.row), i = r.substring(o.column, o.column + 1); if ("]" == i) if (null !== n.$findOpeningBracket("]", { column: o.column + 1, row: o.row }) && f.isAutoInsertedClosing(o, r, s)) return f.popAutoInsertedClosing(), { text: "", selection: [1, 1] } } }), this.add("brackets", "deletion", function (e, t, i, n, s) { var o = n.doc.getTextRange(s); if (!s.isMultiLine() && "[" == o && (d(i), "]" == n.doc.getLine(s.start.row).substring(s.start.column + 1, s.start.column + 2))) return s.end.column++, s }), this.add("string_dquotes", "insertion", function (e, t, i, n, s) { var o = n.$mode.$quotes || w; if (1 == s.length && o[s] && (!this.lineCommentStart || -1 == this.lineCommentStart.indexOf(s))) { d(i); var r = i.getSelectionRange(), a = n.doc.getTextRange(r); if ("" !== a && (1 != a.length || !o[a]) && i.getWrapBehavioursEnabled()) return g(r, a, s, s); if (!a) { var o = i.getCursorPosition(), r = n.doc.getLine(o.row), a = r.substring(o.column - 1, o.column), i = r.substring(o.column, o.column + 1), l = n.getTokenAt(o.row, o.column), h = n.getTokenAt(o.row, o.column + 1); if ("\\" == a && l && /escape/.test(l.type)) return null; var c, l = l && /string|escape/.test(l.type), u = !h || /string|escape/.test(h.type); if (i == s) (c = l !== u) && /string\.end/.test(h.type) && (c = !1); else { if (l && !u) return null; if (l && u) return null; h = n.$mode.tokenRe, l = (h.lastIndex = 0, h.test(a)), u = (h.lastIndex = 0, h.test(a)); if (l || u) return null; if (i && !/[\s;,.})\]\\]/.test(i)) return null; n = r[o.column - 2]; if (a == s && (n == s || h.test(n))) return null; c = !0 } return { text: c ? s + s : "", selection: [1, 1] } } } }), this.add("string_dquotes", "deletion", function (e, t, i, n, s) { var o = n.$mode.$quotes || w, r = n.doc.getTextRange(s); if (!s.isMultiLine() && o.hasOwnProperty(r) && (d(i), n.doc.getLine(s.start.row).substring(s.start.column + 1, s.start.column + 2) == r)) return s.end.column++, s }) } var m, n = e("../../lib/oop"), s = e("../behaviour").Behaviour, o = e("../../token_iterator").TokenIterator, p = e("../../lib/lang"), r = ["text", "paren.rparen", "rparen", "paren", "punctuation.operator"], a = ["text", "paren.rparen", "rparen", "paren", "punctuation.operator", "comment"], l = {}, w = { '"': '"', "'": "'" }; f.isSaneInsertion = function (e, t) { var i = e.getCursorPosition(), n = new o(t, i.row, i.column); if (!this.$matchTokenType(n.getCurrentToken() || "text", r)) { if (/[)}\]]/.test(e.session.getLine(i.row)[i.column])) return !0; e = new o(t, i.row, i.column + 1); if (!this.$matchTokenType(e.getCurrentToken() || "text", r)) return !1 } return n.stepForward(), n.getCurrentTokenRow() !== i.row || this.$matchTokenType(n.getCurrentToken() || "text", a) }, f.$matchTokenType = function (e, t) { return -1 < t.indexOf(e.type || e) }, f.recordAutoInsert = function (e, t, i) { e = e.getCursorPosition(), t = t.doc.getLine(e.row); this.isAutoInsertedClosing(e, t, m.autoInsertedLineEnd[0]) || (m.autoInsertedBrackets = 0), m.autoInsertedRow = e.row, m.autoInsertedLineEnd = i + t.substr(e.column), m.autoInsertedBrackets++ }, f.recordMaybeInsert = function (e, t, i) { e = e.getCursorPosition(), t = t.doc.getLine(e.row); this.isMaybeInsertedClosing(e, t) || (m.maybeInsertedBrackets = 0), m.maybeInsertedRow = e.row, m.maybeInsertedLineStart = t.substr(0, e.column) + i, m.maybeInsertedLineEnd = t.substr(e.column), m.maybeInsertedBrackets++ }, f.isAutoInsertedClosing = function (e, t, i) { return 0 < m.autoInsertedBrackets && e.row === m.autoInsertedRow && i === m.autoInsertedLineEnd[0] && t.substr(e.column) === m.autoInsertedLineEnd }, f.isMaybeInsertedClosing = function (e, t) { return 0 < m.maybeInsertedBrackets && e.row === m.maybeInsertedRow && t.substr(e.column) === m.maybeInsertedLineEnd && t.substr(0, e.column) == m.maybeInsertedLineStart }, f.popAutoInsertedClosing = function () { m.autoInsertedLineEnd = m.autoInsertedLineEnd.substr(1), m.autoInsertedBrackets-- }, f.clearMaybeInsertedClosing = function () { m && (m.maybeInsertedBrackets = 0, m.maybeInsertedRow = -1) }, n.inherits(f, s), t.CstyleBehaviour = f }), define("ace/unicode", ["require", "exports", "module"], function (e, t, i) { "use strict"; for (var n = [48, 9, 8, 25, 5, 0, 2, 25, 48, 0, 11, 0, 5, 0, 6, 22, 2, 30, 2, 457, 5, 11, 15, 4, 8, 0, 2, 0, 18, 116, 2, 1, 3, 3, 9, 0, 2, 2, 2, 0, 2, 19, 2, 82, 2, 138, 2, 4, 3, 155, 12, 37, 3, 0, 8, 38, 10, 44, 2, 0, 2, 1, 2, 1, 2, 0, 9, 26, 6, 2, 30, 10, 7, 61, 2, 9, 5, 101, 2, 7, 3, 9, 2, 18, 3, 0, 17, 58, 3, 100, 15, 53, 5, 0, 6, 45, 211, 57, 3, 18, 2, 5, 3, 11, 3, 9, 2, 1, 7, 6, 2, 2, 2, 7, 3, 1, 3, 21, 2, 6, 2, 0, 4, 3, 3, 8, 3, 1, 3, 3, 9, 0, 5, 1, 2, 4, 3, 11, 16, 2, 2, 5, 5, 1, 3, 21, 2, 6, 2, 1, 2, 1, 2, 1, 3, 0, 2, 4, 5, 1, 3, 2, 4, 0, 8, 3, 2, 0, 8, 15, 12, 2, 2, 8, 2, 2, 2, 21, 2, 6, 2, 1, 2, 4, 3, 9, 2, 2, 2, 2, 3, 0, 16, 3, 3, 9, 18, 2, 2, 7, 3, 1, 3, 21, 2, 6, 2, 1, 2, 4, 3, 8, 3, 1, 3, 2, 9, 1, 5, 1, 2, 4, 3, 9, 2, 0, 17, 1, 2, 5, 4, 2, 2, 3, 4, 1, 2, 0, 2, 1, 4, 1, 4, 2, 4, 11, 5, 4, 4, 2, 2, 3, 3, 0, 7, 0, 15, 9, 18, 2, 2, 7, 2, 2, 2, 22, 2, 9, 2, 4, 4, 7, 2, 2, 2, 3, 8, 1, 2, 1, 7, 3, 3, 9, 19, 1, 2, 7, 2, 2, 2, 22, 2, 9, 2, 4, 3, 8, 2, 2, 2, 3, 8, 1, 8, 0, 2, 3, 3, 9, 19, 1, 2, 7, 2, 2, 2, 22, 2, 15, 4, 7, 2, 2, 2, 3, 10, 0, 9, 3, 3, 9, 11, 5, 3, 1, 2, 17, 4, 23, 2, 8, 2, 0, 3, 6, 4, 0, 5, 5, 2, 0, 2, 7, 19, 1, 14, 57, 6, 14, 2, 9, 40, 1, 2, 0, 3, 1, 2, 0, 3, 0, 7, 3, 2, 6, 2, 2, 2, 0, 2, 0, 3, 1, 2, 12, 2, 2, 3, 4, 2, 0, 2, 5, 3, 9, 3, 1, 35, 0, 24, 1, 7, 9, 12, 0, 2, 0, 2, 0, 5, 9, 2, 35, 5, 19, 2, 5, 5, 7, 2, 35, 10, 0, 58, 73, 7, 77, 3, 37, 11, 42, 2, 0, 4, 328, 2, 3, 3, 6, 2, 0, 2, 3, 3, 40, 2, 3, 3, 32, 2, 3, 3, 6, 2, 0, 2, 3, 3, 14, 2, 56, 2, 3, 3, 66, 5, 0, 33, 15, 17, 84, 13, 619, 3, 16, 2, 25, 6, 74, 22, 12, 2, 6, 12, 20, 12, 19, 13, 12, 2, 2, 2, 1, 13, 51, 3, 29, 4, 0, 5, 1, 3, 9, 34, 2, 3, 9, 7, 87, 9, 42, 6, 69, 11, 28, 4, 11, 5, 11, 11, 39, 3, 4, 12, 43, 5, 25, 7, 10, 38, 27, 5, 62, 2, 28, 3, 10, 7, 9, 14, 0, 89, 75, 5, 9, 18, 8, 13, 42, 4, 11, 71, 55, 9, 9, 4, 48, 83, 2, 2, 30, 14, 230, 23, 280, 3, 5, 3, 37, 3, 5, 3, 7, 2, 0, 2, 0, 2, 0, 2, 30, 3, 52, 2, 6, 2, 0, 4, 2, 2, 6, 4, 3, 3, 5, 5, 12, 6, 2, 2, 6, 67, 1, 20, 0, 29, 0, 14, 0, 17, 4, 60, 12, 5, 0, 4, 11, 18, 0, 5, 0, 3, 9, 2, 0, 4, 4, 7, 0, 2, 0, 2, 0, 2, 3, 2, 10, 3, 3, 6, 4, 5, 0, 53, 1, 2684, 46, 2, 46, 2, 132, 7, 6, 15, 37, 11, 53, 10, 0, 17, 22, 10, 6, 2, 6, 2, 6, 2, 6, 2, 6, 2, 6, 2, 6, 2, 6, 2, 31, 48, 0, 470, 1, 36, 5, 2, 4, 6, 1, 5, 85, 3, 1, 3, 2, 2, 89, 2, 3, 6, 40, 4, 93, 18, 23, 57, 15, 513, 6581, 75, 20939, 53, 1164, 68, 45, 3, 268, 4, 27, 21, 31, 3, 13, 13, 1, 2, 24, 9, 69, 11, 1, 38, 8, 3, 102, 3, 1, 111, 44, 25, 51, 13, 68, 12, 9, 7, 23, 4, 0, 5, 45, 3, 35, 13, 28, 4, 64, 15, 10, 39, 54, 10, 13, 3, 9, 7, 22, 4, 1, 5, 66, 25, 2, 227, 42, 2, 1, 3, 9, 7, 11171, 13, 22, 5, 48, 8453, 301, 3, 61, 3, 105, 39, 6, 13, 4, 6, 11, 2, 12, 2, 4, 2, 0, 2, 1, 2, 1, 2, 107, 34, 362, 19, 63, 3, 53, 41, 11, 5, 15, 17, 6, 13, 1, 25, 2, 33, 4, 2, 134, 20, 9, 8, 25, 5, 0, 2, 25, 12, 88, 4, 5, 3, 5, 3, 5, 3, 2], s = 0, o = [], r = 0; r < n.length; r += 2)o.push(s += n[r]), n[r + 1] && o.push(45, s += n[r + 1]); t.wordChars = String.fromCharCode.apply(null, o) }), define("ace/mode/text", ["require", "exports", "module", "ace/config", "ace/tokenizer", "ace/mode/text_highlight_rules", "ace/mode/behaviour/cstyle", "ace/unicode", "ace/lib/lang", "ace/token_iterator", "ace/range"], function (e, t, i) { "use strict"; function n() { this.HighlightRules = o } var r = e("../config"), s = e("../tokenizer").Tokenizer, o = e("./text_highlight_rules").TextHighlightRules, a = e("./behaviour/cstyle").CstyleBehaviour, l = e("../unicode"), y = e("../lib/lang"), m = e("../token_iterator").TokenIterator, p = e("../range").Range; (function () { this.$defaultBehaviour = new a, this.tokenRe = new RegExp("^[" + l.wordChars + "\\$_]+", "g"), this.nonTokenRe = new RegExp("^(?:[^" + l.wordChars + "\\$_]|\\s])+", "g"), this.getTokenizer = function () { return this.$tokenizer || (this.$highlightRules = this.$highlightRules || new this.HighlightRules(this.$highlightRuleConfig), this.$tokenizer = new s(this.$highlightRules.getRules())), this.$tokenizer }, this.lineCommentStart = "", this.blockComment = "", this.toggleCommentLines = function (e, s, i, n) { function t(e) { for (var t = i; t <= n; t++)e(o.getLine(t), t) } var o = s.doc, r = !0, a = !0, l = 1 / 0, h = s.getTabSize(), c = !1; if (this.lineCommentStart) { p = Array.isArray(this.lineCommentStart) ? (v = this.lineCommentStart.map(y.escapeRegExp).join("|"), this.lineCommentStart[0]) : (v = y.escapeRegExp(this.lineCommentStart), this.lineCommentStart), v = new RegExp("^(\\s*)(?:" + v + ") ?"), c = s.getUseSoftTabs(); var u = function (e, t) { var i, n, s = e.match(v); s && (i = s[1].length, n = s[0].length, m(e, i, n) || " " != s[0][n - 1] || n--, o.removeInLine(t, i, n)) }, d = p + " ", g = function (e, t) { r && !/\S/.test(e) || (m(e, l, l) ? o.insertInLine({ row: t, column: l }, d) : o.insertInLine({ row: t, column: l }, p)) }, f = function (e, t) { return v.test(e) }, m = function (e, t, i) { for (var n = 0; t-- && " " == e.charAt(t);)n++; if (n % h != 0) return !1; for (n = 0; " " == e.charAt(i++);)n++; return 2 < h ? n % h != h - 1 : n % h == 0 } } else { if (!this.blockComment) return !1; var p = this.blockComment.start, w = this.blockComment.end, v = new RegExp("^(\\s*)(?:" + y.escapeRegExp(p) + ")"), $ = new RegExp("(?:" + y.escapeRegExp(w) + ")\\s*$"), g = function (e, t) { f(e, t) || r && !/\S/.test(e) || (o.insertInLine({ row: t, column: e.length }, w), o.insertInLine({ row: t, column: l }, p)) }, u = function (e, t) { var i; (i = e.match($)) && o.removeInLine(t, e.length - i[0].length, e.length), (i = e.match(v)) && o.removeInLine(t, i[1].length, i[0].length) }, f = function (e, t) { if (v.test(e)) return !0; for (var i = s.getTokens(t), n = 0; n < i.length; n++)if ("comment" === i[n].type) return !0 } } var b = 1 / 0; t(function (e, t) { var i = e.search(/\S/); -1 !== i ? (i < l && (l = i), a && !f(e, t) && (a = !1)) : b > e.length && (b = e.length) }), l == 1 / 0 && (l = b, a = r = !1), c && l % h != 0 && (l = Math.floor(l / h) * h), t(a ? u : g) }, this.toggleBlockComment = function (e, t, i, n) { var s = this.blockComment; if (s) { !s.start && s[0] && (s = s[0]); var o, r, a = (d = new m(t, n.row, n.column)).getCurrentToken(), l = (t.selection, t.selection.toOrientedRange()); if (a && /comment/.test(a.type)) { for (; a && /comment/.test(a.type);) { if (-1 != (g = a.value.indexOf(s.start))) { var h = d.getCurrentTokenRow(), c = d.getCurrentTokenColumn() + g, u = new p(h, c, h, c + s.start.length); break } a = d.stepBackward() } for (var d, g, a = (d = new m(t, n.row, n.column)).getCurrentToken(); a && /comment/.test(a.type);) { if (-1 != (g = a.value.indexOf(s.end))) { var h = d.getCurrentTokenRow(), c = d.getCurrentTokenColumn() + g, f = new p(h, c, h, c + s.end.length); break } a = d.stepForward() } f && t.remove(f), u && (t.remove(u), o = u.start.row, r = -s.start.length) } else r = s.start.length, o = i.start.row, t.insert(i.end, s.end), t.insert(i.start, s.start); l.start.row == o && (l.start.column += r), l.end.row == o && (l.end.column += r), t.selection.fromOrientedRange(l) } }, this.getNextLineIndent = function (e, t, i) { return this.$getIndent(t) }, this.checkOutdent = function (e, t, i) { return !1 }, this.autoOutdent = function (e, t, i) { }, this.$getIndent = function (e) { return e.match(/^\s*/)[0] }, this.createWorker = function (e) { return null }, this.createModeDelegates = function (e) { for (var n in this.$embeds = [], this.$modes = {}, e) { var t, i, s; e[n] && (i = (t = e[n]).prototype.$id, (s = r.$modes[i]) || (r.$modes[i] = s = new t), r.$modes[n] || (r.$modes[n] = s), this.$embeds.push(n), this.$modes[n] = s) } for (var o = ["toggleBlockComment", "toggleCommentLines", "getNextLineIndent", "checkOutdent", "autoOutdent", "transformAction", "getCompletions"], n = 0; n < o.length; n++)!function (e) { var t = o[n], i = e[t]; e[o[n]] = function () { return this.$delegator(t, arguments, i) } }(this) }, this.$delegator = function (e, t, i) { var n = t[0] || "start"; if ("string" != typeof n) { if (Array.isArray(n[2])) { var s = n[2][n[2].length - 1]; if (r = this.$modes[s]) return r[e].apply(r, [n[1]].concat([].slice.call(t, 1))) } n = n[0] || "start" } for (var o = 0; o < this.$embeds.length; o++)if (this.$modes[this.$embeds[o]]) { var r, a = n.split(this.$embeds[o]); if (!a[0] && a[1]) return t[0] = a[1], (r = this.$modes[this.$embeds[o]])[e].apply(r, t) } s = i.apply(this, t); return i ? s : void 0 }, this.transformAction = function (e, t, i, n, s) { if (this.$behaviour) { var o, r = this.$behaviour.getBehaviours(); for (o in r) if (r[o][t]) { var a = r[o][t].apply(this, arguments); if (a) return a } } }, this.getKeywords = function (e) { if (!this.completionKeywords) { var t = this.$tokenizer.rules, i = []; for (n in t) for (var n, s = t[n], o = 0, r = s.length; o < r; o++)if ("string" == typeof s[o].token) /keyword|support|storage/.test(s[o].token) && i.push(s[o].regex); else if ("object" == typeof s[o].token) for (var a = 0, l = s[o].token.length; a < l; a++)/keyword|support|storage/.test(s[o].token[a]) && (n = s[o].regex.match(/\(.+?\)/g)[a], i.push(n.substr(1, n.length - 2))); this.completionKeywords = i } return e ? i.concat(this.$keywordList || []) : this.$keywordList }, this.$createKeywordList = function () { return this.$highlightRules || this.getTokenizer(), this.$keywordList = this.$highlightRules.$keywordList || [] }, this.getCompletions = function (e, t, i, n) { return (this.$keywordList || this.$createKeywordList()).map(function (e) { return { name: e, value: e, score: 0, meta: "keyword" } }) }, this.$id = "ace/mode/text" }).call(n.prototype), t.Mode = n }), define("ace/apply_delta", ["require", "exports", "module"], function (e, t, i) { "use strict"; t.applyDelta = function (e, t, i) { var n = t.start.row, s = t.start.column, o = e[n] || ""; switch (t.action) { case "insert": 1 === t.lines.length ? e[n] = o.substring(0, s) + t.lines[0] + o.substring(s) : (r = [n, 1].concat(t.lines), e.splice.apply(e, r), e[n] = o.substring(0, s) + e[n], e[n + t.lines.length - 1] += o.substring(s)); break; case "remove": var r = t.end.column, a = t.end.row; n === a ? e[n] = o.substring(0, s) + o.substring(r) : e.splice(n, a - n + 1, o.substring(0, s) + e[a].substring(r)) } } }), define("ace/anchor", ["require", "exports", "module", "ace/lib/oop", "ace/lib/event_emitter"], function (e, t, i) { "use strict"; var n = e("./lib/oop"), s = e("./lib/event_emitter").EventEmitter, e = t.Anchor = function (e, t, i) { this.$onChange = this.onChange.bind(this), this.attach(e), void 0 === i ? this.setPosition(t.row, t.column) : this.setPosition(t, i) }; !function () { function a(e, t, i) { i = i ? e.column <= t.column : e.column < t.column; return e.row < t.row || e.row == t.row && i } n.implement(this, s), this.getPosition = function () { return this.$clipPositionToDocument(this.row, this.column) }, this.getDocument = function () { return this.document }, this.$insertRight = !1, this.onChange = function (e) { var t, i, n, s, o, r; e.start.row == e.end.row && e.start.row != this.row || e.start.row > this.row || (e = e, t = { row: this.row, column: this.column }, i = this.$insertRight, n = ((r = "insert" == e.action) ? 1 : -1) * (e.end.row - e.start.row), s = (r ? 1 : -1) * (e.end.column - e.start.column), o = e.start, r = r ? o : e.end, e = a(t, o, i) ? { row: t.row, column: t.column } : a(r, t, !i) ? { row: t.row + n, column: t.column + (t.row == r.row ? s : 0) } : { row: o.row, column: o.column }, this.setPosition(e.row, e.column, !0)) }, this.setPosition = function (e, t, i) { i = i ? { row: e, column: t } : this.$clipPositionToDocument(e, t); this.row == i.row && this.column == i.column || (e = { row: this.row, column: this.column }, this.row = i.row, this.column = i.column, this._signal("change", { old: e, value: i })) }, this.detach = function () { this.document.off("change", this.$onChange) }, this.attach = function (e) { this.document = e || this.document, this.document.on("change", this.$onChange) }, this.$clipPositionToDocument = function (e, t) { var i = {}; return e >= this.document.getLength() ? (i.row = Math.max(0, this.document.getLength() - 1), i.column = this.document.getLine(i.row).length) : e < 0 ? (i.row = 0, i.column = 0) : (i.row = e, i.column = Math.min(this.document.getLine(i.row).length, Math.max(0, t))), t < 0 && (i.column = 0), i } }.call(e.prototype) }), define("ace/document", ["require", "exports", "module", "ace/lib/oop", "ace/apply_delta", "ace/lib/event_emitter", "ace/range", "ace/anchor"], function (e, t, i) { "use strict"; function n(e) { this.$lines = [""], 0 === e.length ? this.$lines = [""] : Array.isArray(e) ? this.insertMergedLines({ row: 0, column: 0 }, e) : this.insert({ row: 0, column: 0 }, e) } var s = e("./lib/oop"), o = e("./apply_delta").applyDelta, r = e("./lib/event_emitter").EventEmitter, a = e("./range").Range, l = e("./anchor").Anchor; (function () { s.implement(this, r), this.setValue = function (e) { var t = this.getLength() - 1; this.remove(new a(0, 0, t, this.getLine(t).length)), this.insert({ row: 0, column: 0 }, e || "") }, this.getValue = function () { return this.getAllLines().join(this.getNewLineCharacter()) }, this.createAnchor = function (e, t) { return new l(this, e, t) }, 0 === "aaa".split(/a/).length ? this.$split = function (e) { return e.replace(/\r\n|\r/g, "\n").split("\n") } : this.$split = function (e) { return e.split(/\r\n|\r|\n/) }, this.$detectNewLine = function (e) { e = e.match(/^.*?(\r\n|\r|\n)/m); this.$autoNewLine = e ? e[1] : "\n", this._signal("changeNewLineMode") }, this.getNewLineCharacter = function () { switch (this.$newLineMode) { case "windows": return "\r\n"; case "unix": return "\n"; default: return this.$autoNewLine || "\n" } }, this.$autoNewLine = "", this.$newLineMode = "auto", this.setNewLineMode = function (e) { this.$newLineMode !== e && (this.$newLineMode = e, this._signal("changeNewLineMode")) }, this.getNewLineMode = function () { return this.$newLineMode }, this.isNewLine = function (e) { return "\r\n" == e || "\r" == e || "\n" == e }, this.getLine = function (e) { return this.$lines[e] || "" }, this.getLines = function (e, t) { return this.$lines.slice(e, t + 1) }, this.getAllLines = function () { return this.getLines(0, this.getLength()) }, this.getLength = function () { return this.$lines.length }, this.getTextRange = function (e) { return this.getLinesForRange(e).join(this.getNewLineCharacter()) }, this.getLinesForRange = function (e) { var t, i; return e.start.row === e.end.row ? t = [this.getLine(e.start.row).substring(e.start.column, e.end.column)] : ((t = this.getLines(e.start.row, e.end.row))[0] = (t[0] || "").substring(e.start.column), i = t.length - 1, e.end.row - e.start.row == i && (t[i] = t[i].substring(0, e.end.column))), t }, this.insertLines = function (e, t) { return console.warn("Use of document.insertLines is deprecated. Use the insertFullLines method instead."), this.insertFullLines(e, t) }, this.removeLines = function (e, t) { return console.warn("Use of document.removeLines is deprecated. Use the removeFullLines method instead."), this.removeFullLines(e, t) }, this.insertNewLine = function (e) { return console.warn("Use of document.insertNewLine is deprecated. Use insertMergedLines(position, ['', '']) instead."), this.insertMergedLines(e, ["", ""]) }, this.insert = function (e, t) { return this.getLength() <= 1 && this.$detectNewLine(t), this.insertMergedLines(e, this.$split(t)) }, this.insertInLine = function (e, t) { var i = this.clippedPos(e.row, e.column), e = this.pos(e.row, e.column + t.length); return this.applyDelta({ start: i, end: e, action: "insert", lines: [t] }, !0), this.clonePos(e) }, this.clippedPos = function (e, t) { var i = this.getLength(), i = (void 0 === e ? e = i : e < 0 ? e = 0 : i <= e && (e = i - 1, t = void 0), this.getLine(e)); return null == t && (t = i.length), { row: e, column: t = Math.min(Math.max(t, 0), i.length) } }, this.clonePos = function (e) { return { row: e.row, column: e.column } }, this.pos = function (e, t) { return { row: e, column: t } }, this.$clipPosition = function (e) { var t = this.getLength(); return e.row >= t ? (e.row = Math.max(0, t - 1), e.column = this.getLine(t - 1).length) : (e.row = Math.max(0, e.row), e.column = Math.min(Math.max(e.column, 0), this.getLine(e.row).length)), e }, this.insertFullLines = function (e, t) { var i = 0, i = (e = Math.min(Math.max(e, 0), this.getLength())) < this.getLength() ? (t = t.concat([""]), 0) : (t = [""].concat(t), this.$lines[--e].length); this.insertMergedLines({ row: e, column: i }, t) }, this.insertMergedLines = function (e, t) { var e = this.clippedPos(e.row, e.column), i = { row: e.row + t.length - 1, column: (1 == t.length ? e.column : 0) + t[t.length - 1].length }; return this.applyDelta({ start: e, end: i, action: "insert", lines: t }), this.clonePos(i) }, this.remove = function (e) { var t = this.clippedPos(e.start.row, e.start.column), e = this.clippedPos(e.end.row, e.end.column); return this.applyDelta({ start: t, end: e, action: "remove", lines: this.getLinesForRange({ start: t, end: e }) }), this.clonePos(t) }, this.removeInLine = function (e, t, i) { t = this.clippedPos(e, t), e = this.clippedPos(e, i); return this.applyDelta({ start: t, end: e, action: "remove", lines: this.getLinesForRange({ start: t, end: e }) }, !0), this.clonePos(t) }, this.removeFullLines = function (e, t) { e = Math.min(Math.max(0, e), this.getLength() - 1); var i = (t = Math.min(Math.max(0, t), this.getLength() - 1)) == this.getLength() - 1 && 0 < e, n = t < this.getLength() - 1, s = i ? e - 1 : e, i = i ? this.getLine(s).length : 0, o = n ? t + 1 : t, n = n ? 0 : this.getLine(o).length, s = new a(s, i, o, n), i = this.$lines.slice(e, t + 1); return this.applyDelta({ start: s.start, end: s.end, action: "remove", lines: this.getLinesForRange(s) }), i }, this.removeNewLine = function (e) { e < this.getLength() - 1 && 0 <= e && this.applyDelta({ start: this.pos(e, this.getLine(e).length), end: this.pos(e + 1, 0), action: "remove", lines: ["", ""] }) }, this.replace = function (e, t) { return e instanceof a || (e = a.fromPoints(e.start, e.end)), 0 === t.length && e.isEmpty() ? e.start : t == this.getTextRange(e) ? e.end : (this.remove(e), t ? this.insert(e.start, t) : e.start) }, this.applyDeltas = function (e) { for (var t = 0; t < e.length; t++)this.applyDelta(e[t]) }, this.revertDeltas = function (e) { for (var t = e.length - 1; 0 <= t; t--)this.revertDelta(e[t]) }, this.applyDelta = function (e, t) { var i = "insert" == e.action; (i ? e.lines.length <= 1 && !e.lines[0] : !a.comparePoints(e.start, e.end)) || (i && 2e4 < e.lines.length ? this.$splitAndapplyLargeDelta(e, 2e4) : (o(this.$lines, e, t), this._signal("change", e))) }, this.$safeApplyDelta = function (e) { var t = this.$lines.length; ("remove" == e.action && e.start.row < t && e.end.row < t || "insert" == e.action && e.start.row <= t) && this.applyDelta(e) }, this.$splitAndapplyLargeDelta = function (e, t) { for (var i = e.lines, n = i.length - t + 1, s = e.start.row, o = e.start.column, r = 0, a = 0; r < n; r = a) { a += t - 1; var l = i.slice(r, a); l.push(""), this.applyDelta({ start: this.pos(s + r, o), end: this.pos(s + a, o = 0), action: e.action, lines: l }, !0) } e.lines = i.slice(r), e.start.row = s + r, e.start.column = o, this.applyDelta(e, !0) }, this.revertDelta = function (e) { this.$safeApplyDelta({ start: this.clonePos(e.start), end: this.clonePos(e.end), action: "insert" == e.action ? "remove" : "insert", lines: e.lines.slice() }) }, this.indexToPosition = function (e, t) { for (var i = this.$lines || this.getAllLines(), n = this.getNewLineCharacter().length, s = t || 0, o = i.length; s < o; s++)if ((e -= i[s].length + n) < 0) return { row: s, column: e + i[s].length + n }; return { row: o - 1, column: e + i[o - 1].length + n } }, this.positionToIndex = function (e, t) { for (var i = this.$lines || this.getAllLines(), n = this.getNewLineCharacter().length, s = 0, o = Math.min(e.row, i.length), r = t || 0; r < o; ++r)s += i[r].length + n; return s + e.column } }).call(n.prototype), t.Document = n }), define("ace/background_tokenizer", ["require", "exports", "module", "ace/lib/oop", "ace/lib/event_emitter"], function (e, t, i) { "use strict"; function n(e, t) { this.running = !1, this.lines = [], this.states = [], this.currentLine = 0, this.tokenizer = e; var a = this; this.$worker = function () { if (a.running) { for (var e = new Date, t = a.currentLine, i = -1, n = a.doc, s = t; a.lines[t];)t++; var o = n.getLength(), r = 0; for (a.running = !1; t < o;) { for (a.$tokenizeRow(t), i = t; t++, a.lines[t];); if (++r % 5 == 0 && 20 < new Date - e) { a.running = setTimeout(a.$worker, 20); break } } a.currentLine = t, s <= (i = -1 == i ? t : i) && a.fireUpdateEvent(s, i) } } } var s = e("./lib/oop"), o = e("./lib/event_emitter").EventEmitter; (function () { s.implement(this, o), this.setTokenizer = function (e) { this.tokenizer = e, this.lines = [], this.states = [], this.start(0) }, this.setDocument = function (e) { this.doc = e, this.lines = [], this.states = [], this.stop() }, this.fireUpdateEvent = function (e, t) { this._signal("update", { data: { first: e, last: t } }) }, this.start = function (e) { this.currentLine = Math.min(e || 0, this.currentLine, this.doc.getLength()), this.lines.splice(this.currentLine, this.lines.length), this.states.splice(this.currentLine, this.states.length), this.stop(), this.running = setTimeout(this.$worker, 700) }, this.scheduleStart = function () { this.running || (this.running = setTimeout(this.$worker, 700)) }, this.$updateOnChange = function (e) { var t = e.start.row, i = e.end.row - t; 0 == i ? this.lines[t] = null : "remove" == e.action ? (this.lines.splice(t, 1 + i, null), this.states.splice(t, 1 + i, null)) : ((e = Array(1 + i)).unshift(t, 1), this.lines.splice.apply(this.lines, e), this.states.splice.apply(this.states, e)), this.currentLine = Math.min(t, this.currentLine, this.doc.getLength()), this.stop() }, this.stop = function () { this.running && clearTimeout(this.running), this.running = !1 }, this.getTokens = function (e) { return this.lines[e] || this.$tokenizeRow(e) }, this.getState = function (e) { return this.currentLine == e && this.$tokenizeRow(e), this.states[e] || "start" }, this.$tokenizeRow = function (e) { var t = this.doc.getLine(e), i = this.states[e - 1], t = this.tokenizer.getLineTokens(t, i, e); return this.states[e] + "" != t.state + "" ? (this.states[e] = t.state, this.lines[e + 1] = null, this.currentLine > e + 1 && (this.currentLine = e + 1)) : this.currentLine == e && (this.currentLine = e + 1), this.lines[e] = t.tokens }, this.cleanup = function () { this.running = !1, this.lines = [], this.states = [], this.currentLine = 0, this.removeAllListeners() } }).call(n.prototype), t.BackgroundTokenizer = n }), define("ace/search_highlight", ["require", "exports", "module", "ace/lib/lang", "ace/lib/oop", "ace/range"], function (e, t, i) { "use strict"; function n(e, t, i) { this.setRegexp(e), this.clazz = t, this.type = i || "text" } var d = e("./lib/lang"), g = (e("./lib/oop"), e("./range").Range); (function () { this.MAX_RANGES = 500, this.setRegexp = function (e) { this.regExp + "" != e + "" && (this.regExp = e, this.cache = []) }, this.update = function (e, t, i, n) { if (this.regExp) for (var s = n.firstRow, o = n.lastRow, r = {}, a = s; a <= o; a++) { var l = this.cache[a]; null == l && (l = (l = (l = d.getMatchOffsets(i.getLine(a), this.regExp)).length > this.MAX_RANGES ? l.slice(0, this.MAX_RANGES) : l).map(function (e) { return new g(a, e.offset, a, e.offset + e.length) }), this.cache[a] = l.length ? l : ""); for (var h = l.length; h--;) { var c = l[h].toScreenRange(i), u = c.toString(); r[u] || (r[u] = !0, t.drawSingleLineMarker(e, c, this.clazz, n)) } } } }).call(n.prototype), t.SearchHighlight = n }), define("ace/edit_session/fold_line", ["require", "exports", "module", "ace/range"], function (e, t, i) { "use strict"; function s(e, t) { this.foldData = e, Array.isArray(t) ? this.folds = t : t = this.folds = [t]; e = t[t.length - 1]; this.range = new n(t[0].start.row, t[0].start.column, e.end.row, e.end.column), this.start = this.range.start, this.end = this.range.end, this.folds.forEach(function (e) { e.setFoldLine(this) }, this) } var n = e("../range").Range; (function () { this.shiftRow = function (t) { this.start.row += t, this.end.row += t, this.folds.forEach(function (e) { e.start.row += t, e.end.row += t }) }, this.addFold = function (e) { if (e.sameRow) { if (e.start.row < this.startRow || e.endRow > this.endRow) throw new Error("Can't add a fold to this FoldLine as it has no connection"); this.folds.push(e), this.folds.sort(function (e, t) { return -e.range.compareEnd(t.start.row, t.start.column) }), 0 < this.range.compareEnd(e.start.row, e.start.column) ? (this.end.row = e.end.row, this.end.column = e.end.column) : this.range.compareStart(e.end.row, e.end.column) < 0 && (this.start.row = e.start.row, this.start.column = e.start.column) } else if (e.start.row == this.end.row) this.folds.push(e), this.end.row = e.end.row, this.end.column = e.end.column; else { if (e.end.row != this.start.row) throw new Error("Trying to add fold to FoldRow that doesn't have a matching row"); this.folds.unshift(e), this.start.row = e.start.row, this.start.column = e.start.column } e.foldLine = this }, this.containsRow = function (e) { return e >= this.start.row && e <= this.end.row }, this.walk = function (e, t, i) { var n, s, o = 0, r = this.folds, a = !0; null == t && (t = this.end.row, i = this.end.column); for (var l = 0; l < r.length; l++) { if (-1 == (s = (n = r[l]).range.compareStart(t, i))) return void e(null, t, i, o, a); if (!e(null, n.start.row, n.start.column, o, a) && e(n.placeholder, n.start.row, n.start.column, o) || 0 === s) return; a = !n.sameRow, o = n.end.column } e(null, t, i, o, a) }, this.getNextFoldTo = function (e, t) { for (var i, n, s = 0; s < this.folds.length; s++) { if (-1 == (n = (i = this.folds[s]).range.compareEnd(e, t))) return { fold: i, kind: "after" }; if (0 === n) return { fold: i, kind: "inside" } } return null }, this.addRemoveChars = function (e, t, i) { var n, s = this.getNextFoldTo(e, t); if (s) if (n = s.fold, "inside" == s.kind && n.start.column != t && n.start.row != e) window.console && window.console.log(e, t, n); else if (n.start.row == e) { var o, r = (o = this.folds).indexOf(n); for (0 === r && (this.start.column += i); r < o.length; r++) { if ((n = o[r]).start.column += i, !n.sameRow) return; n.end.column += i } this.end.column += i } }, this.split = function (e, t) { e = this.getNextFoldTo(e, t); if (!e || "inside" == e.kind) return null; var t = e.fold, e = this.folds, i = this.foldData, t = e.indexOf(t), n = e[t - 1], n = (this.end.row = n.end.row, this.end.column = n.end.column, new s(i, e = e.splice(t, e.length - t))); return i.splice(i.indexOf(this) + 1, 0, n), n }, this.merge = function (e) { for (var t = e.folds, i = 0; i < t.length; i++)this.addFold(t[i]); var n = this.foldData; n.splice(n.indexOf(e), 1) }, this.toString = function () { var t = [this.range.toString() + ": ["]; return this.folds.forEach(function (e) { t.push("  " + e.toString()) }), t.push("]"), t.join("\n") }, this.idxToPosition = function (e) { for (var t = 0, i = 0; i < this.folds.length; i++) { var n = this.folds[i]; if ((e -= n.start.column - t) < 0) return { row: n.start.row, column: n.start.column + e }; if ((e -= n.placeholder.length) < 0) return n.start; t = n.end.column } return { row: this.end.row, column: this.end.column + e } } }).call(s.prototype), t.FoldLine = s }), define("ace/range_list", ["require", "exports", "module", "ace/range"], function (e, t, i) { "use strict"; function n() { this.ranges = [], this.$bias = 1 } var a = e("./range").Range.comparePoints; (function () { this.comparePoints = a, this.pointIndex = function (e, t, i) { for (var n = this.ranges, s = i || 0; s < n.length; s++) { var o = n[s], r = a(e, o.end); if (0 < r); else return o = a(e, o.start), 0 === r ? t && 0 !== o ? -s - 2 : s : 0 < o || 0 === o && !t ? s : -s - 1 } return -s - 1 }, this.add = function (e) { var t = !e.isEmpty(), i = this.pointIndex(e.start, t), t = this.pointIndex(e.end, t, i = i < 0 ? -i - 1 : i); return t < 0 ? t = -t - 1 : t++, this.ranges.splice(i, t - i, e) }, this.addList = function (e) { for (var t = [], i = e.length; i--;)t.push.apply(t, this.add(e[i])); return t }, this.substractPoint = function (e) { e = this.pointIndex(e); if (0 <= e) return this.ranges.splice(e, 1) }, this.merge = function () { for (var e = [], t = this.ranges, i = (t = t.sort(function (e, t) { return a(e.start, t.start) }))[0], n = 1; n < t.length; n++) { var s = i, i = t[n], o = a(s.end, i.start); o < 0 || (0 != o || s.isEmpty() || i.isEmpty()) && (a(s.end, i.end) < 0 && (s.end.row = i.end.row, s.end.column = i.end.column), t.splice(n, 1), e.push(i), i = s, n--) } return this.ranges = t, e }, this.contains = function (e, t) { return 0 <= this.pointIndex({ row: e, column: t }) }, this.containsPoint = function (e) { return 0 <= this.pointIndex(e) }, this.rangeAtPoint = function (e) { e = this.pointIndex(e); if (0 <= e) return this.ranges[e] }, this.clipRows = function (e, t) { var i = this.ranges; if (i[0].start.row > t || i[i.length - 1].start.row < e) return []; for (var e = this.pointIndex({ row: e, column: 0 }), n = this.pointIndex({ row: t, column: 0 }, e = e < 0 ? -e - 1 : e), s = (n < 0 && (n = -n - 1), []), o = e; o < n; o++)s.push(i[o]); return s }, this.removeAll = function () { return this.ranges.splice(0, this.ranges.length) }, this.attach = function (e) { this.session && this.detach(), this.session = e, this.onChange = this.$onChange.bind(this), this.session.on("change", this.onChange) }, this.detach = function () { this.session && (this.session.removeListener("change", this.onChange), this.session = null) }, this.$onChange = function (e) { for (var t = e.start, i = e.end, n = t.row, s = i.row, o = this.ranges, r = 0, a = o.length; r < a && !((c = o[r]).end.row >= n); r++); if ("insert" == e.action) for (var l = s - n, h = -t.column + i.column; r < a; r++) { if ((c = o[r]).start.row > n) break; c.start.row == n && c.start.column >= t.column && (c.start.column == t.column && this.$bias <= 0 || (c.start.column += h, c.start.row += l)), c.end.row == n && c.end.column >= t.column && (c.end.column == t.column && this.$bias < 0 || (c.end.column == t.column && 0 < h && r < a - 1 && c.end.column > c.start.column && c.end.column == o[r + 1].start.column && (c.end.column -= h), c.end.column += h, c.end.row += l)) } else for (var c, l = n - s, h = t.column - i.column; r < a; r++) { if ((c = o[r]).start.row > s) break; c.end.row < s && (n < c.end.row || n == c.end.row && t.column < c.end.column) ? (c.end.row = n, c.end.column = t.column) : c.end.row == s ? c.end.column <= i.column ? (l || c.end.column > t.column) && (c.end.column = t.column, c.end.row = t.row) : (c.end.column += h, c.end.row += l) : c.end.row > s && (c.end.row += l), c.start.row < s && (n < c.start.row || n == c.start.row && t.column < c.start.column) ? (c.start.row = n, c.start.column = t.column) : c.start.row == s ? c.start.column <= i.column ? (l || c.start.column > t.column) && (c.start.column = t.column, c.start.row = t.row) : (c.start.column += h, c.start.row += l) : c.start.row > s && (c.start.row += l) } if (0 != l && r < a) for (; r < a; r++)(c = o[r]).start.row += l, c.end.row += l } }).call(n.prototype), t.RangeList = n }), define("ace/edit_session/fold", ["require", "exports", "module", "ace/range_list", "ace/lib/oop"], function (e, t, i) { "use strict"; function u(e, t) { e.row -= t.row, 0 == e.row && (e.column -= t.column) } function n(e, t) { 0 == e.row && (e.column += t.column), e.row += t.row } var s = e("../range_list").RangeList, e = e("../lib/oop"), o = t.Fold = function (e, t) { this.foldLine = null, this.placeholder = t, this.range = e, this.start = e.start, this.end = e.end, this.sameRow = e.start.row == e.end.row, this.subFolds = this.ranges = [] }; e.inherits(o, s), function () { this.toString = function () { return '"' + this.placeholder + '" ' + this.range.toString() }, this.setFoldLine = function (t) { this.foldLine = t, this.subFolds.forEach(function (e) { e.setFoldLine(t) }) }, this.clone = function () { var e = this.range.clone(), t = new o(e, this.placeholder); return this.subFolds.forEach(function (e) { t.subFolds.push(e.clone()) }), t.collapseChildren = this.collapseChildren, t }, this.addSubFold = function (e) { if (!this.range.isEqual(e)) { o = e, r = this.start, u(o.start, r), u(o.end, r); for (var t = e.start.row, i = e.start.column, n = 0, s = -1; n < this.subFolds.length && 1 == (s = this.subFolds[n].range.compare(t, i)); n++); var o = this.subFolds[n], r = 0; if (0 == s) { if (o.range.containsRange(e)) return o.addSubFold(e); r = 1 } for (var t = e.range.end.row, i = e.range.end.column, a = n, s = -1; a < this.subFolds.length && 1 == (s = this.subFolds[a].range.compare(t, i)); a++); 0 == s && a++; for (var l = this.subFolds.splice(n, a - n, e), h = 0 == s ? l.length - 1 : l.length, c = r; c < h; c++)e.addSubFold(l[c]); return e.setFoldLine(this.foldLine), e } }, this.restoreRange = function (e) { return e = e, t = this.start, n(e.start, t), void n(e.end, t); var t } }.call(o.prototype) }), define("ace/edit_session/folding", ["require", "exports", "module", "ace/range", "ace/edit_session/fold_line", "ace/edit_session/fold", "ace/token_iterator"], function (e, t, i) { "use strict"; var h = e("../range").Range, d = e("./fold_line").FoldLine, g = e("./fold").Fold, c = e("../token_iterator").TokenIterator; t.Folding = function () { this.getFoldAt = function (e, t, i) { var n = this.getFoldLine(e); if (!n) return null; for (var s = n.folds, o = 0; o < s.length; o++) { var r = s[o].range; if (r.contains(e, t) && (1 != i || !r.isEnd(e, t) || r.isEmpty()) && (-1 != i || !r.isStart(e, t) || r.isEmpty())) return s[o] } }, this.getFoldsInRange = function (e) { var t = e.start, i = e.end, n = this.$foldData, s = []; t.column += 1, --i.column; for (var o = 0; o < n.length; o++) { var r = n[o].range.compareRange(e); if (2 != r) { if (-2 == r) break; for (var a = n[o].folds, l = 0; l < a.length; l++) { var h = a[l]; if (-2 == (r = h.range.compareRange(e))) break; if (2 != r) { if (42 == r) break; s.push(h) } } } } return --t.column, i.column += 1, s }, this.getFoldsInRangeList = function (e) { var t; return Array.isArray(e) ? (t = [], e.forEach(function (e) { t = t.concat(this.getFoldsInRange(e)) }, this)) : t = this.getFoldsInRange(e), t }, this.getAllFolds = function () { for (var e = [], t = this.$foldData, i = 0; i < t.length; i++)for (var n = 0; n < t[i].folds.length; n++)e.push(t[i].folds[n]); return e }, this.getFoldStringAt = function (e, t, i, n) { if (!(n = n || this.getFoldLine(e))) return null; for (var s, o = { end: { column: 0 } }, r = 0; r < n.folds.length; r++) { var a, l = (a = n.folds[r]).range.compareEnd(e, t); if (-1 == l) { s = this.getLine(a.start.row).substring(o.end.column, a.start.column); break } if (0 === l) return null; o = a } return s = s || this.getLine(a.start.row).substring(o.end.column), -1 == i ? s.substring(0, t - o.end.column) : 1 == i ? s.substring(t - o.end.column) : s }, this.getFoldLine = function (e, t) { var i = this.$foldData, n = 0; for (-1 == (n = t ? i.indexOf(t) : n) && (n = 0); n < i.length; n++) { var s = i[n]; if (s.start.row <= e && s.end.row >= e) return s; if (s.end.row > e) return null } return null }, this.getNextFoldLine = function (e, t) { var i = this.$foldData, n = 0; for (-1 == (n = t ? i.indexOf(t) : n) && (n = 0); n < i.length; n++) { var s = i[n]; if (s.end.row >= e) return s } return null }, this.getFoldedRowCount = function (e, t) { for (var i = this.$foldData, n = t - e + 1, s = 0; s < i.length; s++) { var o = i[s], r = o.end.row, o = o.start.row; if (t <= r) { o < t && (e <= o ? n -= t - o : n = 0); break } e <= r && (n -= e <= o ? r - o : r - e + 1) } return n }, this.$addFoldLine = function (e) { return this.$foldData.push(e), this.$foldData.sort(function (e, t) { return e.start.row - t.start.row }), e }, this.addFold = function (e, t) { var i, n = this.$foldData, s = !1, o = (e instanceof g ? i = e : (i = new g(t, e)).collapseChildren = t.collapseChildren, this.$clipRangeToDocument(i.range), i.start.row), e = i.start.column, r = i.end.row, t = i.end.column, a = this.getFoldAt(o, e, 1), l = this.getFoldAt(r, t, -1); if (a && l == a) return a.addSubFold(i); a && !a.range.isStart(o, e) && this.removeFold(a), l && !l.range.isEnd(r, t) && this.removeFold(l); e = this.getFoldsInRange(i.range); 0 < e.length && (this.removeFolds(e), i.collapseChildren || e.forEach(function (e) { i.addSubFold(e) })); for (var h = 0; h < n.length; h++) { var c = n[h]; if (r == c.start.row) { c.addFold(i), s = !0; break } if (o == c.end.row) { if (c.addFold(i), s = !0, !i.sameRow) { var u = n[h + 1]; if (u && u.start.row == r) { c.merge(u); break } } break } if (r <= c.start.row) break } return s || (c = this.$addFoldLine(new d(this.$foldData, i))), this.$useWrapMode ? this.$updateWrapData(c.start.row, c.start.row) : this.$updateRowLengthCache(c.start.row, c.start.row), this.$modified = !0, this._signal("changeFold", { data: i, action: "add" }), i }, this.addFolds = function (e) { e.forEach(function (e) { this.addFold(e) }, this) }, this.removeFold = function (e) { var t = e.foldLine, i = t.start.row, n = t.end.row, s = this.$foldData, o = t.folds; 1 == o.length ? s.splice(s.indexOf(t), 1) : t.range.isEnd(e.end.row, e.end.column) ? (o.pop(), t.end.row = o[o.length - 1].end.row, t.end.column = o[o.length - 1].end.column) : t.range.isStart(e.start.row, e.start.column) ? (o.shift(), t.start.row = o[0].start.row, t.start.column = o[0].start.column) : e.sameRow ? o.splice(o.indexOf(e), 1) : ((o = (s = t.split(e.start.row, e.start.column)).folds).shift(), s.start.row = o[0].start.row, s.start.column = o[0].start.column), this.$updating || (this.$useWrapMode ? this.$updateWrapData(i, n) : this.$updateRowLengthCache(i, n)), this.$modified = !0, this._signal("changeFold", { data: e, action: "remove" }) }, this.removeFolds = function (e) { for (var t = [], i = 0; i < e.length; i++)t.push(e[i]); t.forEach(function (e) { this.removeFold(e) }, this), this.$modified = !0 }, this.expandFold = function (t) { this.removeFold(t), t.subFolds.forEach(function (e) { t.restoreRange(e), this.addFold(e) }, this), 0 < t.collapseChildren && this.foldAll(t.start.row + 1, t.end.row, t.collapseChildren - 1), t.subFolds = [] }, this.expandFolds = function (e) { e.forEach(function (e) { this.expandFold(e) }, this) }, this.unfold = function (e, t) { var i; if (null == e) i = new h(0, 0, this.getLength(), 0), null == t && (t = !0); else if ("number" == typeof e) i = new h(e, 0, e, this.getLine(e).length); else if ("row" in e) i = h.fromPoints(e, e); else { if (Array.isArray(e)) return n = [], e.forEach(function (e) { n = n.concat(this.unfold(e)) }, this), n; i = e } for (var n, e = n = this.getFoldsInRangeList(i); 1 == n.length && h.comparePoints(n[0].start, i.start) < 0 && 0 < h.comparePoints(n[0].end, i.end);)this.expandFolds(n), n = this.getFoldsInRangeList(i); if (0 != t ? this.removeFolds(n) : this.expandFolds(n), e.length) return e }, this.isRowFolded = function (e, t) { return !!this.getFoldLine(e, t) }, this.getRowFoldEnd = function (e, t) { t = this.getFoldLine(e, t); return t ? t.end.row : e }, this.getRowFoldStart = function (e, t) { t = this.getFoldLine(e, t); return t ? t.start.row : e }, this.getFoldDisplayLine = function (e, t, i, s, o) { null == s && (s = e.start.row), null == o && (o = 0), null == t && (t = e.end.row), null == i && (i = this.getLine(t).length); var r = this.doc, a = ""; return e.walk(function (e, t, i, n) { if (!(t < s)) { if (t == s) { if (i < o) return; n = Math.max(o, n) } a += null != e ? e : r.getLine(t).substring(n, i) } }, t, i), a }, this.getDisplayLine = function (e, t, i, n) { var s = this.getFoldLine(e); return s ? this.getFoldDisplayLine(s, e, t, i, n) : (s = this.doc.getLine(e)).substring(n || 0, t || s.length) }, this.$cloneFoldData = function () { var t = []; return t = this.$foldData.map(function (e) { e = e.folds.map(function (e) { return e.clone() }); return new d(t, e) }) }, this.toggleFold = function (e) { var t = this.selection.getRange(); if (t.isEmpty()) { var i, n = t.start; if (i = this.getFoldAt(n.row, n.column)) return void this.expandFold(i); (s = this.findMatchingBracket(n)) ? 1 == t.comparePoint(s) ? t.end = s : (t.start = s, t.start.column++, t.end.column--) : (s = this.findMatchingBracket({ row: n.row, column: n.column + 1 })) ? (1 == t.comparePoint(s) ? t.end = s : t.start = s, t.start.column++) : t = this.getCommentFoldRange(n.row, n.column) || t } else { var s = this.getFoldsInRange(t); if (e && s.length) return void this.expandFolds(s); 1 == s.length && (i = s[0]) } if ((i = i || this.getFoldAt(t.start.row, t.start.column)) && i.range.toString() == t.toString()) this.expandFold(i); else { n = "..."; if (!t.isMultiLine()) { if ((n = this.getTextRange(t)).length < 4) return; n = n.trim().substring(0, 2) + ".." } this.addFold(n, t) } }, this.getCommentFoldRange = function (e, t, i) { var n = new c(this, e, t), s = n.getCurrentToken(), o = s && s.type; if (s && /^comment|string/.test(o)) { "comment" == (o = o.match(/comment|string/)[0]) && (o += "|doc-start"); var r = new RegExp(o), o = new h; if (1 != i) { for (; (s = n.stepBackward()) && r.test(s.type) && !/^comment.end/.test(s.type);); s = n.stepForward() } if (o.start.row = n.getCurrentTokenRow(), o.start.column = n.getCurrentTokenColumn() + (/^comment.start/.test(s.type) ? s.value.length : 2), n = new c(this, e, t), -1 != i) { var a = -1; do { if (s = n.stepForward(), -1 == a) { var l = this.getState(n.$row); r.test(l) || (a = n.$row) } else if (n.$row > a) break } while (s && r.test(s.type) && !/^comment.start/.test(s.type)); s = n.stepBackward() } else s = n.getCurrentToken(); return o.end.row = n.getCurrentTokenRow(), o.end.column = n.getCurrentTokenColumn(), /^comment.end/.test(s.type) || (o.end.column += s.value.length - 2), o } }, this.foldAll = function (e, t, i, n) { null == i && (i = 1e5); var s = this.foldWidgets; if (s) { t = t || this.getLength(); for (var o, r = e = e || 0; r < t; r++)null == s[r] && (s[r] = this.getFoldWidget(r)), "start" != s[r] || n && !n(r) || (o = this.getFoldWidgetRange(r)) && o.isMultiLine() && o.end.row <= t && o.start.row >= e && (r = o.end.row, o.collapseChildren = i, this.addFold("...", o)) } }, this.foldToLevel = function (e) { for (this.foldAll(); 0 < e--;)this.unfold(null, !1) }, this.foldAllComments = function () { var s = this; this.foldAll(null, null, null, function (e) { for (var t = s.getTokens(e), i = 0; i < t.length; i++) { var n = t[i]; if ("text" != n.type || !/^\s+$/.test(n.value)) return !!/comment/.test(n.type) } }) }, this.$foldStyles = { manual: 1, markbegin: 1, markbeginend: 1 }, this.$foldStyle = "markbegin", this.setFoldStyle = function (e) { if (!this.$foldStyles[e]) throw new Error("invalid fold style: " + e + "[" + Object.keys(this.$foldStyles).join(", ") + "]"); this.$foldStyle != e && ("manual" == (this.$foldStyle = e) && this.unfold(), e = this.$foldMode, this.$setFolding(null), this.$setFolding(e)) }, this.$setFolding = function (e) { this.$foldMode != e && (this.$foldMode = e, this.off("change", this.$updateFoldWidgets), this.off("tokenizerUpdate", this.$tokenizerUpdateFoldWidgets), this._signal("changeAnnotation"), e && "manual" != this.$foldStyle ? (this.foldWidgets = [], this.getFoldWidget = e.getFoldWidget.bind(e, this, this.$foldStyle), this.getFoldWidgetRange = e.getFoldWidgetRange.bind(e, this, this.$foldStyle), this.$updateFoldWidgets = this.updateFoldWidgets.bind(this), this.$tokenizerUpdateFoldWidgets = this.tokenizerUpdateFoldWidgets.bind(this), this.on("change", this.$updateFoldWidgets), this.on("tokenizerUpdate", this.$tokenizerUpdateFoldWidgets)) : this.foldWidgets = null) }, this.getParentFoldRangeData = function (e, t) { var i = this.foldWidgets; if (!i || t && i[e]) return {}; for (var n = e - 1; 0 <= n;) { var s = i[n]; if ("start" == (s = null == s ? i[n] = this.getFoldWidget(n) : s)) { var o = this.getFoldWidgetRange(n), r = r || o; if (o && o.end.row >= e) break } n-- } return { range: -1 !== n && o, firstRange: r } }, this.onFoldWidgetClick = function (e, t) { var i = { children: (t = t.domEvent).shiftKey, all: t.ctrlKey || t.metaKey, siblings: t.altKey }; this.$toggleFoldWidget(e, i) || (e = t.target || t.srcElement) && /ace_fold-widget/.test(e.className) && (e.className += " ace_invalid") }, this.$toggleFoldWidget = function (e, t) { if (this.getFoldWidget) { var i = this.getFoldWidget(e), n = this.getLine(e), i = "end" === i ? -1 : 1, n = this.getFoldAt(e, -1 == i ? 0 : n.length, i); if (n) return t.children || t.all ? this.removeFold(n) : this.expandFold(n), n; var s, o, i = this.getFoldWidgetRange(e, !0); return i && !i.isMultiLine() && (n = this.getFoldAt(i.start.row, i.start.column, 1)) && i.isEqual(n.range) ? (this.removeFold(n), n) : (t.siblings ? ((n = this.getParentFoldRangeData(e)).range && (s = n.range.start.row + 1, o = n.range.end.row), this.foldAll(s, o, t.all ? 1e4 : 0)) : t.children ? (o = i ? i.end.row : this.getLength(), this.foldAll(e + 1, o, t.all ? 1e4 : 0)) : i && (t.all && (i.collapseChildren = 1e4), this.addFold("...", i)), i) } }, this.toggleFoldWidget = function (e) { var t, i = this.selection.getCursor().row; i = this.getRowFoldStart(i), this.$toggleFoldWidget(i, {}) || (t = (t = this.getParentFoldRangeData(i, !0)).range || t.firstRange) && (i = t.start.row, (i = this.getFoldAt(i, this.getLine(i).length, 1)) ? this.removeFold(i) : this.addFold("...", t)) }, this.updateFoldWidgets = function (e) { var t = e.start.row, i = e.end.row - t; 0 == i ? this.foldWidgets[t] = null : "remove" == e.action ? this.foldWidgets.splice(t, 1 + i, null) : ((e = Array(1 + i)).unshift(t, 1), this.foldWidgets.splice.apply(this.foldWidgets, e)) }, this.tokenizerUpdateFoldWidgets = function (e) { e = e.data; e.first != e.last && this.foldWidgets.length > e.first && this.foldWidgets.splice(e.first, this.foldWidgets.length) } } }), define("ace/edit_session/bracket_match", ["require", "exports", "module", "ace/token_iterator", "ace/range"], function (e, t, i) { "use strict"; var u = e("../token_iterator").TokenIterator, w = e("../range").Range; t.BracketMatch = function () { this.findMatchingBracket = function (e, t) { if (0 == e.column) return null; t = t || this.getLine(e.row).charAt(e.column - 1); if ("" == t) return null; t = t.match(/([\(\[\{])|([\)\]\}])/); return t ? t[1] ? this.$findClosingBracket(t[1], e) : this.$findOpeningBracket(t[2], e) : null }, this.getBracketRange = function (e) { var t, i, n = this.getLine(e.row), s = !0, o = n.charAt(e.column - 1), r = o && o.match(/([\(\[\{])|([\)\]\}])/); if (r || (o = n.charAt(e.column), e = { row: e.row, column: e.column + 1 }, r = o && o.match(/([\(\[\{])|([\)\]\}])/), s = !1), !r) return null; if (r[1]) { if (!(i = this.$findClosingBracket(r[1], e))) return null; t = w.fromPoints(e, i), s || (t.end.column++, t.start.column--), t.cursor = t.end } else { if (!(i = this.$findOpeningBracket(r[2], e))) return null; t = w.fromPoints(i, e), s || (t.start.column++, t.end.column--), t.cursor = t.start } return t }, this.getMatchingBracketRanges = function (e, t) { var i = this.getLine(e.row), n = /([\(\[\{])|([\)\]\}])/, s = !t && i.charAt(e.column - 1), o = s && s.match(n); if (o || (s = (void 0 === t || t) && i.charAt(e.column), e = { row: e.row, column: e.column + 1 }, o = s && s.match(n)), !o) return null; t = new w(e.row, e.column - 1, e.row, e.column), i = o[1] ? this.$findClosingBracket(o[1], e) : this.$findOpeningBracket(o[2], e); return i ? [t, new w(i.row, i.column, i.row, i.column + 1)] : [t] }, this.$brackets = { ")": "(", "(": ")", "]": "[", "[": "]", "{": "}", "}": "{", "<": ">", ">": "<" }, this.$findOpeningBracket = function (e, t, i) { var n = this.$brackets[e], s = 1, o = new u(this, t.row, t.column), r = o.getCurrentToken(); if (r = r || o.stepForward()) { i = i || new RegExp("(\\.?" + r.type.replace(".", "\\.").replace("rparen", ".paren").replace(/\b(?:end)\b/, "(?:start|begin|end)") + ")+"); for (var a = t.column - o.getCurrentTokenColumn() - 2, l = r.value; ;) { for (; 0 <= a;) { var h = l.charAt(a); if (h == n) { if (0 == --s) return { row: o.getCurrentTokenRow(), column: a + o.getCurrentTokenColumn() } } else h == e && (s += 1); --a } for (; (r = o.stepBackward()) && !i.test(r.type);); if (null == r) break; a = (l = r.value).length - 1 } return null } }, this.$findClosingBracket = function (e, t, i) { var n = this.$brackets[e], s = 1, o = new u(this, t.row, t.column), r = o.getCurrentToken(); if (r = r || o.stepForward()) { i = i || new RegExp("(\\.?" + r.type.replace(".", "\\.").replace("lparen", ".paren").replace(/\b(?:start|begin)\b/, "(?:start|begin|end)") + ")+"); for (var a = t.column - o.getCurrentTokenColumn(); ;) { for (var l = r.value, h = l.length; a < h;) { var c = l.charAt(a); if (c == n) { if (0 == --s) return { row: o.getCurrentTokenRow(), column: a + o.getCurrentTokenColumn() } } else c == e && (s += 1); a += 1 } for (; (r = o.stepForward()) && !i.test(r.type);); if (null == r) break; a = 0 } return null } }, this.getMatchingTags = function (e) { var e = new u(this, e.row, e.column), t = this.$findTagName(e); if (t) return "<" === e.stepBackward().value ? this.$findClosingTag(e, t) : this.$findOpeningTag(e, t) }, this.$findTagName = function (e) { var t = e.getCurrentToken(), i = !1, n = !1; if (t && -1 === t.type.indexOf("tag-name")) for (; (t = n ? e.stepBackward() : e.stepForward()) && ("/>" === t.value ? n = !0 : -1 !== t.type.indexOf("tag-name") && (i = !0)), t && !i;); return t }, this.$findClosingTag = function (e, t) { var i = t.value, n = t.value, s = 0, o = new w(e.getCurrentTokenRow(), e.getCurrentTokenColumn(), e.getCurrentTokenRow(), e.getCurrentTokenColumn() + 1), r = (t = e.stepForward(), new w(e.getCurrentTokenRow(), e.getCurrentTokenColumn(), e.getCurrentTokenRow(), e.getCurrentTokenColumn() + t.value.length)), a = !1; do { var l, h = t; if (t = e.stepForward()) if (">" !== t.value || a || (l = new w(e.getCurrentTokenRow(), e.getCurrentTokenColumn(), e.getCurrentTokenRow(), e.getCurrentTokenColumn() + 1), a = !0), -1 !== t.type.indexOf("tag-name")) { if (n === (i = t.value)) if ("<" === h.value) s++; else if ("</" === h.value && --s < 0) { e.stepBackward(); var c = new w(e.getCurrentTokenRow(), e.getCurrentTokenColumn(), e.getCurrentTokenRow(), e.getCurrentTokenColumn() + 2), u = (t = e.stepForward(), new w(e.getCurrentTokenRow(), e.getCurrentTokenColumn(), e.getCurrentTokenRow(), e.getCurrentTokenColumn() + t.value.length)); if (!(t = e.stepForward()) || ">" !== t.value) return; var d = new w(e.getCurrentTokenRow(), e.getCurrentTokenColumn(), e.getCurrentTokenRow(), e.getCurrentTokenColumn() + 1) } } else n === i && "/>" === t.value && --s < 0 && (d = u = c = new w(e.getCurrentTokenRow(), e.getCurrentTokenColumn(), e.getCurrentTokenRow(), e.getCurrentTokenColumn() + 2), l = new w(r.end.row, r.end.column, r.end.row, r.end.column + 1)) } while (t && 0 <= s); if (o && l && c && d && r && u) return { openTag: new w(o.start.row, o.start.column, l.end.row, l.end.column), closeTag: new w(c.start.row, c.start.column, d.end.row, d.end.column), openTagName: r, closeTagName: u } }, this.$findOpeningTag = function (e, t) { var i, n = e.getCurrentToken(), s = t.value, o = 0, r = e.getCurrentTokenRow(), a = e.getCurrentTokenColumn(), l = new w(r, a, r, a + 2), h = (e.stepForward(), new w(e.getCurrentTokenRow(), e.getCurrentTokenColumn(), e.getCurrentTokenRow(), e.getCurrentTokenColumn() + t.value.length)); if ((t = e.stepForward()) && ">" === t.value) { var c = new w(e.getCurrentTokenRow(), e.getCurrentTokenColumn(), e.getCurrentTokenRow(), e.getCurrentTokenColumn() + 1); e.stepBackward(), e.stepBackward(); do { if (t = n, r = e.getCurrentTokenRow(), i = (a = e.getCurrentTokenColumn()) + t.value.length, n = e.stepBackward(), t) if (-1 !== t.type.indexOf("tag-name")) { if (s === t.value) if ("<" === n.value) { if (0 < ++o) { for (var u = new w(r, a, r, i), d = new w(e.getCurrentTokenRow(), e.getCurrentTokenColumn(), e.getCurrentTokenRow(), e.getCurrentTokenColumn() + 1); (t = e.stepForward()) && ">" !== t.value;); var g = new w(e.getCurrentTokenRow(), e.getCurrentTokenColumn(), e.getCurrentTokenRow(), e.getCurrentTokenColumn() + 1) } } else "</" === n.value && o-- } else if ("/>" === t.value) { for (var f = 0, m = n; m;) { if (-1 !== m.type.indexOf("tag-name") && m.value === s) { o--; break } if ("<" === m.value) break; m = e.stepBackward(), f++ } for (var p = 0; p < f; p++)e.stepForward() } } while (n && o <= 0); return d && g && l && c && u && h ? { openTag: new w(d.start.row, d.start.column, g.end.row, g.end.column), closeTag: new w(l.start.row, l.start.column, c.end.row, c.end.column), openTagName: u, closeTagName: h } : void 0 } } } }), define("ace/edit_session", ["require", "exports", "module", "ace/lib/oop", "ace/lib/lang", "ace/bidihandler", "ace/config", "ace/lib/event_emitter", "ace/selection", "ace/mode/text", "ace/range", "ace/document", "ace/background_tokenizer", "ace/search_highlight", "ace/edit_session/folding", "ace/edit_session/bracket_match"], function (e, t, i) { "use strict"; function n(e, t) { this.$breakpoints = [], this.$decorations = [], this.$frontMarkers = {}, this.$backMarkers = {}, this.$markerId = 1, this.$undoSelect = !0, this.$foldData = [], this.id = "session" + ++n.$uid, this.$foldData.toString = function () { return this.join("\n") }, this.bgTokenizer = new g((new c).getTokenizer(), this); var i = this; this.bgTokenizer.on("update", function (e) { i._signal("tokenizerUpdate", e) }), this.on("changeFold", this.onChangeFold.bind(this)), this.$onChange = this.onChange.bind(this), "object" == typeof e && e.getLine || (e = new d(e)), this.setDocument(e), this.selection = new a(this), this.$bidiHandler = new r(this), l.resetOptions(this), this.setMode(t), l._signal("session", this), this.destroyed = !1 } var s = e("./lib/oop"), o = e("./lib/lang"), r = e("./bidihandler").BidiHandler, l = e("./config"), h = e("./lib/event_emitter").EventEmitter, a = e("./selection").Selection, c = e("./mode/text").Mode, u = e("./range").Range, d = e("./document").Document, g = e("./background_tokenizer").BackgroundTokenizer, p = e("./search_highlight").SearchHighlight; n.$uid = 0, function () { function a(e) { return !(e < 4352) && (4352 <= e && e <= 4447 || 4515 <= e && e <= 4519 || 4602 <= e && e <= 4607 || 9001 <= e && e <= 9002 || 11904 <= e && e <= 11929 || 11931 <= e && e <= 12019 || 12032 <= e && e <= 12245 || 12272 <= e && e <= 12283 || 12288 <= e && e <= 12350 || 12353 <= e && e <= 12438 || 12441 <= e && e <= 12543 || 12549 <= e && e <= 12589 || 12593 <= e && e <= 12686 || 12688 <= e && e <= 12730 || 12736 <= e && e <= 12771 || 12784 <= e && e <= 12830 || 12832 <= e && e <= 12871 || 12880 <= e && e <= 13054 || 13056 <= e && e <= 19903 || 19968 <= e && e <= 42124 || 42128 <= e && e <= 42182 || 43360 <= e && e <= 43388 || 44032 <= e && e <= 55203 || 55216 <= e && e <= 55238 || 55243 <= e && e <= 55291 || 63744 <= e && e <= 64255 || 65040 <= e && e <= 65049 || 65072 <= e && e <= 65106 || 65108 <= e && e <= 65126 || 65128 <= e && e <= 65131 || 65281 <= e && e <= 65376 || 65504 <= e && e <= 65510) } s.implement(this, h), this.setDocument = function (e) { this.doc && this.doc.off("change", this.$onChange), (this.doc = e).on("change", this.$onChange, !0), this.bgTokenizer.setDocument(this.getDocument()), this.resetCaches() }, this.getDocument = function () { return this.doc }, this.$resetRowCache = function (e) { if (!e) return this.$docRowCache = [], void (this.$screenRowCache = []); var t = this.$docRowCache.length, e = this.$getRowCacheIndex(this.$docRowCache, e) + 1; e < t && (this.$docRowCache.splice(e, t), this.$screenRowCache.splice(e, t)) }, this.$getRowCacheIndex = function (e, t) { for (var i = 0, n = e.length - 1; i <= n;) { var s = i + n >> 1, o = e[s]; if (o < t) i = 1 + s; else { if (!(t < o)) return s; n = s - 1 } } return i - 1 }, this.resetCaches = function () { this.$modified = !0, this.$wrapData = [], this.$rowLengthCache = [], this.$resetRowCache(0), this.destroyed || this.bgTokenizer.start(0) }, this.onChangeFold = function (e) { e = e.data; this.$resetRowCache(e.start.row) }, this.onChange = function (e) { this.$modified = !0, this.$bidiHandler.onChange(e), this.$resetRowCache(e.start.row); var t = this.$updateInternalDataOnChange(e); !this.$fromUndo && this.$undoManager && (t && t.length && (this.$undoManager.add({ action: "removeFolds", folds: t }, this.mergeUndoDeltas), this.mergeUndoDeltas = !0), this.$undoManager.add(e, this.mergeUndoDeltas), this.mergeUndoDeltas = !0, this.$informUndoManager.schedule()), this.bgTokenizer.$updateOnChange(e), this._signal("change", e) }, this.setValue = function (e) { this.doc.setValue(e), this.selection.moveTo(0, 0), this.$resetRowCache(0), this.setUndoManager(this.$undoManager), this.getUndoManager().reset() }, this.getValue = this.toString = function () { return this.doc.getValue() }, this.getSelection = function () { return this.selection }, this.getState = function (e) { return this.bgTokenizer.getState(e) }, this.getTokens = function (e) { return this.bgTokenizer.getTokens(e) }, this.getTokenAt = function (e, t) { var i = this.bgTokenizer.getTokens(e), n = 0; if (null == t) var s = i.length - 1, n = this.getLine(e).length; else for (s = 0; s < i.length && !(t <= (n += i[s].value.length)); s++); return (e = i[s]) ? (e.index = s, e.start = n - e.value.length, e) : null }, this.setUndoManager = function (e) { var t; this.$undoManager = e, this.$informUndoManager && this.$informUndoManager.cancel(), e ? (e.addSession(t = this), this.$syncInformUndoManager = function () { t.$informUndoManager.cancel(), t.mergeUndoDeltas = !1 }, this.$informUndoManager = o.delayedCall(this.$syncInformUndoManager)) : this.$syncInformUndoManager = function () { } }, this.markUndoGroup = function () { this.$syncInformUndoManager && this.$syncInformUndoManager() }, this.$defaultUndoManager = { undo: function () { }, redo: function () { }, hasUndo: function () { }, hasRedo: function () { }, reset: function () { }, add: function () { }, addSelection: function () { }, startNewGroup: function () { }, addSession: function () { } }, this.getUndoManager = function () { return this.$undoManager || this.$defaultUndoManager }, this.getTabString = function () { return this.getUseSoftTabs() ? o.stringRepeat(" ", this.getTabSize()) : "\t" }, this.setUseSoftTabs = function (e) { this.setOption("useSoftTabs", e) }, this.getUseSoftTabs = function () { return this.$useSoftTabs && !this.$mode.$indentWithTabs }, this.setTabSize = function (e) { this.setOption("tabSize", e) }, this.getTabSize = function () { return this.$tabSize }, this.isTabStop = function (e) { return this.$useSoftTabs && e.column % this.$tabSize == 0 }, this.setNavigateWithinSoftTabs = function (e) { this.setOption("navigateWithinSoftTabs", e) }, this.getNavigateWithinSoftTabs = function () { return this.$navigateWithinSoftTabs }, this.$overwrite = !1, this.setOverwrite = function (e) { this.setOption("overwrite", e) }, this.getOverwrite = function () { return this.$overwrite }, this.toggleOverwrite = function () { this.setOverwrite(!this.$overwrite) }, this.addGutterDecoration = function (e, t) { this.$decorations[e] || (this.$decorations[e] = ""), this.$decorations[e] += " " + t, this._signal("changeBreakpoint", {}) }, this.removeGutterDecoration = function (e, t) { this.$decorations[e] = (this.$decorations[e] || "").replace(" " + t, ""), this._signal("changeBreakpoint", {}) }, this.getBreakpoints = function () { return this.$breakpoints }, this.setBreakpoints = function (e) { this.$breakpoints = []; for (var t = 0; t < e.length; t++)this.$breakpoints[e[t]] = "ace_breakpoint"; this._signal("changeBreakpoint", {}) }, this.clearBreakpoints = function () { this.$breakpoints = [], this._signal("changeBreakpoint", {}) }, this.setBreakpoint = function (e, t) { (t = void 0 === t ? "ace_breakpoint" : t) ? this.$breakpoints[e] = t : delete this.$breakpoints[e], this._signal("changeBreakpoint", {}) }, this.clearBreakpoint = function (e) { delete this.$breakpoints[e], this._signal("changeBreakpoint", {}) }, this.addMarker = function (e, t, i, n) { var s = this.$markerId++, e = { range: e, type: i || "line", renderer: "function" == typeof i ? i : null, clazz: t, inFront: !!n, id: s }; return n ? (this.$frontMarkers[s] = e, this._signal("changeFrontMarker")) : (this.$backMarkers[s] = e, this._signal("changeBackMarker")), s }, this.addDynamicMarker = function (e, t) { var i; if (e.update) return i = this.$markerId++, e.id = i, e.inFront = !!t, t ? (this.$frontMarkers[i] = e, this._signal("changeFrontMarker")) : (this.$backMarkers[i] = e, this._signal("changeBackMarker")), e }, this.removeMarker = function (e) { var t = this.$frontMarkers[e] || this.$backMarkers[e]; t && (delete (t.inFront ? this.$frontMarkers : this.$backMarkers)[e], this._signal(t.inFront ? "changeFrontMarker" : "changeBackMarker")) }, this.getMarkers = function (e) { return e ? this.$frontMarkers : this.$backMarkers }, this.highlight = function (e) { var t; this.$searchHighlight || (t = new p(null, "ace_selected-word", "text"), this.$searchHighlight = this.addDynamicMarker(t)), this.$searchHighlight.setRegexp(e) }, this.highlightLines = function (e, t, i, n) { "number" != typeof t && (i = t, t = e), i = i || "ace_step"; e = new u(e, 0, t, 1 / 0); return e.id = this.addMarker(e, i, "fullLine", n), e }, this.setAnnotations = function (e) { this.$annotations = e, this._signal("changeAnnotation", {}) }, this.getAnnotations = function () { return this.$annotations || [] }, this.clearAnnotations = function () { this.setAnnotations([]) }, this.$detectNewLine = function (e) { e = e.match(/^.*?(\r?\n)/m); this.$autoNewLine = e ? e[1] : "\n" }, this.getWordRange = function (e, t) { var i, n = this.getLine(e), s = !1, o = (i = (s = (s = 0 < t ? !!n.charAt(t - 1).match(this.tokenRe) : s) || !!n.charAt(t).match(this.tokenRe)) ? this.tokenRe : /^\s+$/.test(n.slice(t - 1, t + 1)) ? /\s/ : this.nonTokenRe, t); if (0 < o) { for (; 0 <= --o && n.charAt(o).match(i);); o++ } for (var r = t; r < n.length && n.charAt(r).match(i);)r++; return new u(e, o, e, r) }, this.getAWordRange = function (e, t) { for (var i = this.getWordRange(e, t), n = this.getLine(i.end.row); n.charAt(i.end.column).match(/[ \t]/);)i.end.column += 1; return i }, this.setNewLineMode = function (e) { this.doc.setNewLineMode(e) }, this.getNewLineMode = function () { return this.doc.getNewLineMode() }, this.setUseWorker = function (e) { this.setOption("useWorker", e) }, this.getUseWorker = function () { return this.$useWorker }, this.onReloadTokenizer = function (e) { var t = e.data; this.bgTokenizer.start(t.first), this._signal("tokenizerUpdate", e) }, this.$modes = l.$modes, this.$mode = null, this.$modeId = null, this.setMode = function (e, t) { if (e && "object" == typeof e) { if (e.getTokenizer) return this.$onChangeMode(e); var i = e, n = i.path } else n = e || "ace/mode/text"; if (this.$modes["ace/mode/text"] || (this.$modes["ace/mode/text"] = new c), this.$modes[n] && !i) return this.$onChangeMode(this.$modes[n]), void (t && t()); this.$modeId = n, l.loadModule(["mode", n], function (e) { if (this.$modeId !== n) return t && t(); this.$modes[n] && !i ? this.$onChangeMode(this.$modes[n]) : e && e.Mode && (e = new e.Mode(i), i || ((this.$modes[n] = e).$id = n), this.$onChangeMode(e)), t && t() }.bind(this)), this.$mode || this.$onChangeMode(this.$modes["ace/mode/text"], !0) }, this.$onChangeMode = function (e, t) { var i, n, s; t || (this.$modeId = e.$id), this.$mode !== e && (i = this.$mode, this.$mode = e, this.$stopWorker(), this.$useWorker && this.$startWorker(), void 0 !== (n = e.getTokenizer()).on && (s = this.onReloadTokenizer.bind(this), n.on("update", s)), this.bgTokenizer.setTokenizer(n), this.bgTokenizer.setDocument(this.getDocument()), this.tokenRe = e.tokenRe, this.nonTokenRe = e.nonTokenRe, t || (e.attachToSession && e.attachToSession(this), this.$options.wrapMethod.set.call(this, this.$wrapMethod), this.$setFolding(e.foldingRules), this.bgTokenizer.start(0), this._emit("changeMode", { oldMode: i, mode: e }))) }, this.$stopWorker = function () { this.$worker && (this.$worker.terminate(), this.$worker = null) }, this.$startWorker = function () { try { this.$worker = this.$mode.createWorker(this) } catch (e) { l.warn("Could not load worker", e), this.$worker = null } }, this.getMode = function () { return this.$mode }, this.$scrollTop = 0, this.setScrollTop = function (e) { this.$scrollTop === e || isNaN(e) || (this.$scrollTop = e, this._signal("changeScrollTop", e)) }, this.getScrollTop = function () { return this.$scrollTop }, this.$scrollLeft = 0, this.setScrollLeft = function (e) { this.$scrollLeft === e || isNaN(e) || (this.$scrollLeft = e, this._signal("changeScrollLeft", e)) }, this.getScrollLeft = function () { return this.$scrollLeft }, this.getScreenWidth = function () { return this.$computeWidth(), this.lineWidgets ? Math.max(this.getLineWidgetMaxWidth(), this.screenWidth) : this.screenWidth }, this.getLineWidgetMaxWidth = function () { if (null != this.lineWidgetsWidth) return this.lineWidgetsWidth; var t = 0; return this.lineWidgets.forEach(function (e) { e && e.screenWidth > t && (t = e.screenWidth) }), this.lineWidgetWidth = t }, this.$computeWidth = function (e) { if (this.$modified || e) { if (this.$modified = !1, this.$useWrapMode) return this.screenWidth = this.$wrapLimit; for (var t = this.doc.getAllLines(), i = this.$rowLengthCache, n = 0, s = 0, o = this.$foldData[s], r = o ? o.start.row : 1 / 0, a = t.length, l = 0; l < a; l++) { if (r < l) { if (a <= (l = o.end.row + 1)) break; r = (o = this.$foldData[s++]) ? o.start.row : 1 / 0 } null == i[l] && (i[l] = this.$getStringScreenWidth(t[l])[0]), i[l] > n && (n = i[l]) } this.screenWidth = n } }, this.getLine = function (e) { return this.doc.getLine(e) }, this.getLines = function (e, t) { return this.doc.getLines(e, t) }, this.getLength = function () { return this.doc.getLength() }, this.getTextRange = function (e) { return this.doc.getTextRange(e || this.selection.getRange()) }, this.insert = function (e, t) { return this.doc.insert(e, t) }, this.remove = function (e) { return this.doc.remove(e) }, this.removeFullLines = function (e, t) { return this.doc.removeFullLines(e, t) }, this.undoChanges = function (e, t) { if (e.length) { this.$fromUndo = !0; for (var i = e.length - 1; -1 != i; i--) { var n = e[i]; "insert" == n.action || "remove" == n.action ? this.doc.revertDelta(n) : n.folds && this.addFolds(n.folds) } !t && this.$undoSelect && (e.selectionBefore ? this.selection.fromJSON(e.selectionBefore) : this.selection.setRange(this.$getUndoSelection(e, !0))), this.$fromUndo = !1 } }, this.redoChanges = function (e, t) { if (e.length) { this.$fromUndo = !0; for (var i = 0; i < e.length; i++) { var n = e[i]; "insert" != n.action && "remove" != n.action || this.doc.$safeApplyDelta(n) } !t && this.$undoSelect && (e.selectionAfter ? this.selection.fromJSON(e.selectionAfter) : this.selection.setRange(this.$getUndoSelection(e, !1))), this.$fromUndo = !1 } }, this.setUndoSelect = function (e) { this.$undoSelect = e }, this.$getUndoSelection = function (e, t) { function i(e) { return t ? "insert" !== e.action : "insert" === e.action } for (var n, s, o = 0; o < e.length; o++) { var r = e[o]; r.start && (n ? i(r) ? (s = r.start, -1 == n.compare(s.row, s.column) && n.setStart(s), s = r.end, 1 == n.compare(s.row, s.column) && n.setEnd(s)) : (s = r.start, -1 == n.compare(s.row, s.column) && (n = u.fromPoints(r.start, r.start))) : n = i(r) ? u.fromPoints(r.start, r.end) : u.fromPoints(r.start, r.start)) } return n }, this.replace = function (e, t) { return this.doc.replace(e, t) }, this.moveText = function (e, t, i) { var n, s, o, r = this.getTextRange(e), a = this.getFoldsInRange(e), t = u.fromPoints(t, t); return i || (this.remove(e), s = e.start.row - e.end.row, (o = s ? -e.end.column : e.start.column - e.end.column) && (t.start.row == e.end.row && t.start.column > e.end.column && (t.start.column += o), t.end.row == e.end.row && t.end.column > e.end.column && (t.end.column += o)), s && t.start.row >= e.end.row && (t.start.row += s, t.end.row += s)), t.end = this.insert(t.start, r), a.length && (n = e.start, i = t.start, s = i.row - n.row, o = i.column - n.column, this.addFolds(a.map(function (e) { return (e = e.clone()).start.row == n.row && (e.start.column += o), e.end.row == n.row && (e.end.column += o), e.start.row += s, e.end.row += s, e }))), t }, this.indentRows = function (e, t, i) { i = i.replace(/\t/g, this.getTabString()); for (var n = e; n <= t; n++)this.doc.insertInLine({ row: n, column: 0 }, i) }, this.outdentRows = function (e) { for (var t = e.collapseRows(), i = new u(0, 0, 0, 0), n = this.getTabSize(), s = t.start.row; s <= t.end.row; ++s) { var o = this.getLine(s); i.start.row = s, i.end.row = s; for (var r = 0; r < n && " " == o.charAt(r); ++r); r < n && "\t" == o.charAt(r) ? (i.start.column = r, i.end.column = r + 1) : (i.start.column = 0, i.end.column = r), this.remove(i) } }, this.$moveLines = function (e, t, i) { if (e = this.getRowFoldStart(e), t = this.getRowFoldEnd(t), i < 0) { if ((s = this.getRowFoldStart(e + i)) < 0) return 0; var n = s - e } else if (0 < i) { if ((s = this.getRowFoldEnd(t + i)) > this.doc.getLength() - 1) return 0; n = s - t } else { e = this.$clipRowToDocument(e); n = (t = this.$clipRowToDocument(t)) - e + 1 } var s = new u(e, 0, t, Number.MAX_VALUE), s = this.getFoldsInRange(s).map(function (e) { return (e = e.clone()).start.row += n, e.end.row += n, e }), i = 0 == i ? this.doc.getLines(e, t) : this.doc.removeFullLines(e, t); return this.doc.insertFullLines(e + n, i), s.length && this.addFolds(s), n }, this.moveLinesUp = function (e, t) { return this.$moveLines(e, t, -1) }, this.moveLinesDown = function (e, t) { return this.$moveLines(e, t, 1) }, this.duplicateLines = function (e, t) { return this.$moveLines(e, t, 0) }, this.$clipRowToDocument = function (e) { return Math.max(0, Math.min(e, this.doc.getLength() - 1)) }, this.$clipColumnToRow = function (e, t) { return t < 0 ? 0 : Math.min(this.doc.getLine(e).length, t) }, this.$clipPositionToDocument = function (e, t) { var i; return t = Math.max(0, t), t = e < 0 ? e = 0 : (i = this.doc.getLength()) <= e ? this.doc.getLine(e = i - 1).length : Math.min(this.doc.getLine(e).length, t), { row: e, column: t } }, this.$clipRangeToDocument = function (e) { e.start.row < 0 ? (e.start.row = 0, e.start.column = 0) : e.start.column = this.$clipColumnToRow(e.start.row, e.start.column); var t = this.doc.getLength() - 1; return e.end.row > t ? (e.end.row = t, e.end.column = this.doc.getLine(t).length) : e.end.column = this.$clipColumnToRow(e.end.row, e.end.column), e }, this.$wrapLimit = 80, this.$useWrapMode = !1, this.$wrapLimitRange = { min: null, max: null }, this.setUseWrapMode = function (e) { e != this.$useWrapMode && (this.$useWrapMode = e, this.$modified = !0, this.$resetRowCache(0), e && (e = this.getLength(), this.$wrapData = Array(e), this.$updateWrapData(0, e - 1)), this._signal("changeWrapMode")) }, this.getUseWrapMode = function () { return this.$useWrapMode }, this.setWrapLimitRange = function (e, t) { this.$wrapLimitRange.min === e && this.$wrapLimitRange.max === t || (this.$wrapLimitRange = { min: e, max: t }, this.$modified = !0, this.$bidiHandler.markAsDirty(), this.$useWrapMode && this._signal("changeWrapMode")) }, this.adjustWrapLimit = function (e, t) { var i = this.$wrapLimitRange, t = (i.max < 0 && (i = { min: t, max: t }), this.$constrainWrapLimit(e, i.min, i.max)); return t != this.$wrapLimit && 1 < t && (this.$wrapLimit = t, this.$modified = !0, this.$useWrapMode && (this.$updateWrapData(0, this.getLength() - 1), this.$resetRowCache(0), this._signal("changeWrapLimit")), !0) }, this.$constrainWrapLimit = function (e, t, i) { return t && (e = Math.max(t, e)), e = i ? Math.min(i, e) : e }, this.getWrapLimit = function () { return this.$wrapLimit }, this.setWrapLimit = function (e) { this.setWrapLimitRange(e, e) }, this.getWrapLimitRange = function () { return { min: this.$wrapLimitRange.min, max: this.$wrapLimitRange.max } }, this.$updateInternalDataOnChange = function (e) { var t = this.$useWrapMode, i = e.action, n = e.start, s = e.end, o = n.row, r = s.row, a = r - o, l = null; if (this.$updating = !0, 0 != a) if ("remove" === i) { this[t ? "$wrapData" : "$rowLengthCache"].splice(o, a); var h = this.$foldData, l = this.getFoldsInRange(e); this.removeFolds(l); var c = 0; for ((g = this.getFoldLine(s.row)) && (g.addRemoveChars(s.row, s.column, n.column - s.column), g.shiftRow(-a), (u = this.getFoldLine(o)) && u !== g && (u.merge(g), g = u), c = h.indexOf(g) + 1); c < h.length; c++)(g = h[c]).start.row >= s.row && g.shiftRow(-a); r = o } else { var u = Array(a), d = (u.unshift(o, 0), t ? this.$wrapData : this.$rowLengthCache), h = (d.splice.apply(d, u), this.$foldData), c = 0; for ((g = this.getFoldLine(o)) && (0 == (d = g.range.compareInside(n.row, n.column)) ? (g = g.split(n.row, n.column)) && (g.shiftRow(a), g.addRemoveChars(r, 0, s.column - n.column)) : -1 == d && (g.addRemoveChars(o, 0, s.column - n.column), g.shiftRow(a)), c = h.indexOf(g) + 1); c < h.length; c++)(g = h[c]).start.row >= o && g.shiftRow(a) } else { var g, a = Math.abs(e.start.column - e.end.column); "remove" === i && (l = this.getFoldsInRange(e), this.removeFolds(l), a = -a), (g = this.getFoldLine(o)) && g.addRemoveChars(o, n.column, a) } return t && this.$wrapData.length != this.doc.getLength() && console.error("doc.getLength() and $wrapData.length have to be the same!"), this.$updating = !1, t ? this.$updateWrapData(o, r) : this.$updateRowLengthCache(o, r), l }, this.$updateRowLengthCache = function (e, t, i) { this.$rowLengthCache[e] = null, this.$rowLengthCache[t] = null }, this.$updateWrapData = function (e, t) { var r, i, a = this.doc.getAllLines(), n = this.getTabSize(), s = this.$wrapData, o = this.$wrapLimit, l = e; for (t = Math.min(t, a.length - 1); l <= t;)(i = this.getFoldLine(l, i)) ? (r = [], i.walk(function (e, t, i, n) { var s; if (null != e) { (s = this.$getDisplayTokens(e, r.length))[0] = f; for (var o = 1; o < s.length; o++)s[o] = m } else s = this.$getDisplayTokens(a[t].substring(n, i), r.length); r = r.concat(s) }.bind(this), i.end.row, a[i.end.row].length + 1), s[i.start.row] = this.$computeWrapSplits(r, o, n), l = i.end.row + 1) : (r = this.$getDisplayTokens(a[l]), s[l] = this.$computeWrapSplits(r, o, n), l++) }; var f = 3, m = 4; this.$computeWrapSplits = function (s, e, o) { function t(e) { for (var t = e - a, i = a; i < e; i++) { var n = s[i]; 12 !== n && 2 !== n || --t } r.length || (d = function () { var e = 0; if (0 === u) return e; if (c) for (var t = 0; t < s.length; t++) { var i = s[t]; if (10 == i) e += 1; else { if (11 != i) { if (12 == i) continue; break } e += o } } return h && !1 !== c && (e += o), Math.min(e, u) }(), r.indent = d), l += t, r.push(l), a = e } if (0 == s.length) return []; for (var r = [], i = s.length, a = 0, l = 0, h = this.$wrapAsCode, c = this.$indentedSoftWrap, u = e <= Math.max(2 * o, 8) || !1 === c ? 0 : Math.floor(e / 2), d = 0; e - d < i - a;) { var n = a + e - d; if (10 <= s[n - 1] && 10 <= s[n]) t(n); else if (s[n] == f || s[n] == m) { for (; n != a - 1 && s[n] != f; n--); if (a < n) t(n); else { for (n = a + e; n < s.length && s[n] == m; n++); if (n == s.length) break; t(n) } } else { for (var g = Math.max(n - (e - (e >> 2)), a - 1); g < n && s[n] < f;)n--; if (h) { for (; g < n && s[n] < f;)n--; for (; g < n && 9 == s[n];)n-- } else for (; g < n && s[n] < 10;)n--; g < n ? t(++n) : (2 == s[n = a + e] && n--, t(n - d)) } } return r }, this.$getDisplayTokens = function (e, t) { var i, n = []; t = t || 0; for (var s = 0; s < e.length; s++) { var o = e.charCodeAt(s); if (9 == o) { i = this.getScreenTabSize(n.length + t), n.push(11); for (var r = 1; r < i; r++)n.push(12) } else 32 == o ? n.push(10) : 39 < o && o < 48 || 57 < o && o < 64 ? n.push(9) : 4352 <= o && a(o) ? n.push(1, 2) : n.push(1) } return n }, this.$getStringScreenWidth = function (e, t, i) { if (0 == t) return [0, 0]; var n, s; for (null == t && (t = 1 / 0), i = i || 0, s = 0; s < e.length && (9 == (n = e.charCodeAt(s)) ? i += this.getScreenTabSize(i) : 4352 <= n && a(n) ? i += 2 : i += 1, !(t < i)); s++); return [i, s] }, this.lineWidgets = null, this.getRowLength = function (e) { var t = 1; return this.lineWidgets && (t += this.lineWidgets[e] && this.lineWidgets[e].rowCount || 0), this.$useWrapMode && this.$wrapData[e] ? this.$wrapData[e].length + t : t }, this.getRowLineCount = function (e) { return this.$useWrapMode && this.$wrapData[e] ? this.$wrapData[e].length + 1 : 1 }, this.getRowWrapIndent = function (e) { var t; return this.$useWrapMode ? (e = this.screenToDocumentPosition(e, Number.MAX_VALUE), (t = this.$wrapData[e.row]).length && t[0] < e.column ? t.indent : 0) : 0 }, this.getScreenLastRowColumn = function (e) { e = this.screenToDocumentPosition(e, Number.MAX_VALUE); return this.documentToScreenColumn(e.row, e.column) }, this.getDocumentLastRowColumn = function (e, t) { e = this.documentToScreenRow(e, t); return this.getScreenLastRowColumn(e) }, this.getDocumentLastRowColumnPosition = function (e, t) { e = this.documentToScreenRow(e, t); return this.screenToDocumentPosition(e, Number.MAX_VALUE / 10) }, this.getRowSplitData = function (e) { return this.$useWrapMode ? this.$wrapData[e] : void 0 }, this.getScreenTabSize = function (e) { return this.$tabSize - (e % this.$tabSize | 0) }, this.screenToDocumentRow = function (e, t) { return this.screenToDocumentPosition(e, t).row }, this.screenToDocumentColumn = function (e, t) { return this.screenToDocumentPosition(e, t).column }, this.screenToDocumentPosition = function (e, t, i) { if (e < 0) return { row: 0, column: 0 }; for (var n, s, o, r = 0, a = 0, l = 0, h = 0, c = this.$screenRowCache, u = this.$getRowCacheIndex(c, e), d = c.length, g = (o = d && 0 <= u ? (l = c[u], r = this.$docRowCache[u], e > c[d - 1]) : !d, this.getLength() - 1), f = this.getNextFoldLine(r), m = f ? f.start.row : 1 / 0; l <= e && !(e < l + (h = this.getRowLength(r)) || g <= r);)l += h, m < ++r && (r = f.end.row + 1, m = (f = this.getNextFoldLine(r, f)) ? f.start.row : 1 / 0), o && (this.$docRowCache.push(r), this.$screenRowCache.push(l)); if (f && f.start.row <= r) n = this.getFoldDisplayLine(f), r = f.start.row; else { if (l + h <= e || g < r) return { row: g, column: this.getLine(g).length }; n = this.getLine(r), f = null } u = 0, c = Math.floor(e - l); return this.$useWrapMode && (d = this.$wrapData[r]) && (s = d[c], 0 < c && d.length && (u = d.indent, a = d[c - 1] || d[d.length - 1], n = n.substring(a))), void 0 !== i && this.$bidiHandler.isBidiRow(l + c, r, c) && (t = this.$bidiHandler.offsetToCol(i)), a += this.$getStringScreenWidth(n, t - u)[1], this.$useWrapMode && s <= a && (a = s - 1), f ? f.idxToPosition(a) : { row: r, column: a } }, this.documentToScreenPosition = function (e, t) { var i = void 0 === t ? this.$clipPositionToDocument(e.row, e.column) : this.$clipPositionToDocument(e, t), n = (e = i.row, t = i.column, 0), i = null; (a = this.getFoldAt(e, t, 1)) && (e = a.start.row, t = a.start.column); for (var s, o, r = 0, a = this.$docRowCache, l = this.$getRowCacheIndex(a, e), h = a.length, c = (o = h && 0 <= l ? (r = a[l], n = this.$screenRowCache[l], e > a[h - 1]) : !h, this.getNextFoldLine(r)), u = c ? c.start.row : 1 / 0; r < e;) { if (u <= r) { if (e < (s = c.end.row + 1)) break; u = (c = this.getNextFoldLine(s, c)) ? c.start.row : 1 / 0 } else s = r + 1; n += this.getRowLength(r), r = s, o && (this.$docRowCache.push(r), this.$screenRowCache.push(n)) } var d = "", i = c && u <= r ? (d = this.getFoldDisplayLine(c, e, t), c.start.row) : (d = this.getLine(e).substring(0, t), e), l = 0; if (this.$useWrapMode) { var g = this.$wrapData[i]; if (g) { for (var f = 0; d.length >= g[f];)n++, f++; d = d.substring(g[f - 1] || 0, d.length), l = 0 < f ? g.indent : 0 } } return this.lineWidgets && this.lineWidgets[r] && this.lineWidgets[r].rowsAbove && (n += this.lineWidgets[r].rowsAbove), { row: n, column: l + this.$getStringScreenWidth(d)[0] } }, this.documentToScreenColumn = function (e, t) { return this.documentToScreenPosition(e, t).column }, this.documentToScreenRow = function (e, t) { return this.documentToScreenPosition(e, t).row }, this.getScreenLength = function () { var e = 0, t = null; if (this.$useWrapMode) for (var i = this.$wrapData.length, n = 0, s = 0, o = (t = this.$foldData[s++]) ? t.start.row : 1 / 0; n < i;) { var r = this.$wrapData[n]; e += r ? r.length + 1 : 1, o < ++n && (n = t.end.row + 1, o = (t = this.$foldData[s++]) ? t.start.row : 1 / 0) } else for (var e = this.getLength(), a = this.$foldData, s = 0; s < a.length; s++)e -= (t = a[s]).end.row - t.start.row; return this.lineWidgets && (e += this.$getWidgetScreenLength()), e }, this.$setFontMetrics = function (o) { this.$enableVarChar && (this.$getStringScreenWidth = function (e, t, i) { if (0 === t) return [0, 0]; var n, s; for (t = t || 1 / 0, i = i || 0, s = 0; s < e.length && !(t < (i += "\t" === (n = e.charAt(s)) ? this.getScreenTabSize(i) : o.getCharacterWidth(n))); s++); return [i, s] }) }, this.destroy = function () { this.destroyed || (this.bgTokenizer.setDocument(null), this.bgTokenizer.cleanup(), this.destroyed = !0), this.$stopWorker(), this.removeAllListeners(), this.doc && this.doc.off("change", this.$onChange), this.selection.detach() }, this.isFullWidth = a }.call(n.prototype), e("./edit_session/folding").Folding.call(n.prototype), e("./edit_session/bracket_match").BracketMatch.call(n.prototype), l.defineOptions(n.prototype, "session", { wrap: { set: function (e) { e && "off" != e ? "free" == e ? e = !0 : "printMargin" == e ? e = -1 : "string" == typeof e && (e = parseInt(e, 10) || !1) : e = !1, this.$wrap != e && ((this.$wrap = e) ? (this.setWrapLimitRange(e = "number" == typeof e ? e : null, e), this.setUseWrapMode(!0)) : this.setUseWrapMode(!1)) }, get: function () { return this.getUseWrapMode() ? -1 == this.$wrap ? "printMargin" : this.getWrapLimitRange().min ? this.$wrap : "free" : "off" }, handlesSet: !0 }, wrapMethod: { set: function (e) { (e = "auto" == e ? "text" != this.$mode.type : "text" != e) != this.$wrapAsCode && (this.$wrapAsCode = e, this.$useWrapMode && (this.$useWrapMode = !1, this.setUseWrapMode(!0))) }, initialValue: "auto" }, indentedSoftWrap: { set: function () { this.$useWrapMode && (this.$useWrapMode = !1, this.setUseWrapMode(!0)) }, initialValue: !0 }, firstLineNumber: { set: function () { this._signal("changeBreakpoint") }, initialValue: 1 }, useWorker: { set: function (e) { this.$useWorker = e, this.$stopWorker(), e && this.$startWorker() }, initialValue: !0 }, useSoftTabs: { initialValue: !0 }, tabSize: { set: function (e) { 0 < (e = parseInt(e)) && this.$tabSize !== e && (this.$modified = !0, this.$rowLengthCache = [], this.$tabSize = e, this._signal("changeTabSize")) }, initialValue: 4, handlesSet: !0 }, navigateWithinSoftTabs: { initialValue: !1 }, foldStyle: { set: function (e) { this.setFoldStyle(e) }, handlesSet: !0 }, overwrite: { set: function (e) { this._signal("changeOverwrite") }, initialValue: !1 }, newLineMode: { set: function (e) { this.doc.setNewLineMode(e) }, get: function () { return this.doc.getNewLineMode() }, handlesSet: !0 }, mode: { set: function (e) { this.setMode(e) }, get: function () { return this.$modeId }, handlesSet: !0 } }), t.EditSession = n }), define("ace/search", ["require", "exports", "module", "ace/lib/lang", "ace/lib/oop", "ace/range"], function (e, t, i) { "use strict"; function n() { this.$options = {} } var $ = e("./lib/lang"), s = e("./lib/oop"), b = e("./range").Range; (function () { this.set = function (e) { return s.mixin(this.$options, e), this }, this.getOptions = function () { return $.copyObject(this.$options) }, this.setOptions = function (e) { this.$options = e }, this.find = function (e) { var s = this.$options, e = this.$matchIterator(e, s); if (!e) return !1; var o = null; return e.forEach(function (e, t, i, n) { return o = new b(e, t, i, n), !(t == n && s.start && s.start.start && 0 != s.skipCurrent && o.isEqual(s.start)) || (o = null, !1) }), o }, this.findAll = function (e) { var t = this.$options; if (!t.needle) return []; this.$assembleRegExp(t); var i = t.range, n = i ? e.getLines(i.start.row, i.end.row) : e.doc.getAllLines(), s = [], o = t.re; if (t.$isMultiLine) { var r, a = o.length, l = n.length - a; e: for (var h = o.offset || 0; h <= l; h++) { for (var c = 0; c < a; c++)if (-1 == n[h + c].search(o[c])) continue e; var u = n[h], d = n[h + a - 1], u = u.length - u.match(o[0])[0].length, d = d.match(o[a - 1])[0].length; r && r.end.row === h && r.end.column > u || (s.push(r = new b(h, u, h + a - 1, d)), 2 < a && (h = h + a - 2)) } } else for (var g = 0; g < n.length; g++)for (var f = $.getMatchOffsets(n[g], o), c = 0; c < f.length; c++) { var m = f[c]; s.push(new b(g, m.offset, g, m.offset + m.length)) } if (i) { for (var p = i.start.column, w = i.end.column, g = 0, c = s.length - 1; g < c && s[g].start.column < p && 0 == s[g].start.row;)g++; for (var v = i.end.row - i.start.row; g < c && s[c].end.column > w && s[c].end.row == v;)c--; for (s = s.slice(g, c + 1), g = 0, c = s.length; g < c; g++)s[g].start.row += i.start.row, s[g].end.row += i.start.row } return s }, this.replace = function (e, t) { var i = this.$options, n = this.$assembleRegExp(i); if (i.$isMultiLine) return t; if (n) { var s = n.exec(e); if (!s || s[0].length != e.length) return null; if (t = e.replace(n, t), i.preserveCase) { t = t.split(""); for (var o = Math.min(e.length, e.length); o--;) { var r = e[o]; r && r.toLowerCase() != r ? t[o] = t[o].toUpperCase() : t[o] = t[o].toLowerCase() } t = t.join("") } return t } }, this.$assembleRegExp = function (e, t) { if (e.needle instanceof RegExp) return e.re = e.needle; var i, n = e.needle; if (!e.needle) return e.re = !1; function s(e) { return /\w/.test(e) || i.regExp ? "\\b" : "" } e.regExp || (n = $.escapeRegExp(n)), e.wholeWord && (i = e, n = s((o = n)[0]) + o + s(o[o.length - 1])); var o = e.caseSensitive ? "gm" : "gmi"; if (e.$isMultiLine = !t && /[\n\r]/.test(n), e.$isMultiLine) return e.re = this.$assembleMultilineRegExp(n, o); try { var r = new RegExp(n, o) } catch (e) { r = !1 } return e.re = r }, this.$assembleMultilineRegExp = function (e, t) { for (var i = e.replace(/\r\n|\r|\n/g, "$\n^").split("\n"), n = [], s = 0; s < i.length; s++)try { n.push(new RegExp(i[s], t)) } catch (e) { return !1 } return n }, this.$matchIterator = function (c, i) { var u = this.$assembleRegExp(i); if (!u) return !1; var a, n, l = 1 == i.backwards, e = 0 != i.skipCurrent, t = i.range, s = i.start, o = ((s = s || (t ? t[l ? "end" : "start"] : c.selection.getRange())).start && (s = s[e != l ? "end" : "start"]), t ? t.start.row : 0), r = t ? t.end.row : c.getLength() - 1; return n = i.$isMultiLine ? (a = u.length, function (e, t, i) { var n = l ? e - a + 1 : e; if (!(n < 0 || n + a > c.getLength())) { var s = c.getLine(n), e = s.search(u[0]); if (!(!l && e < t || -1 === e)) { for (var o = 1; o < a; o++)if (-1 == (s = c.getLine(n + o)).search(u[o])) return; var r = s.match(u[a - 1])[0].length; if (!(l && t < r)) return !!i(n, e, n + a - 1, r) || void 0 } } }) : l ? function (e, t, i) { var n, s = c.getLine(e), o = []; for (u.lastIndex = 0; n = u.exec(s);) { var r = n[0].length, a = n.index; if (!r) { if (a >= s.length) break; u.lastIndex = a += 1 } if (n.index + r > t) break; o.push(n.index, r) } for (var l = o.length - 1; 0 <= l; l -= 2) { var h = o[l - 1]; if (i(e, h, e, h + (r = o[l]))) return !0 } } : function (e, t, i) { var n = c.getLine(e); for (u.lastIndex = t; s = u.exec(n);) { var s, o = s[0].length; if (i(e, s = s.index, e, s + o)) return !0; if (!o && (u.lastIndex = s += 1, s >= n.length)) return !1 } }, { forEach: l ? function (e) { var t = s.row; if (!n(t, s.column, e)) { for (t--; o <= t; t--)if (n(t, Number.MAX_VALUE, e)) return; if (0 != i.wrap) for (t = r, o = s.row; o <= t; t--)if (n(t, Number.MAX_VALUE, e)) return } } : function (e) { var t = s.row; if (!n(t, s.column, e)) { for (t += 1; t <= r; t++)if (n(t, 0, e)) return; if (0 != i.wrap) for (t = o, r = s.row; t <= r; t++)if (n(t, 0, e)) return } } } } }).call(n.prototype), t.Search = n }), define("ace/keyboard/hash_handler", ["require", "exports", "module", "ace/lib/keys", "ace/lib/useragent"], function (e, t, i) { "use strict"; function n(e, t) { this.platform = t || (o.isMac ? "mac" : "win"), this.commands = {}, this.commandKeyBinding = {}, this.addCommands(e), this.$singleCommand = !0 } function s(e, t) { n.call(this, e, t), this.$singleCommand = !1 } var a = e("../lib/keys"), o = e("../lib/useragent"), l = a.KEY_MODS; s.prototype = n.prototype, function () { function r(e) { return "object" == typeof e && e.bindKey && e.bindKey.position || (e.isDefault ? -100 : 0) } this.addCommand = function (e) { this.commands[e.name] && this.removeCommand(e), (this.commands[e.name] = e).bindKey && this._buildKeyHash(e) }, this.removeCommand = function (e, t) { var i, n = e && ("string" == typeof e ? e : e.name), s = (e = this.commands[n], t || delete this.commands[n], this.commandKeyBinding); for (i in s) { var o, r = s[i]; r == e ? delete s[i] : Array.isArray(r) && -1 != (o = r.indexOf(e)) && (r.splice(o, 1), 1 == r.length && (s[i] = r[0])) } }, this.bindKey = function (e, n, s) { if ("object" == typeof e && e && (null == s && (s = e.position), e = e[this.platform]), e) return "function" == typeof n ? this.addCommand({ exec: n, bindKey: e, name: n.name || e }) : void e.split("|").forEach(function (e) { var t = "", i = (-1 != e.indexOf(" ") && (e = (i = e.split(/\s+/)).pop(), i.forEach(function (e) { e = this.parseKeys(e), e = l[e.hashId] + e.key; t += (t ? " " : "") + e, this._addCommandToBinding(t, "chainKeys") }, this), t += " "), this.parseKeys(e)), e = l[i.hashId] + i.key; this._addCommandToBinding(t + e, n, s) }, this) }, this._addCommandToBinding = function (e, t, i) { var n = this.commandKeyBinding; if (t) if (!n[e] || this.$singleCommand) n[e] = t; else { Array.isArray(n[e]) ? -1 != (o = n[e].indexOf(t)) && n[e].splice(o, 1) : n[e] = [n[e]], "number" != typeof i && (i = r(t)); for (var s = n[e], o = 0; o < s.length; o++)if (i < r(s[o])) break; s.splice(o, 0, t) } else delete n[e] }, this.addCommands = function (i) { i && Object.keys(i).forEach(function (e) { var t = i[e]; if (t) { if ("string" == typeof t) return this.bindKey(t, e); "object" == typeof (t = "function" == typeof t ? { exec: t } : t) && (t.name || (t.name = e), this.addCommand(t)) } }, this) }, this.removeCommands = function (t) { Object.keys(t).forEach(function (e) { this.removeCommand(t[e]) }, this) }, this.bindKeys = function (t) { Object.keys(t).forEach(function (e) { this.bindKey(e, t[e]) }, this) }, this._buildKeyHash = function (e) { this.bindKey(e.bindKey, e) }, this.parseKeys = function (e) { var t = e.toLowerCase().split(/[\-\+]([\-\+])?/).filter(function (e) { return e }), i = t.pop(), n = a[i]; if (a.FUNCTION_KEYS[n]) i = a.FUNCTION_KEYS[n].toLowerCase(); else { if (!t.length) return { key: i, hashId: -1 }; if (1 == t.length && "shift" == t[0]) return { key: i.toUpperCase(), hashId: -1 } } for (var s = 0, o = t.length; o--;) { var r = a.KEY_MODS[t[o]]; if (null == r) return "undefined" != typeof console && console.error("invalid modifier " + t[o] + " in " + e), !1; s |= r } return { key: i, hashId: s } }, this.findKeyCommand = function (e, t) { e = l[e] + t; return this.commandKeyBinding[e] }, this.handleKeyboard = function (e, t, i, n) { var s, o; if (!(n < 0)) return s = l[t] + i, o = this.commandKeyBinding[s], e.$keyChain && (e.$keyChain += " " + s, o = this.commandKeyBinding[e.$keyChain] || o), !o || "chainKeys" != o && "chainKeys" != o[o.length - 1] ? (e.$keyChain && (t && 4 != t || 1 != i.length ? (-1 == t || 0 < n) && (e.$keyChain = "") : e.$keyChain = e.$keyChain.slice(0, -s.length - 1)), { command: o }) : (e.$keyChain = e.$keyChain || s, { command: "null" }) }, this.getStatusText = function (e, t) { return t.$keyChain || "" } }.call(n.prototype), t.HashHandler = n, t.MultiHashHandler = s }), define("ace/commands/command_manager", ["require", "exports", "module", "ace/lib/oop", "ace/keyboard/hash_handler", "ace/lib/event_emitter"], function (e, t, i) { "use strict"; function n(e, t) { o.call(this, t, e), this.byName = this.commands, this.setDefaultHandler("exec", function (e) { return e.args ? e.command.exec(e.editor, e.args, e.event, !1) : e.command.exec(e.editor, {}, e.event, !0) }) } var s = e("../lib/oop"), o = e("../keyboard/hash_handler").MultiHashHandler, r = e("../lib/event_emitter").EventEmitter; s.inherits(n, o), function () { s.implement(this, r), this.exec = function (e, t, i) { if (Array.isArray(e)) { for (var n = e.length; n--;)if (this.exec(e[n], t, i)) return !0; return !1 } if (!(e = "string" == typeof e ? this.commands[e] : e)) return !1; if (t && t.$readOnly && !e.readOnly) return !1; if (0 != this.$checkCommandState && e.isAvailable && !e.isAvailable(t)) return !1; var s = { editor: t, command: e, args: i }; return s.returnValue = this._emit("exec", s), this._signal("afterExec", s), !1 !== s.returnValue }, this.toggleRecording = function (e) { if (!this.$inReplay) return e && e._emit("changeStatus"), this.recording ? (this.macro.pop(), this.off("exec", this.$addCommandToMacro), this.macro.length || (this.macro = this.oldMacro), this.recording = !1) : (this.$addCommandToMacro || (this.$addCommandToMacro = function (e) { this.macro.push([e.command, e.args]) }.bind(this)), this.oldMacro = this.macro, this.macro = [], this.on("exec", this.$addCommandToMacro), this.recording = !0) }, this.replay = function (t) { if (!this.$inReplay && this.macro) { if (this.recording) return this.toggleRecording(t); try { this.$inReplay = !0, this.macro.forEach(function (e) { "string" == typeof e ? this.exec(e, t) : this.exec(e[0], t, e[1]) }, this) } finally { this.$inReplay = !1 } } }, this.trimMacro = function (e) { return e.map(function (e) { return "string" != typeof e[0] && (e[0] = e[0].name), e = e[1] ? e : e[0] }) } }.call(n.prototype), t.CommandManager = n }), define("ace/commands/default_commands", ["require", "exports", "module", "ace/lib/lang", "ace/config", "ace/range"], function (e, t, i) { "use strict"; function n(e, t) { return { win: e, mac: t } } var l = e("../lib/lang"), s = e("../config"), h = e("../range").Range; t.commands = [{ name: "showSettingsMenu", description: "Show settings menu", bindKey: n("Ctrl-,", "Command-,"), exec: function (t) { s.loadModule("ace/ext/settings_menu", function (e) { e.init(t), t.showSettingsMenu() }) }, readOnly: !0 }, { name: "goToNextError", description: "Go to next error", bindKey: n("Alt-E", "F4"), exec: function (t) { s.loadModule("./ext/error_marker", function (e) { e.showErrorMarker(t, 1) }) }, scrollIntoView: "animate", readOnly: !0 }, { name: "goToPreviousError", description: "Go to previous error", bindKey: n("Alt-Shift-E", "Shift-F4"), exec: function (t) { s.loadModule("./ext/error_marker", function (e) { e.showErrorMarker(t, -1) }) }, scrollIntoView: "animate", readOnly: !0 }, { name: "selectall", description: "Select all", bindKey: n("Ctrl-A", "Command-A"), exec: function (e) { e.selectAll() }, readOnly: !0 }, { name: "centerselection", description: "Center selection", bindKey: n(null, "Ctrl-L"), exec: function (e) { e.centerSelection() }, readOnly: !0 }, { name: "gotoline", description: "Go to line...", bindKey: n("Ctrl-L", "Command-L"), exec: function (e, t) { "number" != typeof t || isNaN(t) || e.gotoLine(t), e.prompt({ $type: "gotoLine" }) }, readOnly: !0 }, { name: "fold", bindKey: n("Alt-L|Ctrl-F1", "Command-Alt-L|Command-F1"), exec: function (e) { e.session.toggleFold(!1) }, multiSelectAction: "forEach", scrollIntoView: "center", readOnly: !0 }, { name: "unfold", bindKey: n("Alt-Shift-L|Ctrl-Shift-F1", "Command-Alt-Shift-L|Command-Shift-F1"), exec: function (e) { e.session.toggleFold(!0) }, multiSelectAction: "forEach", scrollIntoView: "center", readOnly: !0 }, { name: "toggleFoldWidget", description: "Toggle fold widget", bindKey: n("F2", "F2"), exec: function (e) { e.session.toggleFoldWidget() }, multiSelectAction: "forEach", scrollIntoView: "center", readOnly: !0 }, { name: "toggleParentFoldWidget", description: "Toggle parent fold widget", bindKey: n("Alt-F2", "Alt-F2"), exec: function (e) { e.session.toggleFoldWidget(!0) }, multiSelectAction: "forEach", scrollIntoView: "center", readOnly: !0 }, { name: "foldall", description: "Fold all", bindKey: n(null, "Ctrl-Command-Option-0"), exec: function (e) { e.session.foldAll() }, scrollIntoView: "center", readOnly: !0 }, { name: "foldAllComments", description: "Fold all comments", bindKey: n(null, "Ctrl-Command-Option-0"), exec: function (e) { e.session.foldAllComments() }, scrollIntoView: "center", readOnly: !0 }, { name: "foldOther", description: "Fold other", bindKey: n("Alt-0", "Command-Option-0"), exec: function (e) { e.session.foldAll(), e.session.unfold(e.selection.getAllRanges()) }, scrollIntoView: "center", readOnly: !0 }, { name: "unfoldall", description: "Unfold all", bindKey: n("Alt-Shift-0", "Command-Option-Shift-0"), exec: function (e) { e.session.unfold() }, scrollIntoView: "center", readOnly: !0 }, { name: "findnext", description: "Find next", bindKey: n("Ctrl-K", "Command-G"), exec: function (e) { e.findNext() }, multiSelectAction: "forEach", scrollIntoView: "center", readOnly: !0 }, { name: "findprevious", description: "Find previous", bindKey: n("Ctrl-Shift-K", "Command-Shift-G"), exec: function (e) { e.findPrevious() }, multiSelectAction: "forEach", scrollIntoView: "center", readOnly: !0 }, { name: "selectOrFindNext", description: "Select or find next", bindKey: n("Alt-K", "Ctrl-G"), exec: function (e) { e.selection.isEmpty() ? e.selection.selectWord() : e.findNext() }, readOnly: !0 }, { name: "selectOrFindPrevious", description: "Select or find previous", bindKey: n("Alt-Shift-K", "Ctrl-Shift-G"), exec: function (e) { e.selection.isEmpty() ? e.selection.selectWord() : e.findPrevious() }, readOnly: !0 }, { name: "find", description: "Find", bindKey: n("Ctrl-F", "Command-F"), exec: function (t) { s.loadModule("ace/ext/searchbox", function (e) { e.Search(t) }) }, readOnly: !0 }, { name: "overwrite", description: "Overwrite", bindKey: "Insert", exec: function (e) { e.toggleOverwrite() }, readOnly: !0 }, { name: "selecttostart", description: "Select to start", bindKey: n("Ctrl-Shift-Home", "Command-Shift-Home|Command-Shift-Up"), exec: function (e) { e.getSelection().selectFileStart() }, multiSelectAction: "forEach", readOnly: !0, scrollIntoView: "animate", aceCommandGroup: "fileJump" }, { name: "gotostart", description: "Go to start", bindKey: n("Ctrl-Home", "Command-Home|Command-Up"), exec: function (e) { e.navigateFileStart() }, multiSelectAction: "forEach", readOnly: !0, scrollIntoView: "animate", aceCommandGroup: "fileJump" }, { name: "selectup", description: "Select up", bindKey: n("Shift-Up", "Shift-Up|Ctrl-Shift-P"), exec: function (e) { e.getSelection().selectUp() }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "golineup", description: "Go line up", bindKey: n("Up", "Up|Ctrl-P"), exec: function (e, t) { e.navigateUp(t.times) }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "selecttoend", description: "Select to end", bindKey: n("Ctrl-Shift-End", "Command-Shift-End|Command-Shift-Down"), exec: function (e) { e.getSelection().selectFileEnd() }, multiSelectAction: "forEach", readOnly: !0, scrollIntoView: "animate", aceCommandGroup: "fileJump" }, { name: "gotoend", description: "Go to end", bindKey: n("Ctrl-End", "Command-End|Command-Down"), exec: function (e) { e.navigateFileEnd() }, multiSelectAction: "forEach", readOnly: !0, scrollIntoView: "animate", aceCommandGroup: "fileJump" }, { name: "selectdown", description: "Select down", bindKey: n("Shift-Down", "Shift-Down|Ctrl-Shift-N"), exec: function (e) { e.getSelection().selectDown() }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "golinedown", description: "Go line down", bindKey: n("Down", "Down|Ctrl-N"), exec: function (e, t) { e.navigateDown(t.times) }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "selectwordleft", description: "Select word left", bindKey: n("Ctrl-Shift-Left", "Option-Shift-Left"), exec: function (e) { e.getSelection().selectWordLeft() }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "gotowordleft", description: "Go to word left", bindKey: n("Ctrl-Left", "Option-Left"), exec: function (e) { e.navigateWordLeft() }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "selecttolinestart", description: "Select to line start", bindKey: n("Alt-Shift-Left", "Command-Shift-Left|Ctrl-Shift-A"), exec: function (e) { e.getSelection().selectLineStart() }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "gotolinestart", description: "Go to line start", bindKey: n("Alt-Left|Home", "Command-Left|Home|Ctrl-A"), exec: function (e) { e.navigateLineStart() }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "selectleft", description: "Select left", bindKey: n("Shift-Left", "Shift-Left|Ctrl-Shift-B"), exec: function (e) { e.getSelection().selectLeft() }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "gotoleft", description: "Go to left", bindKey: n("Left", "Left|Ctrl-B"), exec: function (e, t) { e.navigateLeft(t.times) }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "selectwordright", description: "Select word right", bindKey: n("Ctrl-Shift-Right", "Option-Shift-Right"), exec: function (e) { e.getSelection().selectWordRight() }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "gotowordright", description: "Go to word right", bindKey: n("Ctrl-Right", "Option-Right"), exec: function (e) { e.navigateWordRight() }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "selecttolineend", description: "Select to line end", bindKey: n("Alt-Shift-Right", "Command-Shift-Right|Shift-End|Ctrl-Shift-E"), exec: function (e) { e.getSelection().selectLineEnd() }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "gotolineend", description: "Go to line end", bindKey: n("Alt-Right|End", "Command-Right|End|Ctrl-E"), exec: function (e) { e.navigateLineEnd() }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "selectright", description: "Select right", bindKey: n("Shift-Right", "Shift-Right"), exec: function (e) { e.getSelection().selectRight() }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "gotoright", description: "Go to right", bindKey: n("Right", "Right|Ctrl-F"), exec: function (e, t) { e.navigateRight(t.times) }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "selectpagedown", description: "Select page down", bindKey: "Shift-PageDown", exec: function (e) { e.selectPageDown() }, readOnly: !0 }, { name: "pagedown", description: "Page down", bindKey: n(null, "Option-PageDown"), exec: function (e) { e.scrollPageDown() }, readOnly: !0 }, { name: "gotopagedown", description: "Go to page down", bindKey: n("PageDown", "PageDown|Ctrl-V"), exec: function (e) { e.gotoPageDown() }, readOnly: !0 }, { name: "selectpageup", description: "Select page up", bindKey: "Shift-PageUp", exec: function (e) { e.selectPageUp() }, readOnly: !0 }, { name: "pageup", description: "Page up", bindKey: n(null, "Option-PageUp"), exec: function (e) { e.scrollPageUp() }, readOnly: !0 }, { name: "gotopageup", description: "Go to page up", bindKey: "PageUp", exec: function (e) { e.gotoPageUp() }, readOnly: !0 }, { name: "scrollup", description: "Scroll up", bindKey: n("Ctrl-Up", null), exec: function (e) { e.renderer.scrollBy(0, -2 * e.renderer.layerConfig.lineHeight) }, readOnly: !0 }, { name: "scrolldown", description: "Scroll down", bindKey: n("Ctrl-Down", null), exec: function (e) { e.renderer.scrollBy(0, 2 * e.renderer.layerConfig.lineHeight) }, readOnly: !0 }, { name: "selectlinestart", description: "Select line start", bindKey: "Shift-Home", exec: function (e) { e.getSelection().selectLineStart() }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "selectlineend", description: "Select line end", bindKey: "Shift-End", exec: function (e) { e.getSelection().selectLineEnd() }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "togglerecording", description: "Toggle recording", bindKey: n("Ctrl-Alt-E", "Command-Option-E"), exec: function (e) { e.commands.toggleRecording(e) }, readOnly: !0 }, { name: "replaymacro", description: "Replay macro", bindKey: n("Ctrl-Shift-E", "Command-Shift-E"), exec: function (e) { e.commands.replay(e) }, readOnly: !0 }, { name: "jumptomatching", description: "Jump to matching", bindKey: n("Ctrl-\\|Ctrl-P", "Command-\\"), exec: function (e) { e.jumpToMatching() }, multiSelectAction: "forEach", scrollIntoView: "animate", readOnly: !0 }, { name: "selecttomatching", description: "Select to matching", bindKey: n("Ctrl-Shift-\\|Ctrl-Shift-P", "Command-Shift-\\"), exec: function (e) { e.jumpToMatching(!0) }, multiSelectAction: "forEach", scrollIntoView: "animate", readOnly: !0 }, { name: "expandToMatching", description: "Expand to matching", bindKey: n("Ctrl-Shift-M", "Ctrl-Shift-M"), exec: function (e) { e.jumpToMatching(!0, !0) }, multiSelectAction: "forEach", scrollIntoView: "animate", readOnly: !0 }, { name: "passKeysToBrowser", description: "Pass keys to browser", bindKey: n(null, null), exec: function () { }, passEvent: !0, readOnly: !0 }, { name: "copy", description: "Copy", exec: function (e) { }, readOnly: !0 }, { name: "cut", description: "Cut", exec: function (e) { var t = e.$copyWithEmptySelection && e.selection.isEmpty() ? e.selection.getLineRange() : e.selection.getRange(); e._emit("cut", t), t.isEmpty() || e.session.remove(t), e.clearSelection() }, scrollIntoView: "cursor", multiSelectAction: "forEach" }, { name: "paste", description: "Paste", exec: function (e, t) { e.$handlePaste(t) }, scrollIntoView: "cursor" }, { name: "removeline", description: "Remove line", bindKey: n("Ctrl-D", "Command-D"), exec: function (e) { e.removeLines() }, scrollIntoView: "cursor", multiSelectAction: "forEachLine" }, { name: "duplicateSelection", description: "Duplicate selection", bindKey: n("Ctrl-Shift-D", "Command-Shift-D"), exec: function (e) { e.duplicateSelection() }, scrollIntoView: "cursor", multiSelectAction: "forEach" }, { name: "sortlines", description: "Sort lines", bindKey: n("Ctrl-Alt-S", "Command-Alt-S"), exec: function (e) { e.sortLines() }, scrollIntoView: "selection", multiSelectAction: "forEachLine" }, { name: "togglecomment", description: "Toggle comment", bindKey: n("Ctrl-/", "Command-/"), exec: function (e) { e.toggleCommentLines() }, multiSelectAction: "forEachLine", scrollIntoView: "selectionPart" }, { name: "toggleBlockComment", description: "Toggle block comment", bindKey: n("Ctrl-Shift-/", "Command-Shift-/"), exec: function (e) { e.toggleBlockComment() }, multiSelectAction: "forEach", scrollIntoView: "selectionPart" }, { name: "modifyNumberUp", description: "Modify number up", bindKey: n("Ctrl-Shift-Up", "Alt-Shift-Up"), exec: function (e) { e.modifyNumber(1) }, scrollIntoView: "cursor", multiSelectAction: "forEach" }, { name: "modifyNumberDown", description: "Modify number down", bindKey: n("Ctrl-Shift-Down", "Alt-Shift-Down"), exec: function (e) { e.modifyNumber(-1) }, scrollIntoView: "cursor", multiSelectAction: "forEach" }, { name: "replace", description: "Replace", bindKey: n("Ctrl-H", "Command-Option-F"), exec: function (t) { s.loadModule("ace/ext/searchbox", function (e) { e.Search(t, !0) }) } }, { name: "undo", description: "Undo", bindKey: n("Ctrl-Z", "Command-Z"), exec: function (e) { e.undo() } }, { name: "redo", description: "Redo", bindKey: n("Ctrl-Shift-Z|Ctrl-Y", "Command-Shift-Z|Command-Y"), exec: function (e) { e.redo() } }, { name: "copylinesup", description: "Copy lines up", bindKey: n("Alt-Shift-Up", "Command-Option-Up"), exec: function (e) { e.copyLinesUp() }, scrollIntoView: "cursor" }, { name: "movelinesup", description: "Move lines up", bindKey: n("Alt-Up", "Option-Up"), exec: function (e) { e.moveLinesUp() }, scrollIntoView: "cursor" }, { name: "copylinesdown", description: "Copy lines down", bindKey: n("Alt-Shift-Down", "Command-Option-Down"), exec: function (e) { e.copyLinesDown() }, scrollIntoView: "cursor" }, { name: "movelinesdown", description: "Move lines down", bindKey: n("Alt-Down", "Option-Down"), exec: function (e) { e.moveLinesDown() }, scrollIntoView: "cursor" }, { name: "del", description: "Delete", bindKey: n("Delete", "Delete|Ctrl-D|Shift-Delete"), exec: function (e) { e.remove("right") }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "backspace", description: "Backspace", bindKey: n("Shift-Backspace|Backspace", "Ctrl-Backspace|Shift-Backspace|Backspace|Ctrl-H"), exec: function (e) { e.remove("left") }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "cut_or_delete", description: "Cut or delete", bindKey: n("Shift-Delete", null), exec: function (e) { if (!e.selection.isEmpty()) return !1; e.remove("left") }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "removetolinestart", description: "Remove to line start", bindKey: n("Alt-Backspace", "Command-Backspace"), exec: function (e) { e.removeToLineStart() }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "removetolineend", description: "Remove to line end", bindKey: n("Alt-Delete", "Ctrl-K|Command-Delete"), exec: function (e) { e.removeToLineEnd() }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "removetolinestarthard", description: "Remove to line start hard", bindKey: n("Ctrl-Shift-Backspace", null), exec: function (e) { var t = e.selection.getRange(); t.start.column = 0, e.session.remove(t) }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "removetolineendhard", description: "Remove to line end hard", bindKey: n("Ctrl-Shift-Delete", null), exec: function (e) { var t = e.selection.getRange(); t.end.column = Number.MAX_VALUE, e.session.remove(t) }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "removewordleft", description: "Remove word left", bindKey: n("Ctrl-Backspace", "Alt-Backspace|Ctrl-Alt-Backspace"), exec: function (e) { e.removeWordLeft() }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "removewordright", description: "Remove word right", bindKey: n("Ctrl-Delete", "Alt-Delete"), exec: function (e) { e.removeWordRight() }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "outdent", description: "Outdent", bindKey: n("Shift-Tab", "Shift-Tab"), exec: function (e) { e.blockOutdent() }, multiSelectAction: "forEach", scrollIntoView: "selectionPart" }, { name: "indent", description: "Indent", bindKey: n("Tab", "Tab"), exec: function (e) { e.indent() }, multiSelectAction: "forEach", scrollIntoView: "selectionPart" }, { name: "blockoutdent", description: "Block outdent", bindKey: n("Ctrl-[", "Ctrl-["), exec: function (e) { e.blockOutdent() }, multiSelectAction: "forEachLine", scrollIntoView: "selectionPart" }, { name: "blockindent", description: "Block indent", bindKey: n("Ctrl-]", "Ctrl-]"), exec: function (e) { e.blockIndent() }, multiSelectAction: "forEachLine", scrollIntoView: "selectionPart" }, { name: "insertstring", description: "Insert string", exec: function (e, t) { e.insert(t) }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "inserttext", description: "Insert text", exec: function (e, t) { e.insert(l.stringRepeat(t.text || "", t.times || 1)) }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "splitline", description: "Split line", bindKey: n(null, "Ctrl-O"), exec: function (e) { e.splitLine() }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "transposeletters", description: "Transpose letters", bindKey: n("Alt-Shift-X", "Ctrl-T"), exec: function (e) { e.transposeLetters() }, multiSelectAction: function (e) { e.transposeSelections(1) }, scrollIntoView: "cursor" }, { name: "touppercase", description: "To uppercase", bindKey: n("Ctrl-U", "Ctrl-U"), exec: function (e) { e.toUpperCase() }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "tolowercase", description: "To lowercase", bindKey: n("Ctrl-Shift-U", "Ctrl-Shift-U"), exec: function (e) { e.toLowerCase() }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "autoindent", description: "Auto Indent", bindKey: n(null, null), exec: function (e) { e.autoIndent() }, multiSelectAction: "forEachLine", scrollIntoView: "animate" }, { name: "expandtoline", description: "Expand to line", bindKey: n("Ctrl-Shift-L", "Command-Shift-L"), exec: function (e) { var t = e.selection.getRange(); t.start.column = t.end.column = 0, t.end.row++, e.selection.setRange(t, !1) }, multiSelectAction: "forEach", scrollIntoView: "cursor", readOnly: !0 }, { name: "openlink", bindKey: n("Ctrl+F3", "F3"), exec: function (e) { e.openLink() } }, { name: "joinlines", description: "Join lines", bindKey: n(null, null), exec: function (e) { for (var t = e.selection.isBackwards(), i = t ? e.selection.getSelectionLead() : e.selection.getSelectionAnchor(), n = t ? e.selection.getSelectionAnchor() : e.selection.getSelectionLead(), t = e.session.doc.getLine(i.row).length, s = e.session.doc.getTextRange(e.selection.getRange()).replace(/\n\s*/, " ").length, o = e.session.doc.getLine(i.row), r = i.row + 1; r <= n.row + 1; r++) { var a = l.stringTrimLeft(l.stringTrimRight(e.session.doc.getLine(r))); o += a = 0 !== a.length ? " " + a : a } n.row + 1 < e.session.doc.getLength() - 1 && (o += e.session.doc.getNewLineCharacter()), e.clearSelection(), e.session.doc.replace(new h(i.row, 0, n.row + 2, 0), o), 0 < s ? (e.selection.moveCursorTo(i.row, i.column), e.selection.selectTo(i.row, i.column + s)) : (t = e.session.doc.getLine(i.row).length > t ? t + 1 : t, e.selection.moveCursorTo(i.row, t)) }, multiSelectAction: "forEach", readOnly: !0 }, { name: "invertSelection", description: "Invert selection", bindKey: n(null, null), exec: function (e) { var t = e.session.doc.getLength() - 1, i = e.session.doc.getLine(t).length, n = e.selection.rangeList.ranges, s = []; n.length < 1 && (n = [e.selection.getRange()]); for (var o = 0; o < n.length; o++)o != n.length - 1 || n[o].end.row === t && n[o].end.column === i || s.push(new h(n[o].end.row, n[o].end.column, t, i)), 0 === o ? 0 === n[o].start.row && 0 === n[o].start.column || s.push(new h(0, 0, n[o].start.row, n[o].start.column)) : s.push(new h(n[o - 1].end.row, n[o - 1].end.column, n[o].start.row, n[o].start.column)); e.exitMultiSelectMode(), e.clearSelection(); for (o = 0; o < s.length; o++)e.selection.addRange(s[o], !1) }, readOnly: !0, scrollIntoView: "none" }, { name: "addLineAfter", description: "Add new line after the current line", exec: function (e) { e.selection.clearSelection(), e.navigateLineEnd(), e.insert("\n") }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "addLineBefore", description: "Add new line before the current line", exec: function (e) { e.selection.clearSelection(); var t = e.getCursorPosition(); e.selection.moveTo(t.row - 1, Number.MAX_VALUE), e.insert("\n"), 0 === t.row && e.navigateUp() }, multiSelectAction: "forEach", scrollIntoView: "cursor" }, { name: "openCommandPallete", description: "Open command palette", bindKey: n("F1", "F1"), exec: function (e) { e.prompt({ $type: "commands" }) }, readOnly: !0 }, { name: "modeSelect", description: "Change language mode...", bindKey: n(null, null), exec: function (e) { e.prompt({ $type: "modes" }) }, readOnly: !0 }]; for (var o = 1; o < 9; o++)t.commands.push({ name: "foldToLevel" + o, description: "Fold To Level " + o, level: o, exec: function (e) { e.session.foldToLevel(this.level) }, scrollIntoView: "center", readOnly: !0 }) }), define("ace/editor", ["require", "exports", "module", "ace/lib/oop", "ace/lib/dom", "ace/lib/lang", "ace/lib/useragent", "ace/keyboard/textinput", "ace/mouse/mouse_handler", "ace/mouse/fold_handler", "ace/keyboard/keybinding", "ace/edit_session", "ace/search", "ace/range", "ace/lib/event_emitter", "ace/commands/command_manager", "ace/commands/default_commands", "ace/config", "ace/token_iterator", "ace/clipboard"], function (e, t, i) { "use strict"; var u = this && this.__values || function (e) { var t = "function" == typeof Symbol && Symbol.iterator, i = t && e[t], n = 0; if (i) return i.call(e); if (e && "number" == typeof e.length) return { next: function () { return { value: (e = e && n >= e.length ? void 0 : e) && e[n++], done: !e } } }; throw new TypeError(t ? "Object is not iterable." : "Symbol.iterator is not defined.") }, s = e("./lib/oop"), n = e("./lib/dom"), m = e("./lib/lang"), o = e("./lib/useragent"), r = e("./keyboard/textinput").TextInput, a = e("./mouse/mouse_handler").MouseHandler, l = e("./mouse/fold_handler").FoldHandler, h = e("./keyboard/keybinding").KeyBinding, c = e("./edit_session").EditSession, d = e("./search").Search, p = e("./range").Range, g = e("./lib/event_emitter").EventEmitter, f = e("./commands/command_manager").CommandManager, w = e("./commands/default_commands").commands, v = e("./config"), $ = e("./token_iterator").TokenIterator, b = e("./clipboard"), y = function (e, t, i) { this.$toDestroy = []; var n = e.getContainerElement(); this.container = n, this.renderer = e, this.id = "editor" + ++y.$uid, this.commands = new f(o.isMac ? "mac" : "win", w), "object" == typeof document && (this.textInput = new r(e.getTextAreaContainer(), this), this.renderer.textarea = this.textInput.getElement(), this.$mouseHandler = new a(this), new l(this)), this.keyBinding = new h(this), this.$search = (new d).set({ wrap: !0 }), this.$historyTracker = this.$historyTracker.bind(this), this.commands.on("exec", this.$historyTracker), this.$initOperationListeners(), this._$emitInputEvent = m.delayedCall(function () { this._signal("input", {}), this.session && !this.session.destroyed && this.session.bgTokenizer.scheduleStart() }.bind(this)), this.on("change", function (e, t) { t._$emitInputEvent.schedule(31) }), this.setSession(t || i && i.session || new c("")), v.resetOptions(this), i && this.setOptions(i), v._signal("editor", this) }, C = (y.$uid = 0, function () { s.implement(this, g), this.$initOperationListeners = function () { this.commands.on("exec", this.startOperation.bind(this), !0), this.commands.on("afterExec", this.endOperation.bind(this), !0), this.$opResetTimer = m.delayedCall(this.endOperation.bind(this, !0)), this.on("change", function () { this.curOp || (this.startOperation(), this.curOp.selectionBefore = this.$lastSel), this.curOp.docChanged = !0 }.bind(this), !0), this.on("changeSelection", function () { this.curOp || (this.startOperation(), this.curOp.selectionBefore = this.$lastSel), this.curOp.selectionChanged = !0 }.bind(this), !0) }, this.curOp = null, this.prevOp = {}, this.startOperation = function (e) { if (this.curOp) { if (!e || this.curOp.command) return; this.prevOp = this.curOp } e || (this.previousCommand = null, e = {}), this.$opResetTimer.schedule(), this.curOp = this.session.curOp = { command: e.command || {}, args: e.args, scrollTop: this.renderer.scrollTop }, this.curOp.selectionBefore = this.selection.toJSON() }, this.endOperation = function (e) { if (this.curOp && this.session) { if (e && !1 === e.returnValue || !this.session) return this.curOp = null; if ((1 != e || !this.curOp.command || "mouse" != this.curOp.command.name) && (this._signal("beforeEndOperation"), this.curOp)) { var e = this.curOp.command, t = e && e.scrollIntoView; if (t) { switch (t) { case "center-animate": t = "animate"; case "center": this.renderer.scrollCursorIntoView(null, .5); break; case "animate": case "cursor": this.renderer.scrollCursorIntoView(); break; case "selectionPart": var i = this.selection.getRange(), n = this.renderer.layerConfig; (i.start.row >= n.lastRow || i.end.row <= n.firstRow) && this.renderer.scrollSelectionIntoView(this.selection.anchor, this.selection.lead) }"animate" == t && this.renderer.animateScrolling(this.curOp.scrollTop) } e = this.selection.toJSON(); this.curOp.selectionAfter = e, this.$lastSel = this.selection.toJSON(), this.session.getUndoManager().addSelection(e), this.prevOp = this.curOp, this.curOp = null } } }, this.$mergeableCommands = ["backspace", "del", "insertstring"], this.$historyTracker = function (e) { var t, i, n, s; this.$mergeUndoDeltas && (t = this.prevOp, i = this.$mergeableCommands, n = t.command && e.command.name == t.command.name, "insertstring" == e.command.name ? (s = e.args, void 0 === this.mergeNextCommand && (this.mergeNextCommand = !0), n = n && this.mergeNextCommand && (!/\s/.test(s) || /\s/.test(t.args)), this.mergeNextCommand = !0) : n = n && -1 !== i.indexOf(e.command.name), (n = "always" != this.$mergeUndoDeltas && 2e3 < Date.now() - this.sequenceStartTime ? !1 : n) ? this.session.mergeUndoDeltas = !0 : -1 !== i.indexOf(e.command.name) && (this.sequenceStartTime = Date.now())) }, this.setKeyboardHandler = function (t, i) { var n; t && "string" == typeof t && "ace" != t ? (this.$keybindingId = t, n = this, v.loadModule(["keybinding", t], function (e) { n.$keybindingId == t && n.keyBinding.setKeyboardHandler(e && e.handler), i && i() })) : (this.$keybindingId = null, this.keyBinding.setKeyboardHandler(t), i && i()) }, this.getKeyboardHandler = function () { return this.keyBinding.getKeyboardHandler() }, this.setSession = function (e) { var t, i; this.session != e && (this.curOp && this.endOperation(), this.curOp = {}, (t = this.session) && (this.session.off("change", this.$onDocumentChange), this.session.off("changeMode", this.$onChangeMode), this.session.off("tokenizerUpdate", this.$onTokenizerUpdate), this.session.off("changeTabSize", this.$onChangeTabSize), this.session.off("changeWrapLimit", this.$onChangeWrapLimit), this.session.off("changeWrapMode", this.$onChangeWrapMode), this.session.off("changeFold", this.$onChangeFold), this.session.off("changeFrontMarker", this.$onChangeFrontMarker), this.session.off("changeBackMarker", this.$onChangeBackMarker), this.session.off("changeBreakpoint", this.$onChangeBreakpoint), this.session.off("changeAnnotation", this.$onChangeAnnotation), this.session.off("changeOverwrite", this.$onCursorChange), this.session.off("changeScrollTop", this.$onScrollTopChange), this.session.off("changeScrollLeft", this.$onScrollLeftChange), (i = this.session.getSelection()).off("changeCursor", this.$onCursorChange), i.off("changeSelection", this.$onSelectionChange)), (this.session = e) ? (this.$onDocumentChange = this.onDocumentChange.bind(this), e.on("change", this.$onDocumentChange), this.renderer.setSession(e), this.$onChangeMode = this.onChangeMode.bind(this), e.on("changeMode", this.$onChangeMode), this.$onTokenizerUpdate = this.onTokenizerUpdate.bind(this), e.on("tokenizerUpdate", this.$onTokenizerUpdate), this.$onChangeTabSize = this.renderer.onChangeTabSize.bind(this.renderer), e.on("changeTabSize", this.$onChangeTabSize), this.$onChangeWrapLimit = this.onChangeWrapLimit.bind(this), e.on("changeWrapLimit", this.$onChangeWrapLimit), this.$onChangeWrapMode = this.onChangeWrapMode.bind(this), e.on("changeWrapMode", this.$onChangeWrapMode), this.$onChangeFold = this.onChangeFold.bind(this), e.on("changeFold", this.$onChangeFold), this.$onChangeFrontMarker = this.onChangeFrontMarker.bind(this), this.session.on("changeFrontMarker", this.$onChangeFrontMarker), this.$onChangeBackMarker = this.onChangeBackMarker.bind(this), this.session.on("changeBackMarker", this.$onChangeBackMarker), this.$onChangeBreakpoint = this.onChangeBreakpoint.bind(this), this.session.on("changeBreakpoint", this.$onChangeBreakpoint), this.$onChangeAnnotation = this.onChangeAnnotation.bind(this), this.session.on("changeAnnotation", this.$onChangeAnnotation), this.$onCursorChange = this.onCursorChange.bind(this), this.session.on("changeOverwrite", this.$onCursorChange), this.$onScrollTopChange = this.onScrollTopChange.bind(this), this.session.on("changeScrollTop", this.$onScrollTopChange), this.$onScrollLeftChange = this.onScrollLeftChange.bind(this), this.session.on("changeScrollLeft", this.$onScrollLeftChange), this.selection = e.getSelection(), this.selection.on("changeCursor", this.$onCursorChange), this.$onSelectionChange = this.onSelectionChange.bind(this), this.selection.on("changeSelection", this.$onSelectionChange), this.onChangeMode(), this.onCursorChange(), this.onScrollTopChange(), this.onScrollLeftChange(), this.onSelectionChange(), this.onChangeFrontMarker(), this.onChangeBackMarker(), this.onChangeBreakpoint(), this.onChangeAnnotation(), this.session.getUseWrapMode() && this.renderer.adjustWrapLimit(), this.renderer.updateFull()) : (this.selection = null, this.renderer.setSession(e)), this._signal("changeSession", { session: e, oldSession: t }), this.curOp = null, t && t._signal("changeEditor", { oldEditor: this }), e && e._signal("changeEditor", { editor: this }), e && !e.destroyed && e.bgTokenizer.scheduleStart()) }, this.getSession = function () { return this.session }, this.setValue = function (e, t) { return this.session.doc.setValue(e), t ? 1 == t ? this.navigateFileEnd() : -1 == t && this.navigateFileStart() : this.selectAll(), e }, this.getValue = function () { return this.session.getValue() }, this.getSelection = function () { return this.selection }, this.resize = function (e) { this.renderer.onResize(e) }, this.setTheme = function (e, t) { this.renderer.setTheme(e, t) }, this.getTheme = function () { return this.renderer.getTheme() }, this.setStyle = function (e) { this.renderer.setStyle(e) }, this.unsetStyle = function (e) { this.renderer.unsetStyle(e) }, this.getFontSize = function () { return this.getOption("fontSize") || n.computedStyle(this.container).fontSize }, this.setFontSize = function (e) { this.setOption("fontSize", e) }, this.$highlightBrackets = function () { var o; this.$highlightPending || ((o = this).$highlightPending = !0, setTimeout(function () { o.$highlightPending = !1; var e, t, i, n, s = o.session; s && !s.destroyed && (s.$bracketHighlight && (s.$bracketHighlight.markerIds.forEach(function (e) { s.removeMarker(e) }), s.$bracketHighlight = null), e = o.getCursorPosition(), t = (t = o.getKeyboardHandler()) && t.$getDirectionForHighlight && t.$getDirectionForHighlight(o), (t = s.getMatchingBracketRanges(e, t)) || (i = new $(s, e.row, e.column).getCurrentToken()) && /\b(?:tag-open|tag-name)/.test(i.type) && ((i = s.getMatchingTags(e)) && (t = [i.openTagName, i.closeTagName])), (t = !t && s.$mode.getMatching ? s.$mode.getMatching(o.session) : t) && (n = "ace_bracket", Array.isArray(t) ? 1 == t.length && (n = "ace_error_bracket") : t = [t], 2 == t.length && (0 == p.comparePoints(t[0].end, t[1].start) ? t = [p.fromPoints(t[0].start, t[1].end)] : 0 == p.comparePoints(t[0].start, t[1].end) && (t = [p.fromPoints(t[1].start, t[0].end)])), s.$bracketHighlight = { ranges: t, markerIds: t.map(function (e) { return s.addMarker(e, n, "text") }) }), o.getHighlightIndentGuides() && o.renderer.$textLayer.$highlightIndentGuide()) }, 50)) }, this.focus = function () { this.textInput.focus() }, this.isFocused = function () { return this.textInput.isFocused() }, this.blur = function () { this.textInput.blur() }, this.onFocus = function (e) { this.$isFocused || (this.$isFocused = !0, this.renderer.showCursor(), this.renderer.visualizeFocus(), this._emit("focus", e)) }, this.onBlur = function (e) { this.$isFocused && (this.$isFocused = !1, this.renderer.hideCursor(), this.renderer.visualizeBlur(), this._emit("blur", e)) }, this.$cursorChange = function () { this.renderer.updateCursor(), this.$highlightBrackets(), this.$updateHighlightActiveLine() }, this.onDocumentChange = function (e) { var t = this.session.$useWrapMode, i = e.start.row == e.end.row ? e.end.row : 1 / 0; this.renderer.updateLines(e.start.row, i, t), this._signal("change", e), this.$cursorChange() }, this.onTokenizerUpdate = function (e) { e = e.data; this.renderer.updateLines(e.first, e.last) }, this.onScrollTopChange = function () { this.renderer.scrollToY(this.session.getScrollTop()) }, this.onScrollLeftChange = function () { this.renderer.scrollToX(this.session.getScrollLeft()) }, this.onCursorChange = function () { this.$cursorChange(), this._signal("changeSelection") }, this.$updateHighlightActiveLine = function () { var e, t, i = this.getSession(); this.$highlightActiveLine && ("line" == this.$selectionStyle && this.selection.isMultiLine() || (e = this.getCursorPosition()), this.renderer.theme && this.renderer.theme.$selectionColorConflict && !this.selection.isEmpty() && (e = !1), !this.renderer.$maxLines || 1 !== this.session.getLength() || 1 < this.renderer.$minLines || (e = !1)), i.$highlightLineMarker && !e ? (i.removeMarker(i.$highlightLineMarker.id), i.$highlightLineMarker = null) : !i.$highlightLineMarker && e ? ((t = new p(e.row, e.column, e.row, 1 / 0)).id = i.addMarker(t, "ace_active-line", "screenLine"), i.$highlightLineMarker = t) : e && (i.$highlightLineMarker.start.row = e.row, i.$highlightLineMarker.end.row = e.row, i.$highlightLineMarker.start.column = e.column, i._signal("changeBackMarker")) }, this.onSelectionChange = function (e) { var t, i, n = this.session, n = (n.$selectionMarker && n.removeMarker(n.$selectionMarker), n.$selectionMarker = null, this.selection.isEmpty() ? this.$updateHighlightActiveLine() : (t = this.selection.getRange(), i = this.getSelectionStyle(), n.$selectionMarker = n.addMarker(t, "ace_selection", i)), this.$highlightSelectedWord && this.$getSelectionHighLightRegexp()); this.session.highlight(n), this._signal("changeSelection") }, this.$getSelectionHighLightRegexp = function () { var e = this.session, t = this.getSelectionRange(); if (!t.isEmpty() && !t.isMultiLine()) { var i = t.start.column, n = t.end.column, e = e.getLine(t.start.row), t = e.substring(i, n); if (!(5e3 < t.length) && /[\w\d]/.test(t)) { t = this.$search.$assembleRegExp({ wholeWord: !0, caseSensitive: !0, needle: t }), e = e.substring(i - 1, n + 1); if (t.test(e)) return t } } }, this.onChangeFrontMarker = function () { this.renderer.updateFrontMarkers() }, this.onChangeBackMarker = function () { this.renderer.updateBackMarkers() }, this.onChangeBreakpoint = function () { this.renderer.updateBreakpoints() }, this.onChangeAnnotation = function () { this.renderer.setAnnotations(this.session.getAnnotations()) }, this.onChangeMode = function (e) { this.renderer.updateText(), this._emit("changeMode", e) }, this.onChangeWrapLimit = function () { this.renderer.updateFull() }, this.onChangeWrapMode = function () { this.renderer.onResize(!0) }, this.onChangeFold = function () { this.$updateHighlightActiveLine(), this.renderer.updateFull() }, this.getSelectedText = function () { return this.session.getTextRange(this.getSelectionRange()) }, this.getCopyText = function () { var e = this.getSelectedText(), t = this.session.doc.getNewLineCharacter(), i = !1; if (!e && this.$copyWithEmptySelection) for (var i = !0, n = this.selection.getAllRanges(), s = 0; s < n.length; s++) { var o = n[s]; s && n[s - 1].start.row == o.start.row || (e += this.session.getLine(o.start.row) + t) } var r = { text: e }; return this._signal("copy", r), b.lineMode = !!i && r.text, r.text }, this.onCopy = function () { this.commands.exec("copy", this) }, this.onCut = function () { this.commands.exec("cut", this) }, this.onPaste = function (e, t) { this.commands.exec("paste", this, { text: e, event: t }) }, this.$handlePaste = function (e) { this._signal("paste", e = "string" == typeof e ? { text: e } : e); var t = e.text, e = t === b.lineMode, i = this.session; if (!this.inMultiSelectMode || this.inVirtualSelectionMode) e ? i.insert({ row: this.selection.lead.row, column: 0 }, t) : this.insert(t); else if (e) this.selection.rangeList.ranges.forEach(function (e) { i.insert({ row: e.start.row, column: 0 }, t) }); else { var n = t.split(/\r\n|\r|\n/), s = this.selection.rangeList.ranges, e = !(2 != n.length || n[0] && n[1]); if (n.length != s.length || e) return this.commands.exec("insertstring", this, t); for (var o = s.length; o--;) { var r = s[o]; r.isEmpty() || i.remove(r), i.insert(r.start, n[o]) } } }, this.execCommand = function (e, t) { return this.commands.exec(e, this, t) }, this.insert = function (e, t) { var i, n = this.session, s = n.getMode(), o = this.getCursorPosition(), r = (!this.getBehavioursEnabled() || t || (i = s.transformAction(n.getState(o.row), "insertion", this, n, e)) && (e === i.text || this.inVirtualSelectionMode || (this.session.mergeUndoDeltas = !1, this.mergeNextCommand = !1), e = i.text), "\t" == e && (e = this.session.getTabString()), this.selection.isEmpty() ? this.session.getOverwrite() && -1 == e.indexOf("\n") && ((r = new p.fromPoints(o, o)).end.column += e.length, this.session.remove(r)) : (r = this.getSelectionRange(), o = this.session.remove(r), this.clearSelection()), "\n" != e && "\r\n" != e || (a = n.getLine(o.row), o.column > a.search(/\S|$/) && (t = a.substr(o.column).search(/\S|$/), n.doc.removeInLine(o.row, o.column, o.column + t))), this.clearSelection(), o.column), t = n.getState(o.row), a = n.getLine(o.row), l = s.checkOutdent(t, a, e); n.insert(o, e), i && i.selection && (2 == i.selection.length ? this.selection.setSelectionRange(new p(o.row, r + i.selection[0], o.row, r + i.selection[1])) : this.selection.setSelectionRange(new p(o.row + i.selection[0], i.selection[1], o.row + i.selection[2], i.selection[3]))), this.$enableAutoIndent && (n.getDocument().isNewLine(e) && (r = s.getNextLineIndent(t, a.slice(0, o.column), n.getTabString()), n.insert({ row: o.row + 1, column: 0 }, r)), l && s.autoOutdent(t, n, o.row)) }, this.autoIndent = function () { for (var e, t, i, n, s, o = this.session, r = o.getMode(), a = (i = this.selection.isEmpty() ? (t = 0, o.doc.getLength() - 1) : (t = (e = this.getSelectionRange()).start.row, e.end.row), ""), l = "", h = o.getTabString(), c = t; c <= i; c++)0 < c && (a = o.getState(c - 1), n = o.getLine(c - 1), l = r.getNextLineIndent(a, n, h)), n = o.getLine(c), l !== (s = r.$getIndent(n)) && (0 < s.length && (s = new p(c, 0, c, s.length), o.remove(s)), 0 < l.length && o.insert({ row: c, column: 0 }, l)), r.autoOutdent(a, o, c) }, this.onTextInput = function (e, t) { if (!t) return this.keyBinding.onTextInput(e); this.startOperation({ command: { name: "insertstring" } }); e = this.applyComposition.bind(this, e, t); this.selection.rangeCount ? this.forEachSelection(e) : e(), this.endOperation() }, this.applyComposition = function (e, t) { var i; (t.extendLeft || t.extendRight) && ((i = this.selection.getRange()).start.column -= t.extendLeft, i.end.column += t.extendRight, i.start.column < 0 && (i.start.row--, i.start.column += this.session.getLine(i.start.row).length + 1), this.selection.setRange(i), e || i.isEmpty() || this.remove()), !e && this.selection.isEmpty() || this.insert(e, !0), (t.restoreStart || t.restoreEnd) && ((i = this.selection.getRange()).start.column -= t.restoreStart, i.end.column -= t.restoreEnd, this.selection.setRange(i)) }, this.onCommandKey = function (e, t, i) { return this.keyBinding.onCommandKey(e, t, i) }, this.setOverwrite = function (e) { this.session.setOverwrite(e) }, this.getOverwrite = function () { return this.session.getOverwrite() }, this.toggleOverwrite = function () { this.session.toggleOverwrite() }, this.setScrollSpeed = function (e) { this.setOption("scrollSpeed", e) }, this.getScrollSpeed = function () { return this.getOption("scrollSpeed") }, this.setDragDelay = function (e) { this.setOption("dragDelay", e) }, this.getDragDelay = function () { return this.getOption("dragDelay") }, this.setSelectionStyle = function (e) { this.setOption("selectionStyle", e) }, this.getSelectionStyle = function () { return this.getOption("selectionStyle") }, this.setHighlightActiveLine = function (e) { this.setOption("highlightActiveLine", e) }, this.getHighlightActiveLine = function () { return this.getOption("highlightActiveLine") }, this.setHighlightGutterLine = function (e) { this.setOption("highlightGutterLine", e) }, this.getHighlightGutterLine = function () { return this.getOption("highlightGutterLine") }, this.setHighlightSelectedWord = function (e) { this.setOption("highlightSelectedWord", e) }, this.getHighlightSelectedWord = function () { return this.$highlightSelectedWord }, this.setAnimatedScroll = function (e) { this.renderer.setAnimatedScroll(e) }, this.getAnimatedScroll = function () { return this.renderer.getAnimatedScroll() }, this.setShowInvisibles = function (e) { this.renderer.setShowInvisibles(e) }, this.getShowInvisibles = function () { return this.renderer.getShowInvisibles() }, this.setDisplayIndentGuides = function (e) { this.renderer.setDisplayIndentGuides(e) }, this.getDisplayIndentGuides = function () { return this.renderer.getDisplayIndentGuides() }, this.setHighlightIndentGuides = function (e) { this.renderer.setHighlightIndentGuides(e) }, this.getHighlightIndentGuides = function () { return this.renderer.getHighlightIndentGuides() }, this.setShowPrintMargin = function (e) { this.renderer.setShowPrintMargin(e) }, this.getShowPrintMargin = function () { return this.renderer.getShowPrintMargin() }, this.setPrintMarginColumn = function (e) { this.renderer.setPrintMarginColumn(e) }, this.getPrintMarginColumn = function () { return this.renderer.getPrintMarginColumn() }, this.setReadOnly = function (e) { this.setOption("readOnly", e) }, this.getReadOnly = function () { return this.getOption("readOnly") }, this.setBehavioursEnabled = function (e) { this.setOption("behavioursEnabled", e) }, this.getBehavioursEnabled = function () { return this.getOption("behavioursEnabled") }, this.setWrapBehavioursEnabled = function (e) { this.setOption("wrapBehavioursEnabled", e) }, this.getWrapBehavioursEnabled = function () { return this.getOption("wrapBehavioursEnabled") }, this.setShowFoldWidgets = function (e) { this.setOption("showFoldWidgets", e) }, this.getShowFoldWidgets = function () { return this.getOption("showFoldWidgets") }, this.setFadeFoldWidgets = function (e) { this.setOption("fadeFoldWidgets", e) }, this.getFadeFoldWidgets = function () { return this.getOption("fadeFoldWidgets") }, this.remove = function (e) { this.selection.isEmpty() && ("left" == e ? this.selection.selectLeft() : this.selection.selectRight()); var t, i, n, e = this.getSelectionRange(); this.getBehavioursEnabled() && (i = (t = this.session).getState(e.start.row), i = t.getMode().transformAction(i, "deletion", this, t, e), 0 === e.end.column && "\n" == (n = t.getTextRange(e))[n.length - 1] && (n = t.getLine(e.end.row), /^\s+$/.test(n) && (e.end.column = n.length)), i && (e = i)), this.session.remove(e), this.clearSelection() }, this.removeWordRight = function () { this.selection.isEmpty() && this.selection.selectWordRight(), this.session.remove(this.getSelectionRange()), this.clearSelection() }, this.removeWordLeft = function () { this.selection.isEmpty() && this.selection.selectWordLeft(), this.session.remove(this.getSelectionRange()), this.clearSelection() }, this.removeToLineStart = function () { this.selection.isEmpty() && this.selection.selectLineStart(), this.selection.isEmpty() && this.selection.selectLeft(), this.session.remove(this.getSelectionRange()), this.clearSelection() }, this.removeToLineEnd = function () { this.selection.isEmpty() && this.selection.selectLineEnd(); var e = this.getSelectionRange(); e.start.column == e.end.column && e.start.row == e.end.row && (e.end.column = 0, e.end.row++), this.session.remove(e), this.clearSelection() }, this.splitLine = function () { this.selection.isEmpty() || (this.session.remove(this.getSelectionRange()), this.clearSelection()); var e = this.getCursorPosition(); this.insert("\n"), this.moveCursorToPosition(e) }, this.transposeLetters = function () { var e, t, i, n; !this.selection.isEmpty() || 0 !== (t = (e = this.getCursorPosition()).column) && (n = t < (n = this.session.getLine(e.row)).length ? (i = n.charAt(t) + n.charAt(t - 1), new p(e.row, t - 1, e.row, t + 1)) : (i = n.charAt(t - 1) + n.charAt(t - 2), new p(e.row, t - 2, e.row, t)), this.session.replace(n, i), this.session.selection.moveToPosition(n.end)) }, this.toLowerCase = function () { var e = this.getSelectionRange(), t = (this.selection.isEmpty() && this.selection.selectWord(), this.getSelectionRange()), i = this.session.getTextRange(t); this.session.replace(t, i.toLowerCase()), this.selection.setSelectionRange(e) }, this.toUpperCase = function () { var e = this.getSelectionRange(), t = (this.selection.isEmpty() && this.selection.selectWord(), this.getSelectionRange()), i = this.session.getTextRange(t); this.session.replace(t, i.toUpperCase()), this.selection.setSelectionRange(e) }, this.indent = function () { var e = this.session, t = this.getSelectionRange(); if (t.start.row < t.end.row) return s = this.$getSelectedRows(), void e.indentRows(s.first, s.last, "\t"); if (t.start.column < t.end.column) { var i = e.getTextRange(t); if (!/^\s+$/.test(i)) return s = this.$getSelectedRows(), void e.indentRows(s.first, s.last, "\t") } var n = e.getLine(t.start.row), i = t.start, s = e.getTabSize(), e = e.documentToScreenColumn(i.row, i.column); if (this.session.getUseSoftTabs()) var o = s - e % s, r = m.stringRepeat(" ", o); else { for (o = e % s; " " == n[t.start.column - 1] && o;)t.start.column--, o--; this.selection.setSelectionRange(t), r = "\t" } return this.insert(r) }, this.blockIndent = function () { var e = this.$getSelectedRows(); this.session.indentRows(e.first, e.last, "\t") }, this.blockOutdent = function () { var e = this.session.getSelection(); this.session.outdentRows(e.getRange()) }, this.sortLines = function () { for (var e = this.$getSelectedRows(), t = this.session, i = [], n = e.first; n <= e.last; n++)i.push(t.getLine(n)); i.sort(function (e, t) { return e.toLowerCase() < t.toLowerCase() ? -1 : e.toLowerCase() > t.toLowerCase() ? 1 : 0 }); for (var s = new p(0, 0, 0, 0), n = e.first; n <= e.last; n++) { var o = t.getLine(n); s.start.row = n, s.end.row = n, s.end.column = o.length, t.replace(s, i[n - e.first]) } }, this.toggleCommentLines = function () { var e = this.session.getState(this.getCursorPosition().row), t = this.$getSelectedRows(); this.session.getMode().toggleCommentLines(e, this.session, t.first, t.last) }, this.toggleBlockComment = function () { var e = this.getCursorPosition(), t = this.session.getState(e.row), i = this.getSelectionRange(); this.session.getMode().toggleBlockComment(t, this.session, i, e) }, this.getNumberAt = function (e, t) { for (var i = /[\-]?[0-9]+(?:\.[0-9]+)?/g, n = (i.lastIndex = 0, this.session.getLine(e)); i.lastIndex < t;) { var s = i.exec(n); if (s.index <= t && s.index + s[0].length >= t) return { value: s[0], start: s.index, end: s.index + s[0].length } } return null }, this.modifyNumber = function (e) { var t, i, n, s = this.selection.getCursor().row, o = this.selection.getCursor().column, r = new p(s, o - 1, s, o), r = this.session.getTextRange(r); !isNaN(parseFloat(r)) && isFinite(r) ? (r = this.getNumberAt(s, o)) && (n = 0 <= r.value.indexOf(".") ? r.start + r.value.indexOf(".") + 1 : r.end, t = r.start + r.value.length - n, i = parseFloat(r.value), i *= Math.pow(10, t), n !== r.end && o < n ? e *= Math.pow(10, r.end - o - 1) : e *= Math.pow(10, r.end - o), n = (i = (i += e) / Math.pow(10, t)).toFixed(t), e = new p(s, r.start, s, r.end), this.session.replace(e, n), this.moveCursorTo(s, Math.max(r.start + 1, o + n.length - r.value.length))) : this.toggleWord() }, this.$toggleWordPairs = [["first", "last"], ["true", "false"], ["yes", "no"], ["width", "height"], ["top", "bottom"], ["right", "left"], ["on", "off"], ["x", "y"], ["get", "set"], ["max", "min"], ["horizontal", "vertical"], ["show", "hide"], ["add", "remove"], ["up", "down"], ["before", "after"], ["even", "odd"], ["in", "out"], ["inside", "outside"], ["next", "previous"], ["increase", "decrease"], ["attach", "detach"], ["&&", "||"], ["==", "!="]], this.toggleWord = function () { for (var i, n = this.selection.getCursor().row, e = this.selection.getCursor().column, s = (this.selection.selectWord(), this.getSelectedText()), o = this.selection.getWordRange().start.column, t = s.replace(/([a-z]+|[A-Z]+)(?=[A-Z_]|$)/g, "$1 ").split(/\s/), r = e - o - 1, a = (r < 0 && (r = 0), 0), l = this, h = (s.match(/[A-Za-z0-9_]+/) && t.forEach(function (e, t) { i = a + e.length, a <= r && r <= i && (s = e, l.selection.clearSelection(), l.moveCursorTo(n, a + o), l.selection.selectTo(n, i + o)), a = i }), this.$toggleWordPairs), c = 0; c < h.length; c++)for (var u = h[c], d = 0; d <= 1; d++) { var g = +!d, f = s.match(new RegExp("^\\s?_?(" + m.escapeRegExp(u[d]) + ")\\s?$", "i")); f && s.match(new RegExp("([_]|^|\\s)(" + m.escapeRegExp(f[1]) + ")($|\\s)", "g")) && (f = s.replace(new RegExp(m.escapeRegExp(u[d]), "i"), function (e) { var t = u[g]; return e.toUpperCase() == e ? t = t.toUpperCase() : e.charAt(0).toUpperCase() == e.charAt(0) && (t = t.substr(0, 0) + u[g].charAt(0).toUpperCase() + t.substr(1)), t }), this.insert(f)) } }, this.findLinkAt = function (e, t) { var i, n, s, e = this.session.getLine(e).split(/((?:https?|ftp):\/\/[\S]+)/), o = t, r = (o < 0 && (o = 0), 0); try { for (var a = u(e), l = a.next(); !l.done; l = a.next()) { var h = l.value, c = r + h.length; if (r <= o && o <= c && h.match(/((?:https?|ftp):\/\/[\S]+)/)) { s = h.replace(/[\s:.,'";}\]]+$/, ""); break } r = c } } catch (e) { i = { error: e } } finally { try { l && !l.done && (n = a.return) && n.call(a) } finally { if (i) throw i.error } } return s }, this.openLink = function () { var e = this.selection.getCursor(), e = this.findLinkAt(e.row, e.column); return e && window.open(e, "_blank"), null != e }, this.removeLines = function () { var e = this.$getSelectedRows(); this.session.removeFullLines(e.first, e.last), this.clearSelection() }, this.duplicateSelection = function () { var e, t = this.selection, i = this.session, n = t.getRange(), s = t.isBackwards(); n.isEmpty() ? (e = n.start.row, i.duplicateLines(e, e)) : (e = s ? n.start : n.end, i = i.insert(e, i.getTextRange(n), !1), n.start = e, n.end = i, t.setSelectionRange(n, s)) }, this.moveLinesDown = function () { this.$moveLines(1, !1) }, this.moveLinesUp = function () { this.$moveLines(-1, !1) }, this.moveText = function (e, t, i) { return this.session.moveText(e, t, i) }, this.copyLinesUp = function () { this.$moveLines(-1, !0) }, this.copyLinesDown = function () { this.$moveLines(1, !0) }, this.$moveLines = function (e, t) { var i = this.selection; if (!i.inMultiSelectMode || this.inVirtualSelectionMode) { var n = i.toOrientedRange(), s = this.$getSelectedRows(n), o = this.session.$moveLines(s.first, s.last, t ? 0 : e); n.moveBy(o = t && -1 == e ? 0 : o, 0), i.fromOrientedRange(n) } else { for (var r = i.rangeList.ranges, a = (i.rangeList.detach(this.session), this.inVirtualSelectionMode = !0, 0), l = 0, h = r.length, c = 0; c < h; c++) { for (var u = c, d = (r[c].moveBy(a, 0), (s = this.$getSelectedRows(r[c])).first), g = s.last; ++c < h;) { l && r[c].moveBy(l, 0); var f = this.$getSelectedRows(r[c]); if (t && f.first != g) break; if (!t && f.first > g + 1) break; g = f.last } for (c--, a = this.session.$moveLines(d, g, t ? 0 : e), t && -1 == e && (u = c + 1); u <= c;)r[u].moveBy(a, 0), u++; l += a = t ? a : 0 } i.fromOrientedRange(i.ranges[0]), i.rangeList.attach(this.session), this.inVirtualSelectionMode = !1 } }, this.$getSelectedRows = function (e) { return e = (e || this.getSelectionRange()).collapseRows(), { first: this.session.getRowFoldStart(e.start.row), last: this.session.getRowFoldEnd(e.end.row) } }, this.onCompositionStart = function (e) { this.renderer.showComposition(e) }, this.onCompositionUpdate = function (e) { this.renderer.setCompositionText(e) }, this.onCompositionEnd = function () { this.renderer.hideComposition() }, this.getFirstVisibleRow = function () { return this.renderer.getFirstVisibleRow() }, this.getLastVisibleRow = function () { return this.renderer.getLastVisibleRow() }, this.isRowVisible = function (e) { return e >= this.getFirstVisibleRow() && e <= this.getLastVisibleRow() }, this.isRowFullyVisible = function (e) { return e >= this.renderer.getFirstFullyVisibleRow() && e <= this.renderer.getLastFullyVisibleRow() }, this.$getVisibleRowCount = function () { return this.renderer.getScrollBottomRow() - this.renderer.getScrollTopRow() + 1 }, this.$moveByPage = function (e, t) { var i = this.renderer, n = this.renderer.layerConfig, s = e * Math.floor(n.height / n.lineHeight), e = (!0 === t ? this.selection.$moveSelection(function () { this.moveCursorBy(s, 0) }) : !1 === t && (this.selection.moveCursorBy(s, 0), this.selection.clearSelection()), i.scrollTop); i.scrollBy(0, s * n.lineHeight), null != t && i.scrollCursorIntoView(null, .5), i.animateScrolling(e) }, this.selectPageDown = function () { this.$moveByPage(1, !0) }, this.selectPageUp = function () { this.$moveByPage(-1, !0) }, this.gotoPageDown = function () { this.$moveByPage(1, !1) }, this.gotoPageUp = function () { this.$moveByPage(-1, !1) }, this.scrollPageDown = function () { this.$moveByPage(1) }, this.scrollPageUp = function () { this.$moveByPage(-1) }, this.scrollToRow = function (e) { this.renderer.scrollToRow(e) }, this.scrollToLine = function (e, t, i, n) { this.renderer.scrollToLine(e, t, i, n) }, this.centerSelection = function () { var e = this.getSelectionRange(), e = { row: Math.floor(e.start.row + (e.end.row - e.start.row) / 2), column: Math.floor(e.start.column + (e.end.column - e.start.column) / 2) }; this.renderer.alignCursor(e, .5) }, this.getCursorPosition = function () { return this.selection.getCursor() }, this.getCursorPositionScreen = function () { return this.session.documentToScreenPosition(this.getCursorPosition()) }, this.getSelectionRange = function () { return this.selection.getRange() }, this.selectAll = function () { this.selection.selectAll() }, this.clearSelection = function () { this.selection.clearSelection() }, this.moveCursorTo = function (e, t) { this.selection.moveCursorTo(e, t) }, this.moveCursorToPosition = function (e) { this.selection.moveCursorToPosition(e) }, this.jumpToMatching = function (e, t) { var i = this.getCursorPosition(), n = new $(this.session, i.row, i.column), s = n.getCurrentToken(), o = 0, r = (s = s && -1 !== s.type.indexOf("tag-name") ? n.stepBackward() : s) || n.stepForward(); if (r) { var a, l, h, c, u, d = !1, g = {}, f = i.column - r.start, m = { ")": "(", "(": "(", "]": "[", "[": "[", "{": "{", "}": "{" }; do { if (r.value.match(/[{}()\[\]]/g)) { for (; f < r.value.length && !d; f++)if (m[r.value[f]]) switch (l = m[r.value[f]] + "." + r.type.replace("rparen", "lparen"), isNaN(g[l]) && (g[l] = 0), r.value[f]) { case "(": case "[": case "{": g[l]++; break; case ")": case "]": case "}": g[l]--, -1 === g[l] && (a = "bracket", d = !0) } } else -1 !== r.type.indexOf("tag-name") && (isNaN(g[r.value]) && (g[r.value] = 0), "<" === s.value && 1 < o ? g[r.value]++ : "</" === s.value && g[r.value]--, -1 === g[r.value] && (a = "tag", d = !0)) } while (d || (s = r, o++, r = n.stepForward(), f = 0), r && !d); if (a) { if ("bracket" === a) (u = this.session.getBracketRange(i)) || (h = (u = new p(n.getCurrentTokenRow(), n.getCurrentTokenColumn() + f - 1, n.getCurrentTokenRow(), n.getCurrentTokenColumn() + f - 1)).start, (t || h.row === i.row && Math.abs(h.column - i.column) < 2) && (u = this.session.getBracketRange(h))); else if ("tag" === a) { if (!r || -1 === r.type.indexOf("tag-name")) return; 0 !== (u = new p(n.getCurrentTokenRow(), n.getCurrentTokenColumn() - 2, n.getCurrentTokenRow(), n.getCurrentTokenColumn() - 2)).compare(i.row, i.column) || (c = this.session.getMatchingTags(i)) && (h = c.openTag.contains(i.row, i.column) ? (u = c.closeTag).start : (u = c.openTag, c.closeTag.start.row === i.row && c.closeTag.start.column === i.column ? u.end : u.start)), h = h || u.start } (h = u && u.cursor || h) && (e ? u && t ? this.selection.setRange(u) : u && u.isEqual(this.getSelectionRange()) ? this.clearSelection() : this.selection.selectTo(h.row, h.column) : this.selection.moveTo(h.row, h.column)) } } }, this.gotoLine = function (e, t, i) { this.selection.clearSelection(), this.session.unfold({ row: e - 1, column: t || 0 }), this.exitMultiSelectMode && this.exitMultiSelectMode(), this.moveCursorTo(e - 1, t || 0), this.isRowFullyVisible(e - 1) || this.scrollToLine(e - 1, !0, i) }, this.navigateTo = function (e, t) { this.selection.moveTo(e, t) }, this.navigateUp = function (e) { var t; if (this.selection.isMultiLine() && !this.selection.isBackwards()) return t = this.selection.anchor.getPosition(), this.moveCursorToPosition(t); this.selection.clearSelection(), this.selection.moveCursorBy(-e || -1, 0) }, this.navigateDown = function (e) { var t; if (this.selection.isMultiLine() && this.selection.isBackwards()) return t = this.selection.anchor.getPosition(), this.moveCursorToPosition(t); this.selection.clearSelection(), this.selection.moveCursorBy(e || 1, 0) }, this.navigateLeft = function (e) { if (this.selection.isEmpty()) for (e = e || 1; e--;)this.selection.moveCursorLeft(); else { var t = this.getSelectionRange().start; this.moveCursorToPosition(t) } this.clearSelection() }, this.navigateRight = function (e) { if (this.selection.isEmpty()) for (e = e || 1; e--;)this.selection.moveCursorRight(); else { var t = this.getSelectionRange().end; this.moveCursorToPosition(t) } this.clearSelection() }, this.navigateLineStart = function () { this.selection.moveCursorLineStart(), this.clearSelection() }, this.navigateLineEnd = function () { this.selection.moveCursorLineEnd(), this.clearSelection() }, this.navigateFileEnd = function () { this.selection.moveCursorFileEnd(), this.clearSelection() }, this.navigateFileStart = function () { this.selection.moveCursorFileStart(), this.clearSelection() }, this.navigateWordRight = function () { this.selection.moveCursorWordRight(), this.clearSelection() }, this.navigateWordLeft = function () { this.selection.moveCursorWordLeft(), this.clearSelection() }, this.replace = function (e, t) { t && this.$search.set(t); var t = this.$search.find(this.session), i = 0; return t && (this.$tryReplace(t, e) && (i = 1), this.selection.setSelectionRange(t), this.renderer.scrollSelectionIntoView(t.start, t.end)), i }, this.replaceAll = function (e, t) { t && this.$search.set(t); var i = this.$search.findAll(this.session), n = 0; if (!i.length) return n; t = this.getSelectionRange(); this.selection.moveTo(0, 0); for (var s = i.length - 1; 0 <= s; --s)this.$tryReplace(i[s], e) && n++; return this.selection.setSelectionRange(t), n }, this.$tryReplace = function (e, t) { var i = this.session.getTextRange(e); return null !== (t = this.$search.replace(i, t)) ? (e.end = this.session.replace(e, t), e) : null }, this.getLastSearchOptions = function () { return this.$search.getOptions() }, this.find = function (e, t, i) { t = t || {}, "string" == typeof e || e instanceof RegExp ? t.needle = e : "object" == typeof e && s.mixin(t, e); var n = this.selection.getRange(), e = (null == t.needle && ((e = this.session.getTextRange(n) || this.$search.$options.needle) || (n = this.session.getWordRange(n.start.row, n.start.column), e = this.session.getTextRange(n)), this.$search.set({ needle: e })), this.$search.set(t), t.start || this.$search.set({ start: n }), this.$search.find(this.session)); return t.preventScroll ? e : e ? (this.revealRange(e, i), e) : (t.backwards ? n.start = n.end : n.end = n.start, void this.selection.setRange(n)) }, this.findNext = function (e, t) { this.find({ skipCurrent: !0, backwards: !1 }, e, t) }, this.findPrevious = function (e, t) { this.find(e, { skipCurrent: !0, backwards: !0 }, t) }, this.revealRange = function (e, t) { this.session.unfold(e), this.selection.setSelectionRange(e); var i = this.renderer.scrollTop; this.renderer.scrollSelectionIntoView(e.start, e.end, .5), !1 !== t && this.renderer.animateScrolling(i) }, this.undo = function () { this.session.getUndoManager().undo(this.session), this.renderer.scrollCursorIntoView(null, .5) }, this.redo = function () { this.session.getUndoManager().redo(this.session), this.renderer.scrollCursorIntoView(null, .5) }, this.destroy = function () { this.$toDestroy && (this.$toDestroy.forEach(function (e) { e.destroy() }), this.$toDestroy = null), this.$mouseHandler && this.$mouseHandler.destroy(), this.renderer.destroy(), this._signal("destroy", this), this.session && this.session.destroy(), this._$emitInputEvent && this._$emitInputEvent.cancel(), this.removeAllListeners() }, this.setAutoScrollEditorIntoView = function (e) { var n, s, o, r, t, i, a; e && (o = !1, (s = this).$scrollAnchor || (this.$scrollAnchor = document.createElement("div")), (r = this.$scrollAnchor).style.cssText = "position:absolute", this.container.insertBefore(r, this.container.firstChild), t = this.on("changeSelection", function () { o = !0 }), i = this.renderer.on("beforeRender", function () { o && (n = s.renderer.container.getBoundingClientRect()) }), a = this.renderer.on("afterRender", function () { var e, t, i; o && n && (s.isFocused() || s.searchBox && s.searchBox.isFocused()) && (e = (t = s.renderer).$cursorLayer.$pixelPos, t = t.layerConfig, i = e.top - t.offset, null != (o = 0 <= e.top && i + n.top < 0 || !(e.top < t.height && e.top + n.top + t.lineHeight > window.innerHeight) && null) && (r.style.top = i + "px", r.style.left = e.left + "px", r.style.height = t.lineHeight + "px", r.scrollIntoView(o)), o = n = null) }), this.setAutoScrollEditorIntoView = function (e) { e || (delete this.setAutoScrollEditorIntoView, this.off("changeSelection", t), this.renderer.off("afterRender", a), this.renderer.off("beforeRender", i)) }) }, this.$resetCursorStyle = function () { var e = this.$cursorStyle || "ace", t = this.renderer.$cursorLayer; t && (t.setSmoothBlinking(/smooth/.test(e)), t.isBlinking = !this.$readOnly && "wide" != e, n.setCssClass(t.element, "ace_slim-cursors", /slim/.test(e))) }, this.prompt = function (t, i, n) { var s = this; v.loadModule("./ext/prompt", function (e) { e.prompt(s, t, i, n) }) } }.call(y.prototype), v.defineOptions(y.prototype, "editor", { selectionStyle: { set: function (e) { this.onSelectionChange(), this._signal("changeSelectionStyle", { data: e }) }, initialValue: "line" }, highlightActiveLine: { set: function () { this.$updateHighlightActiveLine() }, initialValue: !0 }, highlightSelectedWord: { set: function (e) { this.$onSelectionChange() }, initialValue: !0 }, readOnly: { set: function (e) { this.textInput.setReadOnly(e), this.$resetCursorStyle() }, initialValue: !1 }, copyWithEmptySelection: { set: function (e) { this.textInput.setCopyWithEmptySelection(e) }, initialValue: !1 }, cursorStyle: { set: function (e) { this.$resetCursorStyle() }, values: ["ace", "slim", "smooth", "wide"], initialValue: "ace" }, mergeUndoDeltas: { values: [!1, !0, "always"], initialValue: !0 }, behavioursEnabled: { initialValue: !0 }, wrapBehavioursEnabled: { initialValue: !0 }, enableAutoIndent: { initialValue: !0 }, autoScrollEditorIntoView: { set: function (e) { this.setAutoScrollEditorIntoView(e) } }, keyboardHandler: { set: function (e) { this.setKeyboardHandler(e) }, get: function () { return this.$keybindingId }, handlesSet: !0 }, value: { set: function (e) { this.session.setValue(e) }, get: function () { return this.getValue() }, handlesSet: !0, hidden: !0 }, session: { set: function (e) { this.setSession(e) }, get: function () { return this.session }, handlesSet: !0, hidden: !0 }, showLineNumbers: { set: function (e) { this.renderer.$gutterLayer.setShowLineNumbers(e), this.renderer.$loop.schedule(this.renderer.CHANGE_GUTTER), e && this.$relativeLineNumbers ? C.attach(this) : C.detach(this) }, initialValue: !0 }, relativeLineNumbers: { set: function (e) { this.$showLineNumbers && e ? C.attach(this) : C.detach(this) } }, placeholder: { set: function (e) { this.$updatePlaceholder || (this.$updatePlaceholder = function () { var e = this.session && (this.renderer.$composition || this.getValue()); e && this.renderer.placeholderNode ? (this.renderer.off("afterRender", this.$updatePlaceholder), n.removeCssClass(this.container, "ace_hasPlaceholder"), this.renderer.placeholderNode.remove(), this.renderer.placeholderNode = null) : e || this.renderer.placeholderNode ? !e && this.renderer.placeholderNode && (this.renderer.placeholderNode.textContent = this.$placeholder || "") : (this.renderer.on("afterRender", this.$updatePlaceholder), n.addCssClass(this.container, "ace_hasPlaceholder"), (e = n.createElement("div")).className = "ace_placeholder", e.textContent = this.$placeholder || "", this.renderer.placeholderNode = e, this.renderer.content.appendChild(this.renderer.placeholderNode)) }.bind(this), this.on("input", this.$updatePlaceholder)), this.$updatePlaceholder() } }, customScrollbar: "renderer", hScrollBarAlwaysVisible: "renderer", vScrollBarAlwaysVisible: "renderer", highlightGutterLine: "renderer", animatedScroll: "renderer", showInvisibles: "renderer", showPrintMargin: "renderer", printMarginColumn: "renderer", printMargin: "renderer", fadeFoldWidgets: "renderer", showFoldWidgets: "renderer", displayIndentGuides: "renderer", highlightIndentGuides: "renderer", showGutter: "renderer", fontSize: "renderer", fontFamily: "renderer", maxLines: "renderer", minLines: "renderer", scrollPastEnd: "renderer", fixedWidthGutter: "renderer", theme: "renderer", hasCssTransforms: "renderer", maxPixelHeight: "renderer", useTextareaForIME: "renderer", scrollSpeed: "$mouseHandler", dragDelay: "$mouseHandler", dragEnabled: "$mouseHandler", focusTimeout: "$mouseHandler", tooltipFollowsMouse: "$mouseHandler", firstLineNumber: "session", overwrite: "session", newLineMode: "session", useWorker: "session", useSoftTabs: "session", navigateWithinSoftTabs: "session", tabSize: "session", wrap: "session", indentedSoftWrap: "session", foldStyle: "session", mode: "session" }), { getText: function (e, t) { return (Math.abs(e.selection.lead.row - t) || t + 1 + (t < 9 ? "·" : "")) + "" }, getWidth: function (e, t, i) { return Math.max(t.toString().length, (i.lastRow + 1).toString().length, 2) * i.characterWidth }, update: function (e, t) { t.renderer.$loop.schedule(t.renderer.CHANGE_GUTTER) }, attach: function (e) { e.renderer.$gutterLayer.$renderer = this, e.on("changeSelection", this.update), this.update(null, e) }, detach: function (e) { e.renderer.$gutterLayer.$renderer == this && (e.renderer.$gutterLayer.$renderer = null), e.off("changeSelection", this.update), this.update(null, e) } }); t.Editor = y }), define("ace/undomanager", ["require", "exports", "module", "ace/range"], function (e, t, i) { "use strict"; function s(e, t) { for (var i = t; i--;) { var n = e[i]; if (n && !n[0].ignore) { for (; i < t - 1;) { var s = function (e, t) { for (var i = e.length; i--;)for (var n = 0; n < t.length; n++)if (!r(e[i], t[n])) { for (; i < e.length;) { for (; n--;)r(t[n], e[i]); n = t.length, i++ } return [e, t] } return e.selectionBefore = t.selectionBefore = e.selectionAfter = t.selectionAfter = null, [t, e] }(e[i], e[i + 1]); e[i] = s[0], e[i + 1] = s[1], i++ } return 1 } } } function a(e) { return { row: e.row, column: e.column } } function n(e) { if (e = e || this, Array.isArray(e)) return e.map(n).join("\n"); var t = ""; return e.action ? (t = "insert" == e.action ? "+" : "-", t += "[" + e.lines + "]") : e.value && (t = Array.isArray(e.value) ? e.value.map(o).join("\n") : o(e.value)), e.start && (t += o(e)), (e.id || e.rev) && (t += "\t(" + (e.id || e.rev) + ")"), t } function o(e) { return e.start.row + ":" + e.start.column + "=>" + e.end.row + ":" + e.end.column } function r(e, t) { var i = "insert" == e.action, n = "insert" == t.action; if (i && n) if (0 <= f(t.start, e.end)) l(t, e, -1); else { if (!(f(t.start, e.start) <= 0)) return; l(e, t, 1) } else if (i && !n) if (0 <= f(t.start, e.end)) l(t, e, -1); else { if (!(f(t.end, e.start) <= 0)) return; l(e, t, -1) } else if (!i && n) if (0 <= f(t.start, e.start)) l(t, e, 1); else { if (!(f(t.start, e.start) <= 0)) return; l(e, t, 1) } else if (!i && !n) if (0 <= f(t.start, e.start)) l(t, e, 1); else { if (!(f(t.end, e.start) <= 0)) return; l(e, t, -1) } return 1 } function l(e, t, i) { h(e.start, t.start, t.end, i), h(e.end, t.start, t.end, i) } function h(e, t, i, n) { e.row == (1 == n ? t : i).row && (e.column += n * (i.column - t.column)), e.row += n * (i.row - t.row) } function c(e, t) { var i = e.lines, n = e.end, s = (e.end = a(t), e.end.row - e.start.row), o = i.splice(s, i.length), s = s ? t.column : t.column - e.start.column; return i.push(o[0].substring(0, s)), o[0] = o[0].substr(s), { start: a(t), end: n, lines: o, action: e.action } } function u(e, t) { var i; t = { start: a((i = t).start), end: a(i.end), action: i.action, lines: i.lines.slice() }; for (var n = e.length; n--;) { for (var s = e[n], o = 0; o < s.length; o++) { var r = function (e, t) { var i, n, s = "insert" == e.action, o = "insert" == t.action; if (s && o) f(e.start, t.start) < 0 ? l(t, e, 1) : l(e, t, 1); else if (s && !o) 0 <= f(e.start, t.end) ? l(e, t, -1) : (f(e.start, t.start) <= 0 || l(e, g.fromPoints(t.start, e.start), -1), l(t, e, 1)); else if (!s && o) 0 <= f(t.start, e.end) ? l(t, e, -1) : (f(t.start, e.start) <= 0 || l(t, g.fromPoints(e.start, t.start), -1), l(e, t, 1)); else if (!s && !o) if (0 <= f(t.start, e.end)) l(t, e, -1); else { if (!(f(t.end, e.start) <= 0)) return f(e.start, t.start) < 0 && (e = c(i = e, t.start)), 0 < f(e.end, t.end) && (n = c(e, t.end)), h(t.end, e.start, e.end, -1), n && !i && (e.lines = n.lines, e.start = n.start, e.end = n.end, n = e), [t, i, n].filter(Boolean); l(e, t, -1) } return [t, e] }(s[o], t); t = r[0], 2 != r.length && (r[2] ? (s.splice(o + 1, 1, r[1], r[2]), o++) : r[1] || (s.splice(o, 1), o--)) } s.length || e.splice(n, 1) } } function d() { this.$maxRev = 0, this.$fromUndo = !1, this.$undoDepth = 1 / 0, this.reset() } !function () { this.addSession = function (e) { this.$session = e }, this.add = function (e, t, i) { this.$fromUndo || e != this.$lastDelta && (this.$keepRedoStack || (this.$redoStack.length = 0), !1 !== t && this.lastDeltas || (this.lastDeltas = [], (t = this.$undoStack.length) > this.$undoDepth - 1 && this.$undoStack.splice(0, t - this.$undoDepth + 1), this.$undoStack.push(this.lastDeltas), e.id = this.$rev = ++this.$maxRev), "remove" != e.action && "insert" != e.action || (this.$lastDelta = e), this.lastDeltas.push(e)) }, this.addSelection = function (e, t) { this.selections.push({ value: e, rev: t || this.$rev }) }, this.startNewGroup = function () { return this.lastDeltas = null, this.$rev }, this.markIgnored = function (e, t) { null == t && (t = this.$rev + 1); for (var i = this.$undoStack, n = i.length; n--;) { var s = i[n][0]; if (s.id <= e) break; s.id < t && (s.ignore = !0) } this.lastDeltas = null }, this.getSelection = function (e, t) { for (var i = this.selections, n = i.length; n--;) { var s = i[n]; if (s.rev < e) return s = t ? i[n + 1] : s } }, this.getRevision = function () { return this.$rev }, this.getDeltas = function (e, t) { null == t && (t = this.$rev + 1); for (var i = this.$undoStack, n = null, s = 0, o = i.length; o--;) { var r = i[o][0]; if (r.id < t && !n && (n = o + 1), r.id <= e) { s = o + 1; break } } return i.slice(s, n) }, this.getChangedRanges = function (e, t) { null == t && this.$rev }, this.getChangedLines = function (e, t) { null == t && this.$rev }, this.undo = function (e, t) { this.lastDeltas = null; var i, n = this.$undoStack; if (s(n, n.length)) return e = e || this.$session, this.$redoStackBaseRev !== this.$rev && this.$redoStack.length && (this.$redoStack = []), this.$fromUndo = !0, n = n.pop(), i = null, n && (i = e.undoChanges(n, t), this.$redoStack.push(n), this.$syncRev()), this.$fromUndo = !1, i }, this.redo = function (e, t) { if (this.lastDeltas = null, e = e || this.$session, this.$fromUndo = !0, this.$redoStackBaseRev != this.$rev) { for (var i = this.getDeltas(this.$redoStackBaseRev, this.$rev + 1), n = this.$redoStack, s = i, o = 0; o < s.length; o++)for (var r = s[o], a = 0; a < r.length; a++)u(n, r[a]); this.$redoStackBaseRev = this.$rev, this.$redoStack.forEach(function (e) { e[0].id = ++this.$maxRev }, this) } var i = this.$redoStack.pop(), l = null; return i && (l = e.redoChanges(i, t), this.$undoStack.push(i), this.$syncRev()), this.$fromUndo = !1, l }, this.$syncRev = function () { var e = this.$undoStack, e = e[e.length - 1], e = e && e[0].id || 0; this.$redoStackBaseRev = e, this.$rev = e }, this.reset = function () { this.lastDeltas = null, this.$lastDelta = null, this.$undoStack = [], this.$redoStack = [], this.$rev = 0, this.mark = 0, this.$redoStackBaseRev = this.$rev, this.selections = [] }, this.canUndo = function () { return 0 < this.$undoStack.length }, this.canRedo = function () { return 0 < this.$redoStack.length }, this.bookmark = function (e) { null == e && (e = this.$rev), this.mark = e }, this.isAtBookmark = function () { return this.$rev === this.mark }, this.toJSON = function () { }, this.fromJSON = function () { }, this.hasUndo = this.canUndo, this.hasRedo = this.canRedo, this.isClean = this.isAtBookmark, this.markClean = this.bookmark, this.$prettyPrint = function (e) { return e ? n(e) : n(this.$undoStack) + "\n---\n" + n(this.$redoStack) } }.call(d.prototype); var g = e("./range").Range, f = g.comparePoints; g.comparePoints; t.UndoManager = d }), define("ace/layer/lines", ["require", "exports", "module", "ace/lib/dom"], function (e, t, i) { "use strict"; function n(e, t) { this.element = e, this.canvasHeight = t || 5e5, this.element.style.height = 2 * this.canvasHeight + "px", this.cells = [], this.cellCache = [], this.$offsetCoefficient = 0 } var r = e("../lib/dom"); (function () { this.moveContainer = function (e) { r.translate(this.element, 0, -(e.firstRowScreen * e.lineHeight % this.canvasHeight) - e.offset * this.$offsetCoefficient) }, this.pageChanged = function (e, t) { return Math.floor(e.firstRowScreen * e.lineHeight / this.canvasHeight) !== Math.floor(t.firstRowScreen * t.lineHeight / this.canvasHeight) }, this.computeLineTop = function (e, t, i) { var n = t.firstRowScreen * t.lineHeight, n = Math.floor(n / this.canvasHeight); return i.documentToScreenRow(e, 0) * t.lineHeight - n * this.canvasHeight }, this.computeLineHeight = function (e, t, i) { return t.lineHeight * i.getRowLineCount(e) }, this.getLength = function () { return this.cells.length }, this.get = function (e) { return this.cells[e] }, this.shift = function () { this.$cacheCell(this.cells.shift()) }, this.pop = function () { this.$cacheCell(this.cells.pop()) }, this.push = function (e) { if (Array.isArray(e)) { this.cells.push.apply(this.cells, e); for (var t = r.createFragment(this.element), i = 0; i < e.length; i++)t.appendChild(e[i].element); this.element.appendChild(t) } else this.cells.push(e), this.element.appendChild(e.element) }, this.unshift = function (e) { if (Array.isArray(e)) { this.cells.unshift.apply(this.cells, e); for (var t = r.createFragment(this.element), i = 0; i < e.length; i++)t.appendChild(e[i].element); this.element.firstChild ? this.element.insertBefore(t, this.element.firstChild) : this.element.appendChild(t) } else this.cells.unshift(e), this.element.insertAdjacentElement("afterbegin", e.element) }, this.last = function () { return this.cells.length ? this.cells[this.cells.length - 1] : null }, this.$cacheCell = function (e) { e && (e.element.remove(), this.cellCache.push(e)) }, this.createCell = function (e, t, i, n) { var s, o = this.cellCache.pop(); return o || (s = r.createElement("div"), n && n(s), this.element.appendChild(s), o = { element: s, text: "", row: e }), o.row = e, o } }).call(n.prototype), t.Lines = n }), define("ace/layer/gutter", ["require", "exports", "module", "ace/lib/dom", "ace/lib/oop", "ace/lib/lang", "ace/lib/event_emitter", "ace/layer/lines"], function (e, t, i) { "use strict"; function h(e) { var t = document.createTextNode(""), t = (e.appendChild(t), p.createElement("span")); return e.appendChild(t), e } function n(e) { this.element = p.createElement("div"), this.element.className = "ace_layer ace_gutter-layer", e.appendChild(this.element), this.setShowFoldWidgets(this.$showFoldWidgets), this.gutterWidth = 0, this.$annotations = [], this.$updateAnnotations = this.$updateAnnotations.bind(this), this.$lines = new a(this.element), this.$lines.$offsetCoefficient = 1 } var p = e("../lib/dom"), s = e("../lib/oop"), o = e("../lib/lang"), r = e("../lib/event_emitter").EventEmitter, a = e("./lines").Lines; (function () { s.implement(this, r), this.setSession = function (e) { this.session && this.session.off("change", this.$updateAnnotations), (this.session = e) && e.on("change", this.$updateAnnotations) }, this.addGutterDecoration = function (e, t) { window.console && console.warn && console.warn("deprecated use session.addGutterDecoration"), this.session.addGutterDecoration(e, t) }, this.removeGutterDecoration = function (e, t) { window.console && console.warn && console.warn("deprecated use session.removeGutterDecoration"), this.session.removeGutterDecoration(e, t) }, this.setAnnotations = function (e) { this.$annotations = []; for (var t = 0; t < e.length; t++) { var i = e[t], n = i.row, s = (s = this.$annotations[n]) || (this.$annotations[n] = { text: [] }), n = (n = i.text) ? o.escapeHTML(n) : i.html || "", n = (-1 === s.text.indexOf(n) && s.text.push(n), i.type), i = i.className; i ? s.className = i : "error" == n ? s.className = " ace_error" : "warning" == n && " ace_error" != s.className ? s.className = " ace_warning" : "info" != n || s.className || (s.className = " ace_info") } }, this.$updateAnnotations = function (e) { var t, i; this.$annotations.length && (t = e.start.row, 0 != (i = e.end.row - t) && ("remove" == e.action ? this.$annotations.splice(t, 1 + i, null) : ((e = new Array(1 + i)).unshift(t, 1), this.$annotations.splice.apply(this.$annotations, e)))) }, this.update = function (e) { this.config = e; for (var t = this.session, i = e.firstRow, n = Math.min(e.lastRow + e.gutterOffset, t.getLength() - 1), s = (this.oldLastRow = n, this.config = e, this.$lines.moveContainer(e), this.$updateCursorRow(), t.getNextFoldLine(i)), o = s ? s.start.row : 1 / 0, r = null, a = -1, l = i; ;) { if (o < l && (l = s.end.row + 1, o = (s = t.getNextFoldLine(l, s)) ? s.start.row : 1 / 0), n < l) { for (; this.$lines.getLength() > a + 1;)this.$lines.pop(); break } (r = this.$lines.get(++a)) ? r.row = l : (r = this.$lines.createCell(l, e, this.session, h), this.$lines.push(r)), this.$renderCell(r, e, s, l), l++ } this._signal("afterRender"), this.$updateGutterWidth(e) }, this.$updateGutterWidth = function (e) { var t = this.session, i = t.gutterRenderer || this.$renderer, n = t.$firstLineNumber, s = this.$lines.last() ? this.$lines.last().text : "", n = ((this.$fixedWidth || t.$useWrapMode) && (s = t.getLength() + n - 1), i ? i.getWidth(t, s, e) : s.toString().length * e.characterWidth), i = this.$padding || this.$computePadding(); (n += i.left + i.right) === this.gutterWidth || isNaN(n) || (this.gutterWidth = n, this.element.parentNode.style.width = this.element.style.width = Math.ceil(this.gutterWidth) + "px", this._signal("changeGutterWidth", n)) }, this.$updateCursorRow = function () { var e; this.$highlightGutterLine && (e = this.session.selection.getCursor(), this.$cursorRow !== e.row && (this.$cursorRow = e.row)) }, this.updateLineHighlight = function () { if (this.$highlightGutterLine) { var e = this.session.selection.cursor.row; if (this.$cursorRow = e, !this.$cursorCell || this.$cursorCell.row != e) { this.$cursorCell && (this.$cursorCell.element.className = this.$cursorCell.element.className.replace("ace_gutter-active-line ", "")); var t = this.$lines.cells; this.$cursorCell = null; for (var i = 0; i < t.length; i++) { var n = t[i]; if (n.row >= this.$cursorRow) { if (n.row > this.$cursorRow) { var s = this.session.getFoldLine(this.$cursorRow); if (!(0 < i && s && s.start.row == t[i - 1].row)) break; n = t[i - 1] } n.element.className = "ace_gutter-active-line " + n.element.className, this.$cursorCell = n; break } } } } }, this.scrollLines = function (e) { var t = this.config; if (this.config = e, this.$updateCursorRow(), this.$lines.pageChanged(t, e)) return this.update(e); this.$lines.moveContainer(e); var i = Math.min(e.lastRow + e.gutterOffset, this.session.getLength() - 1), n = this.oldLastRow; if (this.oldLastRow = i, !t || n < e.firstRow) return this.update(e); if (i < t.firstRow) return this.update(e); if (t.firstRow < e.firstRow) for (var s = this.session.getFoldedRowCount(t.firstRow, e.firstRow - 1); 0 < s; s--)this.$lines.shift(); if (i < n) for (s = this.session.getFoldedRowCount(i + 1, n); 0 < s; s--)this.$lines.pop(); e.firstRow < t.firstRow && this.$lines.unshift(this.$renderLines(e, e.firstRow, t.firstRow - 1)), n < i && this.$lines.push(this.$renderLines(e, n + 1, i)), this.updateLineHighlight(), this._signal("afterRender"), this.$updateGutterWidth(e) }, this.$renderLines = function (e, t, i) { for (var n = [], s = t, o = this.session.getNextFoldLine(s), r = o ? o.start.row : 1 / 0; r < s && (s = o.end.row + 1, r = (o = this.session.getNextFoldLine(s, o)) ? o.start.row : 1 / 0), !(i < s);) { var a = this.$lines.createCell(s, e, this.session, h); this.$renderCell(a, e, o, s), n.push(a), s++ } return n }, this.$renderCell = function (e, t, i, n) { var s, o = e.element, r = this.session, a = o.childNodes[0], l = o.childNodes[1], h = r.$firstLineNumber, c = r.$breakpoints, u = r.$decorations, d = r.gutterRenderer || this.$renderer, g = this.$showFoldWidgets && r.foldWidgets, f = i ? i.start.row : Number.MAX_VALUE, m = "ace_gutter-cell ", u = (this.$highlightGutterLine && (n == this.$cursorRow || i && n < this.$cursorRow && f <= n && this.$cursorRow <= i.end.row) && (m += "ace_gutter-active-line ", this.$cursorCell != e && (this.$cursorCell && (this.$cursorCell.element.className = this.$cursorCell.element.className.replace("ace_gutter-active-line ", "")), this.$cursorCell = e)), c[n] && (m += c[n]), u[n] && (m += u[n]), this.$annotations[n] && (m += this.$annotations[n].className), o.className != m && (o.className = m), !g || null == (s = g[n]) && (s = g[n] = r.getFoldWidget(n)), s ? (m = "ace_fold-widget ace_" + s, "start" == s && n == f && n < i.end.row ? m += " ace_closed" : m += " ace_open", l.className != m && (l.className = m), c = t.lineHeight + "px", p.setStyle(l.style, "height", c), p.setStyle(l.style, "display", "inline-block")) : l && p.setStyle(l.style, "display", "none"), (d ? d.getText(r, n) : n + h).toString()); return u !== a.data && (a.data = u), p.setStyle(e.element.style, "height", this.$lines.computeLineHeight(n, t, r) + "px"), p.setStyle(e.element.style, "top", this.$lines.computeLineTop(n, t, r) + "px"), e.text = u, e }, this.$fixedWidth = !1, this.$highlightGutterLine = !0, this.$renderer = "", this.setHighlightGutterLine = function (e) { this.$highlightGutterLine = e }, this.$showLineNumbers = !0, this.$renderer = "", this.setShowLineNumbers = function (e) { this.$renderer = !e && { getWidth: function () { return 0 }, getText: function () { return "" } } }, this.getShowLineNumbers = function () { return this.$showLineNumbers }, this.$showFoldWidgets = !0, this.setShowFoldWidgets = function (e) { e ? p.addCssClass(this.element, "ace_folding-enabled") : p.removeCssClass(this.element, "ace_folding-enabled"), this.$showFoldWidgets = e, this.$padding = null }, this.getShowFoldWidgets = function () { return this.$showFoldWidgets }, this.$computePadding = function () { if (!this.element.firstChild) return { left: 0, right: 0 }; var e = p.computedStyle(this.element.firstChild); return this.$padding = {}, this.$padding.left = (parseInt(e.borderLeftWidth) || 0) + (parseInt(e.paddingLeft) || 0) + 1, this.$padding.right = (parseInt(e.borderRightWidth) || 0) + (parseInt(e.paddingRight) || 0), this.$padding }, this.getRegion = function (e) { var t = this.$padding || this.$computePadding(), i = this.element.getBoundingClientRect(); return e.x < t.left + i.left ? "markers" : this.$showFoldWidgets && e.x > i.right - t.right ? "foldWidgets" : void 0 } }).call(n.prototype), t.Gutter = n }), define("ace/layer/marker", ["require", "exports", "module", "ace/range", "ace/lib/dom"], function (e, t, i) { "use strict"; function n(e) { this.element = s.createElement("div"), this.element.className = "ace_layer ace_marker-layer", e.appendChild(this.element) } var g = e("../range").Range, s = e("../lib/dom"); (function () { this.$padding = 0, this.setPadding = function (e) { this.$padding = e }, this.setSession = function (e) { this.session = e }, this.setMarkers = function (e) { this.markers = e }, this.elt = function (e, t) { var i = -1 != this.i && this.element.childNodes[this.i]; i ? this.i++ : (i = document.createElement("div"), this.element.appendChild(i), this.i = -1), i.style.cssText = t, i.className = e }, this.update = function (e) { if (e) { var t, i; for (i in this.config = e, this.i = 0, this.markers) { var n, s, o, r = this.markers[i]; r.range ? (o = r.range.clipRows(e.firstRow, e.lastRow)).isEmpty() || (o = o.toScreenRange(this.session), r.renderer ? (n = this.$getTop(o.start.row, e), s = this.$padding + o.start.column * e.characterWidth, r.renderer(t, o, s, n, e)) : "fullLine" == r.type ? this.drawFullLineMarker(t, o, r.clazz, e) : "screenLine" == r.type ? this.drawScreenLineMarker(t, o, r.clazz, e) : o.isMultiLine() ? "text" == r.type ? this.drawTextMarker(t, o, r.clazz, e) : this.drawMultiLineMarker(t, o, r.clazz, e) : this.drawSingleLineMarker(t, o, r.clazz + " ace_start ace_br15", e)) : r.update(t, this, this.session, e) } if (-1 != this.i) for (; this.i < this.element.childElementCount;)this.element.removeChild(this.element.lastChild) } }, this.$getTop = function (e, t) { return (e - t.firstRowScreen) * t.lineHeight }, this.drawTextMarker = function (e, t, i, n, s) { for (var o, r = this.session, a = t.start.row, l = t.end.row, h = a, c = 0, u = r.getScreenLastRowColumn(h), d = new g(h, t.start.column, h, c); h <= l; h++)d.start.row = d.end.row = h, d.start.column = h == a ? t.start.column : r.getRowWrapIndent(h), d.end.column = u, o = c, c = u, u = h + 1 < l ? r.getScreenLastRowColumn(h + 1) : h == l ? 0 : t.end.column, this.drawSingleLineMarker(e, d, i + (h == a ? " ace_start" : "") + " ace_br" + ((h == a || h == a + 1 && t.start.column ? 1 : 0) | (o < c ? 2 : 0) | (u < c ? 4 : 0) | (h == l ? 8 : 0)), n, h == l ? 0 : 1, s) }, this.drawMultiLineMarker = function (e, t, i, n, s) { var o, r = this.$padding, a = n.lineHeight, l = this.$getTop(t.start.row, n), h = r + t.start.column * n.characterWidth; s = s || "", this.session.$bidiHandler.isBidiRow(t.start.row) ? ((o = t.clone()).end.row = o.start.row, o.end.column = this.session.getLine(o.start.row).length, this.drawBidiSingleLineMarker(e, o, i + " ace_br1 ace_start", n, null, s)) : this.elt(i + " ace_br1 ace_start", "height:" + a + "px;right:0;top:" + l + "px;left:" + h + "px;" + (s || "")), this.session.$bidiHandler.isBidiRow(t.end.row) ? ((o = t.clone()).start.row = o.end.row, o.start.column = 0, this.drawBidiSingleLineMarker(e, o, i + " ace_br12", n, null, s)) : (l = this.$getTop(t.end.row, n), h = t.end.column * n.characterWidth, this.elt(i + " ace_br12", "height:" + a + "px;width:" + h + "px;top:" + l + "px;left:" + r + "px;" + (s || ""))), (a = (t.end.row - t.start.row - 1) * n.lineHeight) <= 0 || (l = this.$getTop(t.start.row + 1, n), e = (t.start.column ? 1 : 0) | (t.end.column ? 0 : 8), this.elt(i + (e ? " ace_br" + e : ""), "height:" + a + "px;right:0;top:" + l + "px;left:" + r + "px;" + (s || ""))) }, this.drawSingleLineMarker = function (e, t, i, n, s, o) { if (this.session.$bidiHandler.isBidiRow(t.start.row)) return this.drawBidiSingleLineMarker(e, t, i, n, s, o); var e = n.lineHeight, s = (t.end.column + (s || 0) - t.start.column) * n.characterWidth, r = this.$getTop(t.start.row, n), t = this.$padding + t.start.column * n.characterWidth; this.elt(i, "height:" + e + "px;width:" + s + "px;top:" + r + "px;left:" + t + "px;" + (o || "")) }, this.drawBidiSingleLineMarker = function (e, t, i, n, s, o) { var r = n.lineHeight, a = this.$getTop(t.start.row, n), l = this.$padding; this.session.$bidiHandler.getSelections(t.start.column, t.end.column).forEach(function (e) { this.elt(i, "height:" + r + "px;width:" + e.width + (s || 0) + "px;top:" + a + "px;left:" + (l + e.left) + "px;" + (o || "")) }, this) }, this.drawFullLineMarker = function (e, t, i, n, s) { var o = this.$getTop(t.start.row, n), r = n.lineHeight; t.start.row != t.end.row && (r += this.$getTop(t.end.row, n) - o), this.elt(i, "height:" + r + "px;top:" + o + "px;left:0;right:0;" + (s || "")) }, this.drawScreenLineMarker = function (e, t, i, n, s) { t = this.$getTop(t.start.row, n), n = n.lineHeight; this.elt(i, "height:" + n + "px;top:" + t + "px;left:0;right:0;" + (s || "")) } }).call(n.prototype), t.Marker = n }), define("ace/layer/text", ["require", "exports", "module", "ace/lib/oop", "ace/lib/dom", "ace/lib/lang", "ace/layer/lines", "ace/lib/event_emitter"], function (e, t, i) { "use strict"; function n(e) { this.dom = h, this.element = this.dom.createElement("div"), this.element.className = "ace_layer ace_text-layer", e.appendChild(this.element), this.$updateEolChar = this.$updateEolChar.bind(this), this.$lines = new o(this.element) } var s = e("../lib/oop"), h = e("../lib/dom"), w = e("../lib/lang"), o = e("./lines").Lines, r = e("../lib/event_emitter").EventEmitter; (function () { s.implement(this, r), this.EOF_CHAR = "¶", this.EOL_CHAR_LF = "¬", this.EOL_CHAR_CRLF = "¤", this.EOL_CHAR = this.EOL_CHAR_LF, this.TAB_CHAR = "—", this.SPACE_CHAR = "·", this.$padding = 0, this.MAX_LINE_LENGTH = 1e4, this.MAX_CHUNK_LENGTH = 250, this.$updateEolChar = function () { var e = this.session.doc, e = "\n" == e.getNewLineCharacter() && "windows" != e.getNewLineMode() ? this.EOL_CHAR_LF : this.EOL_CHAR_CRLF; if (this.EOL_CHAR != e) return this.EOL_CHAR = e, !0 }, this.setPadding = function (e) { this.$padding = e, this.element.style.margin = "0 " + e + "px" }, this.getLineHeight = function () { return this.$fontMetrics.$characterSize.height || 0 }, this.getCharacterWidth = function () { return this.$fontMetrics.$characterSize.width || 0 }, this.$setFontMetrics = function (e) { this.$fontMetrics = e, this.$fontMetrics.on("changeCharacterSize", function (e) { this._signal("changeCharacterSize", e) }.bind(this)), this.$pollSizeChanges() }, this.checkForSizeChanges = function () { this.$fontMetrics.checkForSizeChanges() }, this.$pollSizeChanges = function () { return this.$pollSizeChangesTimer = this.$fontMetrics.$pollSizeChanges() }, this.setSession = function (e) { (this.session = e) && this.$computeTabString() }, this.showInvisibles = !1, this.showSpaces = !1, this.showTabs = !1, this.showEOL = !1, this.setShowInvisibles = function (e) { return this.showInvisibles != e && ("string" == typeof (this.showInvisibles = e) ? (this.showSpaces = /tab/i.test(e), this.showTabs = /space/i.test(e), this.showEOL = /eol/i.test(e)) : this.showSpaces = this.showTabs = this.showEOL = e, this.$computeTabString(), !0) }, this.displayIndentGuides = !0, this.setDisplayIndentGuides = function (e) { return this.displayIndentGuides != e && (this.displayIndentGuides = e, this.$computeTabString(), !0) }, this.$highlightIndentGuides = !0, this.setHighlightIndentGuides = function (e) { return this.$highlightIndentGuides !== e && (this.$highlightIndentGuides = e) }, this.$tabStrings = [], this.onChangeTabSize = this.$computeTabString = function () { for (var e, t, i, n, s, o, r = this.session.getTabSize(), a = (this.tabSize = r, this.$tabStrings = [0]), l = 1; l < r + 1; l++)this.showTabs ? ((o = this.dom.createElement("span")).className = "ace_invisible ace_invisible_tab", o.textContent = w.stringRepeat(this.TAB_CHAR, l), a.push(o)) : a.push(this.dom.createTextNode(w.stringRepeat(" ", l), this.element)); this.displayIndentGuides && (this.$indentGuideRe = /\s\S| \t|\t |\s$/, e = "ace_indent-guide", t = this.showSpaces ? " ace_invisible ace_invisible_space" : "", i = this.showSpaces ? w.stringRepeat(this.SPACE_CHAR, this.tabSize) : w.stringRepeat(" ", this.tabSize), n = this.showTabs ? " ace_invisible ace_invisible_tab" : "", s = this.showTabs ? w.stringRepeat(this.TAB_CHAR, this.tabSize) : i, (o = this.dom.createElement("span")).className = e + t, o.textContent = i, this.$tabStrings[" "] = o, (o = this.dom.createElement("span")).className = e + n, o.textContent = s, this.$tabStrings["\t"] = o) }, this.updateLines = function (e, t, i) { if (this.config.lastRow != e.lastRow || this.config.firstRow != e.firstRow) return this.update(e); this.config = e; for (var n = Math.max(t, e.firstRow), s = Math.min(i, e.lastRow), o = this.element.childNodes, r = 0, a = e.firstRow; a < n; a++) { if (l = this.session.getFoldLine(a)) { if (l.containsRow(n)) { n = l.start.row; break } a = l.end.row } r++ } for (var l, h = !1, a = n, c = (l = this.session.getNextFoldLine(a)) ? l.start.row : 1 / 0; c < a && (a = l.end.row + 1, c = (l = this.session.getNextFoldLine(a, l)) ? l.start.row : 1 / 0), !(s < a);) { var u, d = o[r++]; d && (this.dom.removeChildren(d), this.$renderLine(d, a, a == c && l), h && (d.style.top = this.$lines.computeLineTop(a, e, this.session) + "px"), u = e.lineHeight * this.session.getRowLength(a) + "px", d.style.height != u && (h = !0, d.style.height = u)), a++ } if (h) for (; r < this.$lines.cells.length;) { var g = this.$lines.cells[r++]; g.element.style.top = this.$lines.computeLineTop(g.row, e, this.session) + "px" } }, this.scrollLines = function (e) { var t = this.config; if (this.config = e, this.$lines.pageChanged(t, e)) return this.update(e); this.$lines.moveContainer(e); var i = e.lastRow, n = t ? t.lastRow : -1; if (!t || n < e.firstRow) return this.update(e); if (i < t.firstRow) return this.update(e); if (!t || t.lastRow < e.firstRow) return this.update(e); if (e.lastRow < t.firstRow) return this.update(e); if (t.firstRow < e.firstRow) for (var s = this.session.getFoldedRowCount(t.firstRow, e.firstRow - 1); 0 < s; s--)this.$lines.shift(); if (t.lastRow > e.lastRow) for (s = this.session.getFoldedRowCount(e.lastRow + 1, t.lastRow); 0 < s; s--)this.$lines.pop(); e.firstRow < t.firstRow && this.$lines.unshift(this.$renderLinesFragment(e, e.firstRow, t.firstRow - 1)), e.lastRow > t.lastRow && this.$lines.push(this.$renderLinesFragment(e, t.lastRow + 1, e.lastRow)), this.$highlightIndentGuide() }, this.$renderLinesFragment = function (e, t, i) { for (var n = [], s = t, o = this.session.getNextFoldLine(s), r = o ? o.start.row : 1 / 0; r < s && (s = o.end.row + 1, r = (o = this.session.getNextFoldLine(s, o)) ? o.start.row : 1 / 0), !(i < s);) { var a = this.$lines.createCell(s, e, this.session), l = a.element; this.dom.removeChildren(l), h.setStyle(l.style, "height", this.$lines.computeLineHeight(s, e, this.session) + "px"), h.setStyle(l.style, "top", this.$lines.computeLineTop(s, e, this.session) + "px"), this.$renderLine(l, s, s == r && o), this.$useLineGroups() ? l.className = "ace_line_group" : l.className = "ace_line", n.push(a), s++ } return n }, this.update = function (e) { this.$lines.moveContainer(e); for (var t = (this.config = e).firstRow, i = e.lastRow, n = this.$lines; n.getLength();)n.pop(); n.push(this.$renderLinesFragment(e, t, i)) }, this.$textToken = { text: !0, rparen: !0, lparen: !0 }, this.$renderTokenInChunks = function (e, t, i, n) { for (var s = 0; s < n.length; s += this.MAX_CHUNK_LENGTH)var o = n.substring(s, s + this.MAX_CHUNK_LENGTH), r = { type: i.type, value: o }, a = this.$renderToken(e, t + s, r, o); return a }, this.$renderToken = function (e, t, i, n) { for (var s, o = this, r = /(\t)|( +)|([\x00-\x1f\x80-\xa0\xad\u1680\u180E\u2000-\u200f\u2028\u2029\u202F\u205F\uFEFF\uFFF9-\uFFFC\u2066\u2067\u2068\u202A\u202B\u202D\u202E\u202C\u2069]+)|(\u3000)|([\u1100-\u115F\u11A3-\u11A7\u11FA-\u11FF\u2329-\u232A\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFB\u3001-\u303E\u3041-\u3096\u3099-\u30FF\u3105-\u312D\u3131-\u318E\u3190-\u31BA\u31C0-\u31E3\u31F0-\u321E\u3220-\u3247\u3250-\u32FE\u3300-\u4DBF\u4E00-\uA48C\uA490-\uA4C6\uA960-\uA97C\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFAFF\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE66\uFE68-\uFE6B\uFF01-\uFF60\uFFE0-\uFFE6]|[\uD800-\uDBFF][\uDC00-\uDFFF])/g, a = this.dom.createFragment(this.element), l = 0; s = r.exec(n);) { var h, c = s[1], u = s[2], d = s[3], g = s[4], f = s[5]; !o.showSpaces && u || (h = l != s.index ? n.slice(l, s.index) : "", l = s.index + s[0].length, h && a.appendChild(this.dom.createTextNode(h, this.element)), c ? (h = o.session.getScreenTabSize(t + s.index), a.appendChild(o.$tabStrings[h].cloneNode(!0)), t += h - 1) : u ? o.showSpaces ? ((p = this.dom.createElement("span")).className = "ace_invisible ace_invisible_space", p.textContent = w.stringRepeat(o.SPACE_CHAR, u.length), a.appendChild(p)) : a.appendChild(this.com.createTextNode(u, this.element)) : d ? ((p = this.dom.createElement("span")).className = "ace_invisible ace_invisible_space ace_invalid", p.textContent = w.stringRepeat(o.SPACE_CHAR, d.length), a.appendChild(p)) : g ? (t += 1, (p = this.dom.createElement("span")).style.width = 2 * o.config.characterWidth + "px", p.className = o.showSpaces ? "ace_cjk ace_invisible ace_invisible_space" : "ace_cjk", p.textContent = o.showSpaces ? o.SPACE_CHAR : g, a.appendChild(p)) : f && (t += 1, (p = this.dom.createElement("span")).style.width = 2 * o.config.characterWidth + "px", p.className = "ace_cjk", p.textContent = f, a.appendChild(p))) } a.appendChild(this.dom.createTextNode(l ? n.slice(l) : n, this.element)); var m, p = this.dom.createElement("span"); return this.$textToken[i.type] || (m = "ace_" + i.type.replace(/\./g, " ace_"), "fold" == i.type && (p.style.width = i.value.length * this.config.characterWidth + "px"), p.className = m), p.appendChild(a), e.appendChild(p), t + n.length }, this.renderIndentGuide = function (e, t, i) { var n = t.search(this.$indentGuideRe); if (n <= 0 || i <= n) return t; if (" " == t[0]) { for (var s = (n -= n % this.tabSize) / this.tabSize, o = 0; o < s; o++)e.appendChild(this.$tabStrings[" "].cloneNode(!0)); return this.$highlightIndentGuide(), t.substr(n) } if ("\t" != t[0]) return this.$highlightIndentGuide(), t; for (o = 0; o < n; o++)e.appendChild(this.$tabStrings["\t"].cloneNode(!0)); return this.$highlightIndentGuide(), t.substr(n) }, this.$highlightIndentGuide = function () { if (this.$highlightIndentGuides && this.displayIndentGuides) { this.$highlightIndentGuideMarker = { indentLevel: void 0, start: void 0, end: void 0, dir: void 0 }; var e = this.session.doc.$lines; if (e) { var t = this.session.selection.getCursor(), i = /^\s*/.exec(this.session.doc.getLine(t.row))[0].length, n = Math.floor(i / this.tabSize); if (this.$highlightIndentGuideMarker = { indentLevel: n, start: t.row }, this.session.$bracketHighlight) for (var s = this.session.$bracketHighlight.ranges, o = 0; o < s.length; o++)if (t.row !== s[o].start.row) { this.$highlightIndentGuideMarker.end = s[o].start.row, t.row > s[o].start.row ? this.$highlightIndentGuideMarker.dir = -1 : this.$highlightIndentGuideMarker.dir = 1; break } if (!this.$highlightIndentGuideMarker.end && "" !== e[t.row] && t.column === e[t.row].length) { this.$highlightIndentGuideMarker.dir = 1; for (o = t.row + 1; o < e.length; o++) { var r = e[o], a = /^\s*/.exec(r)[0].length; if ("" !== r && (this.$highlightIndentGuideMarker.end = o, a <= i)) break } } this.$renderHighlightIndentGuide() } } }, this.$clearActiveIndentGuide = function () { for (var e = this.$lines.cells, t = 0; t < e.length; t++) { var i = e[t].element.childNodes; if (0 < i.length) for (var n = 0; n < i.length; n++)if (i[n].classList && i[n].classList.contains("ace_indent-guide-active")) { i[n].classList.remove("ace_indent-guide-active"); break } } }, this.$setIndentGuideActive = function (e, t) { "" !== this.session.doc.getLine(e.row) && (e = e.element.childNodes) && (e = e[t - 1]) && e.classList && e.classList.contains("ace_indent-guide") && e.classList.add("ace_indent-guide-active") }, this.$renderHighlightIndentGuide = function () { if (this.$lines) { var e = this.$lines.cells, t = (this.$clearActiveIndentGuide(), this.$highlightIndentGuideMarker.indentLevel); if (0 !== t) if (1 === this.$highlightIndentGuideMarker.dir) for (var i = 0; i < e.length; i++) { var n = e[i]; if (this.$highlightIndentGuideMarker.end && n.row >= this.$highlightIndentGuideMarker.start + 1) { if (n.row >= this.$highlightIndentGuideMarker.end) break; this.$setIndentGuideActive(n, t) } } else for (i = e.length - 1; 0 <= i; i--) { n = e[i]; if (this.$highlightIndentGuideMarker.end && n.row < this.$highlightIndentGuideMarker.start) { if (n.row <= this.$highlightIndentGuideMarker.end) break; this.$setIndentGuideActive(n, t) } } } }, this.$createLineElement = function (e) { var t = this.dom.createElement("div"); return t.className = "ace_line", t.style.height = this.config.lineHeight + "px", t }, this.$renderWrappedLine = function (e, t, i) { var n = 0, s = 0, o = i[0], r = 0, a = this.$createLineElement(); e.appendChild(a); for (var l = 0; l < t.length; l++) { var h = t[l], c = h.value; if (0 == l && this.displayIndentGuides) { if (n = c.length, !(c = this.renderIndentGuide(a, c, o))) continue; n -= c.length } if (n + c.length < o) r = this.$renderTokenInChunks(a, r, h, c), n += c.length; else { for (; n + c.length >= o;)r = this.$renderTokenInChunks(a, r, h, c.substring(0, o - n)), c = c.substring(o - n), n = o, a = this.$createLineElement(), e.appendChild(a), a.appendChild(this.dom.createTextNode(w.stringRepeat(" ", i.indent), this.element)), r = 0, o = i[++s] || Number.MAX_VALUE; 0 != c.length && (n += c.length, r = this.$renderTokenInChunks(a, r, h, c)) } } i[i.length - 1] > this.MAX_LINE_LENGTH && this.$renderOverflowMessage(a, r, null, "", !0) }, this.$renderSimpleLine = function (e, t) { for (var i = 0, n = 0; n < t.length; n++) { var s = t[n], o = s.value; if (0 != n || !this.displayIndentGuides || (o = this.renderIndentGuide(e, o))) { if (i + o.length > this.MAX_LINE_LENGTH) return void this.$renderOverflowMessage(e, i, s, o); i = this.$renderTokenInChunks(e, i, s, o) } } }, this.$renderOverflowMessage = function (e, t, i, n, s) { i && this.$renderTokenInChunks(e, t, i, n.slice(0, this.MAX_LINE_LENGTH - t)); i = this.dom.createElement("span"); i.className = "ace_inline_button ace_keyword ace_toggle_wrap", i.textContent = s ? "<hide>" : "<click to see more...>", e.appendChild(i) }, this.$renderLine = function (e, t, i) { var n, s, o = e; (n = (i = i || 0 == i ? i : this.session.getFoldLine(t)) ? this.$getFoldLineTokens(t, i) : this.session.getTokens(t)).length ? (s = this.session.getRowSplitData(t)) && s.length ? (this.$renderWrappedLine(e, n, s), o = e.lastChild) : (o = e, this.$useLineGroups() && (o = this.$createLineElement(), e.appendChild(o)), this.$renderSimpleLine(o, n)) : this.$useLineGroups() && (o = this.$createLineElement(), e.appendChild(o)), this.showEOL && o && (i && (t = i.end.row), (s = this.dom.createElement("span")).className = "ace_invisible ace_invisible_eol", s.textContent = t == this.session.getLength() - 1 ? this.EOF_CHAR : this.EOL_CHAR, o.appendChild(s)) }, this.$getFoldLineTokens = function (e, t) { var u = this.session, d = [], g = u.getTokens(e); return t.walk(function (e, t, i, n, s) { if (null != e) d.push({ type: "fold", value: e }); else if ((g = s ? u.getTokens(t) : g).length) { for (var o, r = g, a = n, l = i, h = 0, c = 0; c + r[h].value.length < a;)if (c += r[h].value.length, ++h == r.length) return; for (c != a && ((o = r[h].value.substring(a - c)).length > l - a && (o = o.substring(0, l - a)), d.push({ type: r[h].type, value: o }), c = a + o.length, h += 1); c < l && h < r.length;)(o = r[h].value).length + c > l ? d.push({ type: r[h].type, value: o.substring(0, l - c) }) : d.push(r[h]), c += o.length, h += 1 } }, t.end.row, this.session.getLine(t.end.row).length), d }, this.$useLineGroups = function () { return this.session.getUseWrapMode() }, this.destroy = function () { } }).call(n.prototype), t.Text = n }), define("ace/layer/cursor", ["require", "exports", "module", "ace/lib/dom"], function (e, t, i) { "use strict"; function n(e) { this.element = h.createElement("div"), this.element.className = "ace_layer ace_cursor-layer", e.appendChild(this.element), this.isVisible = !1, this.isBlinking = !0, this.blinkInterval = 1e3, this.smoothBlinking = !1, this.cursors = [], this.cursor = this.addCursor(), h.addCssClass(this.element, "ace_hidden-cursors"), this.$updateCursors = this.$updateOpacity.bind(this) } var h = e("../lib/dom"); (function () { this.$updateOpacity = function (e) { for (var t = this.cursors, i = t.length; i--;)h.setStyle(t[i].style, "opacity", e ? "" : "0") }, this.$startCssAnimation = function () { for (var e = this.cursors, t = e.length; t--;)e[t].style.animationDuration = this.blinkInterval + "ms"; this.$isAnimating = !0, setTimeout(function () { this.$isAnimating && h.addCssClass(this.element, "ace_animate-blinking") }.bind(this)) }, this.$stopCssAnimation = function () { this.$isAnimating = !1, h.removeCssClass(this.element, "ace_animate-blinking") }, this.$padding = 0, this.setPadding = function (e) { this.$padding = e }, this.setSession = function (e) { this.session = e }, this.setBlinking = function (e) { e != this.isBlinking && (this.isBlinking = e, this.restartTimer()) }, this.setBlinkInterval = function (e) { e != this.blinkInterval && (this.blinkInterval = e, this.restartTimer()) }, this.setSmoothBlinking = function (e) { e != this.smoothBlinking && (this.smoothBlinking = e, h.setCssClass(this.element, "ace_smooth-blinking", e), this.$updateCursors(!0), this.restartTimer()) }, this.addCursor = function () { var e = h.createElement("div"); return e.className = "ace_cursor", this.element.appendChild(e), this.cursors.push(e), e }, this.removeCursor = function () { var e; if (1 < this.cursors.length) return (e = this.cursors.pop()).parentNode.removeChild(e), e }, this.hideCursor = function () { this.isVisible = !1, h.addCssClass(this.element, "ace_hidden-cursors"), this.restartTimer() }, this.showCursor = function () { this.isVisible = !0, h.removeCssClass(this.element, "ace_hidden-cursors"), this.restartTimer() }, this.restartTimer = function () { var e, t = this.$updateCursors; clearInterval(this.intervalId), clearTimeout(this.timeoutId), this.$stopCssAnimation(), this.smoothBlinking && (this.$isSmoothBlinking = !1, h.removeCssClass(this.element, "ace_smooth-blinking")), t(!0), this.isBlinking && this.blinkInterval && this.isVisible ? (this.smoothBlinking && (this.$isSmoothBlinking = !0, setTimeout(function () { this.$isSmoothBlinking && h.addCssClass(this.element, "ace_smooth-blinking") }.bind(this))), h.HAS_CSS_ANIMATION ? this.$startCssAnimation() : (e = function () { this.timeoutId = setTimeout(function () { t(!1) }, .6 * this.blinkInterval) }.bind(this), this.intervalId = setInterval(function () { t(!0), e() }, this.blinkInterval), e())) : this.$stopCssAnimation() }, this.getPixelPosition = function (e, t) { if (!this.config || !this.session) return { left: 0, top: 0 }; e = e || this.session.selection.getCursor(); var i = this.session.documentToScreenPosition(e); return { left: this.$padding + (this.session.$bidiHandler.isBidiRow(i.row, e.row) ? this.session.$bidiHandler.getPosLeft(i.column) : i.column * this.config.characterWidth), top: (i.row - (t ? this.config.firstRowScreen : 0)) * this.config.lineHeight } }, this.isCursorInView = function (e, t) { return 0 <= e.top && e.top < t.maxHeight }, this.update = function (e) { this.config = e; for (var t = this.session.$selectionMarkers, i = 0, n = 0, i = 0, s = (t = void 0 !== t && 0 !== t.length ? t : [{ cursor: null }]).length; i < s; i++) { var o, r, a = this.getPixelPosition(t[i].cursor, !0); (a.top > e.height + e.offset || a.top < 0) && 1 < i || (r = (o = this.cursors[n++] || this.addCursor()).style, this.drawCursor ? this.drawCursor(o, a, e, t[i], this.session) : this.isCursorInView(a, e) ? (h.setStyle(r, "display", "block"), h.translate(o, a.left, a.top), h.setStyle(r, "width", Math.round(e.characterWidth) + "px"), h.setStyle(r, "height", e.lineHeight + "px")) : h.setStyle(r, "display", "none")) } for (; this.cursors.length > n;)this.removeCursor(); var l = this.session.getOverwrite(); this.$setOverwrite(l), this.$pixelPos = a, this.restartTimer() }, this.drawCursor = null, this.$setOverwrite = function (e) { e != this.overwrite && ((this.overwrite = e) ? h.addCssClass(this.element, "ace_overwrite-cursors") : h.removeCssClass(this.element, "ace_overwrite-cursors")) }, this.destroy = function () { clearInterval(this.intervalId), clearTimeout(this.timeoutId) } }).call(n.prototype), t.Cursor = n }), define("ace/scrollbar", ["require", "exports", "module", "ace/lib/oop", "ace/lib/dom", "ace/lib/event", "ace/lib/event_emitter"], function (e, t, i) { "use strict"; function n(e) { this.element = a.createElement("div"), this.element.className = "ace_scrollbar ace_scrollbar" + this.classSuffix, this.inner = a.createElement("div"), this.inner.className = "ace_scrollbar-inner", this.inner.textContent = " ", this.element.appendChild(this.inner), e.appendChild(this.element), this.setVisible(!1), this.skipEvent = !1, l.addListener(this.element, "scroll", this.onScroll.bind(this)), l.addListener(this.element, "mousedown", l.preventDefault) } function s(e, t) { n.call(this, e), this.scrollTop = 0, this.scrollHeight = 0, t.$scrollbarWidth = this.width = a.scrollbarWidth(e.ownerDocument), this.inner.style.width = this.element.style.width = (this.width || 15) + 5 + "px", this.$minWidth = 0 } function o(e, t) { n.call(this, e), this.scrollLeft = 0, this.height = t.$scrollbarWidth, this.inner.style.height = this.element.style.height = (this.height || 15) + 5 + "px" } var r = e("./lib/oop"), a = e("./lib/dom"), l = e("./lib/event"), h = e("./lib/event_emitter").EventEmitter; !function () { r.implement(this, h), this.setVisible = function (e) { this.element.style.display = e ? "" : "none", this.isVisible = e, this.coeff = 1 } }.call(n.prototype), r.inherits(s, n), function () { this.classSuffix = "-v", this.onScroll = function () { var e; this.skipEvent || (this.scrollTop = this.element.scrollTop, 1 != this.coeff && (e = this.element.clientHeight / this.scrollHeight, this.scrollTop = this.scrollTop * (1 - e) / (this.coeff - e)), this._emit("scroll", { data: this.scrollTop })), this.skipEvent = !1 }, this.getWidth = function () { return Math.max(this.isVisible ? this.width : 0, this.$minWidth || 0) }, this.setHeight = function (e) { this.element.style.height = e + "px" }, this.setInnerHeight = this.setScrollHeight = function (e) { 32768 < (this.scrollHeight = e) ? (this.coeff = 32768 / e, e = 32768) : 1 != this.coeff && (this.coeff = 1), this.inner.style.height = e + "px" }, this.setScrollTop = function (e) { this.scrollTop != e && (this.skipEvent = !0, this.scrollTop = e, this.element.scrollTop = e * this.coeff) } }.call(s.prototype); r.inherits(o, n), function () { this.classSuffix = "-h", this.onScroll = function () { this.skipEvent || (this.scrollLeft = this.element.scrollLeft, this._emit("scroll", { data: this.scrollLeft })), this.skipEvent = !1 }, this.getHeight = function () { return this.isVisible ? this.height : 0 }, this.setWidth = function (e) { this.element.style.width = e + "px" }, this.setInnerWidth = function (e) { this.inner.style.width = e + "px" }, this.setScrollWidth = function (e) { this.inner.style.width = e + "px" }, this.setScrollLeft = function (e) { this.scrollLeft != e && (this.skipEvent = !0, this.scrollLeft = this.element.scrollLeft = e) } }.call(o.prototype), t.ScrollBar = s, t.ScrollBarV = s, t.ScrollBarH = o, t.VScrollBar = s, t.HScrollBar = o }), define("ace/scrollbar_custom", ["require", "exports", "module", "ace/lib/oop", "ace/lib/dom", "ace/lib/event", "ace/lib/event_emitter"], function (e, t, i) { "use strict"; function n(e) { this.element = a.createElement("div"), this.element.className = "ace_sb" + this.classSuffix, this.inner = a.createElement("div"), this.inner.className = "", this.element.appendChild(this.inner), this.VScrollWidth = 12, this.HScrollHeight = 12, e.appendChild(this.element), this.setVisible(!1), this.skipEvent = !1, l.addMultiMouseDownListener(this.element, [500, 300, 300], this, "onMouseDown") } function s(e, t) { n.call(this, e), this.scrollTop = 0, this.scrollHeight = 0, this.parent = e, this.width = this.VScrollWidth, this.renderer = t, this.inner.style.width = this.element.style.width = (this.width || 15) + "px", this.$minWidth = 0 } function o(e, t) { n.call(this, e), this.scrollLeft = 0, this.scrollWidth = 0, this.height = this.HScrollHeight, this.inner.style.height = this.element.style.height = (this.height || 12) + "px", this.renderer = t } var r = e("./lib/oop"), a = e("./lib/dom"), l = e("./lib/event"), h = e("./lib/event_emitter").EventEmitter; a.importCssString(".ace_editor>.ace_sb-v div, .ace_editor>.ace_sb-h div{\n  position: absolute;\n  background: rgba(128, 128, 128, 0.6);\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n  border: 1px solid #bbb;\n  border-radius: 2px;\n  z-index: 8;\n}\n.ace_editor>.ace_sb-v, .ace_editor>.ace_sb-h {\n  position: absolute;\n  z-index: 6;\n  background: none;\n  overflow: hidden!important;\n}\n.ace_editor>.ace_sb-v {\n  z-index: 6;\n  right: 0;\n  top: 0;\n  width: 12px;\n}\n.ace_editor>.ace_sb-v div {\n  z-index: 8;\n  right: 0;\n  width: 100%;\n}\n.ace_editor>.ace_sb-h {\n  bottom: 0;\n  left: 0;\n  height: 12px;\n}\n.ace_editor>.ace_sb-h div {\n  bottom: 0;\n  height: 100%;\n}\n.ace_editor>.ace_sb_grabbed {\n  z-index: 8;\n  background: #000;\n}", "ace_scrollbar.css", !1), function () { r.implement(this, h), this.setVisible = function (e) { this.element.style.display = e ? "" : "none", this.isVisible = e, this.coeff = 1 } }.call(n.prototype), r.inherits(s, n), function () { this.classSuffix = "-v", r.implement(this, h), this.onMouseDown = function (e, t) { if ("mousedown" === e && 0 === l.getButton(t) && 2 !== t.detail) { var i, n, s, o, r; if (t.target === this.inner) return i = this, n = t.clientY, s = t.clientY, o = this.thumbTop, l.capture(this.inner, function (e) { n = e.clientY }, function () { clearInterval(r) }), r = setInterval(function () { var e; void 0 !== n && (e = i.scrollTopFromThumbTop(o + n - s)) !== i.scrollTop && i._emit("scroll", { data: e }) }, 20), l.preventDefault(t); e = t.clientY - this.element.getBoundingClientRect().top - this.thumbHeight / 2; return this._emit("scroll", { data: this.scrollTopFromThumbTop(e) }), l.preventDefault(t) } }, this.getHeight = function () { return this.height }, this.scrollTopFromThumbTop = function (e) { e = e * (this.pageHeight - this.viewHeight) / (this.slideHeight - this.thumbHeight); return (e >>= 0) < 0 ? e = 0 : e > this.pageHeight - this.viewHeight && (e = this.pageHeight - this.viewHeight), e }, this.getWidth = function () { return Math.max(this.isVisible ? this.width : 0, this.$minWidth || 0) }, this.setHeight = function (e) { this.height = Math.max(0, e), this.slideHeight = this.height, this.viewHeight = this.height, this.setScrollHeight(this.pageHeight, !0) }, this.setInnerHeight = this.setScrollHeight = function (e, t) { this.pageHeight === e && !t || (this.pageHeight = e, this.thumbHeight = this.slideHeight * this.viewHeight / this.pageHeight, this.thumbHeight > this.slideHeight && (this.thumbHeight = this.slideHeight), this.thumbHeight < 15 && (this.thumbHeight = 15), this.inner.style.height = this.thumbHeight + "px", this.scrollTop > this.pageHeight - this.viewHeight && (this.scrollTop = this.pageHeight - this.viewHeight, this.scrollTop < 0 && (this.scrollTop = 0), this._emit("scroll", { data: this.scrollTop }))) }, this.setScrollTop = function (e) { this.scrollTop = e, this.thumbTop = (e = e < 0 ? 0 : e) * (this.slideHeight - this.thumbHeight) / (this.pageHeight - this.viewHeight), this.inner.style.top = this.thumbTop + "px" } }.call(s.prototype); r.inherits(o, n), function () { this.classSuffix = "-h", r.implement(this, h), this.onMouseDown = function (e, t) { if ("mousedown" === e && 0 === l.getButton(t) && 2 !== t.detail) { var i, n, s, o, r; if (t.target === this.inner) return i = this, n = t.clientX, s = t.clientX, o = this.thumbLeft, l.capture(this.inner, function (e) { n = e.clientX }, function () { clearInterval(r) }), r = setInterval(function () { var e; void 0 !== n && (e = i.scrollLeftFromThumbLeft(o + n - s)) !== i.scrollLeft && i._emit("scroll", { data: e }) }, 20), l.preventDefault(t); e = t.clientX - this.element.getBoundingClientRect().left - this.thumbWidth / 2; return this._emit("scroll", { data: this.scrollLeftFromThumbLeft(e) }), l.preventDefault(t) } }, this.getHeight = function () { return this.isVisible ? this.height : 0 }, this.scrollLeftFromThumbLeft = function (e) { e = e * (this.pageWidth - this.viewWidth) / (this.slideWidth - this.thumbWidth); return (e >>= 0) < 0 ? e = 0 : e > this.pageWidth - this.viewWidth && (e = this.pageWidth - this.viewWidth), e }, this.setWidth = function (e) { this.width = Math.max(0, e), this.element.style.width = this.width + "px", this.slideWidth = this.width, this.viewWidth = this.width, this.setScrollWidth(this.pageWidth, !0) }, this.setInnerWidth = this.setScrollWidth = function (e, t) { this.pageWidth === e && !t || (this.pageWidth = e, this.thumbWidth = this.slideWidth * this.viewWidth / this.pageWidth, this.thumbWidth > this.slideWidth && (this.thumbWidth = this.slideWidth), this.thumbWidth < 15 && (this.thumbWidth = 15), this.inner.style.width = this.thumbWidth + "px", this.scrollLeft > this.pageWidth - this.viewWidth && (this.scrollLeft = this.pageWidth - this.viewWidth, this.scrollLeft < 0 && (this.scrollLeft = 0), this._emit("scroll", { data: this.scrollLeft }))) }, this.setScrollLeft = function (e) { this.scrollLeft = e, this.thumbLeft = (e = e < 0 ? 0 : e) * (this.slideWidth - this.thumbWidth) / (this.pageWidth - this.viewWidth), this.inner.style.left = this.thumbLeft + "px" } }.call(o.prototype), t.ScrollBar = s, t.ScrollBarV = s, t.ScrollBarH = o, t.VScrollBar = s, t.HScrollBar = o }), define("ace/renderloop", ["require", "exports", "module", "ace/lib/event"], function (e, t, i) { "use strict"; function n(e, t) { this.onRender = e, this.pending = !1, this.changes = 0, this.$recursionLimit = 2, this.window = t || window; var i = this; this._flush = function (e) { i.pending = !1; var t = i.changes; t && (s.blockIdle(100), i.changes = 0, i.onRender(t)), i.changes ? i.$recursionLimit-- < 0 || i.schedule() : i.$recursionLimit = 2 } } var s = e("./lib/event"); (function () { this.schedule = function (e) { this.changes = this.changes | e, this.changes && !this.pending && (s.nextFrame(this._flush), this.pending = !0) }, this.clear = function (e) { var t = this.changes; return this.changes = 0, t } }).call(n.prototype), t.RenderLoop = n }), define("ace/layer/font_metrics", ["require", "exports", "module", "ace/lib/oop", "ace/lib/dom", "ace/lib/lang", "ace/lib/event", "ace/lib/useragent", "ace/lib/event_emitter"], function (e, t, i) { var n = e("../lib/oop"), s = e("../lib/dom"), o = e("../lib/lang"), r = e("../lib/event"), a = e("../lib/useragent"), l = e("../lib/event_emitter").EventEmitter, h = "function" == typeof ResizeObserver, e = t.FontMetrics = function (e, t) { this.charCount = t || 250, this.el = s.createElement("div"), this.$setMeasureNodeStyles(this.el.style, !0), this.$main = s.createElement("div"), this.$setMeasureNodeStyles(this.$main.style), this.$measureNode = s.createElement("div"), this.$setMeasureNodeStyles(this.$measureNode.style), this.el.appendChild(this.$main), this.el.appendChild(this.$measureNode), e.appendChild(this.el), this.$measureNode.textContent = o.stringRepeat("X", this.charCount), this.$characterSize = { width: 0, height: 0 }, h ? this.$addObserver() : this.checkForSizeChanges() }; !function () { n.implement(this, l), this.$characterSize = { width: 0, height: 0 }, this.$setMeasureNodeStyles = function (e, t) { e.width = e.height = "auto", e.left = e.top = "0px", e.visibility = "hidden", e.position = "absolute", e.whiteSpace = "pre", a.isIE < 8 ? e["font-family"] = "inherit" : e.font = "inherit", e.overflow = t ? "hidden" : "visible" }, this.checkForSizeChanges = function (e) { var t; !(e = void 0 === e ? this.$measureSizes() : e) || this.$characterSize.width === e.width && this.$characterSize.height === e.height || (this.$measureNode.style.fontWeight = "bold", t = this.$measureSizes(), this.$measureNode.style.fontWeight = "", this.$characterSize = e, this.charSizes = Object.create(null), this.allowBoldFonts = t && t.width === e.width && t.height === e.height, this._emit("changeCharacterSize", { data: e })) }, this.$addObserver = function () { var t = this; this.$observer = new window.ResizeObserver(function (e) { t.checkForSizeChanges() }), this.$observer.observe(this.$measureNode) }, this.$pollSizeChanges = function () { if (this.$pollSizeChangesTimer || this.$observer) return this.$pollSizeChangesTimer; var t = this; return this.$pollSizeChangesTimer = r.onIdle(function e() { t.checkForSizeChanges(), r.onIdle(e, 500) }, 500) }, this.setPolling = function (e) { e ? this.$pollSizeChanges() : this.$pollSizeChangesTimer && (clearInterval(this.$pollSizeChangesTimer), this.$pollSizeChangesTimer = 0) }, this.$measureSizes = function (e) { e = (e = e || this.$measureNode).getBoundingClientRect(), e = { height: e.height, width: e.width / this.charCount }; return 0 === e.width || 0 === e.height ? null : e }, this.$measureCharWidth = function (e) { return this.$main.textContent = o.stringRepeat(e, this.charCount), this.$main.getBoundingClientRect().width / this.charCount }, this.getCharacterWidth = function (e) { var t = this.charSizes[e]; return t = void 0 === t ? this.charSizes[e] = this.$measureCharWidth(e) / this.$characterSize.width : t }, this.destroy = function () { clearInterval(this.$pollSizeChangesTimer), this.$observer && this.$observer.disconnect(), this.el && this.el.parentNode && this.el.parentNode.removeChild(this.el) }, this.$getZoom = function e(t) { return t && t.parentElement ? (window.getComputedStyle(t).zoom || 1) * e(t.parentElement) : 1 }, this.$initTransformMeasureNodes = function () { function e(e, t) { return ["div", { style: "position: absolute;top:" + e + "px;left:" + t + "px;" }] } this.els = s.buildDom([e(0, 0), e(200, 0), e(0, 200), e(200, 200)], this.el) }, this.transformCoordinates = function (e, t) { function i(e, t, i) { var n = e[1] * t[0] - e[0] * t[1]; return [(-t[1] * i[0] + t[0] * i[1]) / n, (+e[1] * i[0] - e[0] * i[1]) / n] } function n(e, t) { return [e[0] - t[0], e[1] - t[1]] } function s(e, t) { return [e[0] + t[0], e[1] + t[1]] } function o(e, t) { return [e * t[0], e * t[1]] } function r(e) { e = e.getBoundingClientRect(); return [e.left, e.top] } e = e && o(1 / this.$getZoom(this.el), e), this.els || this.$initTransformMeasureNodes(); var a = r(this.els[0]), l = r(this.els[1]), h = r(this.els[2]), c = r(this.els[3]), c = i(n(c, l), n(c, h), n(s(l, h), s(c, a))), l = o(1 + c[0], n(l, a)), h = o(1 + c[1], n(h, a)); if (t) return u = c[0] * t[0] / 200 + c[1] * t[1] / 200 + 1, t = s(o(t[0], l), o(t[1], h)), s(o(1 / u / 200, t), a); var u = n(e, a), t = i(n(l, o(c[0], u)), n(h, o(c[1], u)), u); return o(200, t) } }.call(e.prototype) }), define("ace/css/editor.css", ["require", "exports", "module"], function (e, t, i) { i.exports = '/*\nstyles = []\nfor (var i = 1; i < 16; i++) {\n    styles.push(".ace_br" + i + "{" + (\n        ["top-left", "top-right", "bottom-right", "bottom-left"]\n    ).map(function(x, j) {\n        return i & (1<<j) ? "border-" + x + "-radius: 3px;" : "" \n    }).filter(Boolean).join(" ") + "}")\n}\nstyles.join("\\n")\n*/\n.ace_br1 {border-top-left-radius    : 3px;}\n.ace_br2 {border-top-right-radius   : 3px;}\n.ace_br3 {border-top-left-radius    : 3px; border-top-right-radius:    3px;}\n.ace_br4 {border-bottom-right-radius: 3px;}\n.ace_br5 {border-top-left-radius    : 3px; border-bottom-right-radius: 3px;}\n.ace_br6 {border-top-right-radius   : 3px; border-bottom-right-radius: 3px;}\n.ace_br7 {border-top-left-radius    : 3px; border-top-right-radius:    3px; border-bottom-right-radius: 3px;}\n.ace_br8 {border-bottom-left-radius : 3px;}\n.ace_br9 {border-top-left-radius    : 3px; border-bottom-left-radius:  3px;}\n.ace_br10{border-top-right-radius   : 3px; border-bottom-left-radius:  3px;}\n.ace_br11{border-top-left-radius    : 3px; border-top-right-radius:    3px; border-bottom-left-radius:  3px;}\n.ace_br12{border-bottom-right-radius: 3px; border-bottom-left-radius:  3px;}\n.ace_br13{border-top-left-radius    : 3px; border-bottom-right-radius: 3px; border-bottom-left-radius:  3px;}\n.ace_br14{border-top-right-radius   : 3px; border-bottom-right-radius: 3px; border-bottom-left-radius:  3px;}\n.ace_br15{border-top-left-radius    : 3px; border-top-right-radius:    3px; border-bottom-right-radius: 3px; border-bottom-left-radius: 3px;}\n\n\n.ace_editor {\n    position: relative;\n    overflow: hidden;\n    padding: 0;\n    font: 12px/normal \'Monaco\', \'Menlo\', \'Ubuntu Mono\', \'Consolas\', \'source-code-pro\', monospace;\n    direction: ltr;\n    text-align: left;\n    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n}\n\n.ace_scroller {\n    position: absolute;\n    overflow: hidden;\n    top: 0;\n    bottom: 0;\n    background-color: inherit;\n    -ms-user-select: none;\n    -moz-user-select: none;\n    -webkit-user-select: none;\n    user-select: none;\n    cursor: text;\n}\n\n.ace_content {\n    position: absolute;\n    box-sizing: border-box;\n    min-width: 100%;\n    contain: style size layout;\n    font-variant-ligatures: no-common-ligatures;\n}\n\n.ace_dragging .ace_scroller:before{\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    content: \'\';\n    background: rgba(250, 250, 250, 0.01);\n    z-index: 1000;\n}\n.ace_dragging.ace_dark .ace_scroller:before{\n    background: rgba(0, 0, 0, 0.01);\n}\n\n.ace_gutter {\n    position: absolute;\n    overflow : hidden;\n    width: auto;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    cursor: default;\n    z-index: 4;\n    -ms-user-select: none;\n    -moz-user-select: none;\n    -webkit-user-select: none;\n    user-select: none;\n    contain: style size layout;\n}\n\n.ace_gutter-active-line {\n    position: absolute;\n    left: 0;\n    right: 0;\n}\n\n.ace_scroller.ace_scroll-left {\n    box-shadow: 17px 0 16px -16px rgba(0, 0, 0, 0.4) inset;\n}\n\n.ace_gutter-cell {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    padding-left: 19px;\n    padding-right: 6px;\n    background-repeat: no-repeat;\n}\n\n.ace_gutter-cell.ace_error {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAABOFBMVEX/////////QRswFAb/Ui4wFAYwFAYwFAaWGAfDRymzOSH/PxswFAb/SiUwFAYwFAbUPRvjQiDllog5HhHdRybsTi3/Tyv9Tir+Syj/UC3////XurebMBIwFAb/RSHbPx/gUzfdwL3kzMivKBAwFAbbvbnhPx66NhowFAYwFAaZJg8wFAaxKBDZurf/RB6mMxb/SCMwFAYwFAbxQB3+RB4wFAb/Qhy4Oh+4QifbNRcwFAYwFAYwFAb/QRzdNhgwFAYwFAbav7v/Uy7oaE68MBK5LxLewr/r2NXewLswFAaxJw4wFAbkPRy2PyYwFAaxKhLm1tMwFAazPiQwFAaUGAb/QBrfOx3bvrv/VC/maE4wFAbRPBq6MRO8Qynew8Dp2tjfwb0wFAbx6eju5+by6uns4uH9/f36+vr/GkHjAAAAYnRSTlMAGt+64rnWu/bo8eAA4InH3+DwoN7j4eLi4xP99Nfg4+b+/u9B/eDs1MD1mO7+4PHg2MXa347g7vDizMLN4eG+Pv7i5evs/v79yu7S3/DV7/498Yv24eH+4ufQ3Ozu/v7+y13sRqwAAADLSURBVHjaZc/XDsFgGIBhtDrshlitmk2IrbHFqL2pvXf/+78DPokj7+Fz9qpU/9UXJIlhmPaTaQ6QPaz0mm+5gwkgovcV6GZzd5JtCQwgsxoHOvJO15kleRLAnMgHFIESUEPmawB9ngmelTtipwwfASilxOLyiV5UVUyVAfbG0cCPHig+GBkzAENHS0AstVF6bacZIOzgLmxsHbt2OecNgJC83JERmePUYq8ARGkJx6XtFsdddBQgZE2nPR6CICZhawjA4Fb/chv+399kfR+MMMDGOQAAAABJRU5ErkJggg==");\n    background-repeat: no-repeat;\n    background-position: 2px center;\n}\n\n.ace_gutter-cell.ace_warning {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAmVBMVEX///8AAAD///8AAAAAAABPSzb/5sAAAAB/blH/73z/ulkAAAAAAAD85pkAAAAAAAACAgP/vGz/rkDerGbGrV7/pkQICAf////e0IsAAAD/oED/qTvhrnUAAAD/yHD/njcAAADuv2r/nz//oTj/p064oGf/zHAAAAA9Nir/tFIAAAD/tlTiuWf/tkIAAACynXEAAAAAAAAtIRW7zBpBAAAAM3RSTlMAABR1m7RXO8Ln31Z36zT+neXe5OzooRDfn+TZ4p3h2hTf4t3k3ucyrN1K5+Xaks52Sfs9CXgrAAAAjklEQVR42o3PbQ+CIBQFYEwboPhSYgoYunIqqLn6/z8uYdH8Vmdnu9vz4WwXgN/xTPRD2+sgOcZjsge/whXZgUaYYvT8QnuJaUrjrHUQreGczuEafQCO/SJTufTbroWsPgsllVhq3wJEk2jUSzX3CUEDJC84707djRc5MTAQxoLgupWRwW6UB5fS++NV8AbOZgnsC7BpEAAAAABJRU5ErkJggg==");\n    background-position: 2px center;\n}\n\n.ace_gutter-cell.ace_info {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAAAAAA6mKC9AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAAJ0Uk5TAAB2k804AAAAPklEQVQY02NgIB68QuO3tiLznjAwpKTgNyDbMegwisCHZUETUZV0ZqOquBpXj2rtnpSJT1AEnnRmL2OgGgAAIKkRQap2htgAAAAASUVORK5CYII=");\n    background-position: 2px center;\n}\n.ace_dark .ace_gutter-cell.ace_info {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQBAMAAADt3eJSAAAAJFBMVEUAAAChoaGAgIAqKiq+vr6tra1ZWVmUlJSbm5s8PDxubm56enrdgzg3AAAAAXRSTlMAQObYZgAAAClJREFUeNpjYMAPdsMYHegyJZFQBlsUlMFVCWUYKkAZMxZAGdxlDMQBAG+TBP4B6RyJAAAAAElFTkSuQmCC");\n}\n\n.ace_scrollbar {\n    contain: strict;\n    position: absolute;\n    right: 0;\n    bottom: 0;\n    z-index: 6;\n}\n\n.ace_scrollbar-inner {\n    position: absolute;\n    cursor: text;\n    left: 0;\n    top: 0;\n}\n\n.ace_scrollbar-v{\n    overflow-x: hidden;\n    overflow-y: scroll;\n    top: 0;\n}\n\n.ace_scrollbar-h {\n    overflow-x: scroll;\n    overflow-y: hidden;\n    left: 0;\n}\n\n.ace_print-margin {\n    position: absolute;\n    height: 100%;\n}\n\n.ace_text-input {\n    position: absolute;\n    z-index: 0;\n    width: 0.5em;\n    height: 1em;\n    opacity: 0;\n    background: transparent;\n    -moz-appearance: none;\n    appearance: none;\n    border: none;\n    resize: none;\n    outline: none;\n    overflow: hidden;\n    font: inherit;\n    padding: 0 1px;\n    margin: 0 -1px;\n    contain: strict;\n    -ms-user-select: text;\n    -moz-user-select: text;\n    -webkit-user-select: text;\n    user-select: text;\n    /*with `pre-line` chrome inserts &nbsp; instead of space*/\n    white-space: pre!important;\n}\n.ace_text-input.ace_composition {\n    background: transparent;\n    color: inherit;\n    z-index: 1000;\n    opacity: 1;\n}\n.ace_composition_placeholder { color: transparent }\n.ace_composition_marker { \n    border-bottom: 1px solid;\n    position: absolute;\n    border-radius: 0;\n    margin-top: 1px;\n}\n\n[ace_nocontext=true] {\n    transform: none!important;\n    filter: none!important;\n    clip-path: none!important;\n    mask : none!important;\n    contain: none!important;\n    perspective: none!important;\n    mix-blend-mode: initial!important;\n    z-index: auto;\n}\n\n.ace_layer {\n    z-index: 1;\n    position: absolute;\n    overflow: hidden;\n    /* workaround for chrome bug https://github.com/ajaxorg/ace/issues/2312*/\n    word-wrap: normal;\n    white-space: pre;\n    height: 100%;\n    width: 100%;\n    box-sizing: border-box;\n    /* setting pointer-events: auto; on node under the mouse, which changes\n        during scroll, will break mouse wheel scrolling in Safari */\n    pointer-events: none;\n}\n\n.ace_gutter-layer {\n    position: relative;\n    width: auto;\n    text-align: right;\n    pointer-events: auto;\n    height: 1000000px;\n    contain: style size layout;\n}\n\n.ace_text-layer {\n    font: inherit !important;\n    position: absolute;\n    height: 1000000px;\n    width: 1000000px;\n    contain: style size layout;\n}\n\n.ace_text-layer > .ace_line, .ace_text-layer > .ace_line_group {\n    contain: style size layout;\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n}\n\n.ace_hidpi .ace_text-layer,\n.ace_hidpi .ace_gutter-layer,\n.ace_hidpi .ace_content,\n.ace_hidpi .ace_gutter {\n    contain: strict;\n    will-change: transform;\n}\n.ace_hidpi .ace_text-layer > .ace_line, \n.ace_hidpi .ace_text-layer > .ace_line_group {\n    contain: strict;\n}\n\n.ace_cjk {\n    display: inline-block;\n    text-align: center;\n}\n\n.ace_cursor-layer {\n    z-index: 4;\n}\n\n.ace_cursor {\n    z-index: 4;\n    position: absolute;\n    box-sizing: border-box;\n    border-left: 2px solid;\n    /* workaround for smooth cursor repaintng whole screen in chrome */\n    transform: translatez(0);\n}\n\n.ace_multiselect .ace_cursor {\n    border-left-width: 1px;\n}\n\n.ace_slim-cursors .ace_cursor {\n    border-left-width: 1px;\n}\n\n.ace_overwrite-cursors .ace_cursor {\n    border-left-width: 0;\n    border-bottom: 1px solid;\n}\n\n.ace_hidden-cursors .ace_cursor {\n    opacity: 0.2;\n}\n\n.ace_hasPlaceholder .ace_hidden-cursors .ace_cursor {\n    opacity: 0;\n}\n\n.ace_smooth-blinking .ace_cursor {\n    transition: opacity 0.18s;\n}\n\n.ace_animate-blinking .ace_cursor {\n    animation-duration: 1000ms;\n    animation-timing-function: step-end;\n    animation-name: blink-ace-animate;\n    animation-iteration-count: infinite;\n}\n\n.ace_animate-blinking.ace_smooth-blinking .ace_cursor {\n    animation-duration: 1000ms;\n    animation-timing-function: ease-in-out;\n    animation-name: blink-ace-animate-smooth;\n}\n    \n@keyframes blink-ace-animate {\n    from, to { opacity: 1; }\n    60% { opacity: 0; }\n}\n\n@keyframes blink-ace-animate-smooth {\n    from, to { opacity: 1; }\n    45% { opacity: 1; }\n    60% { opacity: 0; }\n    85% { opacity: 0; }\n}\n\n.ace_marker-layer .ace_step, .ace_marker-layer .ace_stack {\n    position: absolute;\n    z-index: 3;\n}\n\n.ace_marker-layer .ace_selection {\n    position: absolute;\n    z-index: 5;\n}\n\n.ace_marker-layer .ace_bracket {\n    position: absolute;\n    z-index: 6;\n}\n\n.ace_marker-layer .ace_error_bracket {\n    position: absolute;\n    border-bottom: 1px solid #DE5555;\n    border-radius: 0;\n}\n\n.ace_marker-layer .ace_active-line {\n    position: absolute;\n    z-index: 2;\n}\n\n.ace_marker-layer .ace_selected-word {\n    position: absolute;\n    z-index: 4;\n    box-sizing: border-box;\n}\n\n.ace_line .ace_fold {\n    box-sizing: border-box;\n\n    display: inline-block;\n    height: 11px;\n    margin-top: -2px;\n    vertical-align: middle;\n\n    background-image:\n        url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAJCAYAAADU6McMAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAJpJREFUeNpi/P//PwOlgAXGYGRklAVSokD8GmjwY1wasKljQpYACtpCFeADcHVQfQyMQAwzwAZI3wJKvCLkfKBaMSClBlR7BOQikCFGQEErIH0VqkabiGCAqwUadAzZJRxQr/0gwiXIal8zQQPnNVTgJ1TdawL0T5gBIP1MUJNhBv2HKoQHHjqNrA4WO4zY0glyNKLT2KIfIMAAQsdgGiXvgnYAAAAASUVORK5CYII="),\n        url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAA3CAYAAADNNiA5AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAACJJREFUeNpi+P//fxgTAwPDBxDxD078RSX+YeEyDFMCIMAAI3INmXiwf2YAAAAASUVORK5CYII=");\n    background-repeat: no-repeat, repeat-x;\n    background-position: center center, top left;\n    color: transparent;\n\n    border: 1px solid black;\n    border-radius: 2px;\n\n    cursor: pointer;\n    pointer-events: auto;\n}\n\n.ace_dark .ace_fold {\n}\n\n.ace_fold:hover{\n    background-image:\n        url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAJCAYAAADU6McMAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAJpJREFUeNpi/P//PwOlgAXGYGRklAVSokD8GmjwY1wasKljQpYACtpCFeADcHVQfQyMQAwzwAZI3wJKvCLkfKBaMSClBlR7BOQikCFGQEErIH0VqkabiGCAqwUadAzZJRxQr/0gwiXIal8zQQPnNVTgJ1TdawL0T5gBIP1MUJNhBv2HKoQHHjqNrA4WO4zY0glyNKLT2KIfIMAAQsdgGiXvgnYAAAAASUVORK5CYII="),\n        url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAA3CAYAAADNNiA5AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAACBJREFUeNpi+P//fz4TAwPDZxDxD5X4i5fLMEwJgAADAEPVDbjNw87ZAAAAAElFTkSuQmCC");\n}\n\n.ace_tooltip {\n    background-color: #FFF;\n    background-image: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.1));\n    border: 1px solid gray;\n    border-radius: 1px;\n    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n    color: black;\n    max-width: 100%;\n    padding: 3px 4px;\n    position: fixed;\n    z-index: 999999;\n    box-sizing: border-box;\n    cursor: default;\n    white-space: pre;\n    word-wrap: break-word;\n    line-height: normal;\n    font-style: normal;\n    font-weight: normal;\n    letter-spacing: normal;\n    pointer-events: none;\n}\n\n.ace_folding-enabled > .ace_gutter-cell {\n    padding-right: 13px;\n}\n\n.ace_fold-widget {\n    box-sizing: border-box;\n\n    margin: 0 -12px 0 1px;\n    display: none;\n    width: 11px;\n    vertical-align: top;\n\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAANElEQVR42mWKsQ0AMAzC8ixLlrzQjzmBiEjp0A6WwBCSPgKAXoLkqSot7nN3yMwR7pZ32NzpKkVoDBUxKAAAAABJRU5ErkJggg==");\n    background-repeat: no-repeat;\n    background-position: center;\n\n    border-radius: 3px;\n    \n    border: 1px solid transparent;\n    cursor: pointer;\n}\n\n.ace_folding-enabled .ace_fold-widget {\n    display: inline-block;   \n}\n\n.ace_fold-widget.ace_end {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAANElEQVR42m3HwQkAMAhD0YzsRchFKI7sAikeWkrxwScEB0nh5e7KTPWimZki4tYfVbX+MNl4pyZXejUO1QAAAABJRU5ErkJggg==");\n}\n\n.ace_fold-widget.ace_closed {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAAGCAYAAAAG5SQMAAAAOUlEQVR42jXKwQkAMAgDwKwqKD4EwQ26sSOkVWjgIIHAzPiCgaqiqnJHZnKICBERHN194O5b9vbLuAVRL+l0YWnZAAAAAElFTkSuQmCCXA==");\n}\n\n.ace_fold-widget:hover {\n    border: 1px solid rgba(0, 0, 0, 0.3);\n    background-color: rgba(255, 255, 255, 0.2);\n    box-shadow: 0 1px 1px rgba(255, 255, 255, 0.7);\n}\n\n.ace_fold-widget:active {\n    border: 1px solid rgba(0, 0, 0, 0.4);\n    background-color: rgba(0, 0, 0, 0.05);\n    box-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);\n}\n/**\n * Dark version for fold widgets\n */\n.ace_dark .ace_fold-widget {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHklEQVQIW2P4//8/AzoGEQ7oGCaLLAhWiSwB146BAQCSTPYocqT0AAAAAElFTkSuQmCC");\n}\n.ace_dark .ace_fold-widget.ace_end {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAH0lEQVQIW2P4//8/AxQ7wNjIAjDMgC4AxjCVKBirIAAF0kz2rlhxpAAAAABJRU5ErkJggg==");\n}\n.ace_dark .ace_fold-widget.ace_closed {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAAFCAYAAACAcVaiAAAAHElEQVQIW2P4//+/AxAzgDADlOOAznHAKgPWAwARji8UIDTfQQAAAABJRU5ErkJggg==");\n}\n.ace_dark .ace_fold-widget:hover {\n    box-shadow: 0 1px 1px rgba(255, 255, 255, 0.2);\n    background-color: rgba(255, 255, 255, 0.1);\n}\n.ace_dark .ace_fold-widget:active {\n    box-shadow: 0 1px 1px rgba(255, 255, 255, 0.2);\n}\n\n.ace_inline_button {\n    border: 1px solid lightgray;\n    display: inline-block;\n    margin: -1px 8px;\n    padding: 0 5px;\n    pointer-events: auto;\n    cursor: pointer;\n}\n.ace_inline_button:hover {\n    border-color: gray;\n    background: rgba(200,200,200,0.2);\n    display: inline-block;\n    pointer-events: auto;\n}\n\n.ace_fold-widget.ace_invalid {\n    background-color: #FFB4B4;\n    border-color: #DE5555;\n}\n\n.ace_fade-fold-widgets .ace_fold-widget {\n    transition: opacity 0.4s ease 0.05s;\n    opacity: 0;\n}\n\n.ace_fade-fold-widgets:hover .ace_fold-widget {\n    transition: opacity 0.05s ease 0.05s;\n    opacity:1;\n}\n\n.ace_underline {\n    text-decoration: underline;\n}\n\n.ace_bold {\n    font-weight: bold;\n}\n\n.ace_nobold .ace_bold {\n    font-weight: normal;\n}\n\n.ace_italic {\n    font-style: italic;\n}\n\n\n.ace_error-marker {\n    background-color: rgba(255, 0, 0,0.2);\n    position: absolute;\n    z-index: 9;\n}\n\n.ace_highlight-marker {\n    background-color: rgba(255, 255, 0,0.2);\n    position: absolute;\n    z-index: 8;\n}\n\n.ace_mobile-menu {\n    position: absolute;\n    line-height: 1.5;\n    border-radius: 4px;\n    -ms-user-select: none;\n    -moz-user-select: none;\n    -webkit-user-select: none;\n    user-select: none;\n    background: white;\n    box-shadow: 1px 3px 2px grey;\n    border: 1px solid #dcdcdc;\n    color: black;\n}\n.ace_dark > .ace_mobile-menu {\n    background: #333;\n    color: #ccc;\n    box-shadow: 1px 3px 2px grey;\n    border: 1px solid #444;\n\n}\n.ace_mobile-button {\n    padding: 2px;\n    cursor: pointer;\n    overflow: hidden;\n}\n.ace_mobile-button:hover {\n    background-color: #eee;\n    opacity:1;\n}\n.ace_mobile-button:active {\n    background-color: #ddd;\n}\n\n.ace_placeholder {\n    font-family: arial;\n    transform: scale(0.9);\n    transform-origin: left;\n    white-space: pre;\n    opacity: 0.7;\n    margin: 0 10px;\n}' }), define("ace/layer/decorators", ["require", "exports", "module", "ace/lib/dom", "ace/lib/oop", "ace/lib/event_emitter"], function (e, t, i) { "use strict"; function n(e, t) { this.canvas = s.createElement("canvas"), this.renderer = t, this.pixelRatio = 1, this.maxHeight = t.layerConfig.maxHeight, this.lineHeight = t.layerConfig.lineHeight, this.canvasHeight = e.parent.scrollHeight, this.heightRatio = this.canvasHeight / this.maxHeight, this.canvasWidth = e.width, this.minDecorationHeight = 2 * this.pixelRatio | 0, this.halfMinDecorationHeight = this.minDecorationHeight / 2 | 0, this.canvas.width = this.canvasWidth, this.canvas.height = this.canvasHeight, this.canvas.style.top = "0px", this.canvas.style.right = "0px", this.canvas.style.zIndex = "7px", this.canvas.style.position = "absolute", this.colors = {}, this.colors.dark = { error: "rgba(255, 18, 18, 1)", warning: "rgba(18, 136, 18, 1)", info: "rgba(18, 18, 136, 1)" }, this.colors.light = { error: "rgb(255,51,51)", warning: "rgb(32,133,72)", info: "rgb(35,68,138)" }, e.element.appendChild(this.canvas) } var s = e("../lib/dom"), o = e("../lib/oop"), r = e("../lib/event_emitter").EventEmitter; (function () { o.implement(this, r), this.$updateDecorators = function (e) { var t = !0 === this.renderer.theme.isDark ? this.colors.dark : this.colors.light, i = (e && (this.maxHeight = e.maxHeight, this.lineHeight = e.lineHeight, this.canvasHeight = e.height, (e.lastRow + 1) * this.lineHeight < this.canvasHeight ? this.heightRatio = 1 : this.heightRatio = this.canvasHeight / this.maxHeight), this.canvas.getContext("2d")), n = this.renderer.session.$annotations; if (i.clearRect(0, 0, this.canvas.width, this.canvas.height), n) for (var s = { info: 1, warning: 2, error: 3 }, o = (n.forEach(function (e) { e.priority = s[e.type] || null }), n = n.sort(function (e, t) { return e.priority < t.priority ? -1 : e.priority > t.priority ? 1 : 0 }), this.renderer.session.$foldData), r = 0; r < n.length; r++) { var a, l = n[r].row, h = this.compensateFoldRows(l, o), c = Math.round((l - h) * this.lineHeight * this.heightRatio), u = Math.round((l - h) * this.lineHeight * this.heightRatio), l = Math.round(((l - h) * this.lineHeight + this.lineHeight) * this.heightRatio); l - u < this.minDecorationHeight && ((a = (u + l) / 2 | 0) < this.halfMinDecorationHeight ? a = this.halfMinDecorationHeight : a + this.halfMinDecorationHeight > this.canvasHeight && (a = this.canvasHeight - this.halfMinDecorationHeight), u = Math.round(a - this.halfMinDecorationHeight), l = Math.round(a + this.halfMinDecorationHeight)), i.fillStyle = t[n[r].type] || null, i.fillRect(0, c, this.canvasWidth, l - u) } e = this.renderer.session.selection.getCursor(); e && (h = this.compensateFoldRows(e.row, o), c = Math.round((e.row - h) * this.lineHeight * this.heightRatio), i.fillStyle = "rgba(0, 0, 0, 0.5)", i.fillRect(0, c, this.canvasWidth, 2)) }, this.compensateFoldRows = function (e, t) { var i = 0; if (t && 0 < t.length) for (var n = 0; n < t.length; n++)e > t[n].start.row && e < t[n].end.row ? i += e - t[n].start.row : e >= t[n].end.row && (i += t[n].end.row - t[n].start.row); return i } }).call(n.prototype), t.Decorator = n }), define("ace/virtual_renderer", ["require", "exports", "module", "ace/lib/oop", "ace/lib/dom", "ace/config", "ace/layer/gutter", "ace/layer/marker", "ace/layer/text", "ace/layer/cursor", "ace/scrollbar", "ace/scrollbar", "ace/scrollbar_custom", "ace/scrollbar_custom", "ace/renderloop", "ace/layer/font_metrics", "ace/lib/event_emitter", "ace/css/editor.css", "ace/layer/decorators", "ace/lib/useragent"], function (e, t, i) { "use strict"; function n(e, t) { var i = this, e = (this.container = e || a.createElement("div"), a.addCssClass(this.container, "ace_editor"), a.HI_DPI && a.addCssClass(this.container, "ace_hidpi"), this.setTheme(t), null == o.get("useStrictCSP") && o.set("useStrictCSP", !1), this.$gutter = a.createElement("div"), this.$gutter.className = "ace_gutter", this.container.appendChild(this.$gutter), this.$gutter.setAttribute("aria-hidden", !0), this.scroller = a.createElement("div"), this.scroller.className = "ace_scroller", this.container.appendChild(this.scroller), this.content = a.createElement("div"), this.content.className = "ace_content", this.scroller.appendChild(this.content), this.$gutterLayer = new r(this.$gutter), this.$gutterLayer.on("changeGutterWidth", this.onGutterResize.bind(this)), this.$markerBack = new l(this.content), this.$textLayer = new h(this.content)); this.canvas = e.element, this.$markerFront = new l(this.content), this.$cursorLayer = new c(this.content), this.$horizScroll = !1, this.$vScroll = !1, this.scrollBar = this.scrollBarV = new d(this.container, this), this.scrollBarH = new u(this.container, this), this.scrollBarV.on("scroll", function (e) { i.$scrollAnimation || i.session.setScrollTop(e.data - i.scrollMargin.top) }), this.scrollBarH.on("scroll", function (e) { i.$scrollAnimation || i.session.setScrollLeft(e.data - i.scrollMargin.left) }), this.scrollTop = 0, this.scrollLeft = 0, this.cursorPos = { row: 0, column: 0 }, this.$fontMetrics = new p(this.container, this.$textLayer.MAX_CHUNK_LENGTH), this.$textLayer.$setFontMetrics(this.$fontMetrics), this.$textLayer.on("changeCharacterSize", function (e) { i.updateCharacterSize(), i.onResize(!0, i.gutterWidth, i.$size.width, i.$size.height), i._signal("changeCharacterSize", e) }), this.$size = { width: 0, height: 0, scrollerHeight: 0, scrollerWidth: 0, $dirty: !0 }, this.layerConfig = { width: 1, padding: 0, firstRow: 0, firstRowScreen: 0, lastRow: 0, lineHeight: 0, characterWidth: 0, minHeight: 1, maxHeight: 1, offset: 0, height: 1, gutterOffset: 1 }, this.scrollMargin = { left: 0, right: 0, top: 0, bottom: 0, v: 0, h: 0 }, this.margin = { left: 0, right: 0, top: 0, bottom: 0, v: 0, h: 0 }, this.$keepTextAreaAtCursor = !b.isIOS, this.$loop = new m(this.$renderChanges.bind(this), this.container.ownerDocument.defaultView), this.$loop.schedule(this.CHANGE_FULL), this.updateCharacterSize(), this.setPadding(4), o.resetOptions(this), o._signal("renderer", this) } var s = e("./lib/oop"), a = e("./lib/dom"), o = e("./config"), r = e("./layer/gutter").Gutter, l = e("./layer/marker").Marker, h = e("./layer/text").Text, c = e("./layer/cursor").Cursor, u = e("./scrollbar").HScrollBar, d = e("./scrollbar").VScrollBar, g = e("./scrollbar_custom").HScrollBar, f = e("./scrollbar_custom").VScrollBar, m = e("./renderloop").RenderLoop, p = e("./layer/font_metrics").FontMetrics, w = e("./lib/event_emitter").EventEmitter, v = e("./css/editor.css"), $ = e("./layer/decorators").Decorator, b = e("./lib/useragent"), y = b.isIE; a.importCssString(v, "ace_editor.css", !1); (function () { this.CHANGE_CURSOR = 1, this.CHANGE_MARKER = 2, this.CHANGE_GUTTER = 4, this.CHANGE_SCROLL = 8, this.CHANGE_LINES = 16, this.CHANGE_TEXT = 32, this.CHANGE_SIZE = 64, this.CHANGE_MARKER_BACK = 128, this.CHANGE_MARKER_FRONT = 256, this.CHANGE_FULL = 512, this.CHANGE_H_SCROLL = 1024, s.implement(this, w), this.updateCharacterSize = function () { this.$textLayer.allowBoldFonts != this.$allowBoldFonts && (this.$allowBoldFonts = this.$textLayer.allowBoldFonts, this.setStyle("ace_nobold", !this.$allowBoldFonts)), this.layerConfig.characterWidth = this.characterWidth = this.$textLayer.getCharacterWidth(), this.layerConfig.lineHeight = this.lineHeight = this.$textLayer.getLineHeight(), this.$updatePrintMargin(), a.setStyle(this.scroller.style, "line-height", this.lineHeight + "px") }, this.setSession = function (e) { this.session && this.session.doc.off("changeNewLineMode", this.onChangeNewLineMode), (this.session = e) && this.scrollMargin.top && e.getScrollTop() <= 0 && e.setScrollTop(-this.scrollMargin.top), this.$cursorLayer.setSession(e), this.$markerBack.setSession(e), this.$markerFront.setSession(e), this.$gutterLayer.setSession(e), this.$textLayer.setSession(e), e && (this.$loop.schedule(this.CHANGE_FULL), this.session.$setFontMetrics(this.$fontMetrics), this.scrollBarH.scrollLeft = this.scrollBarV.scrollTop = null, this.onChangeNewLineMode = this.onChangeNewLineMode.bind(this), this.onChangeNewLineMode(), this.session.doc.on("changeNewLineMode", this.onChangeNewLineMode)) }, this.updateLines = function (e, t, i) { if (void 0 === t && (t = 1 / 0), this.$changedLines ? (this.$changedLines.firstRow > e && (this.$changedLines.firstRow = e), this.$changedLines.lastRow < t && (this.$changedLines.lastRow = t)) : this.$changedLines = { firstRow: e, lastRow: t }, this.$changedLines.lastRow < this.layerConfig.firstRow) { if (!i) return; this.$changedLines.lastRow = this.layerConfig.lastRow } this.$changedLines.firstRow > this.layerConfig.lastRow || this.$loop.schedule(this.CHANGE_LINES) }, this.onChangeNewLineMode = function () { this.$loop.schedule(this.CHANGE_TEXT), this.$textLayer.$updateEolChar(), this.session.$bidiHandler.setEolChar(this.$textLayer.EOL_CHAR) }, this.onChangeTabSize = function () { this.$loop.schedule(this.CHANGE_TEXT | this.CHANGE_MARKER), this.$textLayer.onChangeTabSize() }, this.updateText = function () { this.$loop.schedule(this.CHANGE_TEXT) }, this.updateFull = function (e) { e ? this.$renderChanges(this.CHANGE_FULL, !0) : this.$loop.schedule(this.CHANGE_FULL) }, this.updateFontSize = function () { this.$textLayer.checkForSizeChanges() }, this.$changes = 0, this.$updateSizeAsync = function () { this.$loop.pending ? this.$size.$dirty = !0 : this.onResize() }, this.onResize = function (e, t, i, n) { if (!(2 < this.resizing)) { 0 < this.resizing ? this.resizing++ : this.resizing = e ? 1 : 0; var s = this.container, s = (n = n || (s.clientHeight || s.scrollHeight), i = i || (s.clientWidth || s.scrollWidth), this.$updateCachedSize(e, t, i, n)); if (!this.$size.scrollerHeight || !i && !n) return this.resizing = 0; e && (this.$gutterLayer.$padding = null), e ? this.$renderChanges(s | this.$changes, !0) : this.$loop.schedule(s | this.$changes), this.resizing && (this.resizing = 0), this.scrollBarH.scrollLeft = this.scrollBarV.scrollTop = null, this.$customScrollbar && this.$updateCustomScrollbar(!0) } }, this.$updateCachedSize = function (e, t, i, n) { n -= this.$extraHeight || 0; var s = 0, o = this.$size, r = { width: o.width, height: o.height, scrollerHeight: o.scrollerHeight, scrollerWidth: o.scrollerWidth }; return n && (e || o.height != n) && (o.height = n, s |= this.CHANGE_SIZE, o.scrollerHeight = o.height, this.$horizScroll && (o.scrollerHeight -= this.scrollBarH.getHeight()), this.scrollBarV.setHeight(o.scrollerHeight), this.scrollBarV.element.style.bottom = this.scrollBarH.getHeight() + "px", s |= this.CHANGE_SCROLL), i && (e || o.width != i) && (s |= this.CHANGE_SIZE, o.width = i, null == t && (t = this.$showGutter ? this.$gutter.offsetWidth : 0), this.gutterWidth = t, a.setStyle(this.scrollBarH.element.style, "left", t + "px"), a.setStyle(this.scroller.style, "left", t + this.margin.left + "px"), o.scrollerWidth = Math.max(0, i - t - this.scrollBarV.getWidth() - this.margin.h), a.setStyle(this.$gutter.style, "left", this.margin.left + "px"), t = this.scrollBarV.getWidth() + "px", a.setStyle(this.scrollBarH.element.style, "right", t), a.setStyle(this.scroller.style, "right", t), a.setStyle(this.scroller.style, "bottom", this.scrollBarH.getHeight()), this.scrollBarH.setWidth(o.scrollerWidth), (this.session && this.session.getUseWrapMode() && this.adjustWrapLimit() || e) && (s |= this.CHANGE_FULL)), o.$dirty = !i || !n, s && this._signal("resize", r), s }, this.onGutterResize = function (e) { e = this.$showGutter ? e : 0; e != this.gutterWidth && (this.$changes |= this.$updateCachedSize(!0, e, this.$size.width, this.$size.height)), this.session.getUseWrapMode() && this.adjustWrapLimit() || this.$size.$dirty ? this.$loop.schedule(this.CHANGE_FULL) : this.$computeLayerConfig() }, this.adjustWrapLimit = function () { var e = this.$size.scrollerWidth - 2 * this.$padding, e = Math.floor(e / this.characterWidth); return this.session.adjustWrapLimit(e, this.$showPrintMargin && this.$printMarginColumn) }, this.setAnimatedScroll = function (e) { this.setOption("animatedScroll", e) }, this.getAnimatedScroll = function () { return this.$animatedScroll }, this.setShowInvisibles = function (e) { this.setOption("showInvisibles", e), this.session.$bidiHandler.setShowInvisibles(e) }, this.getShowInvisibles = function () { return this.getOption("showInvisibles") }, this.getDisplayIndentGuides = function () { return this.getOption("displayIndentGuides") }, this.setDisplayIndentGuides = function (e) { this.setOption("displayIndentGuides", e) }, this.getHighlightIndentGuides = function () { return this.getOption("highlightIndentGuides") }, this.setHighlightIndentGuides = function (e) { this.setOption("highlightIndentGuides", e) }, this.setShowPrintMargin = function (e) { this.setOption("showPrintMargin", e) }, this.getShowPrintMargin = function () { return this.getOption("showPrintMargin") }, this.setPrintMarginColumn = function (e) { this.setOption("printMarginColumn", e) }, this.getPrintMarginColumn = function () { return this.getOption("printMarginColumn") }, this.getShowGutter = function () { return this.getOption("showGutter") }, this.setShowGutter = function (e) { return this.setOption("showGutter", e) }, this.getFadeFoldWidgets = function () { return this.getOption("fadeFoldWidgets") }, this.setFadeFoldWidgets = function (e) { this.setOption("fadeFoldWidgets", e) }, this.setHighlightGutterLine = function (e) { this.setOption("highlightGutterLine", e) }, this.getHighlightGutterLine = function () { return this.getOption("highlightGutterLine") }, this.$updatePrintMargin = function () { var e; (this.$showPrintMargin || this.$printMarginEl) && (this.$printMarginEl || ((e = a.createElement("div")).className = "ace_layer ace_print-margin-layer", this.$printMarginEl = a.createElement("div"), this.$printMarginEl.className = "ace_print-margin", e.appendChild(this.$printMarginEl), this.content.insertBefore(e, this.content.firstChild)), (e = this.$printMarginEl.style).left = Math.round(this.characterWidth * this.$printMarginColumn + this.$padding) + "px", e.visibility = this.$showPrintMargin ? "visible" : "hidden", this.session && -1 == this.session.$wrap && this.adjustWrapLimit()) }, this.getContainerElement = function () { return this.container }, this.getMouseEventTarget = function () { return this.scroller }, this.getTextAreaContainer = function () { return this.container }, this.$moveTextAreaToCursor = function () { var e, t, i, n, s, o, r; this.$isMousePressed || (e = this.textarea.style, o = this.$composition, this.$keepTextAreaAtCursor || o ? (i = this.$cursorLayer.$pixelPos) && (o && o.markerRange && (i = this.$cursorLayer.getPixelPosition(o.markerRange.start, !0)), r = this.layerConfig, t = i.top, i = i.left, t -= r.offset, n = o && o.useTextareaForIME ? this.lineHeight : y ? 0 : 1, t < 0 || t > r.height - n ? a.translate(this.textarea, 0, 0) : (r = 1, s = this.$size.height - n, o ? o.useTextareaForIME ? (o = this.textarea.value, r = this.characterWidth * this.session.$getStringScreenWidth(o)[0]) : t += this.lineHeight + 2 : t += this.lineHeight, (i -= this.scrollLeft) > this.$size.scrollerWidth - r && (i = this.$size.scrollerWidth - r), i += this.gutterWidth + this.margin.left, a.setStyle(e, "height", n + "px"), a.setStyle(e, "width", r + "px"), a.translate(this.textarea, Math.min(i, this.$size.scrollerWidth - r), Math.min(t, s)))) : a.translate(this.textarea, -100, 0)) }, this.getFirstVisibleRow = function () { return this.layerConfig.firstRow }, this.getFirstFullyVisibleRow = function () { return this.layerConfig.firstRow + (0 === this.layerConfig.offset ? 0 : 1) }, this.getLastFullyVisibleRow = function () { var e = this.layerConfig, t = e.lastRow; return this.session.documentToScreenRow(t, 0) * e.lineHeight - this.session.getScrollTop() > e.height - e.lineHeight ? t - 1 : t }, this.getLastVisibleRow = function () { return this.layerConfig.lastRow }, this.$padding = null, this.setPadding = function (e) { this.$padding = e, this.$textLayer.setPadding(e), this.$cursorLayer.setPadding(e), this.$markerFront.setPadding(e), this.$markerBack.setPadding(e), this.$loop.schedule(this.CHANGE_FULL), this.$updatePrintMargin() }, this.setScrollMargin = function (e, t, i, n) { var s = this.scrollMargin; s.top = 0 | e, s.bottom = 0 | t, s.right = 0 | n, s.left = 0 | i, s.v = s.top + s.bottom, s.h = s.left + s.right, s.top && this.scrollTop <= 0 && this.session && this.session.setScrollTop(-s.top), this.updateFull() }, this.setMargin = function (e, t, i, n) { var s = this.margin; s.top = 0 | e, s.bottom = 0 | t, s.right = 0 | n, s.left = 0 | i, s.v = s.top + s.bottom, s.h = s.left + s.right, this.$updateCachedSize(!0, this.gutterWidth, this.$size.width, this.$size.height), this.updateFull() }, this.getHScrollBarAlwaysVisible = function () { return this.$hScrollBarAlwaysVisible }, this.setHScrollBarAlwaysVisible = function (e) { this.setOption("hScrollBarAlwaysVisible", e) }, this.getVScrollBarAlwaysVisible = function () { return this.$vScrollBarAlwaysVisible }, this.setVScrollBarAlwaysVisible = function (e) { this.setOption("vScrollBarAlwaysVisible", e) }, this.$updateScrollBarV = function () { var e = this.layerConfig.maxHeight, t = this.$size.scrollerHeight; !this.$maxLines && this.$scrollPastEnd && (e -= (t - this.lineHeight) * this.$scrollPastEnd, this.scrollTop > e - t && (e = this.scrollTop + t, this.scrollBarV.scrollTop = null)), this.scrollBarV.setScrollHeight(e + this.scrollMargin.v), this.scrollBarV.setScrollTop(this.scrollTop + this.scrollMargin.top) }, this.$updateScrollBarH = function () { this.scrollBarH.setScrollWidth(this.layerConfig.width + 2 * this.$padding + this.scrollMargin.h), this.scrollBarH.setScrollLeft(this.scrollLeft + this.scrollMargin.left) }, this.$frozen = !1, this.freeze = function () { this.$frozen = !0 }, this.unfreeze = function () { this.$frozen = !1 }, this.$renderChanges = function (e, t) { if (this.$changes && (e |= this.$changes, this.$changes = 0), this.session && this.container.offsetWidth && !this.$frozen && (e || t)) { if (this.$size.$dirty) return this.$changes |= e, this.onResize(!0); this.lineHeight || this.$textLayer.checkForSizeChanges(), this._signal("beforeRender", e), this.session && this.session.$bidiHandler && this.session.$bidiHandler.updateCharacterWidths(this.$fontMetrics); var i, n, t = this.layerConfig; (e & this.CHANGE_FULL || e & this.CHANGE_SIZE || e & this.CHANGE_TEXT || e & this.CHANGE_LINES || e & this.CHANGE_SCROLL || e & this.CHANGE_H_SCROLL) && (e |= this.$computeLayerConfig() | this.$loop.clear(), t.firstRow != this.layerConfig.firstRow && t.firstRowScreen == this.layerConfig.firstRowScreen && 0 < (i = this.scrollTop + (t.firstRow - this.layerConfig.firstRow) * this.lineHeight) && (this.scrollTop = i, e = (e |= this.CHANGE_SCROLL) | (this.$computeLayerConfig() | this.$loop.clear())), t = this.layerConfig, this.$updateScrollBarV(), e & this.CHANGE_H_SCROLL && this.$updateScrollBarH(), a.translate(this.content, -this.scrollLeft, -t.offset), i = t.width + 2 * this.$padding + "px", n = t.minHeight + "px", a.setStyle(this.content.style, "width", i), a.setStyle(this.content.style, "height", n)), e & this.CHANGE_H_SCROLL && (a.translate(this.content, -this.scrollLeft, -t.offset), this.scroller.className = this.scrollLeft <= 0 ? "ace_scroller" : "ace_scroller ace_scroll-left"), e & this.CHANGE_FULL ? (this.$changedLines = null, this.$textLayer.update(t), this.$showGutter && this.$gutterLayer.update(t), this.$customScrollbar && this.$scrollDecorator.$updateDecorators(t), this.$markerBack.update(t), this.$markerFront.update(t), this.$cursorLayer.update(t), this.$moveTextAreaToCursor()) : e & this.CHANGE_SCROLL ? (this.$changedLines = null, e & this.CHANGE_TEXT || e & this.CHANGE_LINES ? this.$textLayer.update(t) : this.$textLayer.scrollLines(t), this.$showGutter && (e & this.CHANGE_GUTTER || e & this.CHANGE_LINES ? this.$gutterLayer.update(t) : this.$gutterLayer.scrollLines(t)), this.$customScrollbar && this.$scrollDecorator.$updateDecorators(t), this.$markerBack.update(t), this.$markerFront.update(t), this.$cursorLayer.update(t), this.$moveTextAreaToCursor()) : (e & this.CHANGE_TEXT ? (this.$changedLines = null, this.$textLayer.update(t), this.$showGutter && this.$gutterLayer.update(t), this.$customScrollbar && this.$scrollDecorator.$updateDecorators(t)) : e & this.CHANGE_LINES ? ((this.$updateLines() || e & this.CHANGE_GUTTER && this.$showGutter) && this.$gutterLayer.update(t), this.$customScrollbar && this.$scrollDecorator.$updateDecorators(t)) : e & this.CHANGE_TEXT || e & this.CHANGE_GUTTER ? (this.$showGutter && this.$gutterLayer.update(t), this.$customScrollbar && this.$scrollDecorator.$updateDecorators(t)) : e & this.CHANGE_CURSOR && (this.$highlightGutterLine && this.$gutterLayer.updateLineHighlight(t), this.$customScrollbar && this.$scrollDecorator.$updateDecorators(t)), e & this.CHANGE_CURSOR && (this.$cursorLayer.update(t), this.$moveTextAreaToCursor()), e & (this.CHANGE_MARKER | this.CHANGE_MARKER_FRONT) && this.$markerFront.update(t), e & (this.CHANGE_MARKER | this.CHANGE_MARKER_BACK) && this.$markerBack.update(t)), void this._signal("afterRender", e) } else this.$changes |= e }, this.$autosize = function () { var e = this.session.getScreenLength() * this.lineHeight, t = this.$maxLines * this.lineHeight, i = Math.min(t, Math.max((this.$minLines || 1) * this.lineHeight, e)) + this.scrollMargin.v + (this.$extraHeight || 0); this.$horizScroll && (i += this.scrollBarH.getHeight()); t = !((i = this.$maxPixelHeight && i > this.$maxPixelHeight ? this.$maxPixelHeight : i) <= 2 * this.lineHeight) && t < e; i == this.desiredHeight && this.$size.height == this.desiredHeight && t == this.$vScroll || (t != this.$vScroll && (this.$vScroll = t, this.scrollBarV.setVisible(t)), e = this.container.clientWidth, this.container.style.height = i + "px", this.$updateCachedSize(!0, this.$gutterWidth, e, i), this.desiredHeight = i, this._signal("autosize")) }, this.$computeLayerConfig = function () { var e, t = this.session, i = this.$size, n = i.height <= 2 * this.lineHeight, s = this.session.getScreenLength() * this.lineHeight, o = this.$getLongestLine(), r = !n && (this.$hScrollBarAlwaysVisible || i.scrollerWidth - o - 2 * this.$padding < 0), a = this.$horizScroll !== r, r = (a && (this.$horizScroll = r, this.scrollBarH.setVisible(r)), this.$vScroll), l = (this.$maxLines && 1 < this.lineHeight && this.$autosize(), i.scrollerHeight + this.lineHeight), h = !this.$maxLines && this.$scrollPastEnd ? (i.scrollerHeight - this.lineHeight) * this.$scrollPastEnd : 0, c = (s += h, this.scrollMargin), n = (this.session.setScrollTop(Math.max(-c.top, Math.min(this.scrollTop, s - i.scrollerHeight + c.bottom))), this.session.setScrollLeft(Math.max(-c.left, Math.min(this.scrollLeft, o + 2 * this.$padding - i.scrollerWidth + c.right))), !n && (this.$vScrollBarAlwaysVisible || i.scrollerHeight - s + h < 0 || this.scrollTop > c.top)), h = r !== n, c = (h && (this.$vScroll = n, this.scrollBarV.setVisible(n)), this.scrollTop % this.lineHeight), r = Math.ceil(l / this.lineHeight) - 1, r = (n = Math.max(0, Math.round((this.scrollTop - c) / this.lineHeight))) + r, u = this.lineHeight, n = t.screenToDocumentRow(n, 0), d = t.getFoldLine(n), t = (d && (n = d.start.row), d = t.documentToScreenRow(n, 0), e = t.getRowLength(n) * u, r = Math.min(t.screenToDocumentRow(r, 0), t.getLength() - 1), l = i.scrollerHeight + t.getRowLength(r) * u + e, c = this.scrollTop - d * u, 0); return this.layerConfig.width == o && !a || (t = this.CHANGE_H_SCROLL), (a || h) && (t |= this.$updateCachedSize(!0, this.gutterWidth, i.width, i.height), this._signal("scrollbarVisibilityChanged"), h && (o = this.$getLongestLine())), this.layerConfig = { width: o, padding: this.$padding, firstRow: n, firstRowScreen: d, lastRow: r, lineHeight: u, characterWidth: this.characterWidth, minHeight: l, maxHeight: s, offset: c, gutterOffset: u ? Math.max(0, Math.ceil((c + i.height - i.scrollerHeight) / u)) : 0, height: this.$size.scrollerHeight }, this.session.$bidiHandler && this.session.$bidiHandler.setContentWidth(o - this.$padding), t }, this.$updateLines = function () { if (this.$changedLines) { var e = this.$changedLines.firstRow, t = this.$changedLines.lastRow, i = (this.$changedLines = null, this.layerConfig); if (!(e > i.lastRow + 1 || t < i.firstRow)) return t === 1 / 0 ? (this.$showGutter && this.$gutterLayer.update(i), void this.$textLayer.update(i)) : (this.$textLayer.updateLines(i, e, t), !0) } }, this.$getLongestLine = function () { var e = this.session.getScreenWidth(); return this.showInvisibles && !this.session.$useWrapMode && (e += 1), this.$textLayer && e > this.$textLayer.MAX_LINE_LENGTH && (e = this.$textLayer.MAX_LINE_LENGTH + 30), Math.max(this.$size.scrollerWidth - 2 * this.$padding, Math.round(e * this.characterWidth)) }, this.updateFrontMarkers = function () { this.$markerFront.setMarkers(this.session.getMarkers(!0)), this.$loop.schedule(this.CHANGE_MARKER_FRONT) }, this.updateBackMarkers = function () { this.$markerBack.setMarkers(this.session.getMarkers()), this.$loop.schedule(this.CHANGE_MARKER_BACK) }, this.addGutterDecoration = function (e, t) { this.$gutterLayer.addGutterDecoration(e, t) }, this.removeGutterDecoration = function (e, t) { this.$gutterLayer.removeGutterDecoration(e, t) }, this.updateBreakpoints = function (e) { this.$loop.schedule(this.CHANGE_GUTTER) }, this.setAnnotations = function (e) { this.$gutterLayer.setAnnotations(e), this.$loop.schedule(this.CHANGE_GUTTER) }, this.updateCursor = function () { this.$loop.schedule(this.CHANGE_CURSOR) }, this.hideCursor = function () { this.$cursorLayer.hideCursor() }, this.showCursor = function () { this.$cursorLayer.showCursor() }, this.scrollSelectionIntoView = function (e, t, i) { this.scrollCursorIntoView(e, i), this.scrollCursorIntoView(t, i) }, this.scrollCursorIntoView = function (e, t, i) { var n, s, o; 0 !== this.$size.scrollerHeight && (n = (e = this.$cursorLayer.getPixelPosition(e)).left, e = e.top, o = i && i.top || 0, i = i && i.bottom || 0, this.$scrollAnimation && (this.$stopAnimation = !0), e < (s = this.$scrollAnimation ? this.session.getScrollTop() : this.scrollTop) + o ? (t && s + o > e + this.lineHeight && (e -= t * this.$size.scrollerHeight), 0 === e && (e = -this.scrollMargin.top), this.session.setScrollTop(e)) : s + this.$size.scrollerHeight - i < e + this.lineHeight && (t && s + this.$size.scrollerHeight - i < e - this.lineHeight && (e += t * this.$size.scrollerHeight), this.session.setScrollTop(e + this.lineHeight + i - this.$size.scrollerHeight)), n < (o = this.scrollLeft) ? (n < this.$padding + 2 * this.layerConfig.characterWidth && (n = -this.scrollMargin.left), this.session.setScrollLeft(n)) : o + this.$size.scrollerWidth < n + this.characterWidth ? this.session.setScrollLeft(Math.round(n + this.characterWidth - this.$size.scrollerWidth)) : o <= this.$padding && n - o < this.characterWidth && this.session.setScrollLeft(0)) }, this.getScrollTop = function () { return this.session.getScrollTop() }, this.getScrollLeft = function () { return this.session.getScrollLeft() }, this.getScrollTopRow = function () { return this.scrollTop / this.lineHeight }, this.getScrollBottomRow = function () { return Math.max(0, Math.floor((this.scrollTop + this.$size.scrollerHeight) / this.lineHeight) - 1) }, this.scrollToRow = function (e) { this.session.setScrollTop(e * this.lineHeight) }, this.alignCursor = function (e, t) { var e = this.$cursorLayer.getPixelPosition(e = "number" == typeof e ? { row: e, column: 0 } : e), i = this.$size.scrollerHeight - this.lineHeight, e = e.top - i * (t || 0); return this.session.setScrollTop(e), e }, this.STEPS = 8, this.$calcSteps = function (e, t) { for (var i, n, s = 0, o = this.STEPS, r = [], s = 0; s < o; ++s)r.push((i = s / this.STEPS, (t - (n = e)) * (Math.pow(i - 1, 3) + 1) + n)); return r }, this.scrollToLine = function (e, t, i, n) { e = this.$cursorLayer.getPixelPosition({ row: e, column: 0 }).top, t && (e -= this.$size.scrollerHeight / 2), t = this.scrollTop; this.session.setScrollTop(e), !1 !== i && this.animateScrolling(t, n) }, this.animateScrolling = function (e, t) { function i() { s.$timer = clearInterval(s.$timer), s.$scrollAnimation = null, s.$stopAnimation = !1, t && t() } var n = this.scrollTop; if (this.$animatedScroll) { var s = this; if (e != n) { if (this.$scrollAnimation) { var o = this.$scrollAnimation.steps; if (o.length && (e = o[0]) == n) return } var r = s.$calcSteps(e, n); this.$scrollAnimation = { from: e, to: n, steps: r }, clearInterval(this.$timer), s.session.setScrollTop(r.shift()), s.session.$scrollTop = n, this.$timer = setInterval(function () { if (!s.$stopAnimation) return s.session ? void (r.length ? (s.session.setScrollTop(r.shift()), s.session.$scrollTop = n) : null != n ? (s.session.$scrollTop = -1, s.session.setScrollTop(n), n = null) : i()) : clearInterval(s.$timer); i() }, 10) } } }, this.scrollToY = function (e) { this.scrollTop !== e && (this.$loop.schedule(this.CHANGE_SCROLL), this.scrollTop = e) }, this.scrollToX = function (e) { this.scrollLeft !== e && (this.scrollLeft = e), this.$loop.schedule(this.CHANGE_H_SCROLL) }, this.scrollTo = function (e, t) { this.session.setScrollTop(t), this.session.setScrollLeft(e) }, this.scrollBy = function (e, t) { t && this.session.setScrollTop(this.session.getScrollTop() + t), e && this.session.setScrollLeft(this.session.getScrollLeft() + e) }, this.isScrollableBy = function (e, t) { return t < 0 && this.session.getScrollTop() >= 1 - this.scrollMargin.top || (0 < t && this.session.getScrollTop() + this.$size.scrollerHeight - this.layerConfig.maxHeight < -1 + this.scrollMargin.bottom || (e < 0 && this.session.getScrollLeft() >= 1 - this.scrollMargin.left || (0 < e && this.session.getScrollLeft() + this.$size.scrollerWidth - this.layerConfig.width < -1 + this.scrollMargin.right || void 0))) }, this.pixelToScreenCoordinates = function (e, t) { this.$hasCssTransforms ? (n = { top: 0, left: 0 }, e = (i = this.$fontMetrics.transformCoordinates([e, t]))[1] - this.gutterWidth - this.margin.left, t = i[0]) : n = this.scroller.getBoundingClientRect(); var i = e + this.scrollLeft - n.left - this.$padding, e = i / this.characterWidth, t = Math.floor((t + this.scrollTop - n.top) / this.lineHeight), n = this.$blockCursor ? Math.floor(e) : Math.round(e); return { row: t, column: n, side: 0 < e - n ? 1 : -1, offsetX: i } }, this.screenToTextCoordinates = function (e, t) { this.$hasCssTransforms ? (i = { top: 0, left: 0 }, e = (n = this.$fontMetrics.transformCoordinates([e, t]))[1] - this.gutterWidth - this.margin.left, t = n[0]) : i = this.scroller.getBoundingClientRect(); var i, n = e + this.scrollLeft - i.left - this.$padding, e = n / this.characterWidth, e = this.$blockCursor ? Math.floor(e) : Math.round(e), t = Math.floor((t + this.scrollTop - i.top) / this.lineHeight); return this.session.screenToDocumentPosition(t, Math.max(e, 0), n) }, this.textToScreenCoordinates = function (e, t) { var i = this.scroller.getBoundingClientRect(), t = this.session.documentToScreenPosition(e, t), e = this.$padding + (this.session.$bidiHandler.isBidiRow(t.row, e) ? this.session.$bidiHandler.getPosLeft(t.column) : Math.round(t.column * this.characterWidth)), t = t.row * this.lineHeight; return { pageX: i.left + e - this.scrollLeft, pageY: i.top + t - this.scrollTop } }, this.visualizeFocus = function () { a.addCssClass(this.container, "ace_focus") }, this.visualizeBlur = function () { a.removeCssClass(this.container, "ace_focus") }, this.showComposition = function (e) { (this.$composition = e).cssText || (e.cssText = this.textarea.style.cssText), null == e.useTextareaForIME && (e.useTextareaForIME = this.$useTextareaForIME), this.$useTextareaForIME ? (a.addCssClass(this.textarea, "ace_composition"), this.textarea.style.cssText = "", this.$moveTextAreaToCursor(), this.$cursorLayer.element.style.display = "none") : e.markerId = this.session.addMarker(e.markerRange, "ace_composition_marker", "text") }, this.setCompositionText = function (e) { var t = this.session.selection.cursor; this.addToken(e, "composition_placeholder", t.row, t.column), this.$moveTextAreaToCursor() }, this.hideComposition = function () { var e; this.$composition && (this.$composition.markerId && this.session.removeMarker(this.$composition.markerId), a.removeCssClass(this.textarea, "ace_composition"), this.textarea.style.cssText = this.$composition.cssText, e = this.session.selection.cursor, this.removeExtraToken(e.row, e.column), this.$composition = null, this.$cursorLayer.element.style.display = "") }, this.addToken = function (e, t, i, n) { var s = this.session, o = (s.bgTokenizer.lines[i] = null, { type: t, value: e }), r = s.getTokens(i); if (null == n) r.push(o); else for (var a = 0, l = 0; l < r.length; l++) { var h = r[l]; if (n <= (a += h.value.length)) { var c = h.value.length - (a - n), u = h.value.slice(0, c), c = h.value.slice(c); r.splice(l, 1, { type: h.type, value: u }, o, { type: h.type, value: c }); break } } this.updateLines(i, i) }, this.removeExtraToken = function (e, t) { this.updateLines(e, e) }, this.setTheme = function (i, n) { function e(e) { if (s.$themeId != i) return n && n(); if (!e || !e.cssClass) throw new Error("couldn't load module " + i + " or it didn't call define"); e.$id && (s.$themeId = e.$id), a.importCssString(e.cssText, e.cssClass, s.container), s.theme && a.removeCssClass(s.container, s.theme.cssClass); var t = "padding" in e ? e.padding : "padding" in (s.theme || {}) ? 4 : s.$padding; s.$padding && t != s.$padding && s.setPadding(t), s.$theme = e.cssClass, s.theme = e, a.addCssClass(s.container, e.cssClass), a.setCssClass(s.container, "ace_dark", e.isDark), s.$size && (s.$size.width = 0, s.$updateSizeAsync()), s._dispatchEvent("themeLoaded", { theme: e }), n && n() } var t, s = this; this.$themeId = i, s._dispatchEvent("themeChange", { theme: i }), i && "string" != typeof i ? e(i) : (t = i || this.$options.theme.initialValue, o.loadModule(["theme", t], e)) }, this.getTheme = function () { return this.$themeId }, this.setStyle = function (e, t) { a.setCssClass(this.container, e, !1 !== t) }, this.unsetStyle = function (e) { a.removeCssClass(this.container, e) }, this.setCursorStyle = function (e) { a.setStyle(this.scroller.style, "cursor", e) }, this.setMouseCursor = function (e) { a.setStyle(this.scroller.style, "cursor", e) }, this.attachToShadowRoot = function () { a.importCssString(v, "ace_editor.css", this.container) }, this.destroy = function () { this.freeze(), this.$fontMetrics.destroy(), this.$cursorLayer.destroy(), this.removeAllListeners(), this.container.textContent = "" }, this.$updateCustomScrollbar = function (e) { var t = this; this.$horizScroll = this.$vScroll = null, this.scrollBarV.element.remove(), this.scrollBarH.element.remove(), this.$scrollDecorator && delete this.$scrollDecorator, !0 === e ? (this.scrollBarV = new f(this.container, this), this.scrollBarH = new g(this.container, this), this.scrollBarV.setHeight(this.$size.scrollerHeight), this.scrollBarH.setWidth(this.$size.scrollerWidth), this.scrollBarV.addEventListener("scroll", function (e) { t.$scrollAnimation || t.session.setScrollTop(e.data - t.scrollMargin.top) }), this.scrollBarH.addEventListener("scroll", function (e) { t.$scrollAnimation || t.session.setScrollLeft(e.data - t.scrollMargin.left) }), this.$scrollDecorator = new $(this.scrollBarV, this), this.$scrollDecorator.$updateDecorators()) : (this.scrollBarV = new d(this.container, this), this.scrollBarH = new u(this.container, this), this.scrollBarV.addEventListener("scroll", function (e) { t.$scrollAnimation || t.session.setScrollTop(e.data - t.scrollMargin.top) }), this.scrollBarH.addEventListener("scroll", function (e) { t.$scrollAnimation || t.session.setScrollLeft(e.data - t.scrollMargin.left) })) } }).call(n.prototype), o.defineOptions(n.prototype, "renderer", { animatedScroll: { initialValue: !1 }, showInvisibles: { set: function (e) { this.$textLayer.setShowInvisibles(e) && this.$loop.schedule(this.CHANGE_TEXT) }, initialValue: !1 }, showPrintMargin: { set: function () { this.$updatePrintMargin() }, initialValue: !0 }, printMarginColumn: { set: function () { this.$updatePrintMargin() }, initialValue: 80 }, printMargin: { set: function (e) { "number" == typeof e && (this.$printMarginColumn = e), this.$showPrintMargin = !!e, this.$updatePrintMargin() }, get: function () { return this.$showPrintMargin && this.$printMarginColumn } }, showGutter: { set: function (e) { this.$gutter.style.display = e ? "block" : "none", this.$loop.schedule(this.CHANGE_FULL), this.onGutterResize() }, initialValue: !0 }, fadeFoldWidgets: { set: function (e) { a.setCssClass(this.$gutter, "ace_fade-fold-widgets", e) }, initialValue: !1 }, showFoldWidgets: { set: function (e) { this.$gutterLayer.setShowFoldWidgets(e), this.$loop.schedule(this.CHANGE_GUTTER) }, initialValue: !0 }, displayIndentGuides: { set: function (e) { this.$textLayer.setDisplayIndentGuides(e) && this.$loop.schedule(this.CHANGE_TEXT) }, initialValue: !0 }, highlightIndentGuides: { set: function (e) { 1 == this.$textLayer.setHighlightIndentGuides(e) ? this.$textLayer.$highlightIndentGuide() : this.$textLayer.$clearActiveIndentGuide(this.$textLayer.$lines.cells) }, initialValue: !0 }, highlightGutterLine: { set: function (e) { this.$gutterLayer.setHighlightGutterLine(e), this.$loop.schedule(this.CHANGE_GUTTER) }, initialValue: !0 }, hScrollBarAlwaysVisible: { set: function (e) { this.$hScrollBarAlwaysVisible && this.$horizScroll || this.$loop.schedule(this.CHANGE_SCROLL) }, initialValue: !1 }, vScrollBarAlwaysVisible: { set: function (e) { this.$vScrollBarAlwaysVisible && this.$vScroll || this.$loop.schedule(this.CHANGE_SCROLL) }, initialValue: !1 }, fontSize: { set: function (e) { "number" == typeof e && (e += "px"), this.container.style.fontSize = e, this.updateFontSize() }, initialValue: 12 }, fontFamily: { set: function (e) { this.container.style.fontFamily = e, this.updateFontSize() } }, maxLines: { set: function (e) { this.updateFull() } }, minLines: { set: function (e) { this.$minLines < 562949953421311 || (this.$minLines = 0), this.updateFull() } }, maxPixelHeight: { set: function (e) { this.updateFull() }, initialValue: 0 }, scrollPastEnd: { set: function (e) { this.$scrollPastEnd != (e = +e || 0) && (this.$scrollPastEnd = e, this.$loop.schedule(this.CHANGE_SCROLL)) }, initialValue: 0, handlesSet: !0 }, fixedWidthGutter: { set: function (e) { this.$gutterLayer.$fixedWidth = !!e, this.$loop.schedule(this.CHANGE_GUTTER) } }, customScrollbar: { set: function (e) { this.$updateCustomScrollbar(e) }, initialValue: !1 }, theme: { set: function (e) { this.setTheme(e) }, get: function () { return this.$themeId || this.theme }, initialValue: "./theme/textmate", handlesSet: !0 }, hasCssTransforms: {}, useTextareaForIME: { initialValue: !b.isMobile && !b.isIE } }), t.VirtualRenderer = n }), define("ace/worker/worker_client", ["require", "exports", "module", "ace/lib/oop", "ace/lib/net", "ace/lib/event_emitter", "ace/config"], function (a, e, t) { "use strict"; function l(e) { return "undefined" == typeof Worker ? { postMessage: function () { }, terminate: function () { } } : u.get("loadWorkerFromBlob") ? (t = function (t) { t = "importScripts('" + n.qualifyURL(t) + "');"; try { return new Blob([t], { type: "application/javascript" }) } catch (e) { var i = new (window.BlobBuilder || window.WebKitBlobBuilder || window.MozBlobBuilder); return i.append(t), i.getBlob("application/javascript") } }(e), t = (window.URL || window.webkitURL).createObjectURL(t), new Worker(t)) : new Worker(e); var t } function h(e) { e.postMessage || (e = this.$createWorkerFromOldConfig.apply(this, arguments)), this.$worker = e, this.$sendDeltaQueue = this.$sendDeltaQueue.bind(this), this.changeListener = this.changeListener.bind(this), this.onMessage = this.onMessage.bind(this), this.callbackId = 1, this.callbacks = {}, this.$worker.onmessage = this.onMessage } var i = a("../lib/oop"), n = a("../lib/net"), c = a("../lib/event_emitter").EventEmitter, u = a("../config"); (function () { i.implement(this, c), this.$createWorkerFromOldConfig = function (e, t, i, n, s) { var o, r; return a.nameToUrl && !a.toUrl && (a.toUrl = a.nameToUrl), u.get("packaged") || !a.toUrl ? n = n || u.moduleUrl(t, "worker") : (o = this.$normalizePath, n = n || o(a.toUrl("ace/worker/worker.js", null, "_")), r = {}, e.forEach(function (e) { r[e] = o(a.toUrl(e, null, "_").replace(/(\.js)?(\?.*)?$/, "")) })), this.$worker = l(n), s && this.send("importScripts", s), this.$worker.postMessage({ init: !0, tlns: r, module: t, classname: i }), this.$worker }, this.onMessage = function (e) { var t = e.data; switch (t.type) { case "event": this._signal(t.name, { data: t.data }); break; case "call": var i = this.callbacks[t.id]; i && (i(t.data), delete this.callbacks[t.id]); break; case "error": this.reportError(t.data); break; case "log": window.console && console.log && console.log.apply(console, t.data) } }, this.reportError = function (e) { window.console && console.error && console.error(e) }, this.$normalizePath = function (e) { return n.qualifyURL(e) }, this.terminate = function () { this._signal("terminate", {}), this.deltaQueue = null, this.$worker.terminate(), this.$worker = null, this.$doc && this.$doc.off("change", this.changeListener), this.$doc = null }, this.send = function (e, t) { this.$worker.postMessage({ command: e, args: t }) }, this.call = function (e, t, i) { var n; i && (n = this.callbackId++, this.callbacks[n] = i, t.push(n)), this.send(e, t) }, this.emit = function (e, t) { try { t.data && t.data.err && (t.data.err = { message: t.data.err.message, stack: t.data.err.stack, code: t.data.err.code }), this.$worker && this.$worker.postMessage({ event: e, data: { data: t.data } }) } catch (e) { console.error(e.stack) } }, this.attachToDocument = function (e) { this.$doc && this.terminate(), this.$doc = e, this.call("setValue", [e.getValue()]), e.on("change", this.changeListener, !0) }, this.changeListener = function (e) { this.deltaQueue || (this.deltaQueue = [], setTimeout(this.$sendDeltaQueue, 0)), "insert" == e.action ? this.deltaQueue.push(e.start, e.lines) : this.deltaQueue.push(e.start, e.end) }, this.$sendDeltaQueue = function () { var e = this.deltaQueue; e && (this.deltaQueue = null, 50 < e.length && e.length > this.$doc.getLength() >> 1 ? this.call("setValue", [this.$doc.getValue()]) : this.emit("change", { data: e })) } }).call(h.prototype); e.UIWorkerClient = function (e, t, i) { var n = null, s = !1, o = Object.create(c), r = [], a = new h({ messageBuffer: r, terminate: function () { }, postMessage: function (e) { r.push(e), n && (s ? setTimeout(l) : l()) } }), l = (a.setEmitSync = function (e) { s = e }, function () { var e = r.shift(); e.command ? n[e.command].apply(n, e.args) : e.event && o._signal(e.event, e.data) }); return o.postMessage = function (e) { a.onMessage({ data: e }) }, o.callback = function (e, t) { this.postMessage({ type: "call", id: t, data: e }) }, o.emit = function (e, t) { this.postMessage({ type: "event", name: e, data: t }) }, u.loadModule(["worker", t], function (e) { for (n = new e[i](o); r.length;)l() }), a }, e.WorkerClient = h, e.createWorker = l }), define("ace/placeholder", ["require", "exports", "module", "ace/range", "ace/lib/event_emitter", "ace/lib/oop"], function (e, t, i) { "use strict"; function n(e, t, i, n, s, o) { var r = this, t = (this.length = t, this.session = e, this.doc = e.getDocument(), this.mainClass = s, this.othersClass = o, this.$onUpdate = this.onUpdate.bind(this), this.doc.on("change", this.$onUpdate, !0), this.$others = n, this.$onCursorChange = function () { setTimeout(function () { r.onCursorChange() }) }, this.$pos = i, e.getUndoManager().$undoStack || e.getUndoManager().$undostack || { length: -1 }); this.$undoStackDepth = t.length, this.setup(), e.selection.on("changeCursor", this.$onCursorChange) } var l = e("./range").Range, s = e("./lib/event_emitter").EventEmitter, o = e("./lib/oop"); (function () { o.implement(this, s), this.setup = function () { var t = this, i = this.doc, e = this.session, n = (this.selectionBefore = e.selection.toJSON(), e.selection.inMultiSelectMode && e.selection.toSingleRange(), this.pos = i.createAnchor(this.$pos.row, this.$pos.column), this.pos); n.$insertRight = !0, n.detach(), n.markerId = e.addMarker(new l(n.row, n.column, n.row, n.column + this.length), this.mainClass, null, !1), this.others = [], this.$others.forEach(function (e) { e = i.createAnchor(e.row, e.column); e.$insertRight = !0, e.detach(), t.others.push(e) }), e.setUndoSelect(!1) }, this.showOtherMarkers = function () { var t, i; this.othersActive || (t = this.session, (i = this).othersActive = !0, this.others.forEach(function (e) { e.markerId = t.addMarker(new l(e.row, e.column, e.row, e.column + i.length), i.othersClass, null, !1) })) }, this.hideOtherMarkers = function () { if (this.othersActive) { this.othersActive = !1; for (var e = 0; e < this.others.length; e++)this.session.removeMarker(this.others[e].markerId) } }, this.onUpdate = function (e) { if (this.$updating) return this.updateAnchors(e); var t = e; if (t.start.row === t.end.row && t.start.row === this.pos.row) { this.$updating = !0; var i = "insert" === e.action ? t.end.column - t.start.column : t.start.column - t.end.column, n = t.start.column >= this.pos.column && t.start.column <= this.pos.column + this.length + 1, s = t.start.column - this.pos.column; if (this.updateAnchors(e), n && (this.length += i), n && !this.session.$fromUndo) if ("insert" === e.action) for (var o = this.others.length - 1; 0 <= o; o--) { var r = { row: (a = this.others[o]).row, column: a.column + s }; this.doc.insertMergedLines(r, e.lines) } else if ("remove" === e.action) for (o = this.others.length - 1; 0 <= o; o--) { var a, r = { row: (a = this.others[o]).row, column: a.column + s }; this.doc.remove(new l(r.row, r.column, r.row, r.column - i)) } this.$updating = !1, this.updateMarkers() } }, this.updateAnchors = function (e) { this.pos.onChange(e); for (var t = this.others.length; t--;)this.others[t].onChange(e); this.updateMarkers() }, this.updateMarkers = function () { if (!this.$updating) { var i = this, n = this.session, e = function (e, t) { n.removeMarker(e.markerId), e.markerId = n.addMarker(new l(e.row, e.column, e.row, e.column + i.length), t, null, !1) }; e(this.pos, this.mainClass); for (var t = this.others.length; t--;)e(this.others[t], this.othersClass) } }, this.onCursorChange = function (e) { var t; !this.$updating && this.session && ((t = this.session.selection.getCursor()).row === this.pos.row && t.column >= this.pos.column && t.column <= this.pos.column + this.length ? (this.showOtherMarkers(), this._emit("cursorEnter", e)) : (this.hideOtherMarkers(), this._emit("cursorLeave", e))) }, this.detach = function () { this.session.removeMarker(this.pos && this.pos.markerId), this.hideOtherMarkers(), this.doc.off("change", this.$onUpdate), this.session.selection.off("changeCursor", this.$onCursorChange), this.session.setUndoSelect(!0), this.session = null }, this.cancel = function () { if (-1 !== this.$undoStackDepth) { for (var e = this.session.getUndoManager(), t = (e.$undoStack || e.$undostack).length - this.$undoStackDepth, i = 0; i < t; i++)e.undo(this.session, !0); this.selectionBefore && this.session.selection.fromJSON(this.selectionBefore) } } }).call(n.prototype), t.PlaceHolder = n }), define("ace/mouse/multi_select_handler", ["require", "exports", "module", "ace/lib/event", "ace/lib/useragent"], function (e, t, i) { function x(e, t) { return e.row == t.row && e.column == t.column } var A = e("../lib/event"), L = e("../lib/useragent"); t.onMouseDown = function (e) { var t = e.domEvent, i = t.altKey, n = t.shiftKey, s = t.ctrlKey, o = e.getAccelKey(), r = e.getButton(); if (s && L.isMac && (r = t.button), e.editor.inMultiSelectMode && 2 == r) e.editor.textInput.onContextMenu(e.domEvent); else if (s || i || o) { if (0 === r) { var a, l, h, c, u, d, g, f, m = e.editor, p = m.selection, w = m.inMultiSelectMode, v = e.getDocumentPosition(), $ = p.getCursor(), $ = e.inSelection() || p.isEmpty() && x(v, $), b = e.x, y = e.y, C = m.session, S = m.renderer.pixelToScreenCoordinates(b, y), k = S; if (m.$mouseHandler.$enableJumpToDef) s && i || o && i ? a = n ? "block" : "add" : i && m.$blockSelectEnabled && (a = "block"); else if (o && !i) { if (a = "add", !w && n) return } else i && m.$blockSelectEnabled && (a = "block"); if (a && L.isMac && t.ctrlKey && m.$mouseHandler.cancelContextMenu(), "add" == a) !w && $ || (w || (l = p.toOrientedRange(), m.addSelectionMarker(l)), h = p.rangeList.rangeAtPoint(v), m.inVirtualSelectionMode = !0, n && (h = null, l = p.ranges[0] || l, m.removeSelectionMarker(l)), m.once("mouseup", function () { var e = p.toOrientedRange(); h && e.isEmpty() && x(h.cursor, e.cursor) ? p.substractPoint(e.cursor) : (n ? p.substractPoint(l.cursor) : l && (m.removeSelectionMarker(l), p.addRange(l)), p.addRange(e)), m.inVirtualSelectionMode = !1 })); else if ("block" == a) return e.stop(), m.inVirtualSelectionMode = !0, u = [], d = function () { var e = m.renderer.pixelToScreenCoordinates(b, y), t = C.screenToDocumentPosition(e.row, e.column, e.offsetX); x(k, e) && x(t, p.lead) || (k = e, m.selection.moveToPosition(t), m.renderer.scrollCursorIntoView(), m.removeSelectionMarkers(u), u = p.rectangularRangeBlock(k, S), m.$mouseHandler.$clickSelection && 1 == u.length && u[0].isEmpty() && (u[0] = m.$mouseHandler.$clickSelection.clone()), u.forEach(m.addSelectionMarker, m), m.updateSelectionMarkers()) }, w && !o ? p.toSingleRange() : !w && o && (c = p.toOrientedRange(), m.addSelectionMarker(c)), n ? S = C.documentToScreenPosition(p.lead) : p.moveToPosition(v), k = { row: -1, column: -1 }, g = d, A.capture(m.container, function (e) { b = e.clientX, y = e.clientY }, function (e) { d(), clearInterval(f), m.removeSelectionMarkers(u), u.length || (u = [p.toOrientedRange()]), c && (m.removeSelectionMarker(c), p.toSingleRange(c)); for (var t = 0; t < u.length; t++)p.addRange(u[t]); m.inVirtualSelectionMode = !1, m.$mouseHandler.$clickSelection = null }), f = setInterval(function () { g() }, 20), e.preventDefault() } } else 0 === r && e.editor.inMultiSelectMode && e.editor.exitMultiSelectMode() } }), define("ace/commands/multi_select_commands", ["require", "exports", "module", "ace/keyboard/hash_handler"], function (e, t, i) { t.defaultCommands = [{ name: "addCursorAbove", description: "Add cursor above", exec: function (e) { e.selectMoreLines(-1) }, bindKey: { win: "Ctrl-Alt-Up", mac: "Ctrl-Alt-Up" }, scrollIntoView: "cursor", readOnly: !0 }, { name: "addCursorBelow", description: "Add cursor below", exec: function (e) { e.selectMoreLines(1) }, bindKey: { win: "Ctrl-Alt-Down", mac: "Ctrl-Alt-Down" }, scrollIntoView: "cursor", readOnly: !0 }, { name: "addCursorAboveSkipCurrent", description: "Add cursor above (skip current)", exec: function (e) { e.selectMoreLines(-1, !0) }, bindKey: { win: "Ctrl-Alt-Shift-Up", mac: "Ctrl-Alt-Shift-Up" }, scrollIntoView: "cursor", readOnly: !0 }, { name: "addCursorBelowSkipCurrent", description: "Add cursor below (skip current)", exec: function (e) { e.selectMoreLines(1, !0) }, bindKey: { win: "Ctrl-Alt-Shift-Down", mac: "Ctrl-Alt-Shift-Down" }, scrollIntoView: "cursor", readOnly: !0 }, { name: "selectMoreBefore", description: "Select more before", exec: function (e) { e.selectMore(-1) }, bindKey: { win: "Ctrl-Alt-Left", mac: "Ctrl-Alt-Left" }, scrollIntoView: "cursor", readOnly: !0 }, { name: "selectMoreAfter", description: "Select more after", exec: function (e) { e.selectMore(1) }, bindKey: { win: "Ctrl-Alt-Right", mac: "Ctrl-Alt-Right" }, scrollIntoView: "cursor", readOnly: !0 }, { name: "selectNextBefore", description: "Select next before", exec: function (e) { e.selectMore(-1, !0) }, bindKey: { win: "Ctrl-Alt-Shift-Left", mac: "Ctrl-Alt-Shift-Left" }, scrollIntoView: "cursor", readOnly: !0 }, { name: "selectNextAfter", description: "Select next after", exec: function (e) { e.selectMore(1, !0) }, bindKey: { win: "Ctrl-Alt-Shift-Right", mac: "Ctrl-Alt-Shift-Right" }, scrollIntoView: "cursor", readOnly: !0 }, { name: "toggleSplitSelectionIntoLines", description: "Split into lines", exec: function (e) { 1 < e.multiSelect.rangeCount ? e.multiSelect.joinSelections() : e.multiSelect.splitIntoLines() }, bindKey: { win: "Ctrl-Alt-L", mac: "Ctrl-Alt-L" }, readOnly: !0 }, { name: "splitSelectionIntoLines", description: "Split into lines", exec: function (e) { e.multiSelect.splitIntoLines() }, readOnly: !0 }, { name: "alignCursors", description: "Align cursors", exec: function (e) { e.alignCursors() }, bindKey: { win: "Ctrl-Alt-A", mac: "Ctrl-Alt-A" }, scrollIntoView: "cursor" }, { name: "findAll", description: "Find all", exec: function (e) { e.findAll() }, bindKey: { win: "Ctrl-Alt-K", mac: "Ctrl-Alt-G" }, scrollIntoView: "cursor", readOnly: !0 }], t.multiSelectCommands = [{ name: "singleSelection", description: "Single selection", bindKey: "esc", exec: function (e) { e.exitMultiSelectMode() }, scrollIntoView: "cursor", readOnly: !0, isAvailable: function (e) { return e && e.inMultiSelectMode } }]; e = e("../keyboard/hash_handler").HashHandler; t.keyboardHandler = new e(t.multiSelectCommands) }), define("ace/multi_select", ["require", "exports", "module", "ace/range_list", "ace/range", "ace/selection", "ace/mouse/multi_select_handler", "ace/lib/event", "ace/lib/lang", "ace/commands/multi_select_commands", "ace/search", "ace/edit_session", "ace/editor", "ace/config"], function (e, s, t) { function i(e) { var t, i; function n(e) { i && (t.renderer.setMouseCursor(""), i = !1) } e.$multiselectOnSessionChange || (e.$onAddRange = e.$onAddRange.bind(e), e.$onRemoveRange = e.$onRemoveRange.bind(e), e.$onMultiSelect = e.$onMultiSelect.bind(e), e.$onSingleSelect = e.$onSingleSelect.bind(e), e.$multiselectOnSessionChange = s.onSessionChange.bind(e), e.$checkMultiselectChange = e.$checkMultiselectChange.bind(e), e.$multiselectOnSessionChange(e), e.on("changeSession", e.$multiselectOnSessionChange), e.on("mousedown", o), e.commands.addCommands(a.defaultCommands), (t = e).textInput && (e = t.textInput.getElement(), i = !1, r.addListener(e, "keydown", function (e) { e = 18 == e.keyCode && !(e.ctrlKey || e.shiftKey || e.metaKey); t.$blockSelectEnabled && e ? i || (t.renderer.setMouseCursor("crosshair"), i = !0) : i && n() }, t), r.addListener(e, "keyup", n, t), r.addListener(e, "blur", n, t))) } var n = e("./range_list").RangeList, b = e("./range").Range, d = e("./selection").Selection, o = e("./mouse/multi_select_handler").onMouseDown, r = e("./lib/event"), g = e("./lib/lang"), a = e("./commands/multi_select_commands"); s.commands = a.defaultCommands.concat(a.multiSelectCommands); var l = new (e("./search").Search), h = e("./edit_session").EditSession, h = (function () { this.getSelectionMarkers = function () { return this.$selectionMarkers } }.call(h.prototype), function () { this.ranges = null, this.rangeList = null, this.addRange = function (e, t) { if (e) { if (!this.inMultiSelectMode && 0 === this.rangeCount) { var i = this.toOrientedRange(); if (this.rangeList.add(i), this.rangeList.add(e), 2 != this.rangeList.ranges.length) return this.rangeList.removeAll(), t || this.fromOrientedRange(e); this.rangeList.removeAll(), this.rangeList.add(i), this.$onAddRange(i) } e.cursor || (e.cursor = e.end); i = this.rangeList.add(e); return this.$onAddRange(e), i.length && this.$onRemoveRange(i), 1 < this.rangeCount && !this.inMultiSelectMode && (this._signal("multiSelect"), this.inMultiSelectMode = !0, this.session.$undoSelect = !1, this.rangeList.attach(this.session)), t || this.fromOrientedRange(e) } }, this.toSingleRange = function (e) { e = e || this.ranges[0]; var t = this.rangeList.removeAll(); t.length && this.$onRemoveRange(t), e && this.fromOrientedRange(e) }, this.substractPoint = function (e) { e = this.rangeList.substractPoint(e); if (e) return this.$onRemoveRange(e), e[0] }, this.mergeOverlappingRanges = function () { var e = this.rangeList.merge(); e.length && this.$onRemoveRange(e) }, this.$onAddRange = function (e) { this.rangeCount = this.rangeList.ranges.length, this.ranges.unshift(e), this._signal("addRange", { range: e }) }, this.$onRemoveRange = function (e) { var t; this.rangeCount = this.rangeList.ranges.length, 1 == this.rangeCount && this.inMultiSelectMode && (t = this.rangeList.ranges.pop(), e.push(t), this.rangeCount = 0); for (var i = e.length; i--;) { var n = this.ranges.indexOf(e[i]); this.ranges.splice(n, 1) } this._signal("removeRange", { ranges: e }), 0 === this.rangeCount && this.inMultiSelectMode && (this.inMultiSelectMode = !1, this._signal("singleSelect"), this.session.$undoSelect = !0, this.rangeList.detach(this.session)), (t = t || this.ranges[0]) && !t.isEqual(this.getRange()) && this.fromOrientedRange(t) }, this.$initRangeList = function () { this.rangeList || (this.rangeList = new n, this.ranges = [], this.rangeCount = 0) }, this.getAllRanges = function () { return this.rangeCount ? this.rangeList.ranges.concat() : [this.getRange()] }, this.splitIntoLines = function () { for (var e = this.ranges.length ? this.ranges : [this.getRange()], t = [], i = 0; i < e.length; i++) { var n = e[i], s = n.start.row, o = n.end.row; if (s === o) t.push(n.clone()); else { for (t.push(new b(s, n.start.column, s, this.session.getLine(s).length)); ++s < o;)t.push(this.getLineRange(s, !0)); t.push(new b(o, 0, o, n.end.column)) } 0 != i || this.isBackwards() || (t = t.reverse()) } this.toSingleRange(); for (i = t.length; i--;)this.addRange(t[i]) }, this.joinSelections = function () { var e = this.rangeList.ranges, t = e[e.length - 1], e = b.fromPoints(e[0].start, t.end); this.toSingleRange(), this.setSelectionRange(e, t.cursor == t.start) }, this.toggleBlockSelection = function () { var e, t; 1 < this.rangeCount ? (t = (e = this.rangeList.ranges)[e.length - 1], e = b.fromPoints(e[0].start, t.end), this.toSingleRange(), this.setSelectionRange(e, t.cursor == t.start)) : (e = this.session.documentToScreenPosition(this.cursor), t = this.session.documentToScreenPosition(this.anchor), this.rectangularRangeBlock(e, t).forEach(this.addRange, this)) }, this.rectangularRangeBlock = function (e, t, i) { var n, s, o, r, a, l, h, c = [], u = e.column < t.column, d = (r = u ? (n = e.column, s = t.column, o = e.offsetX, t.offsetX) : (n = t.column, s = e.column, o = t.offsetX, e.offsetX), e.row < t.row); l = d ? (a = e.row, t.row) : (a = t.row, e.row), n < 0 && (n = 0), (a = a < 0 ? 0 : a) == l && (i = !0); for (var g, f, m = a; m <= l; m++) { var p = b.fromPoints(this.session.screenToDocumentPosition(m, n, o), this.session.screenToDocumentPosition(m, s, r)); if (p.isEmpty()) { if (h && (g = p.end, f = h, g.row == f.row && g.column == f.column)) break; h = p.end } p.cursor = u ? p.start : p.end, c.push(p) } if (d && c.reverse(), !i) { for (var w = c.length - 1; c[w].isEmpty() && 0 < w;)w--; if (0 < w) for (var v = 0; c[v].isEmpty();)v++; for (var $ = w; v <= $; $--)c[$].isEmpty() && c.splice($, 1) } return c } }.call(d.prototype), e("./editor").Editor); (function () { this.updateSelectionMarkers = function () { this.renderer.updateCursor(), this.renderer.updateBackMarkers() }, this.addSelectionMarker = function (e) { e.cursor || (e.cursor = e.end); var t = this.getSelectionStyle(); return e.marker = this.session.addMarker(e, "ace_selection", t), this.session.$selectionMarkers.push(e), this.session.selectionMarkerCount = this.session.$selectionMarkers.length, e }, this.removeSelectionMarker = function (e) { e.marker && (this.session.removeMarker(e.marker), -1 != (e = this.session.$selectionMarkers.indexOf(e)) && this.session.$selectionMarkers.splice(e, 1), this.session.selectionMarkerCount = this.session.$selectionMarkers.length) }, this.removeSelectionMarkers = function (e) { for (var t = this.session.$selectionMarkers, i = e.length; i--;) { var n = e[i]; n.marker && (this.session.removeMarker(n.marker), -1 != (n = t.indexOf(n)) && t.splice(n, 1)) } this.session.selectionMarkerCount = t.length }, this.$onAddRange = function (e) { this.addSelectionMarker(e.range), this.renderer.updateCursor(), this.renderer.updateBackMarkers() }, this.$onRemoveRange = function (e) { this.removeSelectionMarkers(e.ranges), this.renderer.updateCursor(), this.renderer.updateBackMarkers() }, this.$onMultiSelect = function (e) { this.inMultiSelectMode || (this.inMultiSelectMode = !0, this.setStyle("ace_multiselect"), this.keyBinding.addKeyboardHandler(a.keyboardHandler), this.commands.setDefaultHandler("exec", this.$onMultiSelectExec), this.renderer.updateCursor(), this.renderer.updateBackMarkers()) }, this.$onSingleSelect = function (e) { this.session.multiSelect.inVirtualMode || (this.inMultiSelectMode = !1, this.unsetStyle("ace_multiselect"), this.keyBinding.removeKeyboardHandler(a.keyboardHandler), this.commands.removeDefaultHandler("exec", this.$onMultiSelectExec), this.renderer.updateCursor(), this.renderer.updateBackMarkers(), this._emit("changeSelection")) }, this.$onMultiSelectExec = function (e) { var t, i = e.command, n = e.editor; if (n.multiSelect) return i.multiSelectAction ? t = "forEach" == i.multiSelectAction ? n.forEachSelection(i, e.args) : "forEachLine" == i.multiSelectAction ? n.forEachSelection(i, e.args, !0) : "single" == i.multiSelectAction ? (n.exitMultiSelectMode(), i.exec(n, e.args || {})) : i.multiSelectAction(n, e.args || {}) : (t = i.exec(n, e.args || {}), n.multiSelect.addRange(n.multiSelect.toOrientedRange()), n.multiSelect.mergeOverlappingRanges()), t }, this.forEachSelection = function (e, t, i) { if (!this.inVirtualSelectionMode) { var n, s = i && i.keepOrder, o = 1 == i || i && i.$byLines, r = this.session, i = this.selection, a = i.rangeList, l = (s ? i : a).ranges; if (!l.length) return e.exec ? e.exec(this, t || {}) : e(this, t || {}); var s = i._eventRegistry, h = (i._eventRegistry = {}, new d(r)); this.inVirtualSelectionMode = !0; for (var c = l.length; c--;) { if (o) for (; 0 < c && l[c].start.row == l[c - 1].end.row;)c--; h.fromOrientedRange(l[c]), h.index = c, this.selection = r.selection = h; var u = e.exec ? e.exec(this, t || {}) : e(this, t || {}); n || void 0 === u || (n = u), h.toOrientedRange(l[c]) } h.detach(), this.selection = r.selection = i, this.inVirtualSelectionMode = !1, i._eventRegistry = s, i.mergeOverlappingRanges(), i.ranges[0] && i.fromOrientedRange(i.ranges[0]); a = this.renderer.$scrollAnimation; return this.onCursorChange(), this.onSelectionChange(), a && a.from == a.to && this.renderer.animateScrolling(a.from), n } }, this.exitMultiSelectMode = function () { this.inMultiSelectMode && !this.inVirtualSelectionMode && this.multiSelect.toSingleRange() }, this.getSelectedText = function () { var e = ""; if (this.inMultiSelectMode && !this.inVirtualSelectionMode) { for (var t = this.multiSelect.rangeList.ranges, i = [], n = 0; n < t.length; n++)i.push(this.session.getTextRange(t[n])); var s = this.session.getDocument().getNewLineCharacter(); (e = i.join(s)).length == (i.length - 1) * s.length && (e = "") } else this.selection.isEmpty() || (e = this.session.getTextRange(this.getSelectionRange())); return e }, this.$checkMultiselectChange = function (e, t) { var i; this.inMultiSelectMode && !this.inVirtualSelectionMode && (i = this.multiSelect.ranges[0], this.multiSelect.isEmpty() && t == this.multiSelect.anchor || ((i = t == this.multiSelect.anchor ? i.cursor == i.start ? i.end : i.start : i.cursor).row != t.row || this.session.$clipPositionToDocument(i.row, i.column).column != t.column ? this.multiSelect.toSingleRange(this.multiSelect.toOrientedRange()) : this.multiSelect.mergeOverlappingRanges())) }, this.findAll = function (e, t, i) { (t = t || {}).needle = e || t.needle, null == t.needle && (n = this.selection.isEmpty() ? this.selection.getWordRange() : this.selection.getRange(), t.needle = this.session.getTextRange(n)), this.$search.set(t); var n, s = this.$search.findAll(this.session); if (!s.length) return 0; var o = this.multiSelect; i || o.toSingleRange(s[0]); for (var r = s.length; r--;)o.addRange(s[r], !0); return n && o.rangeList.rangeAtPoint(n.start) && o.addRange(n, !0), s.length }, this.selectMoreLines = function (e, t) { var i, n, s, o = this.selection.toOrientedRange(), r = o.cursor == o.end, a = this.session.documentToScreenPosition(o.cursor), l = (this.selection.$desiredColumn && (a.column = this.selection.$desiredColumn), this.session.screenToDocumentPosition(a.row + e, a.column)); e = o.isEmpty() ? l : (i = this.session.documentToScreenPosition(r ? o.end : o.start), this.session.screenToDocumentPosition(i.row + e, i.column)), r ? (n = b.fromPoints(l, e)).cursor = n.start : (n = b.fromPoints(e, l)).cursor = n.end, n.desiredColumn = a.column, this.selection.inMultiSelectMode ? t && (s = o.cursor) : this.selection.addRange(o), this.selection.addRange(n), s && this.selection.substractPoint(s) }, this.transposeSelections = function (e) { for (var t = this.session, i = t.multiSelect, n = i.ranges, s = n.length; s--;)(r = n[s]).isEmpty() && (a = t.getWordRange(r.start.row, r.start.column), r.start.row = a.start.row, r.start.column = a.start.column, r.end.row = a.end.row, r.end.column = a.end.column); i.mergeOverlappingRanges(); for (var o = [], s = n.length; s--;) { var r = n[s]; o.unshift(t.getTextRange(r)) } e < 0 ? o.unshift(o.pop()) : o.push(o.shift()); for (s = n.length; s--;) { var a = (r = n[s]).clone(); t.replace(r, o[s]), r.start.row = a.start.row, r.start.column = a.start.column } i.fromOrientedRange(i.ranges[0]) }, this.selectMore = function (e, t, i) { var n, s = this.session, o = s.multiSelect.toOrientedRange(); o.isEmpty() && ((o = s.getWordRange(o.start.row, o.start.column)).cursor = -1 == e ? o.start : o.end, this.multiSelect.addRange(o), i) || (i = s.getTextRange(o), s = s, i = i, n = e, l.$options.wrap = !0, l.$options.needle = i, l.$options.backwards = -1 == n, (i = l.find(s)) && (i.cursor = -1 == e ? i.start : i.end, this.session.unfold(i), this.multiSelect.addRange(i), this.renderer.scrollCursorIntoView(null, .5)), t && this.multiSelect.substractPoint(o.cursor)) }, this.alignCursors = function () { var s = this.session, t = s.multiSelect, e = t.ranges, i = -1, n = e.filter(function (e) { if (e.cursor.row == i) return !0; i = e.cursor.row }); if (e.length && n.length != e.length - 1) { n.forEach(function (e) { t.substractPoint(e.cursor) }); var o = 0, r = 1 / 0, a = e.map(function (e) { var e = e.cursor, t = s.getLine(e.row).substr(e.column).search(/\S/g); return e.column > o && (o = e.column), (t = -1 == t ? 0 : t) < r && (r = t), t }); e.forEach(function (e, t) { var i = e.cursor, n = o - i.column, t = a[t] - r; t < n ? s.insert(i, g.stringRepeat(" ", n - t)) : s.remove(new b(i.row, i.column, i.row, i.column - n + t)), e.start.column = e.end.column = o, e.start.row = e.end.row = i.row, e.cursor = e.end }), t.fromOrientedRange(e[0]), this.renderer.updateCursor(), this.renderer.updateBackMarkers() } else { var n = this.selection.getRange(), l = n.start.row, h = n.end.row, e = l == h; if (e) { for (var c, u = this.session.getLength(); c = this.session.getLine(h), /[=:]/.test(c) && ++h < u;); for (; c = this.session.getLine(l), /[=:]/.test(c) && 0 < --l;); l < 0 && (l = 0), u <= h && (h = u - 1) } var d = this.session.removeFullLines(l, h), d = this.$reAlignText(d, e); this.session.insert({ row: l, column: 0 }, d.join("\n") + "\n"), e || (n.start.column = 0, n.end.column = d[d.length - 1].length), this.selection.setRange(n) } }, this.$reAlignText = function (e, t) { function i(e) { return g.stringRepeat(" ", e) } function n(e) { return e[2] ? i(s) + e[2] + i(o - e[2].length + r) + e[4].replace(/^([=:])\s+/, "$1 ") : e[0] } var s, o, r, a = !0, l = !0; return e.map(function (e) { var t = e.match(/(\s*)(.*?)(\s*)([=:].*)/); return t ? (null == s ? (s = t[1].length, o = t[2].length, r = t[3].length) : (s + o + r != t[1].length + t[2].length + t[3].length && (l = !1), s != t[1].length && (a = !1), s > t[1].length && (s = t[1].length), o < t[2].length && (o = t[2].length), r > t[3].length && (r = t[3].length)), t) : [e] }).map(t ? n : a ? l ? function (e) { return e[2] ? i(s + o - e[2].length) + e[2] + i(r) + e[4].replace(/^([=:])\s+/, "$1 ") : e[0] } : n : function (e) { return e[2] ? i(s) + e[2] + i(r) + e[4].replace(/^([=:])\s+/, "$1 ") : e[0] }) } }).call(h.prototype), s.onSessionChange = function (e) { var t = e.session, e = (t && !t.multiSelect && (t.$selectionMarkers = [], t.selection.$initRangeList(), t.multiSelect = t.selection), this.multiSelect = t && t.multiSelect, e.oldSession); e && (e.multiSelect.off("addRange", this.$onAddRange), e.multiSelect.off("removeRange", this.$onRemoveRange), e.multiSelect.off("multiSelect", this.$onMultiSelect), e.multiSelect.off("singleSelect", this.$onSingleSelect), e.multiSelect.lead.off("change", this.$checkMultiselectChange), e.multiSelect.anchor.off("change", this.$checkMultiselectChange)), t && (t.multiSelect.on("addRange", this.$onAddRange), t.multiSelect.on("removeRange", this.$onRemoveRange), t.multiSelect.on("multiSelect", this.$onMultiSelect), t.multiSelect.on("singleSelect", this.$onSingleSelect), t.multiSelect.lead.on("change", this.$checkMultiselectChange), t.multiSelect.anchor.on("change", this.$checkMultiselectChange)), t && this.inMultiSelectMode != t.selection.inMultiSelectMode && (t.selection.inMultiSelectMode ? this.$onMultiSelect() : this.$onSingleSelect()) }, s.MultiSelect = i, e("./config").defineOptions(h.prototype, "editor", { enableMultiselect: { set: function (e) { i(this), e ? (this.on("changeSession", this.$multiselectOnSessionChange), this.on("mousedown", o)) : (this.off("changeSession", this.$multiselectOnSessionChange), this.off("mousedown", o)) }, value: !0 }, enableBlockSelect: { set: function (e) { this.$blockSelectEnabled = e }, value: !0 } }) }), define("ace/mode/folding/fold_mode", ["require", "exports", "module", "ace/range"], function (e, t, i) { "use strict"; var c = e("../../range").Range, e = t.FoldMode = function () { }; !function () { this.foldingStartMarker = null, this.foldingStopMarker = null, this.getFoldWidget = function (e, t, i) { e = e.getLine(i); return this.foldingStartMarker.test(e) ? "start" : "markbeginend" == t && this.foldingStopMarker && this.foldingStopMarker.test(e) ? "end" : "" }, this.getFoldWidgetRange = function (e, t, i) { return null }, this.indentationBlock = function (e, t, i) { var n = /\S/, s = e.getLine(t), o = s.search(n); if (-1 != o) { for (var r, i = i || s.length, a = e.getLength(), s = t, l = t; ++t < a;) { var h = e.getLine(t).search(n); if (-1 != h) { if (h <= o) { h = e.getTokenAt(t, 0); if (!h || "string" !== h.type) break } l = t } } return s < l ? (r = e.getLine(l).length, new c(s, i, l, r)) : void 0 } }, this.openingBracketBlock = function (e, t, i, n, s) { i = { row: i, column: n + 1 }, n = e.$findClosingBracket(t, i, s); if (n) return t = e.foldWidgets[n.row], "start" == (t = null == t ? e.getFoldWidget(n.row) : t) && n.row > i.row && (n.row--, n.column = e.getLine(n.row).length), c.fromPoints(i, n) }, this.closingBracketBlock = function (e, t, i, n, s) { i = { row: i, column: n }, n = e.$findOpeningBracket(t, i); if (n) return n.column++, i.column--, c.fromPoints(n, i) } }.call(e.prototype) }), define("ace/line_widgets", ["require", "exports", "module", "ace/lib/dom"], function (e, t, i) { "use strict"; function n(e) { this.session = e, (this.session.widgetManager = this).session.getRowLength = this.getRowLength, this.session.$getWidgetScreenLength = this.$getWidgetScreenLength, this.updateOnChange = this.updateOnChange.bind(this), this.renderWidgets = this.renderWidgets.bind(this), this.measureWidgets = this.measureWidgets.bind(this), this.session._changedWidgets = [], this.$onChangeEditor = this.$onChangeEditor.bind(this), this.session.on("change", this.updateOnChange), this.session.on("changeFold", this.updateOnFold), this.session.on("changeEditor", this.$onChangeEditor) } var s = e("./lib/dom"); (function () { this.getRowLength = function (e) { var t = this.lineWidgets && this.lineWidgets[e] && this.lineWidgets[e].rowCount || 0; return this.$useWrapMode && this.$wrapData[e] ? this.$wrapData[e].length + 1 + t : 1 + t }, this.$getWidgetScreenLength = function () { var t = 0; return this.lineWidgets.forEach(function (e) { e && e.rowCount && !e.hidden && (t += e.rowCount) }), t }, this.$onChangeEditor = function (e) { this.attach(e.editor) }, this.attach = function (e) { e && e.widgetManager && e.widgetManager != this && e.widgetManager.detach(), this.editor != e && (this.detach(), (this.editor = e) && (e.widgetManager = this, e.renderer.on("beforeRender", this.measureWidgets), e.renderer.on("afterRender", this.renderWidgets))) }, this.detach = function (e) { var t = this.editor; t && (this.editor = null, t.widgetManager = null, t.renderer.off("beforeRender", this.measureWidgets), t.renderer.off("afterRender", this.renderWidgets), (t = this.session.lineWidgets) && t.forEach(function (e) { e && e.el && e.el.parentNode && (e._inDocument = !1, e.el.parentNode.removeChild(e.el)) })) }, this.updateOnFold = function (e, t) { var i = t.lineWidgets; if (i && e.action) { for (var t = e.data, n = t.start.row, s = t.end.row, o = "add" == e.action, r = n + 1; r < s; r++)i[r] && (i[r].hidden = o); i[s] && (o ? i[n] ? i[s].hidden = o : i[n] = i[s] : (i[n] == i[s] && (i[n] = void 0), i[s].hidden = o)) } }, this.updateOnChange = function (e) { var t, i, n, s = this.session.lineWidgets; s && (t = e.start.row, 0 != (i = e.end.row - t) && ("remove" == e.action ? (n = s.splice(t + 1, i), !s[t] && n[n.length - 1] && (s[t] = n.pop()), n.forEach(function (e) { e && this.removeLineWidget(e) }, this)) : (n = new Array(i), s[t] && null != s[t].column && e.start.column > s[t].column && t++, n.unshift(t, 0), s.splice.apply(s, n)), this.$updateRows())) }, this.$updateRows = function () { var i, e = this.session.lineWidgets; e && (i = !0, e.forEach(function (e, t) { if (e) for (i = !1, e.row = t; e.$oldWidget;)e.$oldWidget.row = t, e = e.$oldWidget }), i && (this.session.lineWidgets = null)) }, this.$registerLineWidget = function (e) { this.session.lineWidgets || (this.session.lineWidgets = new Array(this.session.getLength())); var t = this.session.lineWidgets[e.row]; return t && ((e.$oldWidget = t).el && t.el.parentNode && (t.el.parentNode.removeChild(t.el), t._inDocument = !1)), this.session.lineWidgets[e.row] = e }, this.addLineWidget = function (e) { if (this.$registerLineWidget(e), e.session = this.session, !this.editor) return e; var t, i = this.editor.renderer, n = (e.html && !e.el && (e.el = s.createElement("div"), e.el.innerHTML = e.html), e.el && (s.addCssClass(e.el, "ace_lineWidgetContainer"), e.el.style.position = "absolute", e.el.style.zIndex = 5, i.container.appendChild(e.el), e._inDocument = !0, e.coverGutter || (e.el.style.zIndex = 3), null == e.pixelHeight && (e.pixelHeight = e.el.offsetHeight)), null == e.rowCount && (e.rowCount = e.pixelHeight / i.layerConfig.lineHeight), this.session.getFoldAt(e.row, 0)); return (e.$fold = n) && (t = this.session.lineWidgets, e.row != n.end.row || t[n.start.row] ? e.hidden = !0 : t[n.start.row] = e), this.session._emit("changeFold", { data: { start: { row: e.row } } }), this.$updateRows(), this.renderWidgets(null, i), this.onWidgetChanged(e), e }, this.removeLineWidget = function (e) { if (e._inDocument = !1, e.session = null, e.el && e.el.parentNode && e.el.parentNode.removeChild(e.el), e.editor && e.editor.destroy) try { e.editor.destroy() } catch (e) { } if (this.session.lineWidgets) { var t = this.session.lineWidgets[e.row]; if (t == e) this.session.lineWidgets[e.row] = e.$oldWidget, e.$oldWidget && this.onWidgetChanged(e.$oldWidget); else for (; t;) { if (t.$oldWidget == e) { t.$oldWidget = e.$oldWidget; break } t = t.$oldWidget } } this.session._emit("changeFold", { data: { start: { row: e.row } } }), this.$updateRows() }, this.getWidgetsAtRow = function (e) { for (var t = this.session.lineWidgets, i = t && t[e], n = []; i;)n.push(i), i = i.$oldWidget; return n }, this.onWidgetChanged = function (e) { this.session._changedWidgets.push(e), this.editor && this.editor.renderer.updateFull() }, this.measureWidgets = function (e, t) { var i = this.session._changedWidgets, n = t.layerConfig; if (i && i.length) { for (var s = 1 / 0, o = 0; o < i.length; o++) { var r = i[o]; if (r && r.el && r.session == this.session) { if (!r._inDocument) { if (this.session.lineWidgets[r.row] != r) continue; r._inDocument = !0, t.container.appendChild(r.el) } r.h = r.el.offsetHeight, r.fixedWidth || (r.w = r.el.offsetWidth, r.screenWidth = Math.ceil(r.w / n.characterWidth)); var a = r.h / n.lineHeight; r.coverLine && ((a -= this.session.getRowLineCount(r.row)) < 0 && (a = 0)), r.rowCount != a && (r.rowCount = a, r.row < s && (s = r.row)) } } s != 1 / 0 && (this.session._emit("changeFold", { data: { start: { row: s } } }), this.session.lineWidgetWidth = null), this.session._changedWidgets = [] } }, this.renderWidgets = function (e, t) { var i = t.layerConfig, n = this.session.lineWidgets; if (n) { for (var s = Math.min(this.firstRow, i.firstRow), o = Math.max(this.lastRow, i.lastRow, n.length); 0 < s && !n[s];)s--; this.firstRow = i.firstRow, this.lastRow = i.lastRow, t.$cursorLayer.config = i; for (var r = s; r <= o; r++) { var a, l = n[r]; l && l.el && (l.hidden ? l.el.style.top = -100 - (l.pixelHeight || 0) + "px" : (l._inDocument || (l._inDocument = !0, t.container.appendChild(l.el)), a = t.$cursorLayer.getPixelPosition({ row: r, column: 0 }, !0).top, l.coverLine || (a += i.lineHeight * this.session.getRowLineCount(l.row)), l.el.style.top = a - i.offset + "px", a = l.coverGutter ? 0 : t.gutterWidth, l.fixedWidth || (a -= t.scrollLeft), l.el.style.left = a + "px", l.fullWidth && l.screenWidth && (l.el.style.minWidth = i.width + 2 * i.padding + "px"), l.fixedWidth ? l.el.style.right = t.scrollBar.getWidth() + "px" : l.el.style.right = "")) } } } }).call(n.prototype), t.LineWidgets = n }), define("ace/ext/error_marker", ["require", "exports", "module", "ace/line_widgets", "ace/lib/dom", "ace/range"], function (e, t, i) { "use strict"; function h(e, t, i) { var n = e.getAnnotations().sort(a.comparePoints); if (n.length) { var s = function (e, t, i) { for (var n = 0, s = e.length - 1; n <= s;) { var o = n + s >> 1, r = i(t, e[o]); if (0 < r) n = 1 + o; else { if (!(r < 0)) return o; s = o - 1 } } return -(n + 1) }(n, { row: t, column: -1 }, a.comparePoints), o = ((s = s < 0 ? -s - 1 : s) >= n.length ? s = 0 < i ? 0 : n.length - 1 : 0 === s && i < 0 && (s = n.length - 1), n[s]); if (o && i) { if (o.row === t) { for (; (o = n[s += i]) && o.row === t;); if (!o) return n.slice() } var r = []; for (t = o.row; r[i < 0 ? "unshift" : "push"](o), (o = n[s += i]) && o.row == t;); return r.length && r } } } var c = e("../line_widgets").LineWidgets, u = e("../lib/dom"), a = e("../range").Range; t.showErrorMarker = function (e, t) { var i, n = e.session, s = (n.widgetManager || (n.widgetManager = new c(n), n.widgetManager.attach(e)), e.getCursorPosition()), o = s.row, r = n.widgetManager.getWidgetsAtRow(o).filter(function (e) { return "errorMarker" == e.type })[0], o = (r ? r.destroy() : o -= t, h(n, o, t)); if (o) { t = o[0]; s.column = (t.pos && "number" != typeof t.column ? t.pos.sc : t.column) || 0, s.row = t.row, i = e.renderer.$gutterLayer.$annotations[s.row] } else { if (r) return; i = { text: ["Looks good!"], className: "ace_ok" } } e.session.unfold(s.row), e.selection.moveToPosition(s); function a(e, t, i) { if (0 === t && ("esc" === i || "return" === i)) return l.destroy(), { command: "null" } } var l = { row: s.row, fixedWidth: !0, coverGutter: !0, el: u.createElement("div"), type: "errorMarker" }, o = l.el.appendChild(u.createElement("div")), t = l.el.appendChild(u.createElement("div")), r = (t.className = "error_widget_arrow " + i.className, e.renderer.$cursorLayer.getPixelPosition(s).left); t.style.left = r + e.renderer.gutterWidth - 5 + "px", l.el.className = "error_widget_wrapper", o.className = "error_widget " + i.className, o.innerHTML = i.text.join("<br>"), o.appendChild(u.createElement("div")); l.destroy = function () { e.$mouseHandler.isMousePressed || (e.keyBinding.removeKeyboardHandler(a), n.widgetManager.removeLineWidget(l), e.off("changeSelection", l.destroy), e.off("changeSession", l.destroy), e.off("mouseup", l.destroy), e.off("change", l.destroy)) }, e.keyBinding.addKeyboardHandler(a), e.on("changeSelection", l.destroy), e.on("changeSession", l.destroy), e.on("mouseup", l.destroy), e.on("change", l.destroy), e.session.widgetManager.addLineWidget(l), l.el.onmousedown = e.focus.bind(e), e.renderer.scrollCursorIntoView(null, .5, { bottom: l.el.offsetHeight }) }, u.importCssString("\n    .error_widget_wrapper {\n        background: inherit;\n        color: inherit;\n        border:none\n    }\n    .error_widget {\n        border-top: solid 2px;\n        border-bottom: solid 2px;\n        margin: 5px 0;\n        padding: 10px 40px;\n        white-space: pre-wrap;\n    }\n    .error_widget.ace_error, .error_widget_arrow.ace_error{\n        border-color: #ff5a5a\n    }\n    .error_widget.ace_warning, .error_widget_arrow.ace_warning{\n        border-color: #F1D817\n    }\n    .error_widget.ace_info, .error_widget_arrow.ace_info{\n        border-color: #5a5a5a\n    }\n    .error_widget.ace_ok, .error_widget_arrow.ace_ok{\n        border-color: #5aaa5a\n    }\n    .error_widget_arrow {\n        position: absolute;\n        border: solid 5px;\n        border-top-color: transparent!important;\n        border-right-color: transparent!important;\n        border-left-color: transparent!important;\n        top: -5px;\n    }\n", "error_marker.css", !1) }), define("ace/ace", ["require", "exports", "module", "ace/lib/dom", "ace/lib/event", "ace/range", "ace/editor", "ace/edit_session", "ace/undomanager", "ace/virtual_renderer", "ace/worker/worker_client", "ace/keyboard/hash_handler", "ace/placeholder", "ace/multi_select", "ace/mode/folding/fold_mode", "ace/theme/textmate", "ace/ext/error_marker", "ace/config", "ace/loader_build"], function (e, o, t) { "use strict"; e("./loader_build")(o); var r = e("./lib/dom"), a = e("./lib/event"), i = e("./range").Range, l = e("./editor").Editor, n = e("./edit_session").EditSession, s = e("./undomanager").UndoManager, h = e("./virtual_renderer").VirtualRenderer; e("./worker/worker_client"), e("./keyboard/hash_handler"), e("./placeholder"), e("./multi_select"), e("./mode/folding/fold_mode"), e("./theme/textmate"), e("./ext/error_marker"), o.config = e("./config"), o.edit = function (e, t) { if ("string" == typeof e) { var i = e; if (!(e = document.getElementById(i))) throw new Error("ace.edit can't find div #" + i) } if (e && e.env && e.env.editor instanceof l) return e.env.editor; var n, i = "", i = (e && /input|textarea/i.test(e.tagName) ? (i = (n = e).value, e = r.createElement("pre"), n.parentNode.replaceChild(e, n)) : e && (i = e.textContent, e.innerHTML = ""), o.createEditSession(i)), e = new l(new h(e), i, t), s = { document: i, editor: e, onResize: e.resize.bind(e, null) }; return n && (s.textarea = n), a.addListener(window, "resize", s.onResize), e.on("destroy", function () { a.removeListener(window, "resize", s.onResize), s.editor.container.env = null }), e.container.env = e.env = s, e }, o.createEditSession = function (e, t) { e = new n(e, t); return e.setUndoManager(new s), e }, o.Range = i, o.Editor = l, o.EditSession = n, o.UndoManager = s, o.VirtualRenderer = h, o.version = o.config.version }), window.require(["ace/ace"], function (e) { for (var t in e && (e.config.init(!0), e.define = window.define), window.ace || (window.ace = e), e) e.hasOwnProperty(t) && (window.ace[t] = e[t]); window.ace.default = window.ace, "object" == typeof module && "object" == typeof exports && module && (module.exports = window.ace) });