function jBoxWrapper(t){function e(n,i){return this.options={id:null,width:"auto",height:"auto",minWidth:null,minHeight:null,maxWidth:null,maxHeight:null,responsiveWidth:!0,responsiveHeight:!0,responsiveMinWidth:100,responsiveMinHeight:100,attach:null,trigger:"click",preventDefault:!1,content:null,getContent:null,title:null,getTitle:null,footer:null,isolateScroll:!0,ajax:{url:null,data:"",reload:!1,getURL:"data-url",getData:"data-ajax",setContent:!0,loadingClass:!0,spinner:!0,spinnerDelay:300,spinnerReposition:!0},cancelAjaxOnClose:!0,target:null,position:{x:"center",y:"center"},outside:null,offset:0,attributes:{x:"left",y:"top"},fixed:!1,adjustPosition:!0,adjustTracker:!1,adjustDistance:5,reposition:!0,repositionOnOpen:!0,repositionOnContent:!0,holdPosition:!0,pointer:!1,pointTo:"target",fade:180,animation:null,theme:"Default",addClass:null,overlay:!1,overlayClass:null,zIndex:1e4,delayOpen:0,delayClose:0,closeOnEsc:!1,closeOnClick:!1,closeOnMouseleave:!1,closeButton:!1,appendTo:t("body"),createOnInit:!1,blockScroll:!1,blockScrollAdjust:!0,draggable:!1,dragOver:!0,autoClose:!1,delayOnHover:!1,showCountdown:!1,preloadAudio:!0,audio:null,volume:100,onInit:null,onAttach:null,onPosition:null,onCreated:null,onOpen:null,onClose:null,onCloseComplete:null,onDragStart:null,onDragEnd:null},this._pluginOptions={Tooltip:{getContent:"title",trigger:"mouseenter",position:{x:"center",y:"top"},outside:"y",pointer:!0},Mouse:{responsiveWidth:!1,responsiveHeight:!1,adjustPosition:"flip",target:"mouse",trigger:"mouseenter",position:{x:"right",y:"bottom"},outside:"xy",offset:5},Modal:{target:t(window),fixed:!0,blockScroll:!0,closeOnEsc:!0,closeOnClick:"overlay",closeButton:!0,overlay:!0,animation:"zoomIn"}},this.options=t.extend(!0,this.options,this._pluginOptions[n]?this._pluginOptions[n]:e._pluginOptions[n],i),"string"==t.type(n)&&(this.type=n),this.isTouchDevice=function(){var t,e=" -webkit- -moz- -o- -ms- ".split(" ");return!!("ontouchstart"in window||window.DocumentTouch&&document instanceof DocumentTouch)||(t=["(",e.join("touch-enabled),("),"heartz",")"].join(""),window.matchMedia(t).matches)}(),this.isTouchDevice&&"mouseenter"===this.options.trigger&&!1===this.options.closeOnClick&&(this.options.closeOnClick="body"),this._fireEvent=function(t,e){this.options["_"+t]&&this.options["_"+t].bind(this)(e),this.options[t]&&this.options[t].bind(this)(e)},null===this.options.id&&(this.options.id="jBox"+e._getUniqueID()),this.id=this.options.id,("center"==this.options.position.x&&"x"==this.options.outside||"center"==this.options.position.y&&"y"==this.options.outside)&&(this.options.outside=null),"target"==this.options.pointTo&&(!this.options.outside||"xy"==this.options.outside)&&(this.options.pointer=!1),"object"!=t.type(this.options.offset)?this.options.offset={x:this.options.offset,y:this.options.offset}:this.options.offset=t.extend({x:0,y:0},this.options.offset),"object"!=t.type(this.options.adjustDistance)?this.options.adjustDistance={top:this.options.adjustDistance,right:this.options.adjustDistance,bottom:this.options.adjustDistance,left:this.options.adjustDistance}:this.options.adjustDistance=t.extend({top:5,left:5,right:5,bottom:5},this.options.adjustDistance),this.outside=!(!this.options.outside||"xy"==this.options.outside)&&this.options.position[this.options.outside],this.align=this.outside?this.outside:"center"!=this.options.position.y&&"number"!=t.type(this.options.position.y)?this.options.position.x:"center"!=this.options.position.x&&"number"!=t.type(this.options.position.x)?this.options.position.y:this.options.attributes.x,e.zIndexMax=Math.max(e.zIndexMax||0,"auto"===this.options.zIndex?1e4:this.options.zIndex),"auto"===this.options.zIndex&&(this.adjustZIndexOnOpen=!0,e.zIndexMax+=2,this.options.zIndex=e.zIndexMax,this.trueModal=this.options.overlay),this._getOpp=function(t){return{left:"right",right:"left",top:"bottom",bottom:"top",x:"y",y:"x"}[t]},this._getXY=function(t){return{left:"x",right:"x",top:"y",bottom:"y",center:"x"}[t]},this._getTL=function(t){return{left:"left",right:"left",top:"top",bottom:"top",center:"left",x:"left",y:"top"}[t]},this._getInt=function(e,n){return"auto"==e?"auto":e&&"string"==t.type(e)&&"%"==e.slice(-1)?t(window)["height"==n?"innerHeight":"innerWidth"]()*parseInt(e.replace("%",""))/100:e},this._createSVG=function(e,n){var i=document.createElementNS("http://www.w3.org/2000/svg",e);return t.each(n,function(t,e){i.setAttribute(e[0],e[1]||"")}),i},this._isolateScroll=function(t){t&&t.length&&t.on("DOMMouseScroll.jBoxIsolateScroll mousewheel.jBoxIsolateScroll",function(e){var n=e.wheelDelta||e.originalEvent&&e.originalEvent.wheelDelta||-e.detail,i=0<=this.scrollTop+t.outerHeight()-this.scrollHeight,o=this.scrollTop<=0;(n<0&&i||0<n&&o)&&e.preventDefault()})},this._setTitleWidth=function(){if(!this.titleContainer||"auto"==this.content[0].style.width&&!this.content[0].style.maxWidth)return null;if("none"==this.wrapper.css("display")){this.wrapper.css("display","block");var t=this.content.outerWidth();this.wrapper.css("display","none")}else t=this.content.outerWidth();this.titleContainer.css({maxWidth:Math.max(t,parseInt(this.content[0].style.maxWidth))||null})},this._draggable=function(){if(!this.options.draggable)return!1;var n="title"==this.options.draggable?this.titleContainer:this.options.draggable instanceof t?this.options.draggable:"string"==t.type(this.options.draggable)?t(this.options.draggable):this.wrapper;return!(!(n&&n instanceof t&&n.length)||n.data("jBox-draggable"))&&(n.addClass("jBox-draggable").data("jBox-draggable",!0).on("touchstart mousedown",function(n){if(2!=n.button&&!t(n.target).hasClass("jBox-noDrag")&&!t(n.target).parents(".jBox-noDrag").length){this.draggingStartX=n.pageX,this.draggingStartY=n.pageY,this.options.dragOver&&!this.trueModal&&parseInt(this.wrapper.css("zIndex"),10)<=e.zIndexMaxDragover&&(e.zIndexMaxDragover+=1,this.wrapper.css("zIndex",e.zIndexMaxDragover));var i=this.wrapper.outerHeight(),o=this.wrapper.outerWidth(),s=this.wrapper.offset().top+i-n.pageY,r=this.wrapper.offset().left+o-n.pageX;t(document).on("touchmove.jBox-draggable-"+this.id+" mousemove.jBox-draggable-"+this.id,function(t){this.dragging||this.draggingStartX==t.pageX||this.draggingStartY==t.pageY||(this._fireEvent("onDragStart"),this.dragging=!0),this.wrapper.offset({top:t.pageY+s-i,left:t.pageX+r-o})}.bind(this)),n.preventDefault()}}.bind(this)).on("touchend mouseup",function(){if(t(document).off("touchmove.jBox-draggable-"+this.id+" mousemove.jBox-draggable-"+this.id),this.dragging&&this._fireEvent("onDragEnd"),this.dragging=!1,("Modal"==this.type||"Confirm"==this.type)&&this.options.holdPosition){var e=t("#"+this.id).offset(),n={x:e.left-t(document).scrollLeft(),y:e.top-t(document).scrollTop()};this.position({position:n,offset:{x:0,y:0}})}}.bind(this)),this.trueModal||(e.zIndexMaxDragover=e.zIndexMaxDragover?Math.max(e.zIndexMaxDragover,this.options.zIndex):this.options.zIndex),this)},this._create=function(){if(!this.wrapper){if(this.wrapper=t("<div/>",{id:this.id,class:"jBox-wrapper"+(this.type?" jBox-"+this.type:"")+(this.options.theme?" jBox-"+this.options.theme:"")+(this.options.addClass?" "+this.options.addClass:"")}).css({position:this.options.fixed?"fixed":"absolute",display:"none",opacity:0,zIndex:this.options.zIndex}).data("jBox",this),this.options.closeOnMouseleave&&this.wrapper.on("mouseleave",function(e){!this.source||e.relatedTarget!=this.source[0]&&-1===t.inArray(this.source[0],t(e.relatedTarget).parents("*"))&&this.close()}.bind(this)),"box"==this.options.closeOnClick&&this.wrapper.on("click",function(){this.close({ignoreDelay:!0})}.bind(this)),this.container=t('<div class="jBox-container"/>').appendTo(this.wrapper),this.content=t('<div class="jBox-content"/>').appendTo(this.container),this.options.footer&&(this.footer=t('<div class="jBox-footer"/>').append(this.options.footer).appendTo(this.container)),this.options.isolateScroll&&this._isolateScroll(this.content),this.options.closeButton){var e=this._createSVG("svg",[["viewBox","0 0 24 24"]]);e.appendChild(this._createSVG("path",[["d","M22.2,4c0,0,0.5,0.6,0,1.1l-6.8,6.8l6.9,6.9c0.5,0.5,0,1.1,0,1.1L20,22.3c0,0-0.6,0.5-1.1,0L12,15.4l-6.9,6.9c-0.5,0.5-1.1,0-1.1,0L1.7,20c0,0-0.5-0.6,0-1.1L8.6,12L1.7,5.1C1.2,4.6,1.7,4,1.7,4L4,1.7c0,0,0.6-0.5,1.1,0L12,8.5l6.8-6.8c0.5-0.5,1.1,0,1.1,0L22.2,4z"]])),this.closeButton=t('<div class="jBox-closeButton jBox-noDrag"/>').on("click",function(t){this.close({ignoreDelay:!0})}.bind(this)).append(e),"box"!=this.options.closeButton&&(!0!==this.options.closeButton||this.options.overlay||this.options.title||this.options.getTitle)||(this.wrapper.addClass("jBox-closeButton-box"),this.closeButton.appendTo(this.container))}if(this.wrapper.appendTo(this.options.appendTo),this.wrapper.find(".jBox-closeButton").length&&t.each(["top","right","bottom","left"],function(t,e){this.wrapper.find(".jBox-closeButton").css(e)&&"auto"!=this.wrapper.find(".jBox-closeButton").css(e)&&(this.options.adjustDistance[e]=Math.max(this.options.adjustDistance[e],this.options.adjustDistance[e]+-1*((parseInt(this.wrapper.find(".jBox-closeButton").css(e))||0)+(parseInt(this.container.css("border-"+e+"-width"))||0))))}.bind(this)),this.options.pointer){if(this.pointer={position:"target"!=this.options.pointTo?this.options.pointTo:this._getOpp(this.outside),xy:"target"!=this.options.pointTo?this._getXY(this.options.pointTo):this._getXY(this.outside),align:"center",offset:0},this.pointer.element=t('<div class="jBox-pointer jBox-pointer-'+this.pointer.position+'"/>').appendTo(this.wrapper),this.pointer.dimensions={x:this.pointer.element.outerWidth(),y:this.pointer.element.outerHeight()},"string"==t.type(this.options.pointer)){var n=this.options.pointer.split(":");n[0]&&(this.pointer.align=n[0]),n[1]&&(this.pointer.offset=parseInt(n[1]))}this.pointer.alignAttribute="x"==this.pointer.xy?"bottom"==this.pointer.align?"bottom":"top":"right"==this.pointer.align?"right":"left",this.wrapper.css("padding-"+this.pointer.position,this.pointer.dimensions[this.pointer.xy]),this.pointer.element.css(this.pointer.alignAttribute,"center"==this.pointer.align?"50%":0).css("margin-"+this.pointer.alignAttribute,this.pointer.offset),this.pointer.margin={},this.pointer.margin["margin-"+this.pointer.alignAttribute]=this.pointer.offset,"center"==this.pointer.align&&this.pointer.element.css("transform","translate("+("y"==this.pointer.xy?-.5*this.pointer.dimensions.x+"px":0)+", "+("x"==this.pointer.xy?-.5*this.pointer.dimensions.y+"px":0)+")"),this.pointer.element.css("x"==this.pointer.xy?"width":"height",parseInt(this.pointer.dimensions[this.pointer.xy])+parseInt(this.container.css("border-"+this.pointer.alignAttribute+"-width"))),this.wrapper.addClass("jBox-pointerPosition-"+this.pointer.position)}this.setContent(this.options.content,!0),this.setTitle(this.options.title,!0),this.options.draggable&&this._draggable(),this._fireEvent("onCreated")}},this.options.createOnInit&&this._create(),this.options.attach&&this.attach(),this._attachEvents=function(){this.options.delayOnHover&&t("#"+this.id).on("mouseenter",function(t){this.isHovered=!0}.bind(this)),this.options.delayOnHover&&t("#"+this.id).on("mouseleave",function(t){this.isHovered=!1}.bind(this)),(this.options.adjustPosition||this.options.reposition)&&!this.fixed&&this.outside&&(this.options.adjustTracker&&t(window).on("scroll.jBox-"+this.id,function(t){this.position()}.bind(this)),(this.options.adjustPosition||this.options.reposition)&&t(window).on("resize.jBox-"+this.id,function(t){this.position()}.bind(this))),"mouse"==this.options.target&&t("body").on("mousemove.jBox-"+this.id,function(t){this.position({mouseTarget:{top:t.pageY,left:t.pageX}})}.bind(this))},this._detachEvents=function(){this.options.closeOnEsc&&t(document).off("keyup.jBox-"+this.id),(!0===this.options.closeOnClick||"body"==this.options.closeOnClick)&&t(document).off("click.jBox-"+this.id),this.options.adjustTracker&&t(window).off("scroll.jBox-"+this.id),(this.options.adjustPosition||this.options.reposition)&&t(window).off("resize.jBox-"+this.id),"mouse"==this.options.target&&t("body").off("mousemove.jBox-"+this.id)},this._showOverlay=function(){this.overlay||(this.overlay=t('<div id="'+this.id+'-overlay"/>').addClass("jBox-overlay"+(this.type?" jBox-overlay-"+this.type:"")).css({display:"none",opacity:0,zIndex:this.options.zIndex-1}).appendTo(this.options.appendTo),this.options.overlayClass&&this.overlay.addClass(this.options.overlayClass),("overlay"==this.options.closeButton||!0===this.options.closeButton)&&this.overlay.append(this.closeButton),"overlay"==this.options.closeOnClick&&this.overlay.on("click",function(){this.close({ignoreDelay:!0})}.bind(this)),t("#"+this.id+"-overlay .jBox-closeButton").length&&(this.options.adjustDistance.top=Math.max(t("#"+this.id+"-overlay .jBox-closeButton").outerHeight(),this.options.adjustDistance.top))),!0===this.adjustZIndexOnOpen&&this.overlay.css("zIndex",parseInt(this.wrapper.css("zIndex"),10)-1),"block"!=this.overlay.css("display")&&(this.options.fade?this.overlay.stop()&&this.overlay.animate({opacity:1},{queue:!1,duration:this.options.fade,start:function(){this.overlay.css({display:"block"})}.bind(this)}):this.overlay.css({display:"block",opacity:1}))},this._hideOverlay=function(){this.overlay&&(this.options.fade?this.overlay.stop()&&this.overlay.animate({opacity:0},{queue:!1,duration:this.options.fade,complete:function(){this.overlay.css({display:"none"})}.bind(this)}):this.overlay.css({display:"none",opacity:0}))},this._exposeDimensions=function(){this.wrapper.css({top:-1e4,left:-1e4,right:"auto",bottom:"auto"});var t={x:this.wrapper.outerWidth(),y:this.wrapper.outerHeight()};return this.wrapper.css({top:"auto",left:"auto"}),t},this._generateAnimationCSS=function(){if("object"!=t.type(this.options.animation)&&(this.options.animation={pulse:{open:"pulse",close:"zoomOut"},zoomIn:{open:"zoomIn",close:"zoomIn"},zoomOut:{open:"zoomOut",close:"zoomOut"},move:{open:"move",close:"move"},slide:{open:"slide",close:"slide"},flip:{open:"flip",close:"flip"},tada:{open:"tada",close:"zoomOut"}}[this.options.animation]),!this.options.animation)return null;this.options.animation.open&&(this.options.animation.open=this.options.animation.open.split(":")),this.options.animation.close&&(this.options.animation.close=this.options.animation.close.split(":")),this.options.animation.openDirection=this.options.animation.open[1]?this.options.animation.open[1]:null,this.options.animation.closeDirection=this.options.animation.close[1]?this.options.animation.close[1]:null,this.options.animation.open&&(this.options.animation.open=this.options.animation.open[0]),this.options.animation.close&&(this.options.animation.close=this.options.animation.close[0]),this.options.animation.open&&(this.options.animation.open+="Open"),this.options.animation.close&&(this.options.animation.close+="Close");var e={pulse:{duration:350,css:[["0%","scale(1)"],["50%","scale(1.1)"],["100%","scale(1)"]]},zoomInOpen:{duration:this.options.fade||180,css:[["0%","scale(0.9)"],["100%","scale(1)"]]},zoomInClose:{duration:this.options.fade||180,css:[["0%","scale(1)"],["100%","scale(0.9)"]]},zoomOutOpen:{duration:this.options.fade||180,css:[["0%","scale(1.1)"],["100%","scale(1)"]]},zoomOutClose:{duration:this.options.fade||180,css:[["0%","scale(1)"],["100%","scale(1.1)"]]},moveOpen:{duration:this.options.fade||180,positions:{top:{"0%":-12},right:{"0%":12},bottom:{"0%":12},left:{"0%":-12}},css:[["0%","translate%XY(%Vpx)"],["100%","translate%XY(0px)"]]},moveClose:{duration:this.options.fade||180,timing:"ease-in",positions:{top:{"100%":-12},right:{"100%":12},bottom:{"100%":12},left:{"100%":-12}},css:[["0%","translate%XY(0px)"],["100%","translate%XY(%Vpx)"]]},slideOpen:{duration:400,positions:{top:{"0%":-400},right:{"0%":400},bottom:{"0%":400},left:{"0%":-400}},css:[["0%","translate%XY(%Vpx)"],["100%","translate%XY(0px)"]]},slideClose:{duration:400,timing:"ease-in",positions:{top:{"100%":-400},right:{"100%":400},bottom:{"100%":400},left:{"100%":-400}},css:[["0%","translate%XY(0px)"],["100%","translate%XY(%Vpx)"]]},flipOpen:{duration:600,css:[["0%","perspective(400px) rotateX(90deg)"],["40%","perspective(400px) rotateX(-15deg)"],["70%","perspective(400px) rotateX(15deg)"],["100%","perspective(400px) rotateX(0deg)"]]},flipClose:{duration:this.options.fade||300,css:[["0%","perspective(400px) rotateX(0deg)"],["100%","perspective(400px) rotateX(90deg)"]]},tada:{duration:800,css:[["0%","scale(1)"],["10%, 20%","scale(0.9) rotate(-3deg)"],["30%, 50%, 70%, 90%","scale(1.1) rotate(3deg)"],["40%, 60%, 80%","scale(1.1) rotate(-3deg)"],["100%","scale(1) rotate(0)"]]}};t.each(["pulse","tada"],function(t,n){e[n+"Open"]=e[n+"Close"]=e[n]});var n=function(n,i){return keyframe_css="@keyframes jBox-"+this.id+"-animation-"+this.options.animation[n]+"-"+n+(i?"-"+i:"")+" {",t.each(e[this.options.animation[n]].css,function(t,o){var s=i?o[1].replace("%XY",this._getXY(i).toUpperCase()):o[1];e[this.options.animation[n]].positions&&(s=s.replace("%V",e[this.options.animation[n]].positions[i][o[0]])),keyframe_css+=o[0]+" {transform:"+s+";}"}.bind(this)),keyframe_css+="}",keyframe_css+=".jBox-"+this.id+"-animation-"+this.options.animation[n]+"-"+n+(i?"-"+i:"")+" {",keyframe_css+="animation-duration: "+e[this.options.animation[n]].duration+"ms;",keyframe_css+="animation-name: jBox-"+this.id+"-animation-"+this.options.animation[n]+"-"+n+(i?"-"+i:"")+";",keyframe_css+=e[this.options.animation[n]].timing?"animation-timing-function: "+e[this.options.animation[n]].timing+";":"",keyframe_css+="}",keyframe_css}.bind(this);this._animationCSS="",t.each(["open","close"],function(i,o){if(!this.options.animation[o]||!e[this.options.animation[o]]||"close"==o&&!this.options.fade)return"";e[this.options.animation[o]].positions?t.each(["top","right","bottom","left"],function(t,e){this._animationCSS+=n(o,e)}.bind(this)):this._animationCSS+=n(o)}.bind(this))},this.options.animation&&this._generateAnimationCSS(),this._blockBodyClick=function(){this.blockBodyClick=!0,setTimeout(function(){this.blockBodyClick=!1}.bind(this),10)},this._animate=function(t){if(!t&&(t=this.isOpen?"open":"close"),!this.options.fade&&"close"==t)return null;var e=this.options.animation[t+"Direction"]||("center"!=this.align?this.align:this.options.attributes.x);this.flipped&&this._getXY(e)==this._getXY(this.align)&&(e=this._getOpp(e));var n="jBox-"+this.id+"-animation-"+this.options.animation[t]+"-"+t+" jBox-"+this.id+"-animation-"+this.options.animation[t]+"-"+t+"-"+e;this.wrapper.addClass(n);var i=1e3*parseFloat(this.wrapper.css("animation-duration"));"close"==t&&(i=Math.min(i,this.options.fade)),setTimeout(function(){this.wrapper.removeClass(n)}.bind(this),i)},this._abortAnimation=function(){var t=this.wrapper.attr("class").split(" ").filter(function(t){return 0!==t.lastIndexOf("jBox-"+this.id+"-animation",0)}.bind(this));this.wrapper.attr("class",t.join(" "))},(this.options.responsiveWidth||this.options.responsiveHeight)&&t(window).on("resize.responsivejBox-"+this.id,function(t){this.isOpen&&this.position()}.bind(this)),"string"===t.type(this.options.preloadAudio)&&(this.options.preloadAudio=[this.options.preloadAudio]),"string"===t.type(this.options.audio)&&(this.options.audio={open:this.options.audio}),"number"===t.type(this.options.volume)&&(this.options.volume={open:this.options.volume,close:this.options.volume}),!0===this.options.preloadAudio&&this.options.audio&&(this.options.preloadAudio=[],t.each(this.options.audio,function(t,e){this.options.preloadAudio.push(e+".mp3"),this.options.preloadAudio.push(e+".ogg")}.bind(this))),this.options.preloadAudio.length&&t.each(this.options.preloadAudio,function(t,e){var n=new Audio;n.src=e,n.preload="auto"}),this._fireEvent("onInit"),this}var n,i;return e.prototype.attach=function(e,n){return!e&&(e=this.options.attach),"string"==t.type(e)&&(e=t(e)),!n&&(n=this.options.trigger),e&&e.length&&t.each(e,function(e,i){(i=t(i)).data("jBox-attached-"+this.id)||("title"==this.options.getContent&&null!=i.attr("title")&&i.data("jBox-getContent",i.attr("title")).removeAttr("title"),this.attachedElements||(this.attachedElements=[]),this.attachedElements.push(i[0]),i.on(n+".jBox-attach-"+this.id,function(t){if(this.timer&&clearTimeout(this.timer),"mouseenter"!=n||!this.isOpen||this.source[0]!=i[0]){if(this.isOpen&&this.source&&this.source[0]!=i[0])var e=!0;this.source=i,!this.options.target&&(this.target=i),"click"==n&&this.options.preventDefault&&t.preventDefault(),this["click"!=n||e?"open":"toggle"]()}}.bind(this)),"mouseenter"==this.options.trigger&&i.on("mouseleave",function(e){if(!this.wrapper)return null;this.options.closeOnMouseleave&&(e.relatedTarget==this.wrapper[0]||t(e.relatedTarget).parents("#"+this.id).length)||this.close()}.bind(this)),i.data("jBox-attached-"+this.id,n),this._fireEvent("onAttach",i))}.bind(this)),this},e.prototype.detach=function(e){return!e&&(e=this.attachedElements||[]),e&&e.length&&t.each(e,function(e,n){(n=t(n)).data("jBox-attached-"+this.id)&&(n.off(n.data("jBox-attached-"+this.id)+".jBox-attach-"+this.id),n.data("jBox-attached-"+this.id,null)),this.attachedElements=t.grep(this.attachedElements,function(t){return t!=n[0]})}.bind(this)),this},e.prototype.setTitle=function(e,n){if(null==e||null==e)return this;!this.wrapper&&this._create();var i=this.wrapper.outerHeight(),o=this.wrapper.outerWidth();return this.title||(this.titleContainer=t('<div class="jBox-title"/>'),this.title=t("<div/>").appendTo(this.titleContainer),("title"==this.options.closeButton||!0===this.options.closeButton&&!this.options.overlay)&&(this.wrapper.addClass("jBox-closeButton-title"),this.closeButton.appendTo(this.titleContainer)),this.titleContainer.insertBefore(this.content),this._setTitleWidth()),this.wrapper[e?"addClass":"removeClass"]("jBox-hasTitle"),this.title.html(e),o!=this.wrapper.outerWidth()&&this._setTitleWidth(),this.options.draggable&&this._draggable(),!n&&this.options.repositionOnContent&&(i!=this.wrapper.outerHeight()||o!=this.wrapper.outerWidth())&&this.position(),this},e.prototype.setContent=function(e,n){if(null==e||null==e)return this;!this.wrapper&&this._create();var i=this.wrapper.outerHeight(),o=this.wrapper.outerWidth();switch(this.content.children("[data-jbox-content-appended]").appendTo("body").css({display:"none"}),t.type(e)){case"string":this.content.html(e);break;case"object":e instanceof t?(this.content.html(""),e.attr("data-jbox-content-appended",1).appendTo(this.content).css({display:"block"})):this.content.html(JSON.stringify(e))}return o!=this.wrapper.outerWidth()&&this._setTitleWidth(),this.options.draggable&&this._draggable(),!n&&this.options.repositionOnContent&&(i!=this.wrapper.outerHeight()||o!=this.wrapper.outerWidth())&&this.position(),this},e.prototype.setDimensions=function(t,e,n){!this.wrapper&&this._create(),null==e&&(e="auto"),this.content.css(t,this._getInt(e)),"width"==t&&this._setTitleWidth(),(null==n||n)&&this.position()},e.prototype.setWidth=function(t,e){this.setDimensions("width",t,e)},e.prototype.setHeight=function(t,e){this.setDimensions("height",t,e)},e.prototype.position=function(e){if(!e&&(e={}),e=t.extend(!0,this.options,e),this.target=e.target||this.target||t(window),!(this.target instanceof t||"mouse"==this.target)&&(this.target=t(this.target)),!this.target.length)return this;this.content.css({width:this._getInt(e.width,"width"),height:this._getInt(e.height,"height"),minWidth:this._getInt(e.minWidth,"width"),minHeight:this._getInt(e.minHeight,"height"),maxWidth:this._getInt(e.maxWidth,"width"),maxHeight:this._getInt(e.maxHeight,"height")}),this._setTitleWidth();var n=this._exposeDimensions();"mouse"!=this.target&&!this.target.data("jBox-"+this.id+"-fixed")&&this.target.data("jBox-"+this.id+"-fixed",this.target[0]!=t(window)[0]&&("fixed"==this.target.css("position")||0<this.target.parents().filter(function(){return"fixed"==t(this).css("position")}).length)?"fixed":"static");var i={x:t(window).outerWidth(),y:t(window).outerHeight(),top:e.fixed&&this.target.data("jBox-"+this.id+"-fixed")?0:t(window).scrollTop(),left:e.fixed&&this.target.data("jBox-"+this.id+"-fixed")?0:t(window).scrollLeft()};i.bottom=i.top+i.y,i.right=i.left+i.x;try{var o=this.target.offset()}catch(i){o={top:0,left:0}}"mouse"!=this.target&&"fixed"==this.target.data("jBox-"+this.id+"-fixed")&&e.fixed&&(o.top=o.top-t(window).scrollTop(),o.left=o.left-t(window).scrollLeft());var s={x:"mouse"==this.target?12:this.target.outerWidth(),y:"mouse"==this.target?20:this.target.outerHeight(),top:"mouse"==this.target&&e.mouseTarget?e.mouseTarget.top:o?o.top:0,left:"mouse"==this.target&&e.mouseTarget?e.mouseTarget.left:o?o.left:0},r=e.outside&&!("center"==e.position.x&&"center"==e.position.y),a={x:i.x-e.adjustDistance.left-e.adjustDistance.right,y:i.y-e.adjustDistance.top-e.adjustDistance.bottom,left:r?s.left-t(window).scrollLeft()-e.adjustDistance.left:0,right:r?i.x-s.left+t(window).scrollLeft()-s.x-e.adjustDistance.right:0,top:r?s.top-t(window).scrollTop()-this.options.adjustDistance.top:0,bottom:r?i.y-s.top+t(window).scrollTop()-s.y-e.adjustDistance.bottom:0},l={x:"x"!=e.outside&&"xy"!=e.outside||"number"==t.type(e.position.x)?null:e.position.x,y:"y"!=e.outside&&"xy"!=e.outside||"number"==t.type(e.position.y)?null:e.position.y},c={x:!1,y:!1};if(l.x&&n.x>a[l.x]&&a[this._getOpp(l.x)]>a[l.x]&&(l.x=this._getOpp(l.x))&&(c.x=!0),l.y&&n.y>a[l.y]&&a[this._getOpp(l.y)]>a[l.y]&&(l.y=this._getOpp(l.y))&&(c.y=!0),e.responsiveWidth||e.responsiveHeight){var d=function(){if(e.responsiveWidth&&n.x>a[l.x||"x"]){var t=a[l.x||"x"]-(this.pointer&&r&&"x"==e.outside?this.pointer.dimensions.x:0)-parseInt(this.container.css("border-left-width"))-parseInt(this.container.css("border-right-width"));this.content.css({width:t>this.options.responsiveMinWidth?t:null,minWidth:t<parseInt(this.content.css("minWidth"))?0:null}),this._setTitleWidth()}n=this._exposeDimensions()}.bind(this);e.responsiveWidth&&d(),e.responsiveWidth&&!c.y&&l.y&&n.y>a[l.y]&&a[this._getOpp(l.y)]>a[l.y]&&(l.y=this._getOpp(l.y))&&(c.y=!0);var p=function(){if(e.responsiveHeight&&n.y>a[l.y||"y"]){var t=function(){if(!this.titleContainer&&!this.footer)return 0;if("none"==this.wrapper.css("display")){this.wrapper.css("display","block");var t=(this.titleContainer?this.titleContainer.outerHeight():0)+(this.footer?this.footer.outerHeight():0);this.wrapper.css("display","none")}else t=(this.titleContainer?this.titleContainer.outerHeight():0)+(this.footer?this.footer.outerHeight():0);return t||0}.bind(this),i=a[l.y||"y"]-(this.pointer&&r&&"y"==e.outside?this.pointer.dimensions.y:0)-t()-parseInt(this.container.css("border-top-width"))-parseInt(this.container.css("border-bottom-width"));this.content.css({height:i>this.options.responsiveMinHeight?i:null}),this._setTitleWidth()}n=this._exposeDimensions()}.bind(this);e.responsiveHeight&&p(),e.responsiveHeight&&!c.x&&l.x&&n.x>a[l.x]&&a[this._getOpp(l.x)]>a[l.x]&&(l.x=this._getOpp(l.x))&&(c.x=!0),e.adjustPosition&&"move"!=e.adjustPosition&&(c.x&&d(),c.y&&p())}var u={},h=function(i){if("number"!=t.type(e.position[i])){var o=e.attributes[i]="x"==i?"left":"top";if(u[o]=s[o],"center"==e.position[i])return u[o]+=Math.ceil((s[i]-n[i])/2),void("mouse"!=this.target&&this.target[0]&&this.target[0]==t(window)[0]&&(u[o]+=.5*(e.adjustDistance[o]-e.adjustDistance[this._getOpp(o)])));o!=e.position[i]&&(u[o]+=s[i]-n[i]),(e.outside==i||"xy"==e.outside)&&(u[o]+=n[i]*(o!=e.position[i]?1:-1))}else u[e.attributes[i]]=e.position[i]}.bind(this);if(h("x"),h("y"),this.pointer&&"target"==e.pointTo&&"number"!=t.type(e.position.x)&&"number"!=t.type(e.position.y)){var f=0;switch(this.pointer.align){case"center":"center"!=e.position[this._getOpp(e.outside)]&&(f+=n[this._getOpp(e.outside)]/2);break;default:switch(e.position[this._getOpp(e.outside)]){case"center":f+=(n[this._getOpp(e.outside)]/2-this.pointer.dimensions[this._getOpp(e.outside)]/2)*(this.pointer.align==this._getTL(this.pointer.align)?1:-1);break;default:f+=this.pointer.align!=e.position[this._getOpp(e.outside)]?this.dimensions[this._getOpp(e.outside)]*(-1!==t.inArray(this.pointer.align,["top","left"])?1:-1)+this.pointer.dimensions[this._getOpp(e.outside)]/2*(-1!==t.inArray(this.pointer.align,["top","left"])?-1:1):this.pointer.dimensions[this._getOpp(e.outside)]/2*(-1!==t.inArray(this.pointer.align,["top","left"])?1:-1)}}f*=e.position[this._getOpp(e.outside)]==this.pointer.alignAttribute?-1:1,f+=this.pointer.offset*(this.pointer.align==this._getOpp(this._getTL(this.pointer.align))?1:-1),u[this._getTL(this._getOpp(this.pointer.xy))]+=f}if(u[e.attributes.x]+=e.offset.x,u[e.attributes.y]+=e.offset.y,this.wrapper.css(u),e.adjustPosition){this.positionAdjusted&&(this.pointer&&this.wrapper.css("padding",0).css("padding-"+this._getOpp(this.outside),this.pointer.dimensions[this._getXY(this.outside)]).removeClass("jBox-pointerPosition-"+this._getOpp(this.pointer.position)).addClass("jBox-pointerPosition-"+this.pointer.position),this.pointer&&this.pointer.element.attr("class","jBox-pointer jBox-pointer-"+this._getOpp(this.outside)).css(this.pointer.margin),this.positionAdjusted=!1,this.flipped=!1);var g=i.top>u.top-(e.adjustDistance.top||0),m=i.right<u.left+n.x+(e.adjustDistance.right||0),y=i.bottom<u.top+n.y+(e.adjustDistance.bottom||0),v=i.left>u.left-(e.adjustDistance.left||0),b=v?"left":m?"right":null,x=g?"top":y?"bottom":null;if(b||x){if(("Modal"==this.type||"Confirm"==this.type)&&"number"==t.type(this.options.position.x)&&"number"==t.type(this.options.position.y)){var w=0,C=0;return this.options.holdPosition&&(v?w=i.left-(u.left-(e.adjustDistance.left||0)):m&&(w=i.right-(u.left+n.x+(e.adjustDistance.right||0))),g?C=i.top-(u.top-(e.adjustDistance.top||0)):y&&(C=i.bottom-(u.top+n.y+(e.adjustDistance.bottom||0))),this.options.position.x=Math.max(i.top,this.options.position.x+w),this.options.position.y=Math.max(i.left,this.options.position.y+C),h("x"),h("y"),this.wrapper.css(u)),this._fireEvent("onPosition"),this}if(!0===e.adjustPosition||"flip"===e.adjustPosition){var T=function(t){this.wrapper.css(this._getTL(t),u[this._getTL(t)]+(n[this._getXY(t)]+e.offset[this._getXY(t)]*("top"==t||"left"==t?-2:2)+s[this._getXY(t)])*("top"==t||"left"==t?1:-1)),this.pointer&&this.wrapper.removeClass("jBox-pointerPosition-"+this.pointer.position).addClass("jBox-pointerPosition-"+this._getOpp(this.pointer.position)).css("padding",0).css("padding-"+t,this.pointer.dimensions[this._getXY(t)]),this.pointer&&this.pointer.element.attr("class","jBox-pointer jBox-pointer-"+t),this.positionAdjusted=!0,this.flipped=!0}.bind(this);c.x&&T(this.options.position.x),c.y&&T(this.options.position.y)}var j="x"==this._getXY(this.outside)?x:b;if(this.pointer&&"target"==e.pointTo&&"flip"!=e.adjustPosition&&this._getXY(j)==this._getOpp(this._getXY(this.outside))){if("center"==this.pointer.align)var E=n[this._getXY(j)]/2-this.pointer.dimensions[this._getOpp(this.pointer.xy)]/2-parseInt(this.pointer.element.css("margin-"+this.pointer.alignAttribute))*(j!=this._getTL(j)?-1:1);else E=j==this.pointer.alignAttribute?parseInt(this.pointer.element.css("margin-"+this.pointer.alignAttribute)):n[this._getXY(j)]-parseInt(this.pointer.element.css("margin-"+this.pointer.alignAttribute))-this.pointer.dimensions[this._getXY(j)];var S=j==this._getTL(j)?i[this._getTL(j)]-u[this._getTL(j)]+e.adjustDistance[j]:-1*(i[this._getOpp(this._getTL(j))]-u[this._getTL(j)]-e.adjustDistance[j]-n[this._getXY(j)]);j==this._getOpp(this._getTL(j))&&u[this._getTL(j)]-S<i[this._getTL(j)]+e.adjustDistance[this._getTL(j)]&&(S-=i[this._getTL(j)]+e.adjustDistance[this._getTL(j)]-(this.pos[this._getTL(j)]-S)),(S=Math.min(S,E))<=E&&0<S&&(this.pointer.element.css("margin-"+this.pointer.alignAttribute,parseInt(this.pointer.element.css("margin-"+this.pointer.alignAttribute))-S*(j!=this.pointer.alignAttribute?-1:1)),this.wrapper.css(this._getTL(j),u[this._getTL(j)]+S*(j!=this._getTL(j)?-1:1)),this.positionAdjusted=!0)}}}return this._fireEvent("onPosition"),this},(e.prototype.unscroll=function(t){if(this.set=function(t,e){window.unscrollStore||(window.unscrollStore={}),window.unscrollStore[t]=e},this.get=function(t){return window.unscrollStore?window.unscrollStore[t]:null},this.getScrollbarWidth=function(){if(this.get("scrollbarWidth"))return this.get("scrollbarWidth")+"px";var t=document.createElement("div");t.style.width="100px",t.style.height="100px",t.style.overflow="scroll",t.style.position="absolute",t.style.top="-10000",document.body.appendChild(t);var e=t.offsetWidth-t.clientWidth;return document.body.removeChild(t),this.set("scrollbarWidth",e),e+"px"},this.getElementsToAdjust=function(t){!t&&(t=[]),"string"==typeof t&&(t=[[t,"padding-right"]]),t.forEach(function(e,n){"string"==typeof e&&(t[n]=[e,"padding-right"])});for(var e=!1,n=0;n<t.length;n++)-1!==t[n][0].indexOf("body")&&(e=!0);return!1===e&&t.push(["body","padding-right"]),t},this.pageHasScrollbar=function(){return this.getScrollbarWidth()&&document.body.offsetHeight>window.innerHeight},this.pageHasScrollbar()){t=this.getElementsToAdjust(t);for(var e=0;e<t.length;e++)for(var n=document.querySelectorAll(t[e][0]),i=0;i<n.length;i++){if(n[i].getAttribute("data-unscroll"))return;var o=t[e][1],s=window.getComputedStyle(n[i]).getPropertyValue(o);n[i].setAttribute("data-unscroll",o),s||(s="0px");var r="padding-right"==o||"right"==o?"+":"-";n[i].style[o]="calc("+s+" "+r+" "+this.getScrollbarWidth()+")"}}!function(){if(!document.getElementById("unscroll-class-name")){var t=document.head||document.getElementsByTagName("head")[0],e=document.createElement("style");e.type="text/css",e.setAttribute("id","unscroll-class-name"),e.appendChild(document.createTextNode(".unscrollable { overflow: hidden !important; }")),t.appendChild(e)}}(),document.body.classList.add("unscrollable")}).reset=function(){for(var t=document.querySelectorAll("[data-unscroll]"),e=0;e<t.length;e++){var n=t[e].getAttribute("data-unscroll");t[e].style[n]=null,t[e].removeAttribute("data-unscroll")}document.body.classList.remove("unscrollable")},e.prototype.open=function(n){if(!n&&(n={}),this.isDestroyed)return this;if(!this.wrapper&&this._create(),!this._styles&&(this._styles=t("<style/>").append(this._animationCSS).appendTo(t("head"))),this.timer&&clearTimeout(this.timer),this._blockBodyClick(),this.isDisabled)return this;this.options.closeOnEsc&&t(document).on("keyup.jBox-"+this.id,function(t){27==t.keyCode&&this.close({ignoreDelay:!0})}.bind(this)),!0!==this.options.closeOnClick&&"body"!==this.options.closeOnClick||(t("body").on("click.jBox-"+this.id,function(t){this.blockBodyClick||"body"==this.options.closeOnClick&&(t.target==this.wrapper[0]||this.wrapper.has(t.target).length)||this.close({ignoreDelay:!0})}.bind(this)),this.isTouchDevice&&t("body > *").on("click.jBox-"+this.id,function(){return!0}));var i=function(){!0===this.adjustZIndexOnOpen&&(e.zIndexMax=Math.max(parseInt(this.wrapper.css("zIndex"),10),this.options.zIndex,e.zIndexMax||0,e.zIndexMaxDragover||0)+2,this.wrapper.css("zIndex",e.zIndexMax),this.options.zIndex=e.zIndexMax),this.source&&this.options.getTitle&&this.source.attr(this.options.getTitle)&&this.setTitle(this.source.attr(this.options.getTitle),!0),this.source&&this.options.getContent&&(this.source.data("jBox-getContent")?this.setContent(this.source.data("jBox-getContent"),!0):this.source.attr(this.options.getContent)?this.setContent(this.source.attr(this.options.getContent),!0):"html"==this.options.getContent&&this.setContent(this.source.html(),!0)),this._fireEvent("onOpen"),(this.options.ajax&&(this.options.ajax.url||this.source&&this.source.attr(this.options.ajax.getURL))&&(!this.ajaxLoaded||this.options.ajax.reload)||n.ajax&&(n.ajax.url||n.ajax.data))&&("strict"==this.options.ajax.reload||!this.source||!this.source.data("jBox-ajax-data")||n.ajax&&(n.ajax.url||n.ajax.data)?this.ajax(n.ajax||null,!0):this.setContent(this.source.data("jBox-ajax-data"))),(!this.positionedOnOpen||this.options.repositionOnOpen)&&this.position(n)&&(this.positionedOnOpen=!0),this.isClosing&&this._abortAnimation(),this.isOpen||(this.isOpen=!0,this.options.autoClose&&(this.options.delayClose=this.options.autoClose)&&this.close(),this._attachEvents(),this.options.blockScroll&&(this.options.blockScrollAdjust?e.blockScrollScopes?e.blockScrollScopes++:(e.blockScrollScopes=1,this.unscroll(Array.isArray(this.options.blockScrollAdjust)||"string"==typeof this.options.blockScrollAdjust?this.options.blockScrollAdjust:null)):t("body").addClass("jBox-blockScroll-"+this.id)),this.options.overlay&&(this._showOverlay(),this.position()),this.options.animation&&!this.isClosing&&this._animate("open"),this.options.audio&&this.options.audio.open&&this.audio(this.options.audio.open,this.options.volume.open),this.options.fade?this.wrapper.stop().animate({opacity:1},{queue:!1,duration:this.options.fade,start:function(){this.isOpening=!0,this.wrapper.css({display:"block"})}.bind(this),always:function(){this.isOpening=!1,setTimeout(function(){this.positionOnFadeComplete&&this.position()&&(this.positionOnFadeComplete=!1)}.bind(this),10)}.bind(this)}):(this.wrapper.css({display:"block",opacity:1}),this.positionOnFadeComplete&&this.position()&&(this.positionOnFadeComplete=!1)))}.bind(this);return!this.options.delayOpen||this.isOpen||this.isClosing||n.ignoreDelay?i():this.timer=setTimeout(i,this.options.delayOpen),this},e.prototype.close=function(n){if(n||(n={}),t("body").off("click.jBox-"+this.id),this.isTouchDevice&&t("body > *").off("click.jBox-"+this.id),this.isDestroyed||this.isClosing)return this;if(this.timer&&clearTimeout(this.timer),this._blockBodyClick(),this.isDisabled)return this;var i=function(){if(this._fireEvent("onClose"),this.options.cancelAjaxOnClose&&this.cancelAjax(),this.isOpen){this.isOpen=!1,this._detachEvents(),this.options.blockScroll&&(this.options.blockScrollAdjust?!(e.blockScrollScopes=e.blockScrollScopes?--e.blockScrollScopes:0)&&this.unscroll.reset():t("body").removeClass("jBox-blockScroll-"+this.id)),this.options.overlay&&this._hideOverlay(),this.options.animation&&!this.isOpening&&this._animate("close"),this.options.audio&&this.options.audio.close&&this.audio(this.options.audio.close,this.options.volume.close);var n=this.isTouchDevice&&"mouse"==this.options.target?0:this.options.fade;n?this.wrapper.stop().animate({opacity:0},{queue:!1,duration:n,start:function(){this.isClosing=!0}.bind(this),complete:function(){this.wrapper.css({display:"none"}),this._fireEvent("onCloseComplete")}.bind(this),always:function(){this.isClosing=!1}.bind(this)}):(this.wrapper.css({display:"none",opacity:0}),this._fireEvent("onCloseComplete"))}}.bind(this);if(n.ignoreDelay||this.isTouchDevice&&"mouse"==this.options.target)i();else if((this.options.delayOnHover||this.options.showCountdown)&&10<this.options.delayClose){var o=this,s=this.options.delayClose,r=Date.now();if(this.options.showCountdown&&!this.inner){var a=t('<div class="jBox-countdown" />');this.inner=t('<div class="jBox-countdown-inner" />'),a.prepend(this.inner),t("#"+this.id).append(a)}this.countdown=function(){var t=Date.now();o.isHovered||(s-=t-r),r=t,0<s?(o.options.showCountdown&&o.inner.css("width",100*s/o.options.delayClose+"%"),window.requestAnimationFrame(o.countdown)):i()},window.requestAnimationFrame(this.countdown)}else this.timer=setTimeout(i,Math.max(this.options.delayClose,10));return this},e.prototype.toggle=function(t){return this[this.isOpen?"close":"open"](t),this},e.prototype.disable=function(){return this.isDisabled=!0,this},e.prototype.enable=function(){return this.isDisabled=!1,this},e.prototype.hide=function(){return this.disable(),this.wrapper&&this.wrapper.css({display:"none"}),this},e.prototype.show=function(){return this.enable(),this.wrapper&&this.wrapper.css({display:"block"}),this},e.prototype.ajax=function(e,n){e||(e={}),t.each([["getData","data"],["getURL","url"]],function(t,n){this.options.ajax[n[0]]&&!e[n[1]]&&this.source&&null!=this.source.attr(this.options.ajax[n[0]])&&(e[n[1]]=this.source.attr(this.options.ajax[n[0]])||"")}.bind(this));var i=t.extend(!0,{},this.options.ajax);this.cancelAjax();var o=e.beforeSend||i.beforeSend||function(){},s=e.complete||i.complete||function(){},r=e.success||i.success||function(){},a=e.error||i.error||function(){},l=t.extend(!0,i,e);return l.beforeSend=function(e){l.loadingClass&&this.wrapper.addClass(!0===l.loadingClass?"jBox-loading":l.loadingClass),l.spinner&&(this.spinnerDelay=setTimeout(function(){this.wrapper.addClass("jBox-loading-spinner"),l.spinnerReposition&&(n?this.positionOnFadeComplete=!0:this.position()),this.spinner=t(!0!==l.spinner?l.spinner:'<div class="jBox-spinner"></div>').appendTo(this.container),this.titleContainer&&"absolute"==this.spinner.css("position")&&this.spinner.css({transform:"translateY("+.5*this.titleContainer.outerHeight()+"px)"})}.bind(this),""==this.content.html()?0:l.spinnerDelay||0)),o.bind(this)(e)}.bind(this),l.complete=function(t){this.spinnerDelay&&clearTimeout(this.spinnerDelay),this.wrapper.removeClass("jBox-loading jBox-loading-spinner jBox-loading-spinner-delay"),this.spinner&&this.spinner.length&&this.spinner.remove()&&l.spinnerReposition&&(n?this.positionOnFadeComplete=!0:this.position()),this.ajaxLoaded=!0,s.bind(this)(t)}.bind(this),l.success=function(t){l.setContent&&this.setContent(t,!0)&&(n?this.positionOnFadeComplete=!0:this.position()),l.setContent&&this.source&&this.source.data("jBox-ajax-data",t),r.bind(this)(t)}.bind(this),l.error=function(t){a.bind(this)(t)}.bind(this),this.ajaxRequest=t.ajax(l),this},e.prototype.cancelAjax=function(){this.ajaxRequest&&(this.ajaxRequest.abort(),this.ajaxLoaded=!1)},e.prototype.audio=function(n,i){if(!n)return this;if(!e._audio&&(e._audio={}),!e._audio[n]){var o=t("<audio/>");t("<source/>",{src:n+".mp3"}).appendTo(o),t("<source/>",{src:n+".ogg"}).appendTo(o),e._audio[n]=o[0]}e._audio[n].volume=Math.min((null!=i?i:100)/100,1);try{e._audio[n].pause(),e._audio[n].currentTime=0}catch(n){}return e._audio[n].play(),this},e._animationSpeeds={tada:1e3,tadaSmall:1e3,flash:500,shake:400,pulseUp:250,pulseDown:250,popIn:250,popOut:250,fadeIn:200,fadeOut:200,slideUp:400,slideRight:400,slideLeft:400,slideDown:400},e.prototype.animate=function(t,n){!n&&(n={}),!this.animationTimeout&&(this.animationTimeout={}),!n.element&&(n.element=this.wrapper),!n.element.data("jBox-animating-id")&&n.element.data("jBox-animating-id",e._getUniqueElementID()),n.element.data("jBox-animating")&&(n.element.removeClass(n.element.data("jBox-animating")).data("jBox-animating",null),this.animationTimeout[n.element.data("jBox-animating-id")]&&clearTimeout(this.animationTimeout[n.element.data("jBox-animating-id")])),n.element.addClass("jBox-animated-"+t).data("jBox-animating","jBox-animated-"+t),this.animationTimeout[n.element.data("jBox-animating-id")]=setTimeout(function(){n.element.removeClass(n.element.data("jBox-animating")).data("jBox-animating",null),n.complete&&n.complete()},e._animationSpeeds[t])},e.prototype.destroy=function(){return this.detach(),this.isOpen&&this.close({ignoreDelay:!0}),this.wrapper&&this.wrapper.remove(),this.overlay&&this.overlay.remove(),this._styles&&this._styles.remove(),this.isDestroyed=!0,this},e._getUniqueID=(n=1,function(){return n++}),e._getUniqueElementID=(i=1,function(){return i++}),e._pluginOptions={},e.plugin=function(t,n){e._pluginOptions[t]=n},t.fn.jBox=function(n,i){return!n&&(n={}),!i&&(i={}),new e(n,t.extend(i,{attach:this}))},e}function jBoxConfirmWrapper(jBox,jQuery){new jBox.plugin("Confirm",{confirmButton:"Submit",cancelButton:"Cancel",confirm:null,cancel:null,closeOnConfirm:!0,target:window,fixed:!0,attach:"[data-confirm]",getContent:"data-confirm",content:"Do you really want to do this?",minWidth:360,maxWidth:500,blockScroll:!0,closeOnEsc:!0,closeOnClick:!1,closeButton:!1,overlay:!0,animation:"zoomIn",preventDefault:!0,_onAttach:function(t){if(!this.options.confirm){var e=t.attr("onclick")?t.attr("onclick"):t.attr("href")?t.attr("target")?'window.open("'+t.attr("href")+'", "'+t.attr("target")+'");':'window.location.href = "'+t.attr("href")+'";':"";t.prop("onclick",null).data("jBox-Confirm-submit",e)}},_onCreated:function(){this.wrapper.addClass("jBox-Modal"),this.footer=jQuery('<div class="jBox-Confirm-footer"/>'),jQuery('<div class="jBox-Confirm-button jBox-Confirm-button-cancel"/>').html(this.options.cancelButton).click(function(){this.options.cancel&&this.options.cancel(this.source),this.close()}.bind(this)).appendTo(this.footer),this.submitButton=jQuery('<div class="jBox-Confirm-button jBox-Confirm-button-submit"/>').html(this.options.confirmButton).appendTo(this.footer),this.footer.appendTo(this.container)},_onOpen:function(){this.submitButton.off("click.jBox-Confirm"+this.id).on("click.jBox-Confirm"+this.id,function(){this.options.confirm?this.options.confirm(this.source):eval(this.source.data("jBox-Confirm-submit")),this.options.closeOnConfirm&&this.close()}.bind(this))}})}function jBoxImageWrapper(t,e){new t.plugin("Image",{src:"href",gallery:"data-jbox-image",imageLabel:"title",imageFade:360,imageSize:"contain",imageCounter:!1,imageCounterSeparator:"/",downloadButton:!1,downloadButtonText:null,downloadButtonUrl:null,mobileImageAttr:null,mobileImageBreakpoint:null,preloadFirstImage:!1,target:window,attach:"[data-jbox-image]",fixed:!0,blockScroll:!0,closeOnEsc:!0,closeOnClick:"button",closeButton:!0,overlay:!0,animation:"zoomIn",preventDefault:!0,width:"100%",height:"100%",adjustDistance:{top:40,right:5,bottom:40,left:5},_onInit:function(){this.images=this.currentImage={},this.imageZIndex=1,this.initImage=function(t){if(!(t=e(t)).data("jBox-image-gallery")){var n=t.attr(this.options.src);this.options.mobileImageAttr&&this.options.mobileImageBreakpoint&&t.attr(this.options.mobileImageAttr)&&e(window).width()<=this.options.mobileImageBreakpoint&&(n=t.attr(this.options.mobileImageAttr));var i=t.attr(this.options.gallery)||"default";!this.images[i]&&(this.images[i]=[]),this.images[i].push({src:n,label:t.attr(this.options.imageLabel)||"",downloadUrl:this.options.downloadButtonUrl&&t.attr(this.options.downloadButtonUrl)?t.attr(this.options.downloadButtonUrl):null}),"title"==this.options.imageLabel&&t.removeAttr("title"),t.data("jBox-image-gallery",i),t.data("jBox-image-id",this.images[i].length-1)}}.bind(this),this.attachedElements&&this.attachedElements.length&&e.each(this.attachedElements,function(t,e){this.initImage(e)}.bind(this));var t=function(t,n,i,o){if(!e("#jBox-image-"+t+"-"+n).length){var s=e("<div/>",{id:"jBox-image-"+t+"-"+n,class:"jBox-image-container"+(i?" jBox-image-"+t+"-current":"")}).css({backgroundSize:this.options.imageSize,opacity:o?1:0,zIndex:i?this.imageZIndex++:0}).appendTo(this.content);return e("<div/>",{id:"jBox-image-label-"+t+"-"+n,class:"jBox-image-label"+(i?" active":"")}).html(this.images[t][n].label).click(function(){e(this).toggleClass("expanded")}).appendTo(this.imageLabel),i&&s.animate({opacity:1},o?0:this.options.imageFade),s}}.bind(this);this.downloadImage=function(t){var e=document.createElement("a");e.href=t,e.setAttribute("download",t.substring(t.lastIndexOf("/")+1)),document.body.appendChild(e),e.click()};var n=function(n,i,o,s){var r=t(n,i,o,s);r.addClass("jBox-image-loading"),e('<img src="'+this.images[n][i].src+'" />').each(function(){var t=new Image;t.onload=function(){r.removeClass("jBox-image-loading"),r.css({backgroundImage:'url("'+this.images[n][i].src+'")'})}.bind(this),t.onerror=function(){r.removeClass("jBox-image-loading"),r.addClass("jBox-image-not-found")}.bind(this),t.src=this.images[n][i].src}.bind(this))}.bind(this);this.showImage=function(t){if("open"!=t){var i=this.currentImage.gallery;o=(o=this.currentImage.id+(1*("prev"==t)?-1:1))>this.images[i].length-1?0:o<0?this.images[i].length-1:o}else{if(this.source){i=this.source.data("jBox-image-gallery");var o=this.source.data("jBox-image-id")}else{if(!this.attachedElements||!this.attachedElements.length)return;i=e(this.attachedElements[0]).data("jBox-image-gallery"),o=e(this.attachedElements[0]).data("jBox-image-id")}e(".jBox-image-pointer-prev, .jBox-image-pointer-next").css({display:1<this.images[i].length?"block":"none"})}if(e(".jBox-image-"+i+"-current").length&&e(".jBox-image-"+i+"-current").removeClass("jBox-image-"+i+"-current").animate({opacity:0},"open"==t?0:this.options.imageFade),this.currentImage={gallery:i,id:o},e("#jBox-image-"+i+"-"+o).length?e("#jBox-image-"+i+"-"+o).addClass("jBox-image-"+i+"-current").css({zIndex:this.imageZIndex++,opacity:0}).animate({opacity:1},"open"==t?0:this.options.imageFade):n(i,o,!0,"open"===t),function(t,n){e(".jBox-image-label.active").removeClass("active expanded"),e("#jBox-image-label-"+t+"-"+n).addClass("active")}(i,o),this.imageCounter&&(1<this.images[i].length?(this.wrapper.addClass("jBox-image-has-counter"),this.imageCounter.find(".jBox-image-counter-all").html(this.images[i].length),this.imageCounter.find(".jBox-image-counter-current").html(o+1)):this.wrapper.removeClass("jBox-image-has-counter")),this.images[i].length-1){var s=o+1;s=s>this.images[i].length-1?0:s<0?this.images[i].length-1:s,e("#jBox-image-"+i+"-"+s).length||n(i,s,!1,!1)}},this.options.preloadFirstImage&&e(window).on("load",function(){this.showImage("open")}.bind(this))},_onAttach:function(t){this.initImage&&this.initImage(t)},_onCreated:function(){this.imageLabel=e("<div/>",{class:"jBox-image-label-container"}).appendTo(this.wrapper),this.imageLabel.append(e("<div/>",{class:"jBox-image-pointer-prev",click:function(){this.showImage("prev")}.bind(this)})).append(e("<div/>",{class:"jBox-image-pointer-next",click:function(){this.showImage("next")}.bind(this)})),this.options.downloadButton&&(this.downloadButton=e("<div/>",{class:"jBox-image-download-button-wrapper"}).appendTo(this.wrapper).append(this.options.downloadButtonText?e("<div/>",{class:"jBox-image-download-button-text"}).html(this.options.downloadButtonText):null).append(e("<div/>",{class:"jBox-image-download-button-icon"})).on("click touchdown",function(){if(this.images[this.currentImage.gallery][this.currentImage.id].downloadUrl)var t=this.images[this.currentImage.gallery][this.currentImage.id].downloadUrl;else t=this.wrapper.find(".jBox-image-"+this.currentImage.gallery+"-current")[0].style.backgroundImage.slice(4,-1).replace(/["']/g,"");this.downloadImage(t)}.bind(this))),this.options.imageCounter&&(this.imageCounter=e("<div/>",{class:"jBox-image-counter-container"}).appendTo(this.wrapper),this.imageCounter.append(e("<span/>",{class:"jBox-image-counter-current"})).append(e("<span/>").html(this.options.imageCounterSeparator)).append(e("<span/>",{class:"jBox-image-counter-all"})))},_onOpen:function(){e(document).on("keyup.jBox-Image-"+this.id,function(t){37==t.keyCode&&this.showImage("prev"),39==t.keyCode&&this.showImage("next")}.bind(this)),this.showImage("open")},_onClose:function(){e(document).off("keyup.jBox-Image-"+this.id)},_onCloseComplete:function(){this.wrapper.find(".jBox-image-container").css("opacity",0)}})}function jBoxNoticeWrapper(t,e){new t.plugin("Notice",{color:null,stack:!0,stackSpacing:10,autoClose:6e3,attributes:{x:"right",y:"top"},position:{x:15,y:15},responsivePositions:{500:{x:5,y:5},768:{x:10,y:10}},target:window,fixed:!0,animation:"zoomIn",closeOnClick:"box",zIndex:12e3,_onInit:function(){this.defaultNoticePosition=e.extend({},this.options.position),this._adjustNoticePositon=function(){var t=e(window),n=t.width();t.height(),this.options.position=e.extend({},this.defaultNoticePosition),e.each(this.options.responsivePositions,function(t,e){if(n<=t)return this.options.position=e,!1}.bind(this)),this.options.adjustDistance={top:this.options.position.y,right:this.options.position.x,bottom:this.options.position.y,left:this.options.position.x}},this.options.content instanceof e&&(this.options.content=this.options.content.clone().attr("id","")),e(window).on("resize.responsivejBoxNotice-"+this.id,function(t){this.isOpen&&this._adjustNoticePositon()}.bind(this)),this.open()},_onCreated:function(){this.wrapper.addClass("jBox-Notice-color jBox-Notice-"+(this.options.color||"gray")),this.wrapper.data("jBox-Notice-position",this.options.attributes.x+"-"+this.options.attributes.y)},_onOpen:function(){this.options.stack||(this._adjustNoticePositon(),e.each(e(".jBox-Notice"),function(t,n){(n=e(n)).attr("id")!=this.id&&n.data("jBox-Notice-position")==this.options.attributes.x+"-"+this.options.attributes.y&&(this.options.stack||n.data("jBox").close({ignoreDelay:!0}))}.bind(this)))},_onPosition:function(){var t={};for(var n in e.each(e(".jBox-Notice"),function(n,i){var o=(i=e(i)).data("jBox-Notice-position");t[o]||(t[o]=[]),t[o].push(i)}),t){var i=n.split("-")[1];t[n].reverse();var o=0;for(var s in t[n])el=t[n][s],el.css("margin-"+i,o),o+=el.outerHeight()+this.options.stackSpacing}},_onCloseComplete:function(){this.destroy(),this.options._onPosition.bind(this).call()}})}if(function(t,e){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return e(t)}:e(t)}("undefined"!=typeof window?window:this,function(t,e){"use strict";var n=[],i=Object.getPrototypeOf,o=n.slice,s=n.flat?function(t){return n.flat.call(t)}:function(t){return n.concat.apply([],t)},r=n.push,a=n.indexOf,l={},c=l.toString,d=l.hasOwnProperty,p=d.toString,u=p.call(Object),h={},f=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType},g=function(t){return null!=t&&t===t.window},m=t.document,y={type:!0,src:!0,nonce:!0,noModule:!0};function v(t,e,n){var i,o,s=(n=n||m).createElement("script");if(s.text=t,e)for(i in y)(o=e[i]||e.getAttribute&&e.getAttribute(i))&&s.setAttribute(i,o);n.head.appendChild(s).parentNode.removeChild(s)}function b(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?l[c.call(t)]||"object":typeof t}var x="3.5.0",w=function(t,e){return new w.fn.init(t,e)};function C(t){var e=!!t&&"length"in t&&t.length,n=b(t);return!f(t)&&!g(t)&&("array"===n||0===e||"number"==typeof e&&0<e&&e-1 in t)}w.fn=w.prototype={jquery:x,constructor:w,length:0,toArray:function(){return o.call(this)},get:function(t){return null==t?o.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=w.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return w.each(this,t)},map:function(t){return this.pushStack(w.map(this,function(e,n){return t.call(e,n,e)}))},slice:function(){return this.pushStack(o.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(w.grep(this,function(t,e){return(e+1)%2}))},odd:function(){return this.pushStack(w.grep(this,function(t,e){return e%2}))},eq:function(t){var e=this.length,n=+t+(t<0?e:0);return this.pushStack(0<=n&&n<e?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:r,sort:n.sort,splice:n.splice},w.extend=w.fn.extend=function(){var t,e,n,i,o,s,r=arguments[0]||{},a=1,l=arguments.length,c=!1;for("boolean"==typeof r&&(c=r,r=arguments[a]||{},a++),"object"==typeof r||f(r)||(r={}),a===l&&(r=this,a--);a<l;a++)if(null!=(t=arguments[a]))for(e in t)i=t[e],"__proto__"!==e&&r!==i&&(c&&i&&(w.isPlainObject(i)||(o=Array.isArray(i)))?(n=r[e],s=o&&!Array.isArray(n)?[]:o||w.isPlainObject(n)?n:{},o=!1,r[e]=w.extend(c,s,i)):void 0!==i&&(r[e]=i));return r},w.extend({expando:"jQuery"+(x+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,n;return!(!t||"[object Object]"!==c.call(t)||(e=i(t))&&("function"!=typeof(n=d.call(e,"constructor")&&e.constructor)||p.call(n)!==u))},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t,e,n){v(t,{nonce:e&&e.nonce},n)},each:function(t,e){var n,i=0;if(C(t))for(n=t.length;i<n&&!1!==e.call(t[i],i,t[i]);i++);else for(i in t)if(!1===e.call(t[i],i,t[i]))break;return t},makeArray:function(t,e){var n=e||[];return null!=t&&(C(Object(t))?w.merge(n,"string"==typeof t?[t]:t):r.call(n,t)),n},inArray:function(t,e,n){return null==e?-1:a.call(e,t,n)},merge:function(t,e){for(var n=+e.length,i=0,o=t.length;i<n;i++)t[o++]=e[i];return t.length=o,t},grep:function(t,e,n){for(var i=[],o=0,s=t.length,r=!n;o<s;o++)!e(t[o],o)!==r&&i.push(t[o]);return i},map:function(t,e,n){var i,o,r=0,a=[];if(C(t))for(i=t.length;r<i;r++)null!=(o=e(t[r],r,n))&&a.push(o);else for(r in t)null!=(o=e(t[r],r,n))&&a.push(o);return s(a)},guid:1,support:h}),"function"==typeof Symbol&&(w.fn[Symbol.iterator]=n[Symbol.iterator]),w.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){l["[object "+e+"]"]=e.toLowerCase()});var T=function(t){var e,n,i,o,s,r,a,l,c,d,p,u,h,f,g,m,y,v,b,x="sizzle"+1*new Date,w=t.document,C=0,T=0,j=lt(),E=lt(),S=lt(),k=lt(),B=function(t,e){return t===e&&(p=!0),0},D={}.hasOwnProperty,I=[],A=I.pop,O=I.push,L=I.push,_=I.slice,H=function(t,e){for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n;return-1},q="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",N="[\\x20\\t\\r\\n\\f]",M="(?:\\\\[\\da-fA-F]{1,6}"+N+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",$="\\["+N+"*("+M+")(?:"+N+"*([*^$|!~]?=)"+N+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+M+"))|)"+N+"*\\]",P=":("+M+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+$+")*)|.*)\\)|)",W=new RegExp(N+"+","g"),R=new RegExp("^"+N+"+|((?:^|[^\\\\])(?:\\\\.)*)"+N+"+$","g"),F=new RegExp("^"+N+"*,"+N+"*"),U=new RegExp("^"+N+"*([>+~]|"+N+")"+N+"*"),z=new RegExp(N+"|>"),X=new RegExp(P),V=new RegExp("^"+M+"$"),Y={ID:new RegExp("^#("+M+")"),CLASS:new RegExp("^\\.("+M+")"),TAG:new RegExp("^("+M+"|[*])"),ATTR:new RegExp("^"+$),PSEUDO:new RegExp("^"+P),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+N+"*(even|odd|(([+-]|)(\\d*)n|)"+N+"*(?:([+-]|)"+N+"*(\\d+)|))"+N+"*\\)|)","i"),bool:new RegExp("^(?:"+q+")$","i"),needsContext:new RegExp("^"+N+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+N+"*((?:-\\d)?\\d*)"+N+"*\\)|)(?=[^-]|$)","i")},Q=/HTML$/i,G=/^(?:input|select|textarea|button)$/i,J=/^h\d$/i,Z=/^[^{]+\{\s*\[native \w/,K=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,tt=/[+~]/,et=new RegExp("\\\\[\\da-fA-F]{1,6}"+N+"?|\\\\([^\\r\\n\\f])","g"),nt=function(t,e){var n="0x"+t.slice(1)-65536;return e||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},it=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,ot=function(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t},st=function(){u()},rt=xt(function(t){return!0===t.disabled&&"fieldset"===t.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{L.apply(I=_.call(w.childNodes),w.childNodes),I[w.childNodes.length].nodeType}catch(e){L={apply:I.length?function(t,e){O.apply(t,_.call(e))}:function(t,e){for(var n=t.length,i=0;t[n++]=e[i++];);t.length=n-1}}}function at(t,e,i,o){var s,a,c,d,p,f,y,v=e&&e.ownerDocument,w=e?e.nodeType:9;if(i=i||[],"string"!=typeof t||!t||1!==w&&9!==w&&11!==w)return i;if(!o&&(u(e),e=e||h,g)){if(11!==w&&(p=K.exec(t)))if(s=p[1]){if(9===w){if(!(c=e.getElementById(s)))return i;if(c.id===s)return i.push(c),i}else if(v&&(c=v.getElementById(s))&&b(e,c)&&c.id===s)return i.push(c),i}else{if(p[2])return L.apply(i,e.getElementsByTagName(t)),i;if((s=p[3])&&n.getElementsByClassName&&e.getElementsByClassName)return L.apply(i,e.getElementsByClassName(s)),i}if(n.qsa&&!k[t+" "]&&(!m||!m.test(t))&&(1!==w||"object"!==e.nodeName.toLowerCase())){if(y=t,v=e,1===w&&(z.test(t)||U.test(t))){for((v=tt.test(t)&&yt(e.parentNode)||e)===e&&n.scope||((d=e.getAttribute("id"))?d=d.replace(it,ot):e.setAttribute("id",d=x)),a=(f=r(t)).length;a--;)f[a]=(d?"#"+d:":scope")+" "+bt(f[a]);y=f.join(",")}try{return L.apply(i,v.querySelectorAll(y)),i}catch(e){k(t,!0)}finally{d===x&&e.removeAttribute("id")}}}return l(t.replace(R,"$1"),e,i,o)}function lt(){var t=[];return function e(n,o){return t.push(n+" ")>i.cacheLength&&delete e[t.shift()],e[n+" "]=o}}function ct(t){return t[x]=!0,t}function dt(t){var e=h.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function pt(t,e){for(var n=t.split("|"),o=n.length;o--;)i.attrHandle[n[o]]=e}function ut(t,e){var n=e&&t,i=n&&1===t.nodeType&&1===e.nodeType&&t.sourceIndex-e.sourceIndex;if(i)return i;if(n)for(;n=n.nextSibling;)if(n===e)return-1;return t?1:-1}function ht(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}function ft(t){return function(e){var n=e.nodeName.toLowerCase();return("input"===n||"button"===n)&&e.type===t}}function gt(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&rt(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function mt(t){return ct(function(e){return e=+e,ct(function(n,i){for(var o,s=t([],n.length,e),r=s.length;r--;)n[o=s[r]]&&(n[o]=!(i[o]=n[o]))})})}function yt(t){return t&&void 0!==t.getElementsByTagName&&t}for(e in n=at.support={},s=at.isXML=function(t){var e=t.namespaceURI,n=(t.ownerDocument||t).documentElement;return!Q.test(e||n&&n.nodeName||"HTML")},u=at.setDocument=function(t){var e,o,r=t?t.ownerDocument||t:w;return r!=h&&9===r.nodeType&&r.documentElement&&(f=(h=r).documentElement,g=!s(h),w!=h&&(o=h.defaultView)&&o.top!==o&&(o.addEventListener?o.addEventListener("unload",st,!1):o.attachEvent&&o.attachEvent("onunload",st)),n.scope=dt(function(t){return f.appendChild(t).appendChild(h.createElement("div")),void 0!==t.querySelectorAll&&!t.querySelectorAll(":scope fieldset div").length}),n.attributes=dt(function(t){return t.className="i",!t.getAttribute("className")}),n.getElementsByTagName=dt(function(t){return t.appendChild(h.createComment("")),!t.getElementsByTagName("*").length}),n.getElementsByClassName=Z.test(h.getElementsByClassName),n.getById=dt(function(t){return f.appendChild(t).id=x,!h.getElementsByName||!h.getElementsByName(x).length}),n.getById?(i.filter.ID=function(t){var e=t.replace(et,nt);return function(t){return t.getAttribute("id")===e}},i.find.ID=function(t,e){if(void 0!==e.getElementById&&g){var n=e.getElementById(t);return n?[n]:[]}}):(i.filter.ID=function(t){var e=t.replace(et,nt);return function(t){var n=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return n&&n.value===e}},i.find.ID=function(t,e){if(void 0!==e.getElementById&&g){var n,i,o,s=e.getElementById(t);if(s){if((n=s.getAttributeNode("id"))&&n.value===t)return[s];for(o=e.getElementsByName(t),i=0;s=o[i++];)if((n=s.getAttributeNode("id"))&&n.value===t)return[s]}return[]}}),i.find.TAG=n.getElementsByTagName?function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):n.qsa?e.querySelectorAll(t):void 0}:function(t,e){var n,i=[],o=0,s=e.getElementsByTagName(t);if("*"===t){for(;n=s[o++];)1===n.nodeType&&i.push(n);return i}return s},i.find.CLASS=n.getElementsByClassName&&function(t,e){if(void 0!==e.getElementsByClassName&&g)return e.getElementsByClassName(t)},y=[],m=[],(n.qsa=Z.test(h.querySelectorAll))&&(dt(function(t){var e;f.appendChild(t).innerHTML="<a id='"+x+"'></a><select id='"+x+"-\r\\' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+N+"*(?:''|\"\")"),t.querySelectorAll("[selected]").length||m.push("\\["+N+"*(?:value|"+q+")"),t.querySelectorAll("[id~="+x+"-]").length||m.push("~="),(e=h.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||m.push("\\["+N+"*name"+N+"*="+N+"*(?:''|\"\")"),t.querySelectorAll(":checked").length||m.push(":checked"),t.querySelectorAll("a#"+x+"+*").length||m.push(".#.+[+~]"),t.querySelectorAll("\\\f"),m.push("[\\r\\n\\f]")}),dt(function(t){t.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var e=h.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&m.push("name"+N+"*[*^$|!~]?="),2!==t.querySelectorAll(":enabled").length&&m.push(":enabled",":disabled"),f.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&m.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),m.push(",.*:")})),(n.matchesSelector=Z.test(v=f.matches||f.webkitMatchesSelector||f.mozMatchesSelector||f.oMatchesSelector||f.msMatchesSelector))&&dt(function(t){n.disconnectedMatch=v.call(t,"*"),v.call(t,"[s!='']:x"),y.push("!=",P)}),m=m.length&&new RegExp(m.join("|")),y=y.length&&new RegExp(y.join("|")),e=Z.test(f.compareDocumentPosition),b=e||Z.test(f.contains)?function(t,e){var n=9===t.nodeType?t.documentElement:t,i=e&&e.parentNode;return t===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):t.compareDocumentPosition&&16&t.compareDocumentPosition(i)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},B=e?function(t,e){if(t===e)return p=!0,0;var i=!t.compareDocumentPosition-!e.compareDocumentPosition;return i||(1&(i=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!n.sortDetached&&e.compareDocumentPosition(t)===i?t==h||t.ownerDocument==w&&b(w,t)?-1:e==h||e.ownerDocument==w&&b(w,e)?1:d?H(d,t)-H(d,e):0:4&i?-1:1)}:function(t,e){if(t===e)return p=!0,0;var n,i=0,o=t.parentNode,s=e.parentNode,r=[t],a=[e];if(!o||!s)return t==h?-1:e==h?1:o?-1:s?1:d?H(d,t)-H(d,e):0;if(o===s)return ut(t,e);for(n=t;n=n.parentNode;)r.unshift(n);for(n=e;n=n.parentNode;)a.unshift(n);for(;r[i]===a[i];)i++;return i?ut(r[i],a[i]):r[i]==w?-1:a[i]==w?1:0}),h},at.matches=function(t,e){return at(t,null,null,e)},at.matchesSelector=function(t,e){if(u(t),n.matchesSelector&&g&&!k[e+" "]&&(!y||!y.test(e))&&(!m||!m.test(e)))try{var i=v.call(t,e);if(i||n.disconnectedMatch||t.document&&11!==t.document.nodeType)return i}catch(t){k(e,!0)}return 0<at(e,h,null,[t]).length},at.contains=function(t,e){return(t.ownerDocument||t)!=h&&u(t),b(t,e)},at.attr=function(t,e){(t.ownerDocument||t)!=h&&u(t);var o=i.attrHandle[e.toLowerCase()],s=o&&D.call(i.attrHandle,e.toLowerCase())?o(t,e,!g):void 0;return void 0!==s?s:n.attributes||!g?t.getAttribute(e):(s=t.getAttributeNode(e))&&s.specified?s.value:null},at.escape=function(t){return(t+"").replace(it,ot)},at.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},at.uniqueSort=function(t){var e,i=[],o=0,s=0;if(p=!n.detectDuplicates,d=!n.sortStable&&t.slice(0),t.sort(B),p){for(;e=t[s++];)e===t[s]&&(o=i.push(s));for(;o--;)t.splice(i[o],1)}return d=null,t},o=at.getText=function(t){var e,n="",i=0,s=t.nodeType;if(s){if(1===s||9===s||11===s){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)n+=o(t)}else if(3===s||4===s)return t.nodeValue}else for(;e=t[i++];)n+=o(e);return n},(i=at.selectors={cacheLength:50,createPseudo:ct,match:Y,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(et,nt),t[3]=(t[3]||t[4]||t[5]||"").replace(et,nt),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||at.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&at.error(t[0]),t},PSEUDO:function(t){var e,n=!t[6]&&t[2];return Y.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&X.test(n)&&(e=r(n,!0))&&(e=n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(et,nt).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===e}},CLASS:function(t){var e=j[t+" "];return e||(e=new RegExp("(^|"+N+")"+t+"("+N+"|$)"))&&j(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(t,e,n){return function(i){var o=at.attr(i,t);return null==o?"!="===e:!e||(o+="","="===e?o===n:"!="===e?o!==n:"^="===e?n&&0===o.indexOf(n):"*="===e?n&&-1<o.indexOf(n):"$="===e?n&&o.slice(-n.length)===n:"~="===e?-1<(" "+o.replace(W," ")+" ").indexOf(n):"|="===e&&(o===n||o.slice(0,n.length+1)===n+"-"))}},CHILD:function(t,e,n,i,o){var s="nth"!==t.slice(0,3),r="last"!==t.slice(-4),a="of-type"===e;return 1===i&&0===o?function(t){return!!t.parentNode}:function(e,n,l){var c,d,p,u,h,f,g=s!==r?"nextSibling":"previousSibling",m=e.parentNode,y=a&&e.nodeName.toLowerCase(),v=!l&&!a,b=!1;if(m){if(s){for(;g;){for(u=e;u=u[g];)if(a?u.nodeName.toLowerCase()===y:1===u.nodeType)return!1;f=g="only"===t&&!f&&"nextSibling"}return!0}if(f=[r?m.firstChild:m.lastChild],r&&v){for(b=(h=(c=(d=(p=(u=m)[x]||(u[x]={}))[u.uniqueID]||(p[u.uniqueID]={}))[t]||[])[0]===C&&c[1])&&c[2],u=h&&m.childNodes[h];u=++h&&u&&u[g]||(b=h=0)||f.pop();)if(1===u.nodeType&&++b&&u===e){d[t]=[C,h,b];break}}else if(v&&(b=h=(c=(d=(p=(u=e)[x]||(u[x]={}))[u.uniqueID]||(p[u.uniqueID]={}))[t]||[])[0]===C&&c[1]),!1===b)for(;(u=++h&&u&&u[g]||(b=h=0)||f.pop())&&((a?u.nodeName.toLowerCase()!==y:1!==u.nodeType)||!++b||(v&&((d=(p=u[x]||(u[x]={}))[u.uniqueID]||(p[u.uniqueID]={}))[t]=[C,b]),u!==e)););return(b-=o)===i||b%i==0&&0<=b/i}}},PSEUDO:function(t,e){var n,o=i.pseudos[t]||i.setFilters[t.toLowerCase()]||at.error("unsupported pseudo: "+t);return o[x]?o(e):1<o.length?(n=[t,t,"",e],i.setFilters.hasOwnProperty(t.toLowerCase())?ct(function(t,n){for(var i,s=o(t,e),r=s.length;r--;)t[i=H(t,s[r])]=!(n[i]=s[r])}):function(t){return o(t,0,n)}):o}},pseudos:{not:ct(function(t){var e=[],n=[],i=a(t.replace(R,"$1"));return i[x]?ct(function(t,e,n,o){for(var s,r=i(t,null,o,[]),a=t.length;a--;)(s=r[a])&&(t[a]=!(e[a]=s))}):function(t,o,s){return e[0]=t,i(e,null,s,n),e[0]=null,!n.pop()}}),has:ct(function(t){return function(e){return 0<at(t,e).length}}),contains:ct(function(t){return t=t.replace(et,nt),function(e){return-1<(e.textContent||o(e)).indexOf(t)}}),lang:ct(function(t){return V.test(t||"")||at.error("unsupported lang: "+t),t=t.replace(et,nt).toLowerCase(),function(e){var n;do{if(n=g?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(n=n.toLowerCase())===t||0===n.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var n=t.location&&t.location.hash;return n&&n.slice(1)===e.id},root:function(t){return t===f},focus:function(t){return t===h.activeElement&&(!h.hasFocus||h.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},enabled:gt(!1),disabled:gt(!0),checked:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!i.pseudos.empty(t)},header:function(t){return J.test(t.nodeName)},input:function(t){return G.test(t.nodeName)},button:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},text:function(t){var e;return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:mt(function(){return[0]}),last:mt(function(t,e){return[e-1]}),eq:mt(function(t,e,n){return[n<0?n+e:n]}),even:mt(function(t,e){for(var n=0;n<e;n+=2)t.push(n);return t}),odd:mt(function(t,e){for(var n=1;n<e;n+=2)t.push(n);return t}),lt:mt(function(t,e,n){for(var i=n<0?n+e:e<n?e:n;0<=--i;)t.push(i);return t}),gt:mt(function(t,e,n){for(var i=n<0?n+e:n;++i<e;)t.push(i);return t})}}).pseudos.nth=i.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})i.pseudos[e]=ht(e);for(e in{submit:!0,reset:!0})i.pseudos[e]=ft(e);function vt(){}function bt(t){for(var e=0,n=t.length,i="";e<n;e++)i+=t[e].value;return i}function xt(t,e,n){var i=e.dir,o=e.next,s=o||i,r=n&&"parentNode"===s,a=T++;return e.first?function(e,n,o){for(;e=e[i];)if(1===e.nodeType||r)return t(e,n,o);return!1}:function(e,n,l){var c,d,p,u=[C,a];if(l){for(;e=e[i];)if((1===e.nodeType||r)&&t(e,n,l))return!0}else for(;e=e[i];)if(1===e.nodeType||r)if(d=(p=e[x]||(e[x]={}))[e.uniqueID]||(p[e.uniqueID]={}),o&&o===e.nodeName.toLowerCase())e=e[i]||e;else{if((c=d[s])&&c[0]===C&&c[1]===a)return u[2]=c[2];if((d[s]=u)[2]=t(e,n,l))return!0}return!1}}function wt(t){return 1<t.length?function(e,n,i){for(var o=t.length;o--;)if(!t[o](e,n,i))return!1;return!0}:t[0]}function Ct(t,e,n,i,o){for(var s,r=[],a=0,l=t.length,c=null!=e;a<l;a++)(s=t[a])&&(n&&!n(s,i,o)||(r.push(s),c&&e.push(a)));return r}function Tt(t,e,n,i,o,s){return i&&!i[x]&&(i=Tt(i)),o&&!o[x]&&(o=Tt(o,s)),ct(function(s,r,a,l){var c,d,p,u=[],h=[],f=r.length,g=s||function(t,e,n){for(var i=0,o=e.length;i<o;i++)at(t,e[i],n);return n}(e||"*",a.nodeType?[a]:a,[]),m=!t||!s&&e?g:Ct(g,u,t,a,l),y=n?o||(s?t:f||i)?[]:r:m;if(n&&n(m,y,a,l),i)for(c=Ct(y,h),i(c,[],a,l),d=c.length;d--;)(p=c[d])&&(y[h[d]]=!(m[h[d]]=p));if(s){if(o||t){if(o){for(c=[],d=y.length;d--;)(p=y[d])&&c.push(m[d]=p);o(null,y=[],c,l)}for(d=y.length;d--;)(p=y[d])&&-1<(c=o?H(s,p):u[d])&&(s[c]=!(r[c]=p))}}else y=Ct(y===r?y.splice(f,y.length):y),o?o(null,r,y,l):L.apply(r,y)})}function jt(t){for(var e,n,o,s=t.length,r=i.relative[t[0].type],a=r||i.relative[" "],l=r?1:0,d=xt(function(t){return t===e},a,!0),p=xt(function(t){return-1<H(e,t)},a,!0),u=[function(t,n,i){var o=!r&&(i||n!==c)||((e=n).nodeType?d(t,n,i):p(t,n,i));return e=null,o}];l<s;l++)if(n=i.relative[t[l].type])u=[xt(wt(u),n)];else{if((n=i.filter[t[l].type].apply(null,t[l].matches))[x]){for(o=++l;o<s&&!i.relative[t[o].type];o++);return Tt(1<l&&wt(u),1<l&&bt(t.slice(0,l-1).concat({value:" "===t[l-2].type?"*":""})).replace(R,"$1"),n,l<o&&jt(t.slice(l,o)),o<s&&jt(t=t.slice(o)),o<s&&bt(t))}u.push(n)}return wt(u)}return vt.prototype=i.filters=i.pseudos,i.setFilters=new vt,r=at.tokenize=function(t,e){var n,o,s,r,a,l,c,d=E[t+" "];if(d)return e?0:d.slice(0);for(a=t,l=[],c=i.preFilter;a;){for(r in n&&!(o=F.exec(a))||(o&&(a=a.slice(o[0].length)||a),l.push(s=[])),n=!1,(o=U.exec(a))&&(n=o.shift(),s.push({value:n,type:o[0].replace(R," ")}),a=a.slice(n.length)),i.filter)!(o=Y[r].exec(a))||c[r]&&!(o=c[r](o))||(n=o.shift(),s.push({value:n,type:r,matches:o}),a=a.slice(n.length));if(!n)break}return e?a.length:a?at.error(t):E(t,l).slice(0)},a=at.compile=function(t,e){var n,o,s,a,l,d,p=[],f=[],m=S[t+" "];if(!m){for(e||(e=r(t)),n=e.length;n--;)(m=jt(e[n]))[x]?p.push(m):f.push(m);(m=S(t,(o=f,a=0<(s=p).length,l=0<o.length,d=function(t,e,n,r,d){var p,f,m,y=0,v="0",b=t&&[],x=[],w=c,T=t||l&&i.find.TAG("*",d),j=C+=null==w?1:Math.random()||.1,E=T.length;for(d&&(c=e==h||e||d);v!==E&&null!=(p=T[v]);v++){if(l&&p){for(f=0,e||p.ownerDocument==h||(u(p),n=!g);m=o[f++];)if(m(p,e||h,n)){r.push(p);break}d&&(C=j)}a&&((p=!m&&p)&&y--,t&&b.push(p))}if(y+=v,a&&v!==y){for(f=0;m=s[f++];)m(b,x,e,n);if(t){if(0<y)for(;v--;)b[v]||x[v]||(x[v]=A.call(r));x=Ct(x)}L.apply(r,x),d&&!t&&0<x.length&&1<y+s.length&&at.uniqueSort(r)}return d&&(C=j,c=w),b},a?ct(d):d))).selector=t}return m},l=at.select=function(t,e,n,o){var s,l,c,d,p,u="function"==typeof t&&t,h=!o&&r(t=u.selector||t);if(n=n||[],1===h.length){if(2<(l=h[0]=h[0].slice(0)).length&&"ID"===(c=l[0]).type&&9===e.nodeType&&g&&i.relative[l[1].type]){if(!(e=(i.find.ID(c.matches[0].replace(et,nt),e)||[])[0]))return n;u&&(e=e.parentNode),t=t.slice(l.shift().value.length)}for(s=Y.needsContext.test(t)?0:l.length;s--&&(c=l[s],!i.relative[d=c.type]);)if((p=i.find[d])&&(o=p(c.matches[0].replace(et,nt),tt.test(l[0].type)&&yt(e.parentNode)||e))){if(l.splice(s,1),!(t=o.length&&bt(l)))return L.apply(n,o),n;break}}return(u||a(t,h))(o,e,!g,n,!e||tt.test(t)&&yt(e.parentNode)||e),n},n.sortStable=x.split("").sort(B).join("")===x,n.detectDuplicates=!!p,u(),n.sortDetached=dt(function(t){return 1&t.compareDocumentPosition(h.createElement("fieldset"))}),dt(function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")})||pt("type|href|height|width",function(t,e,n){if(!n)return t.getAttribute(e,"type"===e.toLowerCase()?1:2)}),n.attributes&&dt(function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")})||pt("value",function(t,e,n){if(!n&&"input"===t.nodeName.toLowerCase())return t.defaultValue}),dt(function(t){return null==t.getAttribute("disabled")})||pt(q,function(t,e,n){var i;if(!n)return!0===t[e]?e.toLowerCase():(i=t.getAttributeNode(e))&&i.specified?i.value:null}),at}(t);w.find=T,w.expr=T.selectors,w.expr[":"]=w.expr.pseudos,w.uniqueSort=w.unique=T.uniqueSort,w.text=T.getText,w.isXMLDoc=T.isXML,w.contains=T.contains,w.escapeSelector=T.escape;var j=function(t,e,n){for(var i=[],o=void 0!==n;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(o&&w(t).is(n))break;i.push(t)}return i},E=function(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n},S=w.expr.match.needsContext;function k(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}var B=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function D(t,e,n){return f(e)?w.grep(t,function(t,i){return!!e.call(t,i,t)!==n}):e.nodeType?w.grep(t,function(t){return t===e!==n}):"string"!=typeof e?w.grep(t,function(t){return-1<a.call(e,t)!==n}):w.filter(e,t,n)}w.filter=function(t,e,n){var i=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===i.nodeType?w.find.matchesSelector(i,t)?[i]:[]:w.find.matches(t,w.grep(e,function(t){return 1===t.nodeType}))},w.fn.extend({find:function(t){var e,n,i=this.length,o=this;if("string"!=typeof t)return this.pushStack(w(t).filter(function(){for(e=0;e<i;e++)if(w.contains(o[e],this))return!0}));for(n=this.pushStack([]),e=0;e<i;e++)w.find(t,o[e],n);return 1<i?w.uniqueSort(n):n},filter:function(t){return this.pushStack(D(this,t||[],!1))},not:function(t){return this.pushStack(D(this,t||[],!0))},is:function(t){return!!D(this,"string"==typeof t&&S.test(t)?w(t):t||[],!1).length}});var I,A=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(w.fn.init=function(t,e,n){var i,o;if(!t)return this;if(n=n||I,"string"==typeof t){if(!(i="<"===t[0]&&">"===t[t.length-1]&&3<=t.length?[null,t,null]:A.exec(t))||!i[1]&&e)return!e||e.jquery?(e||n).find(t):this.constructor(e).find(t);if(i[1]){if(e=e instanceof w?e[0]:e,w.merge(this,w.parseHTML(i[1],e&&e.nodeType?e.ownerDocument||e:m,!0)),B.test(i[1])&&w.isPlainObject(e))for(i in e)f(this[i])?this[i](e[i]):this.attr(i,e[i]);return this}return(o=m.getElementById(i[2]))&&(this[0]=o,this.length=1),this}return t.nodeType?(this[0]=t,this.length=1,this):f(t)?void 0!==n.ready?n.ready(t):t(w):w.makeArray(t,this)}).prototype=w.fn,I=w(m);var O=/^(?:parents|prev(?:Until|All))/,L={children:!0,contents:!0,next:!0,prev:!0};function _(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}w.fn.extend({has:function(t){var e=w(t,this),n=e.length;return this.filter(function(){for(var t=0;t<n;t++)if(w.contains(this,e[t]))return!0})},closest:function(t,e){var n,i=0,o=this.length,s=[],r="string"!=typeof t&&w(t);if(!S.test(t))for(;i<o;i++)for(n=this[i];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(r?-1<r.index(n):1===n.nodeType&&w.find.matchesSelector(n,t))){s.push(n);break}return this.pushStack(1<s.length?w.uniqueSort(s):s)},index:function(t){return t?"string"==typeof t?a.call(w(t),this[0]):a.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(w.uniqueSort(w.merge(this.get(),w(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),w.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return j(t,"parentNode")},parentsUntil:function(t,e,n){return j(t,"parentNode",n)},next:function(t){return _(t,"nextSibling")},prev:function(t){return _(t,"previousSibling")},nextAll:function(t){return j(t,"nextSibling")},prevAll:function(t){return j(t,"previousSibling")},nextUntil:function(t,e,n){return j(t,"nextSibling",n)},prevUntil:function(t,e,n){return j(t,"previousSibling",n)},siblings:function(t){return E((t.parentNode||{}).firstChild,t)},children:function(t){return E(t.firstChild)},contents:function(t){return null!=t.contentDocument&&i(t.contentDocument)?t.contentDocument:(k(t,"template")&&(t=t.content||t),w.merge([],t.childNodes))}},function(t,e){w.fn[t]=function(n,i){var o=w.map(this,e,n);return"Until"!==t.slice(-5)&&(i=n),i&&"string"==typeof i&&(o=w.filter(i,o)),1<this.length&&(L[t]||w.uniqueSort(o),O.test(t)&&o.reverse()),this.pushStack(o)}});var H=/[^\x20\t\r\n\f]+/g;function q(t){return t}function N(t){throw t}function M(t,e,n,i){var o;try{t&&f(o=t.promise)?o.call(t).done(e).fail(n):t&&f(o=t.then)?o.call(t,e,n):e.apply(void 0,[t].slice(i))}catch(t){n.apply(void 0,[t])}}w.Callbacks=function(t){var e,n;t="string"==typeof t?(e=t,n={},w.each(e.match(H)||[],function(t,e){n[e]=!0}),n):w.extend({},t);var i,o,s,r,a=[],l=[],c=-1,d=function(){for(r=r||t.once,s=i=!0;l.length;c=-1)for(o=l.shift();++c<a.length;)!1===a[c].apply(o[0],o[1])&&t.stopOnFalse&&(c=a.length,o=!1);t.memory||(o=!1),i=!1,r&&(a=o?[]:"")},p={add:function(){return a&&(o&&!i&&(c=a.length-1,l.push(o)),function e(n){w.each(n,function(n,i){f(i)?t.unique&&p.has(i)||a.push(i):i&&i.length&&"string"!==b(i)&&e(i)})}(arguments),o&&!i&&d()),this},remove:function(){return w.each(arguments,function(t,e){for(var n;-1<(n=w.inArray(e,a,n));)a.splice(n,1),n<=c&&c--}),this},has:function(t){return t?-1<w.inArray(t,a):0<a.length},empty:function(){return a&&(a=[]),this},disable:function(){return r=l=[],a=o="",this},disabled:function(){return!a},lock:function(){return r=l=[],o||i||(a=o=""),this},locked:function(){return!!r},fireWith:function(t,e){return r||(e=[t,(e=e||[]).slice?e.slice():e],l.push(e),i||d()),this},fire:function(){return p.fireWith(this,arguments),this},fired:function(){return!!s}};return p},w.extend({Deferred:function(e){var n=[["notify","progress",w.Callbacks("memory"),w.Callbacks("memory"),2],["resolve","done",w.Callbacks("once memory"),w.Callbacks("once memory"),0,"resolved"],["reject","fail",w.Callbacks("once memory"),w.Callbacks("once memory"),1,"rejected"]],i="pending",o={state:function(){return i},always:function(){return s.done(arguments).fail(arguments),this},catch:function(t){return o.then(null,t)},pipe:function(){var t=arguments;return w.Deferred(function(e){w.each(n,function(n,i){var o=f(t[i[4]])&&t[i[4]];s[i[1]](function(){var t=o&&o.apply(this,arguments);t&&f(t.promise)?t.promise().progress(e.notify).done(e.resolve).fail(e.reject):e[i[0]+"With"](this,o?[t]:arguments)})}),t=null}).promise()},then:function(e,i,o){var s=0;function r(e,n,i,o){return function(){var a=this,l=arguments,c=function(){var t,c;if(!(e<s)){if((t=i.apply(a,l))===n.promise())throw new TypeError("Thenable self-resolution");c=t&&("object"==typeof t||"function"==typeof t)&&t.then,f(c)?o?c.call(t,r(s,n,q,o),r(s,n,N,o)):(s++,c.call(t,r(s,n,q,o),r(s,n,N,o),r(s,n,q,n.notifyWith))):(i!==q&&(a=void 0,l=[t]),(o||n.resolveWith)(a,l))}},d=o?c:function(){try{c()}catch(t){w.Deferred.exceptionHook&&w.Deferred.exceptionHook(t,d.stackTrace),s<=e+1&&(i!==N&&(a=void 0,l=[t]),n.rejectWith(a,l))}};e?d():(w.Deferred.getStackHook&&(d.stackTrace=w.Deferred.getStackHook()),t.setTimeout(d))}}return w.Deferred(function(t){n[0][3].add(r(0,t,f(o)?o:q,t.notifyWith)),n[1][3].add(r(0,t,f(e)?e:q)),n[2][3].add(r(0,t,f(i)?i:N))}).promise()},promise:function(t){return null!=t?w.extend(t,o):o}},s={};return w.each(n,function(t,e){var r=e[2],a=e[5];o[e[1]]=r.add,a&&r.add(function(){i=a},n[3-t][2].disable,n[3-t][3].disable,n[0][2].lock,n[0][3].lock),r.add(e[3].fire),s[e[0]]=function(){return s[e[0]+"With"](this===s?void 0:this,arguments),this},s[e[0]+"With"]=r.fireWith}),o.promise(s),e&&e.call(s,s),s},when:function(t){var e=arguments.length,n=e,i=Array(n),s=o.call(arguments),r=w.Deferred(),a=function(t){return function(n){i[t]=this,s[t]=1<arguments.length?o.call(arguments):n,--e||r.resolveWith(i,s)}};if(e<=1&&(M(t,r.done(a(n)).resolve,r.reject,!e),"pending"===r.state()||f(s[n]&&s[n].then)))return r.then();for(;n--;)M(s[n],a(n),r.reject);return r.promise()}});var $=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;w.Deferred.exceptionHook=function(e,n){t.console&&t.console.warn&&e&&$.test(e.name)&&t.console.warn("jQuery.Deferred exception: "+e.message,e.stack,n)},w.readyException=function(e){t.setTimeout(function(){throw e})};var P=w.Deferred();function W(){m.removeEventListener("DOMContentLoaded",W),t.removeEventListener("load",W),w.ready()}w.fn.ready=function(t){return P.then(t).catch(function(t){w.readyException(t)}),this},w.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--w.readyWait:w.isReady)||(w.isReady=!0)!==t&&0<--w.readyWait||P.resolveWith(m,[w])}}),w.ready.then=P.then,"complete"===m.readyState||"loading"!==m.readyState&&!m.documentElement.doScroll?t.setTimeout(w.ready):(m.addEventListener("DOMContentLoaded",W),t.addEventListener("load",W));var R=function(t,e,n,i,o,s,r){var a=0,l=t.length,c=null==n;if("object"===b(n))for(a in o=!0,n)R(t,e,a,n[a],!0,s,r);else if(void 0!==i&&(o=!0,f(i)||(r=!0),c&&(r?(e.call(t,i),e=null):(c=e,e=function(t,e,n){return c.call(w(t),n)})),e))for(;a<l;a++)e(t[a],n,r?i:i.call(t[a],a,e(t[a],n)));return o?t:c?e.call(t):l?e(t[0],n):s},F=/^-ms-/,U=/-([a-z])/g;function z(t,e){return e.toUpperCase()}function X(t){return t.replace(F,"ms-").replace(U,z)}var V=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType};function Y(){this.expando=w.expando+Y.uid++}Y.uid=1,Y.prototype={cache:function(t){var e=t[this.expando];return e||(e=Object.create(null),V(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,n){var i,o=this.cache(t);if("string"==typeof e)o[X(e)]=n;else for(i in e)o[X(i)]=e[i];return o},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][X(e)]},access:function(t,e,n){return void 0===e||e&&"string"==typeof e&&void 0===n?this.get(t,e):(this.set(t,e,n),void 0!==n?n:e)},remove:function(t,e){var n,i=t[this.expando];if(void 0!==i){if(void 0!==e){n=(e=Array.isArray(e)?e.map(X):(e=X(e))in i?[e]:e.match(H)||[]).length;for(;n--;)delete i[e[n]]}(void 0===e||w.isEmptyObject(i))&&(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!w.isEmptyObject(e)}};var Q=new Y,G=new Y,J=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Z=/[A-Z]/g;function K(t,e,n){var i,o;if(void 0===n&&1===t.nodeType)if(i="data-"+e.replace(Z,"-$&").toLowerCase(),"string"==typeof(n=t.getAttribute(i))){try{n="true"===(o=n)||"false"!==o&&("null"===o?null:o===+o+""?+o:J.test(o)?JSON.parse(o):o)}catch(t){}G.set(t,e,n)}else n=void 0;return n}w.extend({hasData:function(t){return G.hasData(t)||Q.hasData(t)},data:function(t,e,n){return G.access(t,e,n)},removeData:function(t,e){G.remove(t,e)},_data:function(t,e,n){return Q.access(t,e,n)},_removeData:function(t,e){Q.remove(t,e)}}),w.fn.extend({data:function(t,e){var n,i,o,s=this[0],r=s&&s.attributes;if(void 0===t){if(this.length&&(o=G.get(s),1===s.nodeType&&!Q.get(s,"hasDataAttrs"))){for(n=r.length;n--;)r[n]&&0===(i=r[n].name).indexOf("data-")&&(i=X(i.slice(5)),K(s,i,o[i]));Q.set(s,"hasDataAttrs",!0)}return o}return"object"==typeof t?this.each(function(){G.set(this,t)}):R(this,function(e){var n;if(s&&void 0===e)return void 0!==(n=G.get(s,t))?n:void 0!==(n=K(s,t))?n:void 0;this.each(function(){G.set(this,t,e)})},null,e,1<arguments.length,null,!0)},removeData:function(t){return this.each(function(){G.remove(this,t)})}}),w.extend({queue:function(t,e,n){var i;if(t)return e=(e||"fx")+"queue",i=Q.get(t,e),n&&(!i||Array.isArray(n)?i=Q.access(t,e,w.makeArray(n)):i.push(n)),i||[]},dequeue:function(t,e){e=e||"fx";var n=w.queue(t,e),i=n.length,o=n.shift(),s=w._queueHooks(t,e);"inprogress"===o&&(o=n.shift(),i--),o&&("fx"===e&&n.unshift("inprogress"),delete s.stop,o.call(t,function(){w.dequeue(t,e)},s)),!i&&s&&s.empty.fire()},_queueHooks:function(t,e){var n=e+"queueHooks";return Q.get(t,n)||Q.access(t,n,{empty:w.Callbacks("once memory").add(function(){Q.remove(t,[e+"queue",n])})})}}),w.fn.extend({queue:function(t,e){var n=2;return"string"!=typeof t&&(e=t,t="fx",n--),arguments.length<n?w.queue(this[0],t):void 0===e?this:this.each(function(){var n=w.queue(this,t,e);w._queueHooks(this,t),"fx"===t&&"inprogress"!==n[0]&&w.dequeue(this,t)})},dequeue:function(t){return this.each(function(){w.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var n,i=1,o=w.Deferred(),s=this,r=this.length,a=function(){--i||o.resolveWith(s,[s])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";r--;)(n=Q.get(s[r],t+"queueHooks"))&&n.empty&&(i++,n.empty.add(a));return a(),o.promise(e)}});var tt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,et=new RegExp("^(?:([+-])=|)("+tt+")([a-z%]*)$","i"),nt=["Top","Right","Bottom","Left"],it=m.documentElement,ot=function(t){return w.contains(t.ownerDocument,t)},st={composed:!0};it.getRootNode&&(ot=function(t){return w.contains(t.ownerDocument,t)||t.getRootNode(st)===t.ownerDocument});var rt=function(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&ot(t)&&"none"===w.css(t,"display")};function at(t,e,n,i){var o,s,r=20,a=i?function(){return i.cur()}:function(){return w.css(t,e,"")},l=a(),c=n&&n[3]||(w.cssNumber[e]?"":"px"),d=t.nodeType&&(w.cssNumber[e]||"px"!==c&&+l)&&et.exec(w.css(t,e));if(d&&d[3]!==c){for(l/=2,c=c||d[3],d=+l||1;r--;)w.style(t,e,d+c),(1-s)*(1-(s=a()/l||.5))<=0&&(r=0),d/=s;d*=2,w.style(t,e,d+c),n=n||[]}return n&&(d=+d||+l||0,o=n[1]?d+(n[1]+1)*n[2]:+n[2],i&&(i.unit=c,i.start=d,i.end=o)),o}var lt={};function ct(t,e){for(var n,i,o,s,r,a,l,c=[],d=0,p=t.length;d<p;d++)(i=t[d]).style&&(n=i.style.display,e?("none"===n&&(c[d]=Q.get(i,"display")||null,c[d]||(i.style.display="")),""===i.style.display&&rt(i)&&(c[d]=(l=r=s=void 0,r=(o=i).ownerDocument,a=o.nodeName,(l=lt[a])||(s=r.body.appendChild(r.createElement(a)),l=w.css(s,"display"),s.parentNode.removeChild(s),"none"===l&&(l="block"),lt[a]=l)))):"none"!==n&&(c[d]="none",Q.set(i,"display",n)));for(d=0;d<p;d++)null!=c[d]&&(t[d].style.display=c[d]);return t}w.fn.extend({show:function(){return ct(this,!0)},hide:function(){return ct(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){rt(this)?w(this).show():w(this).hide()})}});var dt,pt,ut=/^(?:checkbox|radio)$/i,ht=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,ft=/^$|^module$|\/(?:java|ecma)script/i;dt=m.createDocumentFragment().appendChild(m.createElement("div")),(pt=m.createElement("input")).setAttribute("type","radio"),pt.setAttribute("checked","checked"),pt.setAttribute("name","t"),dt.appendChild(pt),h.checkClone=dt.cloneNode(!0).cloneNode(!0).lastChild.checked,dt.innerHTML="<textarea>x</textarea>",h.noCloneChecked=!!dt.cloneNode(!0).lastChild.defaultValue,dt.innerHTML="<option></option>",h.option=!!dt.lastChild;var gt={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function mt(t,e){var n;return n=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[],void 0===e||e&&k(t,e)?w.merge([t],n):n}function yt(t,e){for(var n=0,i=t.length;n<i;n++)Q.set(t[n],"globalEval",!e||Q.get(e[n],"globalEval"))}gt.tbody=gt.tfoot=gt.colgroup=gt.caption=gt.thead,gt.th=gt.td,h.option||(gt.optgroup=gt.option=[1,"<select multiple='multiple'>","</select>"]);var vt=/<|&#?\w+;/;function bt(t,e,n,i,o){for(var s,r,a,l,c,d,p=e.createDocumentFragment(),u=[],h=0,f=t.length;h<f;h++)if((s=t[h])||0===s)if("object"===b(s))w.merge(u,s.nodeType?[s]:s);else if(vt.test(s)){for(r=r||p.appendChild(e.createElement("div")),a=(ht.exec(s)||["",""])[1].toLowerCase(),l=gt[a]||gt._default,r.innerHTML=l[1]+w.htmlPrefilter(s)+l[2],d=l[0];d--;)r=r.lastChild;w.merge(u,r.childNodes),(r=p.firstChild).textContent=""}else u.push(e.createTextNode(s));for(p.textContent="",h=0;s=u[h++];)if(i&&-1<w.inArray(s,i))o&&o.push(s);else if(c=ot(s),r=mt(p.appendChild(s),"script"),c&&yt(r),n)for(d=0;s=r[d++];)ft.test(s.type||"")&&n.push(s);return p}var xt=/^key/,wt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Ct=/^([^.]*)(?:\.(.+)|)/;function Tt(){return!0}function jt(){return!1}function Et(t,e){return t===function(){try{return m.activeElement}catch(t){}}()==("focus"===e)}function St(t,e,n,i,o,s){var r,a;if("object"==typeof e){for(a in"string"!=typeof n&&(i=i||n,n=void 0),e)St(t,a,n,i,e[a],s);return t}if(null==i&&null==o?(o=n,i=n=void 0):null==o&&("string"==typeof n?(o=i,i=void 0):(o=i,i=n,n=void 0)),!1===o)o=jt;else if(!o)return t;return 1===s&&(r=o,(o=function(t){return w().off(t),r.apply(this,arguments)}).guid=r.guid||(r.guid=w.guid++)),t.each(function(){w.event.add(this,e,o,i,n)})}function kt(t,e,n){n?(Q.set(t,e,!1),w.event.add(t,e,{namespace:!1,handler:function(t){var i,s,r=Q.get(this,e);if(1&t.isTrigger&&this[e]){if(r.length)(w.event.special[e]||{}).delegateType&&t.stopPropagation();else if(r=o.call(arguments),Q.set(this,e,r),i=n(this,e),this[e](),r!==(s=Q.get(this,e))||i?Q.set(this,e,!1):s={},r!==s)return t.stopImmediatePropagation(),t.preventDefault(),s.value}else r.length&&(Q.set(this,e,{value:w.event.trigger(w.extend(r[0],w.Event.prototype),r.slice(1),this)}),t.stopImmediatePropagation())}})):void 0===Q.get(t,e)&&w.event.add(t,e,Tt)}w.event={global:{},add:function(t,e,n,i,o){var s,r,a,l,c,d,p,u,h,f,g,m=Q.get(t);if(V(t))for(n.handler&&(n=(s=n).handler,o=s.selector),o&&w.find.matchesSelector(it,o),n.guid||(n.guid=w.guid++),(l=m.events)||(l=m.events=Object.create(null)),(r=m.handle)||(r=m.handle=function(e){return void 0!==w&&w.event.triggered!==e.type?w.event.dispatch.apply(t,arguments):void 0}),c=(e=(e||"").match(H)||[""]).length;c--;)h=g=(a=Ct.exec(e[c])||[])[1],f=(a[2]||"").split(".").sort(),h&&(p=w.event.special[h]||{},h=(o?p.delegateType:p.bindType)||h,p=w.event.special[h]||{},d=w.extend({type:h,origType:g,data:i,handler:n,guid:n.guid,selector:o,needsContext:o&&w.expr.match.needsContext.test(o),namespace:f.join(".")},s),(u=l[h])||((u=l[h]=[]).delegateCount=0,p.setup&&!1!==p.setup.call(t,i,f,r)||t.addEventListener&&t.addEventListener(h,r)),p.add&&(p.add.call(t,d),d.handler.guid||(d.handler.guid=n.guid)),o?u.splice(u.delegateCount++,0,d):u.push(d),w.event.global[h]=!0)},remove:function(t,e,n,i,o){var s,r,a,l,c,d,p,u,h,f,g,m=Q.hasData(t)&&Q.get(t);if(m&&(l=m.events)){for(c=(e=(e||"").match(H)||[""]).length;c--;)if(h=g=(a=Ct.exec(e[c])||[])[1],f=(a[2]||"").split(".").sort(),h){for(p=w.event.special[h]||{},u=l[h=(i?p.delegateType:p.bindType)||h]||[],a=a[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),r=s=u.length;s--;)d=u[s],!o&&g!==d.origType||n&&n.guid!==d.guid||a&&!a.test(d.namespace)||i&&i!==d.selector&&("**"!==i||!d.selector)||(u.splice(s,1),d.selector&&u.delegateCount--,p.remove&&p.remove.call(t,d));r&&!u.length&&(p.teardown&&!1!==p.teardown.call(t,f,m.handle)||w.removeEvent(t,h,m.handle),delete l[h])}else for(h in l)w.event.remove(t,h+e[c],n,i,!0);w.isEmptyObject(l)&&Q.remove(t,"handle events")}},dispatch:function(t){var e,n,i,o,s,r,a=new Array(arguments.length),l=w.event.fix(t),c=(Q.get(this,"events")||Object.create(null))[l.type]||[],d=w.event.special[l.type]||{};for(a[0]=l,e=1;e<arguments.length;e++)a[e]=arguments[e];if(l.delegateTarget=this,!d.preDispatch||!1!==d.preDispatch.call(this,l)){for(r=w.event.handlers.call(this,l,c),e=0;(o=r[e++])&&!l.isPropagationStopped();)for(l.currentTarget=o.elem,n=0;(s=o.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==s.namespace&&!l.rnamespace.test(s.namespace)||(l.handleObj=s,l.data=s.data,void 0!==(i=((w.event.special[s.origType]||{}).handle||s.handler).apply(o.elem,a))&&!1===(l.result=i)&&(l.preventDefault(),l.stopPropagation()));return d.postDispatch&&d.postDispatch.call(this,l),l.result}},handlers:function(t,e){var n,i,o,s,r,a=[],l=e.delegateCount,c=t.target;if(l&&c.nodeType&&!("click"===t.type&&1<=t.button))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==t.type||!0!==c.disabled)){for(s=[],r={},n=0;n<l;n++)void 0===r[o=(i=e[n]).selector+" "]&&(r[o]=i.needsContext?-1<w(o,this).index(c):w.find(o,this,null,[c]).length),r[o]&&s.push(i);s.length&&a.push({elem:c,handlers:s})}return c=this,l<e.length&&a.push({elem:c,handlers:e.slice(l)}),a},addProp:function(t,e){Object.defineProperty(w.Event.prototype,t,{enumerable:!0,configurable:!0,get:f(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[w.expando]?t:new w.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){var e=this||t;return ut.test(e.type)&&e.click&&k(e,"input")&&kt(e,"click",Tt),!1},trigger:function(t){var e=this||t;return ut.test(e.type)&&e.click&&k(e,"input")&&kt(e,"click"),!0},_default:function(t){var e=t.target;return ut.test(e.type)&&e.click&&k(e,"input")&&Q.get(e,"click")||k(e,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},w.removeEvent=function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n)},w.Event=function(t,e){if(!(this instanceof w.Event))return new w.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?Tt:jt,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&w.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[w.expando]=!0},w.Event.prototype={constructor:w.Event,isDefaultPrevented:jt,isPropagationStopped:jt,isImmediatePropagationStopped:jt,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=Tt,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=Tt,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=Tt,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},w.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(t){var e=t.button;return null==t.which&&xt.test(t.type)?null!=t.charCode?t.charCode:t.keyCode:!t.which&&void 0!==e&&wt.test(t.type)?1&e?1:2&e?3:4&e?2:0:t.which}},w.event.addProp),w.each({focus:"focusin",blur:"focusout"},function(t,e){w.event.special[t]={setup:function(){return kt(this,t,Et),!1},trigger:function(){return kt(this,t),!0},delegateType:e}}),w.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,e){w.event.special[t]={delegateType:e,bindType:e,handle:function(t){var n,i=t.relatedTarget,o=t.handleObj;return i&&(i===this||w.contains(this,i))||(t.type=o.origType,n=o.handler.apply(this,arguments),t.type=e),n}}}),w.fn.extend({on:function(t,e,n,i){return St(this,t,e,n,i)},one:function(t,e,n,i){return St(this,t,e,n,i,1)},off:function(t,e,n){var i,o;if(t&&t.preventDefault&&t.handleObj)return i=t.handleObj,w(t.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof t){for(o in t)this.off(o,e,t[o]);return this}return!1!==e&&"function"!=typeof e||(n=e,e=void 0),!1===n&&(n=jt),this.each(function(){w.event.remove(this,t,n,e)})}});var Bt=/<script|<style|<link/i,Dt=/checked\s*(?:[^=]|=\s*.checked.)/i,It=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function At(t,e){return k(t,"table")&&k(11!==e.nodeType?e:e.firstChild,"tr")&&w(t).children("tbody")[0]||t}function Ot(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function Lt(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function _t(t,e){var n,i,o,s,r,a;if(1===e.nodeType){if(Q.hasData(t)&&(a=Q.get(t).events))for(o in Q.remove(e,"handle events"),a)for(n=0,i=a[o].length;n<i;n++)w.event.add(e,o,a[o][n]);G.hasData(t)&&(s=G.access(t),r=w.extend({},s),G.set(e,r))}}function Ht(t,e,n,i){e=s(e);var o,r,a,l,c,d,p=0,u=t.length,g=u-1,m=e[0],y=f(m);if(y||1<u&&"string"==typeof m&&!h.checkClone&&Dt.test(m))return t.each(function(o){var s=t.eq(o);y&&(e[0]=m.call(this,o,s.html())),Ht(s,e,n,i)});if(u&&(r=(o=bt(e,t[0].ownerDocument,!1,t,i)).firstChild,1===o.childNodes.length&&(o=r),r||i)){for(l=(a=w.map(mt(o,"script"),Ot)).length;p<u;p++)c=o,p!==g&&(c=w.clone(c,!0,!0),l&&w.merge(a,mt(c,"script"))),n.call(t[p],c,p);if(l)for(d=a[a.length-1].ownerDocument,w.map(a,Lt),p=0;p<l;p++)c=a[p],ft.test(c.type||"")&&!Q.access(c,"globalEval")&&w.contains(d,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?w._evalUrl&&!c.noModule&&w._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},d):v(c.textContent.replace(It,""),c,d))}return t}function qt(t,e,n){for(var i,o=e?w.filter(e,t):t,s=0;null!=(i=o[s]);s++)n||1!==i.nodeType||w.cleanData(mt(i)),i.parentNode&&(n&&ot(i)&&yt(mt(i,"script")),i.parentNode.removeChild(i));return t}w.extend({htmlPrefilter:function(t){return t},clone:function(t,e,n){var i,o,s,r,a,l,c,d=t.cloneNode(!0),p=ot(t);if(!(h.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||w.isXMLDoc(t)))for(r=mt(d),i=0,o=(s=mt(t)).length;i<o;i++)a=s[i],"input"===(c=(l=r[i]).nodeName.toLowerCase())&&ut.test(a.type)?l.checked=a.checked:"input"!==c&&"textarea"!==c||(l.defaultValue=a.defaultValue);if(e)if(n)for(s=s||mt(t),r=r||mt(d),i=0,o=s.length;i<o;i++)_t(s[i],r[i]);else _t(t,d);return 0<(r=mt(d,"script")).length&&yt(r,!p&&mt(t,"script")),d},cleanData:function(t){for(var e,n,i,o=w.event.special,s=0;void 0!==(n=t[s]);s++)if(V(n)){if(e=n[Q.expando]){if(e.events)for(i in e.events)o[i]?w.event.remove(n,i):w.removeEvent(n,i,e.handle);n[Q.expando]=void 0}n[G.expando]&&(n[G.expando]=void 0)}}}),w.fn.extend({detach:function(t){return qt(this,t,!0)},remove:function(t){return qt(this,t)},text:function(t){return R(this,function(t){return void 0===t?w.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)})},null,t,arguments.length)},append:function(){return Ht(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||At(this,t).appendChild(t)})},prepend:function(){return Ht(this,arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=At(this,t);e.insertBefore(t,e.firstChild)}})},before:function(){return Ht(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return Ht(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(w.cleanData(mt(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return w.clone(this,t,e)})},html:function(t){return R(this,function(t){var e=this[0]||{},n=0,i=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!Bt.test(t)&&!gt[(ht.exec(t)||["",""])[1].toLowerCase()]){t=w.htmlPrefilter(t);try{for(;n<i;n++)1===(e=this[n]||{}).nodeType&&(w.cleanData(mt(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var t=[];return Ht(this,arguments,function(e){var n=this.parentNode;w.inArray(this,t)<0&&(w.cleanData(mt(this)),n&&n.replaceChild(e,this))},t)}}),w.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,e){w.fn[t]=function(t){for(var n,i=[],o=w(t),s=o.length-1,a=0;a<=s;a++)n=a===s?this:this.clone(!0),w(o[a])[e](n),r.apply(i,n.get());return this.pushStack(i)}});var Nt=new RegExp("^("+tt+")(?!px)[a-z%]+$","i"),Mt=function(e){var n=e.ownerDocument.defaultView;return n&&n.opener||(n=t),n.getComputedStyle(e)},$t=function(t,e,n){var i,o,s={};for(o in e)s[o]=t.style[o],t.style[o]=e[o];for(o in i=n.call(t),e)t.style[o]=s[o];return i},Pt=new RegExp(nt.join("|"),"i");function Wt(t,e,n){var i,o,s,r,a=t.style;return(n=n||Mt(t))&&(""!==(r=n.getPropertyValue(e)||n[e])||ot(t)||(r=w.style(t,e)),!h.pixelBoxStyles()&&Nt.test(r)&&Pt.test(e)&&(i=a.width,o=a.minWidth,s=a.maxWidth,a.minWidth=a.maxWidth=a.width=r,r=n.width,a.width=i,a.minWidth=o,a.maxWidth=s)),void 0!==r?r+"":r}function Rt(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}!function(){function e(){if(d){c.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",d.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",it.appendChild(c).appendChild(d);var e=t.getComputedStyle(d);i="1%"!==e.top,l=12===n(e.marginLeft),d.style.right="60%",r=36===n(e.right),o=36===n(e.width),d.style.position="absolute",s=12===n(d.offsetWidth/3),it.removeChild(c),d=null}}function n(t){return Math.round(parseFloat(t))}var i,o,s,r,a,l,c=m.createElement("div"),d=m.createElement("div");d.style&&(d.style.backgroundClip="content-box",d.cloneNode(!0).style.backgroundClip="",h.clearCloneStyle="content-box"===d.style.backgroundClip,w.extend(h,{boxSizingReliable:function(){return e(),o},pixelBoxStyles:function(){return e(),r},pixelPosition:function(){return e(),i},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),s},reliableTrDimensions:function(){var e,n,i,o;return null==a&&(e=m.createElement("table"),n=m.createElement("tr"),i=m.createElement("div"),e.style.cssText="position:absolute;left:-11111px",n.style.height="1px",i.style.height="9px",it.appendChild(e).appendChild(n).appendChild(i),o=t.getComputedStyle(n),a=3<parseInt(o.height),it.removeChild(e)),a}}))}();var Ft=["Webkit","Moz","ms"],Ut=m.createElement("div").style,zt={};function Xt(t){return w.cssProps[t]||zt[t]||(t in Ut?t:zt[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),n=Ft.length;n--;)if((t=Ft[n]+e)in Ut)return t}(t)||t)}var Vt=/^(none|table(?!-c[ea]).+)/,Yt=/^--/,Qt={position:"absolute",visibility:"hidden",display:"block"},Gt={letterSpacing:"0",fontWeight:"400"};function Jt(t,e,n){var i=et.exec(e);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):e}function Zt(t,e,n,i,o,s){var r="width"===e?1:0,a=0,l=0;if(n===(i?"border":"content"))return 0;for(;r<4;r+=2)"margin"===n&&(l+=w.css(t,n+nt[r],!0,o)),i?("content"===n&&(l-=w.css(t,"padding"+nt[r],!0,o)),"margin"!==n&&(l-=w.css(t,"border"+nt[r]+"Width",!0,o))):(l+=w.css(t,"padding"+nt[r],!0,o),"padding"!==n?l+=w.css(t,"border"+nt[r]+"Width",!0,o):a+=w.css(t,"border"+nt[r]+"Width",!0,o));return!i&&0<=s&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-s-l-a-.5))||0),l}function Kt(t,e,n){var i=Mt(t),o=(!h.boxSizingReliable()||n)&&"border-box"===w.css(t,"boxSizing",!1,i),s=o,r=Wt(t,e,i),a="offset"+e[0].toUpperCase()+e.slice(1);if(Nt.test(r)){if(!n)return r;r="auto"}return(!h.boxSizingReliable()&&o||!h.reliableTrDimensions()&&k(t,"tr")||"auto"===r||!parseFloat(r)&&"inline"===w.css(t,"display",!1,i))&&t.getClientRects().length&&(o="border-box"===w.css(t,"boxSizing",!1,i),(s=a in t)&&(r=t[a])),(r=parseFloat(r)||0)+Zt(t,e,n||(o?"border":"content"),s,i,r)+"px"}function te(t,e,n,i,o){return new te.prototype.init(t,e,n,i,o)}w.extend({cssHooks:{opacity:{get:function(t,e){if(e){var n=Wt(t,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(t,e,n,i){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var o,s,r,a=X(e),l=Yt.test(e),c=t.style;if(l||(e=Xt(a)),r=w.cssHooks[e]||w.cssHooks[a],void 0===n)return r&&"get"in r&&void 0!==(o=r.get(t,!1,i))?o:c[e];"string"==(s=typeof n)&&(o=et.exec(n))&&o[1]&&(n=at(t,e,o),s="number"),null!=n&&n==n&&("number"!==s||l||(n+=o&&o[3]||(w.cssNumber[a]?"":"px")),h.clearCloneStyle||""!==n||0!==e.indexOf("background")||(c[e]="inherit"),r&&"set"in r&&void 0===(n=r.set(t,n,i))||(l?c.setProperty(e,n):c[e]=n))}},css:function(t,e,n,i){var o,s,r,a=X(e);return Yt.test(e)||(e=Xt(a)),(r=w.cssHooks[e]||w.cssHooks[a])&&"get"in r&&(o=r.get(t,!0,n)),void 0===o&&(o=Wt(t,e,i)),"normal"===o&&e in Gt&&(o=Gt[e]),""===n||n?(s=parseFloat(o),!0===n||isFinite(s)?s||0:o):o}}),w.each(["height","width"],function(t,e){w.cssHooks[e]={get:function(t,n,i){if(n)return!Vt.test(w.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?Kt(t,e,i):$t(t,Qt,function(){return Kt(t,e,i)})},set:function(t,n,i){var o,s=Mt(t),r=!h.scrollboxSize()&&"absolute"===s.position,a=(r||i)&&"border-box"===w.css(t,"boxSizing",!1,s),l=i?Zt(t,e,i,a,s):0;return a&&r&&(l-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(s[e])-Zt(t,e,"border",!1,s)-.5)),l&&(o=et.exec(n))&&"px"!==(o[3]||"px")&&(t.style[e]=n,n=w.css(t,e)),Jt(0,n,l)}}}),w.cssHooks.marginLeft=Rt(h.reliableMarginLeft,function(t,e){if(e)return(parseFloat(Wt(t,"marginLeft"))||t.getBoundingClientRect().left-$t(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}))+"px"}),w.each({margin:"",padding:"",border:"Width"},function(t,e){w.cssHooks[t+e]={expand:function(n){for(var i=0,o={},s="string"==typeof n?n.split(" "):[n];i<4;i++)o[t+nt[i]+e]=s[i]||s[i-2]||s[0];return o}},"margin"!==t&&(w.cssHooks[t+e].set=Jt)}),w.fn.extend({css:function(t,e){return R(this,function(t,e,n){var i,o,s={},r=0;if(Array.isArray(e)){for(i=Mt(t),o=e.length;r<o;r++)s[e[r]]=w.css(t,e[r],!1,i);return s}return void 0!==n?w.style(t,e,n):w.css(t,e)},t,e,1<arguments.length)}}),((w.Tween=te).prototype={constructor:te,init:function(t,e,n,i,o,s){this.elem=t,this.prop=n,this.easing=o||w.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=i,this.unit=s||(w.cssNumber[n]?"":"px")},cur:function(){var t=te.propHooks[this.prop];return t&&t.get?t.get(this):te.propHooks._default.get(this)},run:function(t){var e,n=te.propHooks[this.prop];return this.options.duration?this.pos=e=w.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):te.propHooks._default.set(this),this}}).init.prototype=te.prototype,(te.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=w.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){w.fx.step[t.prop]?w.fx.step[t.prop](t):1!==t.elem.nodeType||!w.cssHooks[t.prop]&&null==t.elem.style[Xt(t.prop)]?t.elem[t.prop]=t.now:w.style(t.elem,t.prop,t.now+t.unit)}}}).scrollTop=te.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},w.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},w.fx=te.prototype.init,w.fx.step={};var ee,ne,ie,oe,se=/^(?:toggle|show|hide)$/,re=/queueHooks$/;function ae(){ne&&(!1===m.hidden&&t.requestAnimationFrame?t.requestAnimationFrame(ae):t.setTimeout(ae,w.fx.interval),w.fx.tick())}function le(){return t.setTimeout(function(){ee=void 0}),ee=Date.now()}function ce(t,e){var n,i=0,o={height:t};for(e=e?1:0;i<4;i+=2-e)o["margin"+(n=nt[i])]=o["padding"+n]=t;return e&&(o.opacity=o.width=t),o}function de(t,e,n){for(var i,o=(pe.tweeners[e]||[]).concat(pe.tweeners["*"]),s=0,r=o.length;s<r;s++)if(i=o[s].call(n,e,t))return i}function pe(t,e,n){var i,o,s=0,r=pe.prefilters.length,a=w.Deferred().always(function(){delete l.elem}),l=function(){if(o)return!1;for(var e=ee||le(),n=Math.max(0,c.startTime+c.duration-e),i=1-(n/c.duration||0),s=0,r=c.tweens.length;s<r;s++)c.tweens[s].run(i);return a.notifyWith(t,[c,i,n]),i<1&&r?n:(r||a.notifyWith(t,[c,1,0]),a.resolveWith(t,[c]),!1)},c=a.promise({elem:t,props:w.extend({},e),opts:w.extend(!0,{specialEasing:{},easing:w.easing._default},n),originalProperties:e,originalOptions:n,startTime:ee||le(),duration:n.duration,tweens:[],createTween:function(e,n){var i=w.Tween(t,c.opts,e,n,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(i),i},stop:function(e){var n=0,i=e?c.tweens.length:0;if(o)return this;for(o=!0;n<i;n++)c.tweens[n].run(1);return e?(a.notifyWith(t,[c,1,0]),a.resolveWith(t,[c,e])):a.rejectWith(t,[c,e]),this}}),d=c.props;for(function(t,e){var n,i,o,s,r;for(n in t)if(o=e[i=X(n)],s=t[n],Array.isArray(s)&&(o=s[1],s=t[n]=s[0]),n!==i&&(t[i]=s,delete t[n]),(r=w.cssHooks[i])&&"expand"in r)for(n in s=r.expand(s),delete t[i],s)n in t||(t[n]=s[n],e[n]=o);else e[i]=o}(d,c.opts.specialEasing);s<r;s++)if(i=pe.prefilters[s].call(c,t,d,c.opts))return f(i.stop)&&(w._queueHooks(c.elem,c.opts.queue).stop=i.stop.bind(i)),i;return w.map(d,de,c),f(c.opts.start)&&c.opts.start.call(t,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),w.fx.timer(w.extend(l,{elem:t,anim:c,queue:c.opts.queue})),c}w.Animation=w.extend(pe,{tweeners:{"*":[function(t,e){var n=this.createTween(t,e);return at(n.elem,t,et.exec(e),n),n}]},tweener:function(t,e){f(t)?(e=t,t=["*"]):t=t.match(H);for(var n,i=0,o=t.length;i<o;i++)n=t[i],pe.tweeners[n]=pe.tweeners[n]||[],pe.tweeners[n].unshift(e)},prefilters:[function(t,e,n){var i,o,s,r,a,l,c,d,p="width"in e||"height"in e,u=this,h={},f=t.style,g=t.nodeType&&rt(t),m=Q.get(t,"fxshow");for(i in n.queue||(null==(r=w._queueHooks(t,"fx")).unqueued&&(r.unqueued=0,a=r.empty.fire,r.empty.fire=function(){r.unqueued||a()}),r.unqueued++,u.always(function(){u.always(function(){r.unqueued--,w.queue(t,"fx").length||r.empty.fire()})})),e)if(o=e[i],se.test(o)){if(delete e[i],s=s||"toggle"===o,o===(g?"hide":"show")){if("show"!==o||!m||void 0===m[i])continue;g=!0}h[i]=m&&m[i]||w.style(t,i)}if((l=!w.isEmptyObject(e))||!w.isEmptyObject(h))for(i in p&&1===t.nodeType&&(n.overflow=[f.overflow,f.overflowX,f.overflowY],null==(c=m&&m.display)&&(c=Q.get(t,"display")),"none"===(d=w.css(t,"display"))&&(c?d=c:(ct([t],!0),c=t.style.display||c,d=w.css(t,"display"),ct([t]))),("inline"===d||"inline-block"===d&&null!=c)&&"none"===w.css(t,"float")&&(l||(u.done(function(){f.display=c}),null==c&&(d=f.display,c="none"===d?"":d)),f.display="inline-block")),n.overflow&&(f.overflow="hidden",u.always(function(){f.overflow=n.overflow[0],f.overflowX=n.overflow[1],f.overflowY=n.overflow[2]})),l=!1,h)l||(m?"hidden"in m&&(g=m.hidden):m=Q.access(t,"fxshow",{display:c}),s&&(m.hidden=!g),g&&ct([t],!0),u.done(function(){for(i in g||ct([t]),Q.remove(t,"fxshow"),h)w.style(t,i,h[i])})),l=de(g?m[i]:0,i,u),i in m||(m[i]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?pe.prefilters.unshift(t):pe.prefilters.push(t)}}),w.speed=function(t,e,n){var i=t&&"object"==typeof t?w.extend({},t):{complete:n||!n&&e||f(t)&&t,duration:t,easing:n&&e||e&&!f(e)&&e};return w.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in w.fx.speeds?i.duration=w.fx.speeds[i.duration]:i.duration=w.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){f(i.old)&&i.old.call(this),i.queue&&w.dequeue(this,i.queue)},i},w.fn.extend({fadeTo:function(t,e,n,i){return this.filter(rt).css("opacity",0).show().end().animate({opacity:e},t,n,i)},animate:function(t,e,n,i){var o=w.isEmptyObject(t),s=w.speed(e,n,i),r=function(){var e=pe(this,w.extend({},t),s);(o||Q.get(this,"finish"))&&e.stop(!0)};return r.finish=r,o||!1===s.queue?this.each(r):this.queue(s.queue,r)},stop:function(t,e,n){var i=function(t){var e=t.stop;delete t.stop,e(n)};return"string"!=typeof t&&(n=e,e=t,t=void 0),e&&this.queue(t||"fx",[]),this.each(function(){var e=!0,o=null!=t&&t+"queueHooks",s=w.timers,r=Q.get(this);if(o)r[o]&&r[o].stop&&i(r[o]);else for(o in r)r[o]&&r[o].stop&&re.test(o)&&i(r[o]);for(o=s.length;o--;)s[o].elem!==this||null!=t&&s[o].queue!==t||(s[o].anim.stop(n),e=!1,s.splice(o,1));!e&&n||w.dequeue(this,t)})},finish:function(t){return!1!==t&&(t=t||"fx"),this.each(function(){var e,n=Q.get(this),i=n[t+"queue"],o=n[t+"queueHooks"],s=w.timers,r=i?i.length:0;for(n.finish=!0,w.queue(this,t,[]),o&&o.stop&&o.stop.call(this,!0),e=s.length;e--;)s[e].elem===this&&s[e].queue===t&&(s[e].anim.stop(!0),s.splice(e,1));for(e=0;e<r;e++)i[e]&&i[e].finish&&i[e].finish.call(this);delete n.finish})}}),w.each(["toggle","show","hide"],function(t,e){var n=w.fn[e];w.fn[e]=function(t,i,o){return null==t||"boolean"==typeof t?n.apply(this,arguments):this.animate(ce(e,!0),t,i,o)}}),w.each({slideDown:ce("show"),slideUp:ce("hide"),slideToggle:ce("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,e){w.fn[t]=function(t,n,i){return this.animate(e,t,n,i)}}),w.timers=[],w.fx.tick=function(){var t,e=0,n=w.timers;for(ee=Date.now();e<n.length;e++)(t=n[e])()||n[e]!==t||n.splice(e--,1);n.length||w.fx.stop(),ee=void 0},w.fx.timer=function(t){w.timers.push(t),w.fx.start()},w.fx.interval=13,w.fx.start=function(){ne||(ne=!0,ae())},w.fx.stop=function(){ne=null},w.fx.speeds={slow:600,fast:200,_default:400},w.fn.delay=function(e,n){return e=w.fx&&w.fx.speeds[e]||e,n=n||"fx",this.queue(n,function(n,i){var o=t.setTimeout(n,e);i.stop=function(){t.clearTimeout(o)}})},ie=m.createElement("input"),oe=m.createElement("select").appendChild(m.createElement("option")),ie.type="checkbox",h.checkOn=""!==ie.value,h.optSelected=oe.selected,(ie=m.createElement("input")).value="t",ie.type="radio",h.radioValue="t"===ie.value;var ue,he=w.expr.attrHandle;w.fn.extend({attr:function(t,e){return R(this,w.attr,t,e,1<arguments.length)},removeAttr:function(t){return this.each(function(){w.removeAttr(this,t)})}}),w.extend({attr:function(t,e,n){var i,o,s=t.nodeType;if(3!==s&&8!==s&&2!==s)return void 0===t.getAttribute?w.prop(t,e,n):(1===s&&w.isXMLDoc(t)||(o=w.attrHooks[e.toLowerCase()]||(w.expr.match.bool.test(e)?ue:void 0)),void 0!==n?null===n?void w.removeAttr(t,e):o&&"set"in o&&void 0!==(i=o.set(t,n,e))?i:(t.setAttribute(e,n+""),n):o&&"get"in o&&null!==(i=o.get(t,e))?i:null==(i=w.find.attr(t,e))?void 0:i)},attrHooks:{type:{set:function(t,e){if(!h.radioValue&&"radio"===e&&k(t,"input")){var n=t.value;return t.setAttribute("type",e),n&&(t.value=n),e}}}},removeAttr:function(t,e){var n,i=0,o=e&&e.match(H);if(o&&1===t.nodeType)for(;n=o[i++];)t.removeAttribute(n)}}),ue={set:function(t,e,n){return!1===e?w.removeAttr(t,n):t.setAttribute(n,n),n}},w.each(w.expr.match.bool.source.match(/\w+/g),function(t,e){var n=he[e]||w.find.attr;he[e]=function(t,e,i){var o,s,r=e.toLowerCase();return i||(s=he[r],he[r]=o,o=null!=n(t,e,i)?r:null,he[r]=s),o}});var fe=/^(?:input|select|textarea|button)$/i,ge=/^(?:a|area)$/i;function me(t){return(t.match(H)||[]).join(" ")}function ye(t){return t.getAttribute&&t.getAttribute("class")||""}function ve(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(H)||[]}w.fn.extend({prop:function(t,e){return R(this,w.prop,t,e,1<arguments.length)},removeProp:function(t){return this.each(function(){delete this[w.propFix[t]||t]})}}),w.extend({prop:function(t,e,n){var i,o,s=t.nodeType;if(3!==s&&8!==s&&2!==s)return 1===s&&w.isXMLDoc(t)||(e=w.propFix[e]||e,o=w.propHooks[e]),void 0!==n?o&&"set"in o&&void 0!==(i=o.set(t,n,e))?i:t[e]=n:o&&"get"in o&&null!==(i=o.get(t,e))?i:t[e]},propHooks:{tabIndex:{get:function(t){var e=w.find.attr(t,"tabindex");return e?parseInt(e,10):fe.test(t.nodeName)||ge.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),h.optSelected||(w.propHooks.selected={get:function(t){var e=t.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),w.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){w.propFix[this.toLowerCase()]=this}),w.fn.extend({addClass:function(t){var e,n,i,o,s,r,a,l=0;if(f(t))return this.each(function(e){w(this).addClass(t.call(this,e,ye(this)))});if((e=ve(t)).length)for(;n=this[l++];)if(o=ye(n),i=1===n.nodeType&&" "+me(o)+" "){for(r=0;s=e[r++];)i.indexOf(" "+s+" ")<0&&(i+=s+" ");o!==(a=me(i))&&n.setAttribute("class",a)}return this},removeClass:function(t){var e,n,i,o,s,r,a,l=0;if(f(t))return this.each(function(e){w(this).removeClass(t.call(this,e,ye(this)))});if(!arguments.length)return this.attr("class","");if((e=ve(t)).length)for(;n=this[l++];)if(o=ye(n),i=1===n.nodeType&&" "+me(o)+" "){for(r=0;s=e[r++];)for(;-1<i.indexOf(" "+s+" ");)i=i.replace(" "+s+" "," ");o!==(a=me(i))&&n.setAttribute("class",a)}return this},toggleClass:function(t,e){var n=typeof t,i="string"===n||Array.isArray(t);return"boolean"==typeof e&&i?e?this.addClass(t):this.removeClass(t):f(t)?this.each(function(n){w(this).toggleClass(t.call(this,n,ye(this),e),e)}):this.each(function(){var e,o,s,r;if(i)for(o=0,s=w(this),r=ve(t);e=r[o++];)s.hasClass(e)?s.removeClass(e):s.addClass(e);else void 0!==t&&"boolean"!==n||((e=ye(this))&&Q.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",e||!1===t?"":Q.get(this,"__className__")||""))})},hasClass:function(t){var e,n,i=0;for(e=" "+t+" ";n=this[i++];)if(1===n.nodeType&&-1<(" "+me(ye(n))+" ").indexOf(e))return!0;return!1}});var be=/\r/g;w.fn.extend({val:function(t){var e,n,i,o=this[0];return arguments.length?(i=f(t),this.each(function(n){var o;1===this.nodeType&&(null==(o=i?t.call(this,n,w(this).val()):t)?o="":"number"==typeof o?o+="":Array.isArray(o)&&(o=w.map(o,function(t){return null==t?"":t+""})),(e=w.valHooks[this.type]||w.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,o,"value")||(this.value=o))})):o?(e=w.valHooks[o.type]||w.valHooks[o.nodeName.toLowerCase()])&&"get"in e&&void 0!==(n=e.get(o,"value"))?n:"string"==typeof(n=o.value)?n.replace(be,""):null==n?"":n:void 0}}),w.extend({valHooks:{option:{get:function(t){var e=w.find.attr(t,"value");return null!=e?e:me(w.text(t))}},select:{get:function(t){var e,n,i,o=t.options,s=t.selectedIndex,r="select-one"===t.type,a=r?null:[],l=r?s+1:o.length;for(i=s<0?l:r?s:0;i<l;i++)if(((n=o[i]).selected||i===s)&&!n.disabled&&(!n.parentNode.disabled||!k(n.parentNode,"optgroup"))){if(e=w(n).val(),r)return e;a.push(e)}return a},set:function(t,e){for(var n,i,o=t.options,s=w.makeArray(e),r=o.length;r--;)((i=o[r]).selected=-1<w.inArray(w.valHooks.option.get(i),s))&&(n=!0);return n||(t.selectedIndex=-1),s}}}}),w.each(["radio","checkbox"],function(){w.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=-1<w.inArray(w(t).val(),e)}},h.checkOn||(w.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})}),h.focusin="onfocusin"in t;var xe=/^(?:focusinfocus|focusoutblur)$/,we=function(t){t.stopPropagation()};w.extend(w.event,{trigger:function(e,n,i,o){var s,r,a,l,c,p,u,h,y=[i||m],v=d.call(e,"type")?e.type:e,b=d.call(e,"namespace")?e.namespace.split("."):[];if(r=h=a=i=i||m,3!==i.nodeType&&8!==i.nodeType&&!xe.test(v+w.event.triggered)&&(-1<v.indexOf(".")&&(v=(b=v.split(".")).shift(),b.sort()),c=v.indexOf(":")<0&&"on"+v,(e=e[w.expando]?e:new w.Event(v,"object"==typeof e&&e)).isTrigger=o?2:3,e.namespace=b.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+b.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=i),n=null==n?[e]:w.makeArray(n,[e]),u=w.event.special[v]||{},o||!u.trigger||!1!==u.trigger.apply(i,n))){if(!o&&!u.noBubble&&!g(i)){for(l=u.delegateType||v,xe.test(l+v)||(r=r.parentNode);r;r=r.parentNode)y.push(r),a=r;a===(i.ownerDocument||m)&&y.push(a.defaultView||a.parentWindow||t)}for(s=0;(r=y[s++])&&!e.isPropagationStopped();)h=r,e.type=1<s?l:u.bindType||v,(p=(Q.get(r,"events")||Object.create(null))[e.type]&&Q.get(r,"handle"))&&p.apply(r,n),(p=c&&r[c])&&p.apply&&V(r)&&(e.result=p.apply(r,n),!1===e.result&&e.preventDefault());return e.type=v,o||e.isDefaultPrevented()||u._default&&!1!==u._default.apply(y.pop(),n)||!V(i)||c&&f(i[v])&&!g(i)&&((a=i[c])&&(i[c]=null),w.event.triggered=v,e.isPropagationStopped()&&h.addEventListener(v,we),i[v](),e.isPropagationStopped()&&h.removeEventListener(v,we),w.event.triggered=void 0,a&&(i[c]=a)),e.result}},simulate:function(t,e,n){var i=w.extend(new w.Event,n,{type:t,isSimulated:!0});w.event.trigger(i,null,e)}}),w.fn.extend({trigger:function(t,e){return this.each(function(){w.event.trigger(t,e,this)})},triggerHandler:function(t,e){var n=this[0];if(n)return w.event.trigger(t,e,n,!0)}}),h.focusin||w.each({focus:"focusin",blur:"focusout"},function(t,e){var n=function(t){w.event.simulate(e,t.target,w.event.fix(t))};w.event.special[e]={setup:function(){var i=this.ownerDocument||this.document||this,o=Q.access(i,e);o||i.addEventListener(t,n,!0),Q.access(i,e,(o||0)+1)},teardown:function(){var i=this.ownerDocument||this.document||this,o=Q.access(i,e)-1;o?Q.access(i,e,o):(i.removeEventListener(t,n,!0),Q.remove(i,e))}}});var Ce=t.location,Te={guid:Date.now()},je=/\?/;w.parseXML=function(e){var n;if(!e||"string"!=typeof e)return null;try{n=(new t.DOMParser).parseFromString(e,"text/xml")}catch(e){n=void 0}return n&&!n.getElementsByTagName("parsererror").length||w.error("Invalid XML: "+e),n};var Ee=/\[\]$/,Se=/\r?\n/g,ke=/^(?:submit|button|image|reset|file)$/i,Be=/^(?:input|select|textarea|keygen)/i;function De(t,e,n,i){var o;if(Array.isArray(e))w.each(e,function(e,o){n||Ee.test(t)?i(t,o):De(t+"["+("object"==typeof o&&null!=o?e:"")+"]",o,n,i)});else if(n||"object"!==b(e))i(t,e);else for(o in e)De(t+"["+o+"]",e[o],n,i)}w.param=function(t,e){var n,i=[],o=function(t,e){var n=f(e)?e():e;i[i.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==n?"":n)};if(null==t)return"";if(Array.isArray(t)||t.jquery&&!w.isPlainObject(t))w.each(t,function(){o(this.name,this.value)});else for(n in t)De(n,t[n],e,o);return i.join("&")},w.fn.extend({serialize:function(){return w.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=w.prop(this,"elements");return t?w.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!w(this).is(":disabled")&&Be.test(this.nodeName)&&!ke.test(t)&&(this.checked||!ut.test(t))}).map(function(t,e){var n=w(this).val();return null==n?null:Array.isArray(n)?w.map(n,function(t){return{name:e.name,value:t.replace(Se,"\r\n")}}):{name:e.name,value:n.replace(Se,"\r\n")}}).get()}});var Ie=/%20/g,Ae=/#.*$/,Oe=/([?&])_=[^&]*/,Le=/^(.*?):[ \t]*([^\r\n]*)$/gm,_e=/^(?:GET|HEAD)$/,He=/^\/\//,qe={},Ne={},Me="*/".concat("*"),$e=m.createElement("a");function Pe(t){return function(e,n){"string"!=typeof e&&(n=e,e="*");var i,o=0,s=e.toLowerCase().match(H)||[];if(f(n))for(;i=s[o++];)"+"===i[0]?(i=i.slice(1)||"*",(t[i]=t[i]||[]).unshift(n)):(t[i]=t[i]||[]).push(n)}}function We(t,e,n,i){var o={},s=t===Ne;function r(a){var l;return o[a]=!0,w.each(t[a]||[],function(t,a){var c=a(e,n,i);return"string"!=typeof c||s||o[c]?s?!(l=c):void 0:(e.dataTypes.unshift(c),r(c),!1)}),l}return r(e.dataTypes[0])||!o["*"]&&r("*")}function Re(t,e){var n,i,o=w.ajaxSettings.flatOptions||{};for(n in e)void 0!==e[n]&&((o[n]?t:i||(i={}))[n]=e[n]);return i&&w.extend(!0,t,i),t}$e.href=Ce.href,w.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ce.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Ce.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Me,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":w.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Re(Re(t,w.ajaxSettings),e):Re(w.ajaxSettings,t)},ajaxPrefilter:Pe(qe),ajaxTransport:Pe(Ne),ajax:function(e,n){"object"==typeof e&&(n=e,e=void 0),n=n||{};var i,o,s,r,a,l,c,d,p,u,h=w.ajaxSetup({},n),f=h.context||h,g=h.context&&(f.nodeType||f.jquery)?w(f):w.event,y=w.Deferred(),v=w.Callbacks("once memory"),b=h.statusCode||{},x={},C={},T="canceled",j={readyState:0,getResponseHeader:function(t){var e;if(c){if(!r)for(r={};e=Le.exec(s);)r[e[1].toLowerCase()+" "]=(r[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=r[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return c?s:null},setRequestHeader:function(t,e){return null==c&&(t=C[t.toLowerCase()]=C[t.toLowerCase()]||t,x[t]=e),this},overrideMimeType:function(t){return null==c&&(h.mimeType=t),this},statusCode:function(t){var e;if(t)if(c)j.always(t[j.status]);else for(e in t)b[e]=[b[e],t[e]];return this},abort:function(t){var e=t||T;return i&&i.abort(e),E(0,e),this}};if(y.promise(j),h.url=((e||h.url||Ce.href)+"").replace(He,Ce.protocol+"//"),h.type=n.method||n.type||h.method||h.type,h.dataTypes=(h.dataType||"*").toLowerCase().match(H)||[""],null==h.crossDomain){l=m.createElement("a");try{l.href=h.url,l.href=l.href,h.crossDomain=$e.protocol+"//"+$e.host!=l.protocol+"//"+l.host}catch(e){h.crossDomain=!0}}if(h.data&&h.processData&&"string"!=typeof h.data&&(h.data=w.param(h.data,h.traditional)),We(qe,h,n,j),c)return j;for(p in(d=w.event&&h.global)&&0==w.active++&&w.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!_e.test(h.type),o=h.url.replace(Ae,""),h.hasContent?h.data&&h.processData&&0===(h.contentType||"").indexOf("application/x-www-form-urlencoded")&&(h.data=h.data.replace(Ie,"+")):(u=h.url.slice(o.length),h.data&&(h.processData||"string"==typeof h.data)&&(o+=(je.test(o)?"&":"?")+h.data,delete h.data),!1===h.cache&&(o=o.replace(Oe,"$1"),u=(je.test(o)?"&":"?")+"_="+Te.guid+++u),h.url=o+u),h.ifModified&&(w.lastModified[o]&&j.setRequestHeader("If-Modified-Since",w.lastModified[o]),w.etag[o]&&j.setRequestHeader("If-None-Match",w.etag[o])),(h.data&&h.hasContent&&!1!==h.contentType||n.contentType)&&j.setRequestHeader("Content-Type",h.contentType),j.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+Me+"; q=0.01":""):h.accepts["*"]),h.headers)j.setRequestHeader(p,h.headers[p]);if(h.beforeSend&&(!1===h.beforeSend.call(f,j,h)||c))return j.abort();if(T="abort",v.add(h.complete),j.done(h.success),j.fail(h.error),i=We(Ne,h,n,j)){if(j.readyState=1,d&&g.trigger("ajaxSend",[j,h]),c)return j;h.async&&0<h.timeout&&(a=t.setTimeout(function(){j.abort("timeout")},h.timeout));try{c=!1,i.send(x,E)}catch(e){if(c)throw e;E(-1,e)}}else E(-1,"No Transport");function E(e,n,r,l){var p,u,m,x,C,T=n;c||(c=!0,a&&t.clearTimeout(a),i=void 0,s=l||"",j.readyState=0<e?4:0,p=200<=e&&e<300||304===e,r&&(x=function(t,e,n){for(var i,o,s,r,a=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=t.mimeType||e.getResponseHeader("Content-Type"));if(i)for(o in a)if(a[o]&&a[o].test(i)){l.unshift(o);break}if(l[0]in n)s=l[0];else{for(o in n){if(!l[0]||t.converters[o+" "+l[0]]){s=o;break}r||(r=o)}s=s||r}if(s)return s!==l[0]&&l.unshift(s),n[s]}(h,j,r)),!p&&-1<w.inArray("script",h.dataTypes)&&(h.converters["text script"]=function(){}),x=function(t,e,n,i){var o,s,r,a,l,c={},d=t.dataTypes.slice();if(d[1])for(r in t.converters)c[r.toLowerCase()]=t.converters[r];for(s=d.shift();s;)if(t.responseFields[s]&&(n[t.responseFields[s]]=e),!l&&i&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=s,s=d.shift())if("*"===s)s=l;else if("*"!==l&&l!==s){if(!(r=c[l+" "+s]||c["* "+s]))for(o in c)if((a=o.split(" "))[1]===s&&(r=c[l+" "+a[0]]||c["* "+a[0]])){!0===r?r=c[o]:!0!==c[o]&&(s=a[0],d.unshift(a[1]));break}if(!0!==r)if(r&&t.throws)e=r(e);else try{e=r(e)}catch(t){return{state:"parsererror",error:r?t:"No conversion from "+l+" to "+s}}}return{state:"success",data:e}}(h,x,j,p),p?(h.ifModified&&((C=j.getResponseHeader("Last-Modified"))&&(w.lastModified[o]=C),(C=j.getResponseHeader("etag"))&&(w.etag[o]=C)),204===e||"HEAD"===h.type?T="nocontent":304===e?T="notmodified":(T=x.state,u=x.data,p=!(m=x.error))):(m=T,!e&&T||(T="error",e<0&&(e=0))),j.status=e,j.statusText=(n||T)+"",p?y.resolveWith(f,[u,T,j]):y.rejectWith(f,[j,T,m]),j.statusCode(b),b=void 0,d&&g.trigger(p?"ajaxSuccess":"ajaxError",[j,h,p?u:m]),v.fireWith(f,[j,T]),d&&(g.trigger("ajaxComplete",[j,h]),--w.active||w.event.trigger("ajaxStop")))}return j},getJSON:function(t,e,n){return w.get(t,e,n,"json")},getScript:function(t,e){return w.get(t,void 0,e,"script")}}),w.each(["get","post"],function(t,e){w[e]=function(t,n,i,o){return f(n)&&(o=o||i,i=n,n=void 0),w.ajax(w.extend({url:t,type:e,dataType:o,data:n,success:i},w.isPlainObject(t)&&t))}}),w.ajaxPrefilter(function(t){var e;for(e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")}),w._evalUrl=function(t,e,n){return w.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){w.globalEval(t,e,n)}})},w.fn.extend({wrapAll:function(t){var e;return this[0]&&(f(t)&&(t=t.call(this[0])),e=w(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t}).append(this)),this},wrapInner:function(t){return f(t)?this.each(function(e){w(this).wrapInner(t.call(this,e))}):this.each(function(){var e=w(this),n=e.contents();n.length?n.wrapAll(t):e.append(t)})},wrap:function(t){var e=f(t);return this.each(function(n){w(this).wrapAll(e?t.call(this,n):t)})},unwrap:function(t){return this.parent(t).not("body").each(function(){w(this).replaceWith(this.childNodes)}),this}}),w.expr.pseudos.hidden=function(t){return!w.expr.pseudos.visible(t)},w.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},w.ajaxSettings.xhr=function(){try{return new t.XMLHttpRequest}catch(t){}};var Fe={0:200,1223:204},Ue=w.ajaxSettings.xhr();h.cors=!!Ue&&"withCredentials"in Ue,h.ajax=Ue=!!Ue,w.ajaxTransport(function(e){var n,i;if(h.cors||Ue&&!e.crossDomain)return{send:function(o,s){var r,a=e.xhr();if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(r in e.xhrFields)a[r]=e.xhrFields[r];for(r in e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),e.crossDomain||o["X-Requested-With"]||(o["X-Requested-With"]="XMLHttpRequest"),o)a.setRequestHeader(r,o[r]);n=function(t){return function(){n&&(n=i=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===t?a.abort():"error"===t?"number"!=typeof a.status?s(0,"error"):s(a.status,a.statusText):s(Fe[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=n(),i=a.onerror=a.ontimeout=n("error"),void 0!==a.onabort?a.onabort=i:a.onreadystatechange=function(){4===a.readyState&&t.setTimeout(function(){n&&i()})},n=n("abort");try{a.send(e.hasContent&&e.data||null)}catch(o){if(n)throw o}},abort:function(){n&&n()}}}),w.ajaxPrefilter(function(t){t.crossDomain&&(t.contents.script=!1)}),w.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return w.globalEval(t),t}}}),w.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")}),w.ajaxTransport("script",function(t){var e,n;if(t.crossDomain||t.scriptAttrs)return{send:function(i,o){e=w("<script>").attr(t.scriptAttrs||{}).prop({charset:t.scriptCharset,src:t.url}).on("load error",n=function(t){e.remove(),n=null,t&&o("error"===t.type?404:200,t.type)}),m.head.appendChild(e[0])},abort:function(){n&&n()}}});var ze,Xe=[],Ve=/(=)\?(?=&|$)|\?\?/;w.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=Xe.pop()||w.expando+"_"+Te.guid++;return this[t]=!0,t}}),w.ajaxPrefilter("json jsonp",function(e,n,i){var o,s,r,a=!1!==e.jsonp&&(Ve.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Ve.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return o=e.jsonpCallback=f(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Ve,"$1"+o):!1!==e.jsonp&&(e.url+=(je.test(e.url)?"&":"?")+e.jsonp+"="+o),e.converters["script json"]=function(){return r||w.error(o+" was not called"),r[0]},e.dataTypes[0]="json",s=t[o],t[o]=function(){r=arguments},i.always(function(){void 0===s?w(t).removeProp(o):t[o]=s,e[o]&&(e.jsonpCallback=n.jsonpCallback,Xe.push(o)),r&&f(s)&&s(r[0]),r=s=void 0}),"script"}),h.createHTMLDocument=((ze=m.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===ze.childNodes.length),w.parseHTML=function(t,e,n){return"string"!=typeof t?[]:("boolean"==typeof e&&(n=e,e=!1),e||(h.createHTMLDocument?((i=(e=m.implementation.createHTMLDocument("")).createElement("base")).href=m.location.href,e.head.appendChild(i)):e=m),s=!n&&[],(o=B.exec(t))?[e.createElement(o[1])]:(o=bt([t],e,s),s&&s.length&&w(s).remove(),w.merge([],o.childNodes)));var i,o,s},w.fn.load=function(t,e,n){var i,o,s,r=this,a=t.indexOf(" ");return-1<a&&(i=me(t.slice(a)),t=t.slice(0,a)),f(e)?(n=e,e=void 0):e&&"object"==typeof e&&(o="POST"),0<r.length&&w.ajax({url:t,type:o||"GET",dataType:"html",data:e}).done(function(t){s=arguments,r.html(i?w("<div>").append(w.parseHTML(t)).find(i):t)}).always(n&&function(t,e){r.each(function(){n.apply(this,s||[t.responseText,e,t])})}),this},w.expr.pseudos.animated=function(t){return w.grep(w.timers,function(e){return t===e.elem}).length},w.offset={setOffset:function(t,e,n){var i,o,s,r,a,l,c=w.css(t,"position"),d=w(t),p={};"static"===c&&(t.style.position="relative"),a=d.offset(),s=w.css(t,"top"),l=w.css(t,"left"),("absolute"===c||"fixed"===c)&&-1<(s+l).indexOf("auto")?(r=(i=d.position()).top,o=i.left):(r=parseFloat(s)||0,o=parseFloat(l)||0),f(e)&&(e=e.call(t,n,w.extend({},a))),null!=e.top&&(p.top=e.top-a.top+r),null!=e.left&&(p.left=e.left-a.left+o),"using"in e?e.using.call(t,p):("number"==typeof p.top&&(p.top+="px"),"number"==typeof p.left&&(p.left+="px"),d.css(p))}},w.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){w.offset.setOffset(this,t,e)});var e,n,i=this[0];return i?i.getClientRects().length?(e=i.getBoundingClientRect(),n=i.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,n,i=this[0],o={top:0,left:0};if("fixed"===w.css(i,"position"))e=i.getBoundingClientRect();else{for(e=this.offset(),n=i.ownerDocument,t=i.offsetParent||n.documentElement;t&&(t===n.body||t===n.documentElement)&&"static"===w.css(t,"position");)t=t.parentNode;t&&t!==i&&1===t.nodeType&&((o=w(t).offset()).top+=w.css(t,"borderTopWidth",!0),o.left+=w.css(t,"borderLeftWidth",!0))}return{top:e.top-o.top-w.css(i,"marginTop",!0),left:e.left-o.left-w.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&"static"===w.css(t,"position");)t=t.offsetParent;return t||it})}}),w.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,e){var n="pageYOffset"===e;w.fn[t]=function(i){return R(this,function(t,i,o){var s;if(g(t)?s=t:9===t.nodeType&&(s=t.defaultView),void 0===o)return s?s[e]:t[i];s?s.scrollTo(n?s.pageXOffset:o,n?o:s.pageYOffset):t[i]=o},t,i,arguments.length)}}),w.each(["top","left"],function(t,e){w.cssHooks[e]=Rt(h.pixelPosition,function(t,n){if(n)return n=Wt(t,e),Nt.test(n)?w(t).position()[e]+"px":n})}),w.each({Height:"height",Width:"width"},function(t,e){w.each({padding:"inner"+t,content:e,"":"outer"+t},function(n,i){w.fn[i]=function(o,s){var r=arguments.length&&(n||"boolean"!=typeof o),a=n||(!0===o||!0===s?"margin":"border");return R(this,function(e,n,o){var s;return g(e)?0===i.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(s=e.documentElement,Math.max(e.body["scroll"+t],s["scroll"+t],e.body["offset"+t],s["offset"+t],s["client"+t])):void 0===o?w.css(e,n,a):w.style(e,n,o,a)},e,r?o:void 0,r)}})}),w.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){w.fn[e]=function(t){return this.on(e,t)}}),w.fn.extend({bind:function(t,e,n){return this.on(t,null,e,n)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,n,i){return this.on(e,t,n,i)},undelegate:function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)},hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)}}),w.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(t,e){w.fn[e]=function(t,n){return 0<arguments.length?this.on(e,null,t,n):this.trigger(e)}});var Ye=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;w.proxy=function(t,e){var n,i,s;if("string"==typeof e&&(n=t[e],e=t,t=n),f(t))return i=o.call(arguments,2),(s=function(){return t.apply(e||this,i.concat(o.call(arguments)))}).guid=t.guid=t.guid||w.guid++,s},w.holdReady=function(t){t?w.readyWait++:w.ready(!0)},w.isArray=Array.isArray,w.parseJSON=JSON.parse,w.nodeName=k,w.isFunction=f,w.isWindow=g,w.camelCase=X,w.type=b,w.now=Date.now,w.isNumeric=function(t){var e=w.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},w.trim=function(t){return null==t?"":(t+"").replace(Ye,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return w});var Qe=t.jQuery,Ge=t.$;return w.noConflict=function(e){return t.$===w&&(t.$=Ge),e&&t.jQuery===w&&(t.jQuery=Qe),w},void 0===e&&(t.jQuery=t.$=w),w}),"function"!=typeof Object.create&&(Object.create=function(t){function e(){}return e.prototype=t,new e}),function(t,e,n,i){"use strict";var o={_positionClasses:["bottom-left","bottom-right","top-right","top-left","bottom-center","top-center","mid-center"],_defaultIcons:["success","error","info","warning"],init:function(e,n){this.prepareOptions(e,t.toast.options),this.process()},prepareOptions:function(e,n){var i={};"string"==typeof e||e instanceof Array?i.text=e:i=e,this.options=t.extend({},n,i)},process:function(){this.setup(),this.addToDom(),this.position(),this.bindToast(),this.animate()},setup:function(){var e="";if(this._toastEl=this._toastEl||t("<div></div>",{class:"jq-toast-single"}),e+='<span class="jq-toast-loader"></span>',this.options.allowToastClose&&(e+='<span class="close-jq-toast-single">&times;</span>'),this.options.text instanceof Array){this.options.heading&&(e+='<h2 class="jq-toast-heading">'+this.options.heading+"</h2>"),e+='<ul class="jq-toast-ul">';for(var n=0;n<this.options.text.length;n++)e+='<li class="jq-toast-li" id="jq-toast-item-'+n+'">'+this.options.text[n]+"</li>";e+="</ul>"}else this.options.heading&&(e+='<h2 class="jq-toast-heading">'+this.options.heading+"</h2>"),e+=this.options.text;this._toastEl.html(e),!1!==this.options.bgColor&&this._toastEl.css("background-color",this.options.bgColor),!1!==this.options.textColor&&this._toastEl.css("color",this.options.textColor),this.options.textAlign&&this._toastEl.css("text-align",this.options.textAlign),!1!==this.options.icon&&(this._toastEl.addClass("jq-has-icon"),-1!==t.inArray(this.options.icon,this._defaultIcons)&&this._toastEl.addClass("jq-icon-"+this.options.icon)),!1!==this.options.class&&this._toastEl.addClass(this.options.class)},position:function(){"string"==typeof this.options.position&&-1!==t.inArray(this.options.position,this._positionClasses)?"bottom-center"===this.options.position?this._container.css({left:t(e).outerWidth()/2-this._container.outerWidth()/2,bottom:20}):"top-center"===this.options.position?this._container.css({left:t(e).outerWidth()/2-this._container.outerWidth()/2,top:20}):"mid-center"===this.options.position?this._container.css({left:t(e).outerWidth()/2-this._container.outerWidth()/2,top:t(e).outerHeight()/2-this._container.outerHeight()/2}):this._container.addClass(this.options.position):"object"==typeof this.options.position?this._container.css({top:this.options.position.top?this.options.position.top:"auto",bottom:this.options.position.bottom?this.options.position.bottom:"auto",left:this.options.position.left?this.options.position.left:"auto",right:this.options.position.right?this.options.position.right:"auto"}):this._container.addClass("bottom-left")},bindToast:function(){var t=this;this._toastEl.on("afterShown",function(){t.processLoader()}),this._toastEl.find(".close-jq-toast-single").on("click",function(e){e.preventDefault(),"fade"===t.options.showHideTransition?(t._toastEl.trigger("beforeHide"),t._toastEl.fadeOut(function(){t._toastEl.trigger("afterHidden")})):"slide"===t.options.showHideTransition?(t._toastEl.trigger("beforeHide"),t._toastEl.slideUp(function(){t._toastEl.trigger("afterHidden")})):(t._toastEl.trigger("beforeHide"),t._toastEl.hide(function(){t._toastEl.trigger("afterHidden")}))}),"function"==typeof this.options.beforeShow&&this._toastEl.on("beforeShow",function(){t.options.beforeShow()}),"function"==typeof this.options.afterShown&&this._toastEl.on("afterShown",function(){t.options.afterShown()}),"function"==typeof this.options.beforeHide&&this._toastEl.on("beforeHide",function(){t.options.beforeHide()}),"function"==typeof this.options.afterHidden&&this._toastEl.on("afterHidden",function(){t.options.afterHidden()})},addToDom:function(){var e=t(".jq-toast-wrap");if(0===e.length?(e=t("<div></div>",{class:"jq-toast-wrap"}),t("body").append(e)):(!this.options.stack||isNaN(parseInt(this.options.stack,10)))&&e.empty(),e.find(".jq-toast-single:hidden").remove(),e.append(this._toastEl),this.options.stack&&!isNaN(parseInt(this.options.stack),10)){var n=e.find(".jq-toast-single").length-this.options.stack;n>0&&t(".jq-toast-wrap").find(".jq-toast-single").slice(0,n).remove()}this._container=e},canAutoHide:function(){return!1!==this.options.hideAfter&&!isNaN(parseInt(this.options.hideAfter,10))},processLoader:function(){if(!this.canAutoHide()||!1===this.options.loader)return!1;var t=this._toastEl.find(".jq-toast-loader"),e=(this.options.hideAfter-400)/1e3+"s",n=this.options.loaderBg,i=t.attr("style")||"";i=i.substring(0,i.indexOf("-webkit-transition")),i+="-webkit-transition: width "+e+" ease-in;                       -o-transition: width "+e+" ease-in;                       transition: width "+e+" ease-in;                       background-color: "+n+";",t.attr("style",i).addClass("jq-toast-loaded")},animate:function(){var t=this;if(this._toastEl.hide(),this._toastEl.trigger("beforeShow"),"fade"===this.options.showHideTransition.toLowerCase()?this._toastEl.fadeIn(function(){t._toastEl.trigger("afterShown")}):"slide"===this.options.showHideTransition.toLowerCase()?this._toastEl.slideDown(function(){t._toastEl.trigger("afterShown")}):this._toastEl.show(function(){t._toastEl.trigger("afterShown")}),this.canAutoHide()){t=this;e.setTimeout(function(){"fade"===t.options.showHideTransition.toLowerCase()?(t._toastEl.trigger("beforeHide"),t._toastEl.fadeOut(function(){t._toastEl.trigger("afterHidden")})):"slide"===t.options.showHideTransition.toLowerCase()?(t._toastEl.trigger("beforeHide"),t._toastEl.slideUp(function(){t._toastEl.trigger("afterHidden")})):(t._toastEl.trigger("beforeHide"),t._toastEl.hide(function(){t._toastEl.trigger("afterHidden")}))},this.options.hideAfter)}},reset:function(e){"all"===e?t(".jq-toast-wrap").remove():this._toastEl.remove()},update:function(t){this.prepareOptions(t,this.options),this.setup(),this.bindToast()}};t.toast=function(t){var e=Object.create(o);return e.init(t,this),{reset:function(t){e.reset(t)},update:function(t){e.update(t)}}},t.toast.options={text:"",heading:"",showHideTransition:"fade",allowToastClose:!0,hideAfter:3e3,loader:!0,loaderBg:"#9EC600",stack:5,position:"bottom-left",bgColor:!1,textColor:!1,textAlign:"left",icon:!1,beforeShow:function(){},afterShown:function(){},beforeHide:function(){},afterHidden:function(){}}}(jQuery,window,document),function(t,e){"function"==typeof define&&define.amd?define(["jquery"],function(n){return t.jBox=e(n)}):"object"==typeof module&&module.exports?module.exports=t.jBox=e(require("jquery")):t.jBox=e(t.jQuery)}(this,function(t){var e=jBoxWrapper(t);try{void 0!==jBoxConfirmWrapper&&jBoxConfirmWrapper&&jBoxConfirmWrapper(e,t)}catch(t){console.error(t)}try{void 0!==jBoxImageWrapper&&jBoxImageWrapper&&jBoxImageWrapper(e,t)}catch(t){console.error(t)}try{void 0!==jBoxNoticeWrapper&&jBoxNoticeWrapper&&jBoxNoticeWrapper(e,t)}catch(t){console.error(t)}return e}),"undefined"==typeof jQuery)throw new Error("Bootstrap's JavaScript requires jQuery");!function(t){"use strict";t.fn.emulateTransitionEnd=function(e){var n=!1,i=this;t(this).one("bsTransitionEnd",function(){n=!0});return setTimeout(function(){n||t(i).trigger(t.support.transition.end)},e),this},t(function(){t.support.transition=function(){var t=document.createElement("bootstrap"),e={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var n in e)if(void 0!==t.style[n])return{end:e[n]};return!1}(),t.support.transition&&(t.event.special.bsTransitionEnd={bindType:t.support.transition.end,delegateType:t.support.transition.end,handle:function(e){return t(e.target).is(this)?e.handleObj.handler.apply(this,arguments):void 0}})})}(jQuery),function(t){"use strict";var e='[data-dismiss="alert"]',n=function(n){t(n).on("click",e,this.close)};n.VERSION="3.2.0",n.prototype.close=function(e){function n(){s.detach().trigger("closed.bs.alert").remove()}var i=t(this),o=i.attr("data-target");o||(o=(o=i.attr("href"))&&o.replace(/.*(?=#[^\s]*$)/,""));var s=t(o);e&&e.preventDefault(),s.length||(s=i.hasClass("alert")?i:i.parent()),s.trigger(e=t.Event("close.bs.alert")),e.isDefaultPrevented()||(s.removeClass("in"),t.support.transition&&s.hasClass("fade")?s.one("bsTransitionEnd",n).emulateTransitionEnd(150):n())};var i=t.fn.alert;t.fn.alert=function(e){return this.each(function(){var i=t(this),o=i.data("bs.alert");o||i.data("bs.alert",o=new n(this)),"string"==typeof e&&o[e].call(i)})},t.fn.alert.Constructor=n,t.fn.alert.noConflict=function(){return t.fn.alert=i,this},t(document).on("click.bs.alert.data-api",e,n.prototype.close)}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),o=i.data("bs.button"),s="object"==typeof e&&e;o||i.data("bs.button",o=new n(this,s)),"toggle"==e?o.toggle():e&&o.setState(e)})}var n=function(e,i){this.$element=t(e),this.options=t.extend({},n.DEFAULTS,i),this.isLoading=!1};n.VERSION="3.2.0",n.DEFAULTS={loadingText:"loading..."},n.prototype.setState=function(e){var n="disabled",i=this.$element,o=i.is("input")?"val":"html",s=i.data();e+="Text",null==s.resetText&&i.data("resetText",i[o]()),i[o](null==s[e]?this.options[e]:s[e]),setTimeout(t.proxy(function(){"loadingText"==e?(this.isLoading=!0,i.addClass(n).attr(n,n)):this.isLoading&&(this.isLoading=!1,i.removeClass(n).removeAttr(n))},this),0)},n.prototype.toggle=function(){var t=!0,e=this.$element.closest('[data-toggle="buttons"]');if(e.length){var n=this.$element.find("input");"radio"==n.prop("type")&&(n.prop("checked")&&this.$element.hasClass("active")?t=!1:e.find(".active").removeClass("active")),t&&n.prop("checked",!this.$element.hasClass("active")).trigger("change")}t&&this.$element.toggleClass("active")};var i=t.fn.button;t.fn.button=e,t.fn.button.Constructor=n,t.fn.button.noConflict=function(){return t.fn.button=i,this},t(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(n){var i=t(n.target);i.hasClass("btn")||(i=i.closest(".btn")),e.call(i,"toggle"),n.preventDefault()})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),o=i.data("bs.carousel"),s=t.extend({},n.DEFAULTS,i.data(),"object"==typeof e&&e),r="string"==typeof e?e:s.slide;o||i.data("bs.carousel",o=new n(this,s)),"number"==typeof e?o.to(e):r?o[r]():s.interval&&o.pause().cycle()})}var n=function(e,n){this.$element=t(e).on("keydown.bs.carousel",t.proxy(this.keydown,this)),this.$indicators=this.$element.find(".carousel-indicators"),this.options=n,this.paused=this.sliding=this.interval=this.$active=this.$items=null,"hover"==this.options.pause&&this.$element.on("mouseenter.bs.carousel",t.proxy(this.pause,this)).on("mouseleave.bs.carousel",t.proxy(this.cycle,this))};n.VERSION="3.2.0",n.DEFAULTS={interval:5e3,pause:"hover",wrap:!0},n.prototype.keydown=function(t){switch(t.which){case 37:this.prev();break;case 39:this.next();break;default:return}t.preventDefault()},n.prototype.cycle=function(e){return e||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(t.proxy(this.next,this),this.options.interval)),this},n.prototype.getItemIndex=function(t){return this.$items=t.parent().children(".item"),this.$items.index(t||this.$active)},n.prototype.to=function(e){var n=this,i=this.getItemIndex(this.$active=this.$element.find(".item.active"));return e>this.$items.length-1||0>e?void 0:this.sliding?this.$element.one("slid.bs.carousel",function(){n.to(e)}):i==e?this.pause().cycle():this.slide(e>i?"next":"prev",t(this.$items[e]))},n.prototype.pause=function(e){return e||(this.paused=!0),this.$element.find(".next, .prev").length&&t.support.transition&&(this.$element.trigger(t.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this},n.prototype.next=function(){return this.sliding?void 0:this.slide("next")},n.prototype.prev=function(){return this.sliding?void 0:this.slide("prev")},n.prototype.slide=function(e,n){var i=this.$element.find(".item.active"),o=n||i[e](),s=this.interval,r="next"==e?"left":"right",a="next"==e?"first":"last",l=this;if(!o.length){if(!this.options.wrap)return;o=this.$element.find(".item")[a]()}if(o.hasClass("active"))return this.sliding=!1;var c=o[0],d=t.Event("slide.bs.carousel",{relatedTarget:c,direction:r});if(this.$element.trigger(d),!d.isDefaultPrevented()){if(this.sliding=!0,s&&this.pause(),this.$indicators.length){this.$indicators.find(".active").removeClass("active");var p=t(this.$indicators.children()[this.getItemIndex(o)]);p&&p.addClass("active")}var u=t.Event("slid.bs.carousel",{relatedTarget:c,direction:r});return t.support.transition&&this.$element.hasClass("slide")?(o.addClass(e),o[0].offsetWidth,i.addClass(r),o.addClass(r),i.one("bsTransitionEnd",function(){o.removeClass([e,r].join(" ")).addClass("active"),i.removeClass(["active",r].join(" ")),l.sliding=!1,setTimeout(function(){l.$element.trigger(u)},0)}).emulateTransitionEnd(1e3*i.css("transition-duration").slice(0,-1))):(i.removeClass("active"),o.addClass("active"),this.sliding=!1,this.$element.trigger(u)),s&&this.cycle(),this}};var i=t.fn.carousel;t.fn.carousel=e,t.fn.carousel.Constructor=n,t.fn.carousel.noConflict=function(){return t.fn.carousel=i,this},t(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",function(n){var i,o=t(this),s=t(o.attr("data-target")||(i=o.attr("href"))&&i.replace(/.*(?=#[^\s]+$)/,""));if(s.hasClass("carousel")){var r=t.extend({},s.data(),o.data()),a=o.attr("data-slide-to");a&&(r.interval=!1),e.call(s,r),a&&s.data("bs.carousel").to(a),n.preventDefault()}}),t(window).on("load",function(){t('[data-ride="carousel"]').each(function(){var n=t(this);e.call(n,n.data())})})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),o=i.data("bs.collapse"),s=t.extend({},n.DEFAULTS,i.data(),"object"==typeof e&&e);!o&&s.toggle&&"show"==e&&(e=!e),o||i.data("bs.collapse",o=new n(this,s)),"string"==typeof e&&o[e]()})}var n=function(e,i){this.$element=t(e),this.options=t.extend({},n.DEFAULTS,i),this.transitioning=null,this.options.parent&&(this.$parent=t(this.options.parent)),this.options.toggle&&this.toggle()};n.VERSION="3.2.0",n.DEFAULTS={toggle:!0},n.prototype.dimension=function(){return this.$element.hasClass("width")?"width":"height"},n.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var n=t.Event("show.bs.collapse");if(this.$element.trigger(n),!n.isDefaultPrevented()){var i=this.$parent&&this.$parent.find("> .panel > .in");if(i&&i.length){var o=i.data("bs.collapse");if(o&&o.transitioning)return;e.call(i,"hide"),o||i.data("bs.collapse",null)}var s=this.dimension();this.$element.removeClass("collapse").addClass("collapsing")[s](0),this.transitioning=1;var r=function(){this.$element.removeClass("collapsing").addClass("collapse in")[s](""),this.transitioning=0,this.$element.trigger("shown.bs.collapse")};if(!t.support.transition)return r.call(this);var a=t.camelCase(["scroll",s].join("-"));this.$element.one("bsTransitionEnd",t.proxy(r,this)).emulateTransitionEnd(350)[s](this.$element[0][a])}}},n.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var e=t.Event("hide.bs.collapse");if(this.$element.trigger(e),!e.isDefaultPrevented()){var n=this.dimension();this.$element[n](this.$element[n]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse").removeClass("in"),this.transitioning=1;var i=function(){this.transitioning=0,this.$element.trigger("hidden.bs.collapse").removeClass("collapsing").addClass("collapse")};return t.support.transition?void this.$element[n](0).one("bsTransitionEnd",t.proxy(i,this)).emulateTransitionEnd(350):i.call(this)}}},n.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()};var i=t.fn.collapse;t.fn.collapse=e,t.fn.collapse.Constructor=n,t.fn.collapse.noConflict=function(){return t.fn.collapse=i,this},t(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(n){var i,o=t(this),s=o.attr("data-target")||n.preventDefault()||(i=o.attr("href"))&&i.replace(/.*(?=#[^\s]+$)/,""),r=t(s),a=r.data("bs.collapse"),l=a?"toggle":o.data(),c=o.attr("data-parent"),d=c&&t(c);a&&a.transitioning||(d&&d.find('[data-toggle="collapse"][data-parent="'+c+'"]').not(o).addClass("collapsed"),o[r.hasClass("in")?"addClass":"removeClass"]("collapsed")),e.call(r,l)})}(jQuery),function(t){"use strict";function e(e){e&&3===e.which||(t(i).remove(),t(o).each(function(){var i=n(t(this)),o={relatedTarget:this};i.hasClass("open")&&(i.trigger(e=t.Event("hide.bs.dropdown",o)),e.isDefaultPrevented()||i.removeClass("open").trigger("hidden.bs.dropdown",o))}))}function n(e){var n=e.attr("data-target");n||(n=(n=e.attr("href"))&&/#[A-Za-z]/.test(n)&&n.replace(/.*(?=#[^\s]*$)/,""));var i=n&&t(n);return i&&i.length?i:e.parent()}var i=".dropdown-backdrop",o='[data-toggle="dropdown"]',s=function(e){t(e).on("click.bs.dropdown",this.toggle)};s.VERSION="3.2.0",s.prototype.toggle=function(i){var o=t(this);if(!o.is(".disabled, :disabled")){var s=n(o),r=s.hasClass("open");if(e(),!r){"ontouchstart"in document.documentElement&&!s.closest(".navbar-nav").length&&t('<div class="dropdown-backdrop"/>').insertAfter(t(this)).on("click",e);var a={relatedTarget:this};if(s.trigger(i=t.Event("show.bs.dropdown",a)),i.isDefaultPrevented())return;o.trigger("focus"),s.toggleClass("open").trigger("shown.bs.dropdown",a)}return!1}},s.prototype.keydown=function(e){if(/(38|40|27)/.test(e.keyCode)){var i=t(this);if(e.preventDefault(),e.stopPropagation(),!i.is(".disabled, :disabled")){var s=n(i),r=s.hasClass("open");if(!r||r&&27==e.keyCode)return 27==e.which&&s.find(o).trigger("focus"),i.trigger("click");var a=" li:not(.divider):visible a",l=s.find('[role="menu"]'+a+', [role="listbox"]'+a);if(l.length){var c=l.index(l.filter(":focus"));38==e.keyCode&&c>0&&c--,40==e.keyCode&&c<l.length-1&&c++,~c||(c=0),l.eq(c).trigger("focus")}}}};var r=t.fn.dropdown;t.fn.dropdown=function(e){return this.each(function(){var n=t(this),i=n.data("bs.dropdown");i||n.data("bs.dropdown",i=new s(this)),"string"==typeof e&&i[e].call(n)})},t.fn.dropdown.Constructor=s,t.fn.dropdown.noConflict=function(){return t.fn.dropdown=r,this},t(document).on("click.bs.dropdown.data-api",e).on("click.bs.dropdown.data-api",".dropdown form",function(t){t.stopPropagation()}).on("click.bs.dropdown.data-api",o,s.prototype.toggle).on("keydown.bs.dropdown.data-api",o+', [role="menu"], [role="listbox"]',s.prototype.keydown)}(jQuery),function(t){"use strict";function e(e,i){return this.each(function(){var o=t(this),s=o.data("bs.modal"),r=t.extend({},n.DEFAULTS,o.data(),"object"==typeof e&&e);s||o.data("bs.modal",s=new n(this,r)),"string"==typeof e?s[e](i):r.show&&s.show(i)})}var n=function(e,n){this.options=n,this.$body=t(document.body),this.$element=t(e),this.$backdrop=this.isShown=null,this.scrollbarWidth=0,this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,t.proxy(function(){this.$element.trigger("loaded.bs.modal")},this))};n.VERSION="3.2.0",n.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},n.prototype.toggle=function(t){return this.isShown?this.hide():this.show(t)},n.prototype.show=function(e){var n=this,i=t.Event("show.bs.modal",{relatedTarget:e});this.$element.trigger(i),this.isShown||i.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.$body.addClass("modal-open"),this.setScrollbar(),this.escape(),this.$element.on("click.dismiss.bs.modal",'[data-dismiss="modal"]',t.proxy(this.hide,this)),this.backdrop(function(){var i=t.support.transition&&n.$element.hasClass("fade");n.$element.parent().length||n.$element.appendTo(n.$body),n.$element.show().scrollTop(0),i&&n.$element[0].offsetWidth,n.$element.addClass("in").attr("aria-hidden",!1),n.enforceFocus();var o=t.Event("shown.bs.modal",{relatedTarget:e});i?n.$element.find(".modal-dialog").one("bsTransitionEnd",function(){n.$element.trigger("focus").trigger(o)}).emulateTransitionEnd(300):n.$element.trigger("focus").trigger(o)}))},n.prototype.hide=function(e){e&&e.preventDefault(),e=t.Event("hide.bs.modal"),this.$element.trigger(e),this.isShown&&!e.isDefaultPrevented()&&(this.isShown=!1,this.$body.removeClass("modal-open"),this.resetScrollbar(),this.escape(),t(document).off("focusin.bs.modal"),this.$element.removeClass("in").attr("aria-hidden",!0).off("click.dismiss.bs.modal"),t.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",t.proxy(this.hideModal,this)).emulateTransitionEnd(300):this.hideModal())},n.prototype.enforceFocus=function(){t(document).off("focusin.bs.modal").on("focusin.bs.modal",t.proxy(function(t){this.$element[0]===t.target||this.$element.has(t.target).length||this.$element.trigger("focus")},this))},n.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keyup.dismiss.bs.modal",t.proxy(function(t){27==t.which&&this.hide()},this)):this.isShown||this.$element.off("keyup.dismiss.bs.modal")},n.prototype.hideModal=function(){var t=this;this.$element.hide(),this.backdrop(function(){t.$element.trigger("hidden.bs.modal")})},n.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},n.prototype.backdrop=function(e){var n=this,i=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var o=t.support.transition&&i;if(this.$backdrop=t('<div class="modal-backdrop '+i+'" />').appendTo(this.$body),this.$element.on("click.dismiss.bs.modal",t.proxy(function(t){t.target===t.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus.call(this.$element[0]):this.hide.call(this))},this)),o&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!e)return;o?this.$backdrop.one("bsTransitionEnd",e).emulateTransitionEnd(150):e()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var s=function(){n.removeBackdrop(),e&&e()};t.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",s).emulateTransitionEnd(150):s()}else e&&e()},n.prototype.checkScrollbar=function(){document.body.clientWidth>=window.innerWidth||(this.scrollbarWidth=this.scrollbarWidth||this.measureScrollbar())},n.prototype.setScrollbar=function(){var t=parseInt(this.$body.css("padding-right")||0,10);this.scrollbarWidth&&this.$body.css("padding-right",t+this.scrollbarWidth)},n.prototype.resetScrollbar=function(){this.$body.css("padding-right","")},n.prototype.measureScrollbar=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",this.$body.append(t);var e=t.offsetWidth-t.clientWidth;return this.$body[0].removeChild(t),e};var i=t.fn.modal;t.fn.modal=e,t.fn.modal.Constructor=n,t.fn.modal.noConflict=function(){return t.fn.modal=i,this},t(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(n){var i=t(this),o=i.attr("href"),s=t(i.attr("data-target")||o&&o.replace(/.*(?=#[^\s]+$)/,"")),r=s.data("bs.modal")?"toggle":t.extend({remote:!/#/.test(o)&&o},s.data(),i.data());i.is("a")&&n.preventDefault(),s.one("show.bs.modal",function(t){t.isDefaultPrevented()||s.one("hidden.bs.modal",function(){i.is(":visible")&&i.trigger("focus")})}),e.call(s,r,this)})}(jQuery),function(t){"use strict";var e=function(t,e){this.type=this.options=this.enabled=this.timeout=this.hoverState=this.$element=null,this.init("tooltip",t,e)};e.VERSION="3.2.0",e.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0}},e.prototype.init=function(e,n,i){this.enabled=!0,this.type=e,this.$element=t(n),this.options=this.getOptions(i),this.$viewport=this.options.viewport&&t(this.options.viewport.selector||this.options.viewport);for(var o=this.options.trigger.split(" "),s=o.length;s--;){var r=o[s];if("click"==r)this.$element.on("click."+this.type,this.options.selector,t.proxy(this.toggle,this));else if("manual"!=r){var a="hover"==r?"mouseenter":"focusin",l="hover"==r?"mouseleave":"focusout";this.$element.on(a+"."+this.type,this.options.selector,t.proxy(this.enter,this)),this.$element.on(l+"."+this.type,this.options.selector,t.proxy(this.leave,this))}}this.options.selector?this._options=t.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},e.prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.getOptions=function(e){return(e=t.extend({},this.getDefaults(),this.$element.data(),e)).delay&&"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),e},e.prototype.getDelegateOptions=function(){var e={},n=this.getDefaults();return this._options&&t.each(this._options,function(t,i){n[t]!=i&&(e[t]=i)}),e},e.prototype.enter=function(e){var n=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);return n||(n=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,n)),clearTimeout(n.timeout),n.hoverState="in",n.options.delay&&n.options.delay.show?void(n.timeout=setTimeout(function(){"in"==n.hoverState&&n.show()},n.options.delay.show)):n.show()},e.prototype.leave=function(e){var n=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);return n||(n=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,n)),clearTimeout(n.timeout),n.hoverState="out",n.options.delay&&n.options.delay.hide?void(n.timeout=setTimeout(function(){"out"==n.hoverState&&n.hide()},n.options.delay.hide)):n.hide()},e.prototype.show=function(){var e=t.Event("show.bs."+this.type);if(this.hasContent()&&this.enabled){this.$element.trigger(e);var n=t.contains(document.documentElement,this.$element[0]);if(e.isDefaultPrevented()||!n)return;var i=this,o=this.tip(),s=this.getUID(this.type);this.setContent(),o.attr("id",s),this.$element.attr("aria-describedby",s),this.options.animation&&o.addClass("fade");var r="function"==typeof this.options.placement?this.options.placement.call(this,o[0],this.$element[0]):this.options.placement,a=/\s?auto?\s?/i,l=a.test(r);l&&(r=r.replace(a,"")||"top"),o.detach().css({top:0,left:0,display:"block"}).addClass(r).data("bs."+this.type,this),this.options.container?o.appendTo(this.options.container):o.insertAfter(this.$element);var c=this.getPosition(),d=o[0].offsetWidth,p=o[0].offsetHeight;if(l){var u=r,h=this.$element.parent(),f=this.getPosition(h);r="bottom"==r&&c.top+c.height+p-f.scroll>f.height?"top":"top"==r&&c.top-f.scroll-p<0?"bottom":"right"==r&&c.right+d>f.width?"left":"left"==r&&c.left-d<f.left?"right":r,o.removeClass(u).addClass(r)}var g=this.getCalculatedOffset(r,c,d,p);this.applyPlacement(g,r);var m=function(){i.$element.trigger("shown.bs."+i.type),i.hoverState=null};t.support.transition&&this.$tip.hasClass("fade")?o.one("bsTransitionEnd",m).emulateTransitionEnd(150):m()}},e.prototype.applyPlacement=function(e,n){var i=this.tip(),o=i[0].offsetWidth,s=i[0].offsetHeight,r=parseInt(i.css("margin-top"),10),a=parseInt(i.css("margin-left"),10);isNaN(r)&&(r=0),isNaN(a)&&(a=0),e.top=e.top+r,e.left=e.left+a,t.offset.setOffset(i[0],t.extend({using:function(t){i.css({top:Math.round(t.top),left:Math.round(t.left)})}},e),0),i.addClass("in");var l=i[0].offsetWidth,c=i[0].offsetHeight;"top"==n&&c!=s&&(e.top=e.top+s-c);var d=this.getViewportAdjustedDelta(n,e,l,c);d.left?e.left+=d.left:e.top+=d.top;var p=d.left?2*d.left-o+l:2*d.top-s+c,u=d.left?"left":"top",h=d.left?"offsetWidth":"offsetHeight";i.offset(e),this.replaceArrow(p,i[0][h],u)},e.prototype.replaceArrow=function(t,e,n){this.arrow().css(n,t?50*(1-t/e)+"%":"")},e.prototype.setContent=function(){var t=this.tip(),e=this.getTitle();t.find(".tooltip-inner")[this.options.html?"html":"text"](e),t.removeClass("fade in top bottom left right")},e.prototype.hide=function(){function e(){"in"!=n.hoverState&&i.detach(),n.$element.trigger("hidden.bs."+n.type)}var n=this,i=this.tip(),o=t.Event("hide.bs."+this.type);return this.$element.removeAttr("aria-describedby"),this.$element.trigger(o),o.isDefaultPrevented()?void 0:(i.removeClass("in"),t.support.transition&&this.$tip.hasClass("fade")?i.one("bsTransitionEnd",e).emulateTransitionEnd(150):e(),this.hoverState=null,this)},e.prototype.fixTitle=function(){var t=this.$element;(t.attr("title")||"string"!=typeof t.attr("data-original-title"))&&t.attr("data-original-title",t.attr("title")||"").attr("title","")},e.prototype.hasContent=function(){return this.getTitle()},e.prototype.getPosition=function(e){var n=(e=e||this.$element)[0],i="BODY"==n.tagName;return t.extend({},"function"==typeof n.getBoundingClientRect?n.getBoundingClientRect():null,{scroll:i?document.documentElement.scrollTop||document.body.scrollTop:e.scrollTop(),width:i?t(window).width():e.outerWidth(),height:i?t(window).height():e.outerHeight()},i?{top:0,left:0}:e.offset())},e.prototype.getCalculatedOffset=function(t,e,n,i){return"bottom"==t?{top:e.top+e.height,left:e.left+e.width/2-n/2}:"top"==t?{top:e.top-i,left:e.left+e.width/2-n/2}:"left"==t?{top:e.top+e.height/2-i/2,left:e.left-n}:{top:e.top+e.height/2-i/2,left:e.left+e.width}},e.prototype.getViewportAdjustedDelta=function(t,e,n,i){var o={top:0,left:0};if(!this.$viewport)return o;var s=this.options.viewport&&this.options.viewport.padding||0,r=this.getPosition(this.$viewport);if(/right|left/.test(t)){var a=e.top-s-r.scroll,l=e.top+s-r.scroll+i;a<r.top?o.top=r.top-a:l>r.top+r.height&&(o.top=r.top+r.height-l)}else{var c=e.left-s,d=e.left+s+n;c<r.left?o.left=r.left-c:d>r.width&&(o.left=r.left+r.width-d)}return o},e.prototype.getTitle=function(){var t=this.$element,e=this.options;return t.attr("data-original-title")||("function"==typeof e.title?e.title.call(t[0]):e.title)},e.prototype.getUID=function(t){do{t+=~~(1e6*Math.random())}while(document.getElementById(t));return t},e.prototype.tip=function(){return this.$tip=this.$tip||t(this.options.template)},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},e.prototype.validate=function(){this.$element[0].parentNode||(this.hide(),this.$element=null,this.options=null)},e.prototype.enable=function(){this.enabled=!0},e.prototype.disable=function(){this.enabled=!1},e.prototype.toggleEnabled=function(){this.enabled=!this.enabled},e.prototype.toggle=function(e){var n=this;e&&((n=t(e.currentTarget).data("bs."+this.type))||(n=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,n))),n.tip().hasClass("in")?n.leave(n):n.enter(n)},e.prototype.destroy=function(){clearTimeout(this.timeout),this.hide().$element.off("."+this.type).removeData("bs."+this.type)};var n=t.fn.tooltip;t.fn.tooltip=function(n){return this.each(function(){var i=t(this),o=i.data("bs.tooltip"),s="object"==typeof n&&n;(o||"destroy"!=n)&&(o||i.data("bs.tooltip",o=new e(this,s)),"string"==typeof n&&o[n]())})},t.fn.tooltip.Constructor=e,t.fn.tooltip.noConflict=function(){return t.fn.tooltip=n,this}}(jQuery),function(t){"use strict";var e=function(t,e){this.init("popover",t,e)};if(!t.fn.tooltip)throw new Error("Popover requires tooltip.js");e.VERSION="3.2.0",e.DEFAULTS=t.extend({},t.fn.tooltip.Constructor.DEFAULTS,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),e.prototype=t.extend({},t.fn.tooltip.Constructor.prototype),e.prototype.constructor=e,e.prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.setContent=function(){var t=this.tip(),e=this.getTitle(),n=this.getContent();t.find(".popover-title")[this.options.html?"html":"text"](e),t.find(".popover-content").empty()[this.options.html?"string"==typeof n?"html":"append":"text"](n),t.removeClass("fade top bottom left right in"),t.find(".popover-title").html()||t.find(".popover-title").hide()},e.prototype.hasContent=function(){return this.getTitle()||this.getContent()},e.prototype.getContent=function(){var t=this.$element,e=this.options;return t.attr("data-content")||("function"==typeof e.content?e.content.call(t[0]):e.content)},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".arrow")},e.prototype.tip=function(){return this.$tip||(this.$tip=t(this.options.template)),this.$tip};var n=t.fn.popover;t.fn.popover=function(n){return this.each(function(){var i=t(this),o=i.data("bs.popover"),s="object"==typeof n&&n;(o||"destroy"!=n)&&(o||i.data("bs.popover",o=new e(this,s)),"string"==typeof n&&o[n]())})},t.fn.popover.Constructor=e,t.fn.popover.noConflict=function(){return t.fn.popover=n,this}}(jQuery),function(t){"use strict";function e(n,i){var o=t.proxy(this.process,this);this.$body=t("body"),this.$scrollElement=t(t(n).is("body")?window:n),this.options=t.extend({},e.DEFAULTS,i),this.selector=(this.options.target||"")+" .nav li > a",this.offsets=[],this.targets=[],this.activeTarget=null,this.scrollHeight=0,this.$scrollElement.on("scroll.bs.scrollspy",o),this.refresh(),this.process()}function n(n){return this.each(function(){var i=t(this),o=i.data("bs.scrollspy"),s="object"==typeof n&&n;o||i.data("bs.scrollspy",o=new e(this,s)),"string"==typeof n&&o[n]()})}e.VERSION="3.2.0",e.DEFAULTS={offset:10},e.prototype.getScrollHeight=function(){return this.$scrollElement[0].scrollHeight||Math.max(this.$body[0].scrollHeight,document.documentElement.scrollHeight)},e.prototype.refresh=function(){var e="offset",n=0;t.isWindow(this.$scrollElement[0])||(e="position",n=this.$scrollElement.scrollTop()),this.offsets=[],this.targets=[],this.scrollHeight=this.getScrollHeight();var i=this;this.$body.find(this.selector).map(function(){var i=t(this),o=i.data("target")||i.attr("href"),s=/^#./.test(o)&&t(o);return s&&s.length&&s.is(":visible")&&[[s[e]().top+n,o]]||null}).sort(function(t,e){return t[0]-e[0]}).each(function(){i.offsets.push(this[0]),i.targets.push(this[1])})},e.prototype.process=function(){var t,e=this.$scrollElement.scrollTop()+this.options.offset,n=this.getScrollHeight(),i=this.options.offset+n-this.$scrollElement.height(),o=this.offsets,s=this.targets,r=this.activeTarget;if(this.scrollHeight!=n&&this.refresh(),e>=i)return r!=(t=s[s.length-1])&&this.activate(t);if(r&&e<=o[0])return r!=(t=s[0])&&this.activate(t);for(t=o.length;t--;)r!=s[t]&&e>=o[t]&&(!o[t+1]||e<=o[t+1])&&this.activate(s[t])},e.prototype.activate=function(e){this.activeTarget=e,t(this.selector).parentsUntil(this.options.target,".active").removeClass("active");var n=this.selector+'[data-target="'+e+'"],'+this.selector+'[href="'+e+'"]',i=t(n).parents("li").addClass("active");i.parent(".dropdown-menu").length&&(i=i.closest("li.dropdown").addClass("active")),i.trigger("activate.bs.scrollspy")};var i=t.fn.scrollspy;t.fn.scrollspy=n,t.fn.scrollspy.Constructor=e,t.fn.scrollspy.noConflict=function(){return t.fn.scrollspy=i,this},t(window).on("load.bs.scrollspy.data-api",function(){t('[data-spy="scroll"]').each(function(){var e=t(this);n.call(e,e.data())})})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),o=i.data("bs.tab");o||i.data("bs.tab",o=new n(this)),"string"==typeof e&&o[e]()})}var n=function(e){this.element=t(e)};n.VERSION="3.2.0",n.prototype.show=function(){var e=this.element,n=e.closest("ul:not(.dropdown-menu)"),i=e.data("target");if(i||(i=(i=e.attr("href"))&&i.replace(/.*(?=#[^\s]*$)/,"")),!e.parent("li").hasClass("active")){var o=n.find(".active:last a")[0],s=t.Event("show.bs.tab",{relatedTarget:o});if(e.trigger(s),!s.isDefaultPrevented()){var r=t(i);this.activate(e.closest("li"),n),this.activate(r,r.parent(),function(){e.trigger({type:"shown.bs.tab",relatedTarget:o})})}}},n.prototype.activate=function(e,n,i){function o(){s.removeClass("active").find("> .dropdown-menu > .active").removeClass("active"),e.addClass("active"),r?(e[0].offsetWidth,e.addClass("in")):e.removeClass("fade"),e.parent(".dropdown-menu")&&e.closest("li.dropdown").addClass("active"),i&&i()}var s=n.find("> .active"),r=i&&t.support.transition&&s.hasClass("fade");r?s.one("bsTransitionEnd",o).emulateTransitionEnd(150):o(),s.removeClass("in")};var i=t.fn.tab;t.fn.tab=e,t.fn.tab.Constructor=n,t.fn.tab.noConflict=function(){return t.fn.tab=i,this},t(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"]',function(n){n.preventDefault(),e.call(t(this),"show")})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),o=i.data("bs.affix"),s="object"==typeof e&&e;o||i.data("bs.affix",o=new n(this,s)),"string"==typeof e&&o[e]()})}var n=function(e,i){this.options=t.extend({},n.DEFAULTS,i),this.$target=t(this.options.target).on("scroll.bs.affix.data-api",t.proxy(this.checkPosition,this)).on("click.bs.affix.data-api",t.proxy(this.checkPositionWithEventLoop,this)),this.$element=t(e),this.affixed=this.unpin=this.pinnedOffset=null,this.checkPosition()};n.VERSION="3.2.0",n.RESET="affix affix-top affix-bottom",n.DEFAULTS={offset:0,target:window},n.prototype.getPinnedOffset=function(){if(this.pinnedOffset)return this.pinnedOffset;this.$element.removeClass(n.RESET).addClass("affix");var t=this.$target.scrollTop(),e=this.$element.offset();return this.pinnedOffset=e.top-t},n.prototype.checkPositionWithEventLoop=function(){setTimeout(t.proxy(this.checkPosition,this),1)},n.prototype.checkPosition=function(){if(this.$element.is(":visible")){var e=t(document).height(),i=this.$target.scrollTop(),o=this.$element.offset(),s=this.options.offset,r=s.top,a=s.bottom;"object"!=typeof s&&(a=r=s),"function"==typeof r&&(r=s.top(this.$element)),"function"==typeof a&&(a=s.bottom(this.$element));var l=!(null!=this.unpin&&i+this.unpin<=o.top)&&(null!=a&&o.top+this.$element.height()>=e-a?"bottom":null!=r&&r>=i&&"top");if(this.affixed!==l){null!=this.unpin&&this.$element.css("top","");var c="affix"+(l?"-"+l:""),d=t.Event(c+".bs.affix");this.$element.trigger(d),d.isDefaultPrevented()||(this.affixed=l,this.unpin="bottom"==l?this.getPinnedOffset():null,this.$element.removeClass(n.RESET).addClass(c).trigger(t.Event(c.replace("affix","affixed"))),"bottom"==l&&this.$element.offset({top:e-this.$element.height()-a}))}}};var i=t.fn.affix;t.fn.affix=e,t.fn.affix.Constructor=n,t.fn.affix.noConflict=function(){return t.fn.affix=i,this},t(window).on("load",function(){t('[data-spy="affix"]').each(function(){var n=t(this),i=n.data();i.offset=i.offset||{},i.offsetBottom&&(i.offset.bottom=i.offsetBottom),i.offsetTop&&(i.offset.top=i.offsetTop),e.call(n,i)})})}(jQuery);const Common={redirectToLoginPage:function(){window.location.replace("index.html")},trimEnd:function(t,e){for(;t.charAt(t.length-1)===e;)t=t.substring(0,t.length-1);return t},get:function(t,e,n,i){var o=new XMLHttpRequest;o.onreadystatechange=function(){if(4===this.readyState&&200===this.status&&e)if(""!==this.responseText){var t=JSON.parse(this.responseText);e(t)}else e()},o.onerror=function(){n&&n()},o.open("GET",t,!0),i&&o.setRequestHeader("Authorization",i),o.send()},post:function(t,e,n,i,o,s){var r=new XMLHttpRequest;r.onreadystatechange=function(){if(4===this.readyState&&200===this.status&&e)if(""!==this.responseText){var t=JSON.parse(this.responseText);e(t)}else e()},r.onerror=function(){n&&n()},r.open("POST",t,!0),o&&r.setRequestHeader("Authorization",o),!0===s?r.send(i):r.send(JSON.stringify(i))},launchType:function(t){switch(t){case 0:return"Startup";case 1:return"Trigger";case 2:return"Periodic";case 3:return"Cron";default:return""}},status:function(t,e){switch(e){case 0:return"<img src='images/pending-small.png' /> <span class='st-pending'>"+t.get("status-pending-label")+"</span>";case 1:return"<img src='images/running-small.png' /> <span class='st-running'>"+t.get("status-running-label")+"</span>";case 2:return"<img src='images/done-small.png' /> <span class='st-done'>"+t.get("status-done-label")+"</span>";case 3:return"<img src='images/failed-small.png' /> <span class='st-failed'>"+t.get("status-failed-label")+"</span>";case 4:return"<img src='images/warning-small.png' /> <span class='st-warning'>"+t.get("status-warning-label")+"</span>";case 6:return"<img src='images/stopped-small.png' /> <span class='st-stopped'>"+t.get("status-stopped-label")+"</span>";case 7:return"<img src='images/disapproved-small.png' /> <span class='st-rejected'>"+t.get("status-disapproved-label")+"</span>";default:return""}},disableButton:function(t,e){t.disabled=e},formatDate:function(t){return("0"+t.getDate()).slice(-2)+"-"+("0"+(t.getMonth()+1)).slice(-2)+"-"+t.getFullYear()+" "+("0"+t.getHours()).slice(-2)+":"+("0"+t.getMinutes()).slice(-2)+":"+("0"+t.getSeconds()).slice(-2)},os:function(){var t="Unknown";return-1!==window.navigator.userAgent.indexOf("Windows NT 10.0")&&(t="Windows 10"),-1!==window.navigator.userAgent.indexOf("Windows NT 6.2")&&(t="Windows 8"),-1!==window.navigator.userAgent.indexOf("Windows NT 6.1")&&(t="Windows 7"),-1!==window.navigator.userAgent.indexOf("Windows NT 6.0")&&(t="Windows Vista"),-1!==window.navigator.userAgent.indexOf("Windows NT 5.1")&&(t="Windows XP"),-1!==window.navigator.userAgent.indexOf("Windows NT 5.0")&&(t="Windows 2000"),-1!==window.navigator.userAgent.indexOf("Mac")&&(t="Mac/iOS"),-1!==window.navigator.userAgent.indexOf("X11")&&(t="UNIX"),-1!==window.navigator.userAgent.indexOf("Linux")&&(t="Linux"),t},toastInfo:function(t){$.toast({heading:"Information",text:t,hideAfter:5e3,icon:"info"})},toastSuccess:function(t){$.toast({heading:"Success",text:t,hideAfter:5e3,icon:"success"})},toastError:function(t){$.toast({heading:"Error",text:t,hideAfter:5e3,icon:"error"})},escape:function(t){return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")},removeItemOnce:function(t,e){var n=t.indexOf(e);return n>-1&&t.splice(n,1),t}};function authorize(t,e,n){set("authorize",'{"Username": "'+t+'", "Password":"'+e+'","UserProfile":'+n+"}")}function getUser(){return get("authorize")}function deleteUser(){remove("authorize")}function set(t,e){isIE()?setCookie(t,e,365):window.localStorage.setItem(t,e)}function get(t){return isIE()?getCookie(t):window.localStorage.getItem(t)}function remove(t){isIE()?setCookie(t,"",-365):window.localStorage.removeItem(t)}function setCookie(t,e,n){let i=new Date;i.setTime(i.getTime()+24*n*60*60*1e3);let o="expires="+i.toUTCString();document.cookie=t+"="+e+";"+o+";path=/"}function getCookie(t){let e=t+"=",n=document.cookie.split(";");for(let t=0;t<n.length;t++){let i=n[t];for(;" "===i.charAt(0);)i=i.substring(1);if(0===i.indexOf(e))return i.substring(e.length,i.length)}return""}function isIE(){let t=navigator.userAgent;return t.indexOf("MSIE ")>-1||t.indexOf("Trident/")>-1}const Language=function(t,e){"use strict";let n=this;function i(){let t=navigator.userAgent;return t.indexOf("MSIE ")>-1||t.indexOf("Trident/")>-1}this.get=function(t){return languageModule.languages[n.getLanguage()][t]||languageModule.languages.en[t]},this.setLanguage=function(t){var e,n;e="language",n=t,i()?function(t,e,n){let i=new Date;i.setTime(i.getTime()+24*n*60*60*1e3);let o="expires="+i.toUTCString();document.cookie=t+"="+e+";"+o+";path=/"}(e,n,365):window.localStorage.setItem(e,n)},this.getLanguage=function(){let t=(e="language",i()?function(t){let e=t+"=",n=document.cookie.split(";");for(let t=0;t<n.length;t++){let i=n[t];for(;" "===i.charAt(0);)i=i.substring(1);if(0===i.indexOf(e))return i.substring(e.length,i.length)}return""}(e):window.localStorage.getItem(e));var e;return t||"en"},this.init=function(){let i=function(){let t=8-n.get("language").length,e="";if(t>0)for(let n=0;n<t;n++)e+="&nbsp;&nbsp;";return e},o='<button class="btn btn-default btn-xs dropdown-toggle" type="button" data-toggle="dropdown"><span id="lang-label">'+n.get("language")+i()+'</span> <span class="caret"></span></button><ul class="dropdown-menu" role="menu">';for(let t=0;t<languageModule.codes.length;t++)o+='<li><div class="lang"><img src="'+languageModule.codes[t].Icon+'" alt="">&nbsp;'+languageModule.codes[t].Name+'<input type="hidden" class="lang-code" value="'+languageModule.codes[t].Code+'" /></div></li>';o+=" </ul>";let s=document.getElementById(t);s.classList.add("btn-group"),s.innerHTML=o;let r=document.getElementsByClassName("lang");for(let t=0;t<r.length;t++)r[t].onclick=function(){let t=this.querySelector(".lang-code").value;n.setLanguage(t),e(n),document.getElementById("lang-label").innerHTML=n.get("language")+i()};e(n)}};window.onload=function(){"use strict";let t=function(){let t=document.getElementsByClassName("jBox-content")[0];t&&(t.querySelector(".edit-record-td-id").innerHTML=n.get("edit-record-td-id"),t.querySelector(".edit-record-td-name").innerHTML=n.get("edit-record-td-name"),t.querySelector(".edit-record-td-description").innerHTML=n.get("edit-record-td-description"),t.querySelector(".edit-record-td-approved").innerHTML=n.get("edit-record-td-approved"),t.querySelector(".edit-record-td-start-date").innerHTML=n.get("edit-record-td-start-date"),t.querySelector(".edit-record-td-end-date").innerHTML=n.get("edit-record-td-end-date"),t.querySelector(".edit-record-td-comments").innerHTML=n.get("edit-record-td-comments"),t.querySelector(".edit-record-td-manager-comments").innerHTML=n.get("edit-record-td-manager-comments"),t.querySelector(".edit-record-td-created-by").innerHTML=n.get("edit-record-td-created-by"),t.querySelector(".edit-record-td-created-on").innerHTML=n.get("edit-record-td-created-on"),t.querySelector(".edit-record-td-modified-by").innerHTML=n.get("edit-record-td-modified-by"),t.querySelector(".edit-record-td-modified-on").innerHTML=n.get("edit-record-td-modified-on"),t.querySelector(".edit-record-td-assigned-to").innerHTML=n.get("edit-record-td-assigned-to"),t.querySelector(".edit-record-td-assigned-on").innerHTML=n.get("edit-record-td-assigned-on"),t.querySelector(".edit-record-td-approvers").innerHTML=n.get("edit-record-td-approvers"),t.querySelector(".th-approved-by").innerHTML=n.get("th-approved-by"),t.querySelector(".th-approved").innerHTML=n.get("th-approved"),t.querySelector(".th-approved-on").innerHTML=n.get("th-approved-on"),t.querySelector(".edit-record-td-versions").innerHTML=n.get("edit-record-td-versions"),t.querySelector(".btn-upload-version").value=n.get("btn-upload-version"));let e=document.getElementsByClassName("jBox-footer")[0];e&&(e.querySelector(".record-save").innerHTML=n.get("record-save"),e.querySelector(".record-cancel").innerHTML=n.get("record-cancel"),e.querySelector(".record-delete").innerHTML=n.get("record-delete"))},e=function(){document.getElementById("th-name")&&(document.getElementById("th-name").innerHTML=n.get("record-name")),document.getElementById("th-approved")&&(document.getElementById("th-approved").innerHTML=n.get("record-approved")),document.getElementById("th-start-date")&&(document.getElementById("th-start-date").innerHTML=n.get("record-start-date")),document.getElementById("th-end-date")&&(document.getElementById("th-end-date").innerHTML=n.get("record-end-date")),document.getElementById("th-assigned-to")&&(document.getElementById("th-assigned-to").innerHTML=n.get("record-assigned-to")),document.getElementById("th-assigned-on")&&(document.getElementById("th-assigned-on").innerHTML=n.get("record-assigned-on"))},n=new Language("lang",function(t){document.getElementById("lnk-records").innerHTML=t.get("lnk-records"),document.getElementById("lnk-approval").innerHTML=t.get("lnk-approval"),document.getElementById("lnk-dashboard").innerHTML=t.get("lnk-dashboard"),document.getElementById("lnk-manager").innerHTML=t.get("lnk-manager"),document.getElementById("lnk-designer").innerHTML=t.get("lnk-designer"),document.getElementById("lnk-history").innerHTML=t.get("lnk-history"),document.getElementById("lnk-users").innerHTML=t.get("lnk-users"),document.getElementById("lnk-profiles").innerHTML=t.get("lnk-profiles"),document.getElementById("spn-logout").innerHTML=t.get("spn-logout"),document.getElementById("search-records").placeholder=t.get("search-records"),document.getElementById("btn-delete").innerHTML=t.get("btn-delete-record"),document.getElementById("btn-new-record").innerHTML=t.get("btn-new-record"),e()});n.init();let i=Common.trimEnd(Settings.Uri,"/"),o=document.getElementById("lnk-records"),s=document.getElementById("lnk-manager"),r=document.getElementById("lnk-designer"),a=document.getElementById("lnk-approval"),l=document.getElementById("lnk-users"),c=document.getElementById("lnk-profiles"),d=document.getElementById("lnk-notifications"),p=document.getElementById("img-notifications"),u=document.getElementById("search-records"),h="",f="",g=-1,m="",y=null,v=getUser();if(null===v||""===v)Common.redirectToLoginPage();else{let x=JSON.parse(v);function b(){let o=function(o){let r=[];for(let t=0;t<o.length;t++){let e=o[t];r.push("<tr><td class='check' "+(1==g?"style='display: none;'":"")+"><input type='checkbox'></td><td class='id'>"+e.Id+"</td><td class='name'>"+e.Name+"</td><td class='approved'><input class='record-approved' type='checkbox' "+(!0===e.Approved?"checked":"")+" disabled></td><td class='start-date'>"+(""===e.StartDate?"-":e.StartDate)+"</td><td class='end-date'>"+(""===e.EndDate?"-":e.EndDate)+"</td><td class='assigned-to'>"+(""===e.AssignedTo?"-":e.AssignedTo)+"</td><td class='assigned-on'>"+(""===e.AssignedOn?"-":e.AssignedOn)+"</td></tr>")}let a="<table id='records-table' class='table'><thead class='thead-dark'><tr><th class='check' "+(1==g?"style='display: none;'":"")+"><input id='check-all' type='checkbox'></th><th class='id'></th><th id='th-name' class='name'>Name</th><th id='th-approved' class='approved'>Approved</th><th id='th-start-date' class='start-date'>Start date</th><th id='th-end-date' class='end-date'>End date</th><th id='th-assigned-to' class='assigned-to'>Assigned to</th><th id='th-assigned-on' class='assigned-on'>Assigned on</th></tr></thead><tbody>"+r.join("")+"</tbody></table>";document.getElementById("content").innerHTML=a,e();let l=function(t){for(let e=0;e<o.length;e++){let n=o[e];if(n.Id===t)return n}return null},c=document.getElementById("records-table").getElementsByTagName("tbody")[0].getElementsByTagName("tr"),d=[];for(let e=0;e<c.length;e++){let o=c[e];o.getElementsByClassName("check")[0].firstChild.onchange=function(){let t=this.parentElement.parentElement.getElementsByClassName("id")[0].innerHTML;!0===this.checked?d.push(t):d=Common.removeItemOnce(d,t)},!0===o.querySelector(".record-approved").checked&&(o.querySelector(".name").innerHTML+='&nbsp;&nbsp;<span class="label label-approved">Approved</span>'),o.onclick=function(e){if(e.target.type&&"checkbox"===e.target.type)return;let o=this.getElementsByClassName("id")[0].innerHTML,r=l(o),a=JSON.parse(JSON.stringify(r));a.ModifiedBy=h;let p=1===g&&r.CreatedBy!==h;y&&y.destroy(),(y=new jBox("Modal",{width:800,height:420,title:n.get("record-information"),content:document.getElementById("edit-record").innerHTML,footer:document.getElementById("edit-record-footer").innerHTML,overlay:!0,delayOpen:0,onOpen:function(){t();let e=document.getElementsByClassName("jBox-content")[0];e.querySelector(".record-id").value=r.Id,e.querySelector(".record-name").value=r.Name,e.querySelector(".record-description").innerHTML=r.Description,e.querySelector(".record-approved").checked=r.Approved,e.querySelector(".record-start-date").value=r.StartDate,e.querySelector(".record-end-date").value=r.EndDate,e.querySelector(".record-comments").innerHTML=r.Comments,e.querySelector(".record-manager-comments").innerHTML=r.ManagerComments,e.querySelector(".record-created-by").value=r.CreatedBy,e.querySelector(".record-created-on").value=r.CreatedOn,e.querySelector(".record-modified-by").value=r.ModifiedBy,e.querySelector(".record-modified-on").value=r.ModifiedOn,e.querySelector(".record-assigned-to").value=r.AssignedTo,e.querySelector(".record-assigned-on").value=r.AssignedOn,!0===p&&(e.querySelector(".record-name").disabled=!0,e.querySelector(".record-description").disabled=!0,e.querySelector(".record-start-date").disabled=!0,e.querySelector(".record-end-date").disabled=!0,e.querySelector(".record-manager-comments").disabled=!0);let l=[];for(let t=0;t<r.Approvers.length;t++){let e=r.Approvers[t];l.push("<tr><td>"+e.ApprovedBy+"</td><td><input type='checkbox' style='width: auto;' disabled"+(!0===e.Approved?" checked":"")+"></td><td>"+e.ApprovedOn+"</td></tr>")}let u=e.querySelector(".record-approvers");u.getElementsByTagName("tbody")[0].innerHTML=l.join(""),0===r.Approvers.length?u.getElementsByTagName("thead")[0].style.display="none":u.getElementsByTagName("thead")[0].style.display="table-header-group";let v=[];for(let t=0;t<r.Versions.length;t++){let e=r.Versions[t];v.push("<tr><td class='version-id'>"+e.Id+"</td><td class='version-file-name'><a class='lnk-version-file-name' href='#'>"+e.FileName+"</a>"+(t===r.Versions.length-1?"&nbsp;&nbsp;<span style='color: #28a745; border: 1px solid #34d058; border-radius: 2px; padding: 3px 4px;'>"+n.get("latest-version")+"</span>":"")+"</td><td class='version-created-on'>"+e.CreatedOn+"</td><td class='version-file-size'>"+e.FileSize+"</td><td class='version-delete'><input type='button' class='btn-delete-version btn btn-danger btn-xs' value='"+n.get("delete-version")+"'></td></tr>")}let x=e.querySelector(".record-versions");x.innerHTML=v.join("");let w=x.querySelectorAll(".lnk-version-file-name");for(let t=0;t<w.length;t++){w[t].onclick=function(){let t=this.parentElement.parentElement.querySelector(".version-id").innerHTML,e=null;for(let n=0;n<r.Versions.length;n++)if(r.Versions[n].Id===t){e=r.Versions[n];break}let n="http://"+encodeURIComponent(h)+":"+encodeURIComponent(f)+"@"+Settings.Hostname+":"+Settings.Port+"/wexflow/downloadFile?p="+encodeURIComponent(e.FilePath);window.open(n,"_self")}}let C=x.querySelectorAll(".btn-delete-version");for(let t=0;t<C.length;t++){C[t].onclick=function(){let t=this.parentElement.parentElement.querySelector(".version-id").innerHTML,e=-1;for(let n=0;n<a.Versions.length;n++)if(a.Versions[n].Id===t){e=n;break}if(e>-1){a.Versions.splice(e,1);let n=x.getElementsByTagName("tbody")[0].getElementsByTagName("tr");for(let e=0;e<n.length;e++){let i=n[e];i.querySelector(".version-id").innerHTML===t&&i.remove()}}}}let T=document.getElementById("file-dialog"),j=function(t){e.querySelector(".spn-upload-version").innerHTML=n.get("uploading"),Common.post(i+"/uploadVersion?r="+o,function(t){if(!0===t.Result){a.Versions.push({RecordId:o,FilePath:t.FilePath,FileName:t.FileName,CreatedOn:""});let r=x.insertRow(-1),l=r.insertCell(0),c=r.insertCell(1),d=r.insertCell(2),p=r.insertCell(3),u=r.insertCell(4);l.classList.add("version-id"),l.innerHTML="",c.classList.add("version-file-name"),c.innerHTML="<a class='lnk-version-file-name' href='#'>"+t.FileName+"</a>",d.classList.add("version-created-on"),d.innerHTML="-",p.classList.add("version-file-size"),p.innerHTML=t.FileSize,u.classList.add("version-delete"),u.innerHTML="<input type='button' class='btn-delete-version btn btn-danger btn-xs' value='"+n.get("delete-version")+"'>",s(e),c.querySelector(".lnk-version-file-name").onclick=function(){let e="http://"+encodeURIComponent(h)+":"+encodeURIComponent(f)+"@"+Settings.Hostname+":"+Settings.Port+"/wexflow/downloadFile?p="+encodeURIComponent(t.FilePath);window.open(e,"_self")},u.querySelector(".btn-delete-version").onclick=function(){Common.post(i+"/deleteTempVersionFile?p="+encodeURIComponent(t.FilePath),function(e){if(!0===e){let e=-1;for(let n=0;n<a.Versions.length;n++)if(a.Versions[n].FilePath===t.FilePath){e=n;break}e>-1&&(a.Versions.splice(e,1),r.remove(),Common.toastSuccess(n.get("toast-version-file-deleted")))}else Common.toastError(n.get("toast-version-file-delete-error"))},function(){},"",m)},e.querySelector(".spn-upload-version").innerHTML=""}T.value=""},function(){},t,m,!0)};e.querySelector(".btn-upload-version").onclick=function(){T.click(),T.onchange=function(t){let e=t.target.files[0],n=new FormData;n.append("file",e),j(n)}},e.addEventListener("dragover",function(t){t.stopPropagation(),t.preventDefault(),t.dataTransfer.dropEffect="copy"}),e.addEventListener("drop",function(t){t.stopPropagation(),t.preventDefault();let e=t.dataTransfer.files[0],n=new FormData;n.append("file",e),j(n)});let E=document.getElementsByClassName("jBox-footer")[0];1===g&&r.CreatedBy!==h&&(E.querySelector(".record-delete").style.display="none"),E.querySelector(".record-save").onclick=function(){a.Name=e.querySelector(".record-name").value,a.Description=e.querySelector(".record-description").value,a.StartDate=e.querySelector(".record-start-date").value,a.EndDate=e.querySelector(".record-end-date").value,a.Comments=e.querySelector(".record-comments").value,a.ManagerComments=e.querySelector(".record-manager-comments").value,Common.post(i+"/saveRecord",function(t){if(!0===t){if(h!==r.CreatedBy){let t="The record "+r.Name+" was updated by the user "+h+".";Common.post(i+"/notifyApprovers?r="+encodeURIComponent(r.Id)+"&m="+encodeURIComponent(t),function(t){!0===t?Common.toastInfo(n.get("toast-approvers-notified")):Common.toastError(n.get("toast-approvers-notify-error"))},function(){},"",m)}if(""!==r.AssignedTo&&h!==r.AssignedTo){let t="The record "+r.Name+" was updated by the user "+h+".";Common.post(i+"/notify?a="+encodeURIComponent(r.AssignedTo)+"&m="+encodeURIComponent(t),function(t){!0===t?Common.toastInfo(n.get("toast-assigned-to-notified")):Common.toastError(n.get("toast-assigned-to-notify-error"))},function(){},"",m)}y.close(),y.destroy(),b(),Common.toastSuccess(n.get("toast-record-saved"))}else Common.toastError(n.get("toast-record-save-error"))},function(){},a,m)},E.querySelector(".record-cancel").onclick=function(){Common.post(i+"/deleteTempVersionFiles",function(t){!0===t?Common.toastSuccess(n.get("toast-modifications-canceled")):Common.toastError(n.get("toast-modifications-cancel-error")),y.close(),y.destroy()},function(){},a,m)},E.querySelector(".record-delete").onclick=function(){!0===confirm(n.get("confirm-delete-record"))&&Common.post(i+"/deleteRecords",function(t){if(!0===t)for(let t=0;t<c.length;t++){let e=c[t],n=e.getElementsByClassName("id")[0].innerHTML;o===n&&(d=Common.removeItemOnce(d,o),e.remove(),y.destroy())}},function(){},[o],m)}},onClose:function(){Common.post(i+"/deleteTempVersionFiles",function(t){!1===t&&Common.toastError(n.get("toast-modifications-cancel-error"))},function(){},a,m)}})).open()}}document.getElementById("check-all").onchange=function(){for(let t=0;t<c.length;t++){let e=c[t],n=e.getElementsByClassName("check")[0].firstChild,i=e.getElementsByClassName("id")[0].innerHTML;!0===n.checked?(n.checked=!1,d=Common.removeItemOnce(d,i)):(n.checked=!0,d.push(i))}},document.getElementById("btn-delete").onclick=function(){if(0===d.length)Common.toastInfo(n.get("toast-select-records"));else{!0===confirm(1==d.length?n.get("confirm-delete-record"):n.get("confirm-delete-records"))&&Common.post(i+"/deleteRecords",function(t){if(!0===t)for(let t=d.length-1;t>=0;t--){let e=d[t];for(let t=0;t<c.length;t++){let n=c[t];e===n.getElementsByClassName("id")[0].innerHTML&&(d=Common.removeItemOnce(d,e),n.remove())}}},function(){},d,m)}},document.getElementById("btn-new-record").onclick=function(){y&&y.destroy();let e={Versions:[]};(y=new jBox("Modal",{width:800,height:420,title:n.get("record-information"),content:document.getElementById("edit-record").innerHTML,footer:document.getElementById("edit-record-footer").innerHTML,overlay:!0,isolateScroll:!1,delayOpen:0,onOpen:function(){t();let o=document.getElementsByClassName("jBox-content")[0];o.querySelector(".edit-record-tr-id").style.display="none",o.querySelector(".edit-record-tr-approved").style.display="none",o.querySelector(".edit-record-tr-created-by").style.display="none",o.querySelector(".edit-record-tr-created-on").style.display="none",o.querySelector(".edit-record-tr-modified-by").style.display="none",o.querySelector(".edit-record-tr-modified-on").style.display="none",o.querySelector(".edit-record-tr-assigned-to").style.display="none",o.querySelector(".edit-record-tr-assigned-on").style.display="none",o.querySelector(".edit-record-td-start-date").innerHTML+=n.get("optional"),o.querySelector(".edit-record-td-end-date").innerHTML+=" (Optional)",o.querySelector(".edit-record-tr-approvers").style.display="none",setTimeout(function(){let t=o.querySelector(".record-name");t.focus(),t.select()},0);let r=document.getElementById("file-dialog"),a=function(t){Common.post(i+"/uploadVersion?r=-1",function(t){if(!0===t.Result){e.Versions.push({RecordId:"-1",FilePath:t.FilePath,FileName:t.FileName,CreatedOn:""});let r=o.querySelector(".record-versions").insertRow(-1),a=r.insertCell(0),l=r.insertCell(1),c=r.insertCell(2),d=r.insertCell(3),p=r.insertCell(4);a.classList.add("version-id"),a.innerHTML="",l.classList.add("version-file-name"),l.innerHTML="<a class='lnk-version-file-name' href='#'>"+t.FileName+"</a>",c.classList.add("version-created-on"),c.innerHTML="-",d.classList.add("version-file-size"),d.innerHTML=t.FileSize,p.classList.add("version-delete"),p.innerHTML="<input type='button' class='btn-delete-version btn btn-danger btn-xs' value='"+n.get("delete-version")+"'>",s(o),l.querySelector(".lnk-version-file-name").onclick=function(){let e="http://"+encodeURIComponent(h)+":"+encodeURIComponent(f)+"@"+Settings.Hostname+":"+Settings.Port+"/wexflow/downloadFile?p="+encodeURIComponent(t.FilePath);window.open(e,"_self")},p.querySelector(".btn-delete-version").onclick=function(){Common.post(i+"/deleteTempVersionFile?p="+encodeURIComponent(t.FilePath),function(i){if(!0===i){let i=-1;for(let n=0;n<e.Versions.length;n++)if(e.Versions[n].FilePath===t.FilePath){i=n;break}i>-1&&(e.Versions.splice(i,1),r.remove(),Common.toastSuccess(n.get("toast-version-file-deleted")))}else Common.toastError(n.get("toast-version-file-delete-error"))},function(){},"",m)},o.querySelector(".spn-upload-version").innerHTML=""}r.value=""},function(){},t,m,!0)};o.querySelector(".btn-upload-version").onclick=function(){r.click(),r.onchange=function(t){o.querySelector(".spn-upload-version").innerHTML=n.get("uploading");let e=t.target.files[0],i=new FormData;i.append("file",e),a(i)}},o.addEventListener("dragover",function(t){t.stopPropagation(),t.preventDefault(),t.dataTransfer.dropEffect="copy"}),o.addEventListener("drop",function(t){t.stopPropagation(),t.preventDefault();let e=t.dataTransfer.files[0],n=new FormData;n.append("file",e),a(n)});let l=document.getElementsByClassName("jBox-footer")[0];l.querySelector(".record-delete").style.display="none",l.querySelector(".record-save").onclick=function(){""!==o.querySelector(".record-name").value?(e.Id="-1",e.Name=o.querySelector(".record-name").value,e.Description=o.querySelector(".record-description").value,e.StartDate=o.querySelector(".record-start-date").value,e.EndDate=o.querySelector(".record-end-date").value,e.Comments=o.querySelector(".record-comments").value,e.Approved=!1,e.ManagerComments=o.querySelector(".record-manager-comments").value,e.ModifiedBy="",e.ModifiedOn="",e.CreatedBy=h,e.CreatedOn="",e.AssignedTo="",e.AssignedOn="",Common.post(i+"/saveRecord",function(t){!0===t?(y.close(),y.destroy(),b(),Common.toastSuccess(n.get("toast-record-saved"))):Common.toastError(n.get("toast-record-save-error"))},function(){},e,m)):Common.toastInfo(n.get("toast-record-name"))},l.querySelector(".record-cancel").onclick=function(){Common.post(i+"/deleteTempVersionFiles",function(t){!0===t?Common.toastSuccess(n.get("toast-modifications-canceled")):Common.toastError(n.get("toast-modifications-cancel-error")),y.close(),y.destroy()},function(){},e,m)},l.querySelector(".record-delete").style.display="none"},onClose:function(){Common.post(i+"/deleteTempVersionFiles",function(t){!1===t&&Common.toastError(n.get("toast-modifications-cancel-error"))},function(){},e,m)}})).open()}};function s(t){t.scrollTop=t.scrollHeight-t.clientHeight}0===g?Common.get(i+"/searchRecords?s="+encodeURIComponent(u.value),function(t){o(t)},function(){},m):1===g&&Common.get(i+"/searchRecordsCreatedByOrAssignedTo?s="+encodeURIComponent(u.value)+"&c="+encodeURIComponent(h)+"&a="+encodeURIComponent(h),function(t){o(t)},function(){},m)}h=x.Username,f=x.Password,m="Basic "+btoa(h+":"+f),Common.get(i+"/user?username="+encodeURIComponent(x.Username),function(t){x.Password!==t.Password?Common.redirectToLoginPage():0===t.UserProfile||1===t.UserProfile?Common.get(i+"/hasNotifications?a="+encodeURIComponent(x.Username),function(e){o.style.display="inline",s.style.display="inline",r.style.display="inline",a.style.display="inline",l.style.display="inline",d.style.display="inline",g=t.UserProfile,0===t.UserProfile&&(c.style.display="inline"),1===t.UserProfile&&(document.getElementById("btn-delete").style.display="none"),p.src=!0===e?"images/notification-active.png":"images/notification.png";let n=document.getElementById("btn-logout");document.getElementById("navigation").style.display="block",document.getElementById("content").style.display="block",n.onclick=function(){deleteUser(),Common.redirectToLoginPage()},document.getElementById("spn-username").innerHTML=" ("+t.Username+")",u.onkeyup=function(t){return t.preventDefault(),13===t.keyCode&&b(),!1},b()},function(){},m):Common.redirectToLoginPage()},function(){},m)}};