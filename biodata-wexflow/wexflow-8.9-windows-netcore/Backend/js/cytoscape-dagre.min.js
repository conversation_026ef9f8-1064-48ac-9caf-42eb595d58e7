!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.cytoscape=t():e.cytoscape=t()}(this,function(){return function(e){function t(r){if(n[r])return n[r].exports;var i=n[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,t),i.l=!0,i.exports}var n={};return t.m=e,t.c=n,t.i=function(e){return e},t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:r})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=117)}([function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=n(4),a=i?i.navigator:null,o=i?i.document:null,s=r(""),u=r({}),l=r(function(){}),c="undefined"==typeof HTMLElement?"undefined":r(HTMLElement),d=function(e){return e&&e.instanceString&&h.fn(e.instanceString)?e.instanceString():null},h={defined:function(e){return null!=e},string:function(e){return null!=e&&(void 0===e?"undefined":r(e))==s},fn:function(e){return null!=e&&(void 0===e?"undefined":r(e))===l},array:function(e){return Array.isArray?Array.isArray(e):null!=e&&e instanceof Array},plainObject:function(e){return null!=e&&(void 0===e?"undefined":r(e))===u&&!h.array(e)&&e.constructor===Object},object:function(e){return null!=e&&(void 0===e?"undefined":r(e))===u},number:function(e){return null!=e&&(void 0===e?"undefined":r(e))===r(1)&&!isNaN(e)},integer:function(e){return h.number(e)&&Math.floor(e)===e},bool:function(e){return null!=e&&(void 0===e?"undefined":r(e))===r(!0)},htmlElement:function(e){return"undefined"===c?void 0:null!=e&&e instanceof HTMLElement},elementOrCollection:function(e){return h.element(e)||h.collection(e)},element:function(e){return"collection"===d(e)&&e._private.single},collection:function(e){return"collection"===d(e)&&!e._private.single},core:function(e){return"core"===d(e)},style:function(e){return"style"===d(e)},stylesheet:function(e){return"stylesheet"===d(e)},event:function(e){return"event"===d(e)},thread:function(e){return"thread"===d(e)},fabric:function(e){return"fabric"===d(e)},emptyString:function(e){return void 0===e||null===e||!(""!==e&&!e.match(/^\s+$/))},nonemptyString:function(e){return!(!e||!h.string(e)||""===e||e.match(/^\s+$/))},domElement:function(e){return"undefined"!=typeof HTMLElement&&e instanceof HTMLElement},boundingBox:function(e){return h.plainObject(e)&&h.number(e.x1)&&h.number(e.x2)&&h.number(e.y1)&&h.number(e.y2)},promise:function(e){return h.object(e)&&h.fn(e.then)},touch:function(){return i&&("ontouchstart"in i||i.DocumentTouch&&o instanceof DocumentTouch)},gecko:function(){return i&&("undefined"!=typeof InstallTrigger||"MozAppearance"in o.documentElement.style)},webkit:function(){return i&&("undefined"!=typeof webkitURL||"WebkitAppearance"in o.documentElement.style)},chromium:function(){return i&&"undefined"!=typeof chrome},khtml:function(){return a&&a.vendor.match(/kde/i)},khtmlEtc:function(){return h.khtml()||h.webkit()||h.chromium()},ms:function(){return a&&a.userAgent.match(/msie|trident|edge/i)},windows:function(){return a&&a.appVersion.match(/Win/i)},mac:function(){return a&&a.appVersion.match(/Mac/i)},linux:function(){return a&&a.appVersion.match(/Linux/i)},unix:function(){return a&&a.appVersion.match(/X11/i)}};e.exports=h},function(e,t,n){"use strict";var r=n(0),i=n(2),a={MAX_INT:Number.MAX_SAFE_INTEGER||9007199254740991,trueify:function(){return!0},falsify:function(){return!1},zeroify:function(){return 0},noop:function(){},error:function(e){console.error?(console.error.apply(console,arguments),console.trace&&console.trace()):(console.log.apply(console,arguments),console.trace&&console.trace())},clone:function(e){return this.extend({},e)},copy:function(e){return null==e?e:r.array(e)?e.slice():r.plainObject(e)?this.clone(e):e},copyArray:function(e){return e.slice()},clonePosition:function(e){return{x:e.x,y:e.y}},uuid:function(e,t){for(t=e="";e++<36;t+=51*e&52?(15^e?8^Math.random()*(20^e?16:4):4).toString(16):"-");return t}};a.makeBoundingBox=i.makeBoundingBox.bind(i),a._staticEmptyObject={},a.staticEmptyObject=function(){return a._staticEmptyObject},a.extend=null!=Object.assign?Object.assign.bind(Object):function(e){for(var t=arguments,n=1;n<t.length;n++){var r=t[n];if(null!=r)for(var i=Object.keys(r),a=0;a<i.length;a++){var o=i[a];e[o]=r[o]}}return e},a.assign=a.extend,a.default=function(e,t){return void 0===e?t:e},a.removeFromArray=function(e,t,n){for(var r=e.length;r>=0&&(e[r]!==t||(e.splice(r,1),n));r--);},a.clearArray=function(e){e.splice(0,e.length)},a.push=function(e,t){for(var n=0;n<t.length;n++){var r=t[n];e.push(r)}},a.getPrefixedProperty=function(e,t,n){return n&&(t=this.prependCamel(n,t)),e[t]},a.setPrefixedProperty=function(e,t,n,r){n&&(t=this.prependCamel(n,t)),e[t]=r},[n(131),n(132),{memoize:n(19)},n(133),n(135),n(136),n(134)].forEach(function(e){a.extend(a,e)}),e.exports=a},function(e,t,n){"use strict";var r={};r.arePositionsSame=function(e,t){return e.x===t.x&&e.y===t.y},r.copyPosition=function(e){return{x:e.x,y:e.y}},r.modelToRenderedPosition=function(e,t,n){return{x:e.x*t+n.x,y:e.y*t+n.y}},r.renderedToModelPosition=function(e,t,n){return{x:(e.x-n.x)/t,y:(e.y-n.y)/t}},r.array2point=function(e){return{x:e[0],y:e[1]}},r.deg2rad=function(e){return Math.PI*e/180},r.getAngleFromDisp=function(e,t){return Math.atan2(t,e)-Math.PI/2},r.log2=Math.log2||function(e){return Math.log(e)/Math.log(2)},r.signum=function(e){return e>0?1:e<0?-1:0},r.dist=function(e,t){return Math.sqrt(r.sqdist(e,t))},r.sqdist=function(e,t){var n=t.x-e.x,r=t.y-e.y;return n*n+r*r},r.qbezierAt=function(e,t,n,r){return(1-r)*(1-r)*e+2*(1-r)*r*t+r*r*n},r.qbezierPtAt=function(e,t,n,i){return{x:r.qbezierAt(e.x,t.x,n.x,i),y:r.qbezierAt(e.y,t.y,n.y,i)}},r.lineAt=function(e,t,n,i){var a={x:t.x-e.x,y:t.y-e.y},o=r.dist(e,t),s={x:a.x/o,y:a.y/o};return n=null==n?0:n,i=null!=i?i:n*o,{x:e.x+s.x*i,y:e.y+s.y*i}},r.lineAtDist=function(e,t,n){return r.lineAt(e,t,void 0,n)},r.triangleAngle=function(e,t,n){var i=r.dist(t,n),a=r.dist(e,n),o=r.dist(e,t);return Math.acos((i*i+a*a-o*o)/(2*i*a))},r.bound=function(e,t,n){return Math.max(e,Math.min(n,t))},r.makeBoundingBox=function(e){if(null==e)return{x1:1/0,y1:1/0,x2:-1/0,y2:-1/0,w:0,h:0};if(null!=e.x1&&null!=e.y1){if(null!=e.x2&&null!=e.y2&&e.x2>=e.x1&&e.y2>=e.y1)return{x1:e.x1,y1:e.y1,x2:e.x2,y2:e.y2,w:e.x2-e.x1,h:e.y2-e.y1};if(null!=e.w&&null!=e.h&&e.w>=0&&e.h>=0)return{x1:e.x1,y1:e.y1,x2:e.x1+e.w,y2:e.y1+e.h,w:e.w,h:e.h}}},r.updateBoundingBox=function(e,t){e.x1=Math.min(e.x1,t.x1),e.x2=Math.max(e.x2,t.x2),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,t.y1),e.y2=Math.max(e.y2,t.y2),e.h=e.y2-e.y1},r.expandBoundingBoxByPoint=function(e,t,n){e.x1=Math.min(e.x1,t),e.x2=Math.max(e.x2,t),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,n),e.y2=Math.max(e.y2,n),e.h=e.y2-e.y1},r.expandBoundingBox=function(e,t){return e.x1-=t,e.x2+=t,e.y1-=t,e.y2+=t,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},r.boundingBoxesIntersect=function(e,t){return!(e.x1>t.x2||t.x1>e.x2||e.x2<t.x1||t.x2<e.x1||e.y2<t.y1||t.y2<e.y1||e.y1>t.y2||t.y1>e.y2)},r.inBoundingBox=function(e,t,n){return e.x1<=t&&t<=e.x2&&e.y1<=n&&n<=e.y2},r.pointInBoundingBox=function(e,t){return this.inBoundingBox(e,t.x,t.y)},r.boundingBoxInBoundingBox=function(e,t){return r.inBoundingBox(e,t.x1,t.y1)&&r.inBoundingBox(e,t.x2,t.y2)},r.roundRectangleIntersectLine=function(e,t,n,r,i,a,o){var s=this.getRoundRectangleRadius(i,a),u=i/2,l=a/2,c=void 0,d=n-u+s-o,h=r-l-o,f=n+u-s+o,p=h;if(c=this.finiteLinesIntersect(e,t,n,r,d,h,f,p,!1),c.length>0)return c;var v=n+u+o,g=r-l+s-o,y=v,m=r+l-s+o;if(c=this.finiteLinesIntersect(e,t,n,r,v,g,y,m,!1),c.length>0)return c;var b=n-u+s-o,x=r+l+o,w=n+u-s+o,_=x;if(c=this.finiteLinesIntersect(e,t,n,r,b,x,w,_,!1),c.length>0)return c;var E=n-u-o,k=r-l+s-o,C=E,P=r+l-s+o;if(c=this.finiteLinesIntersect(e,t,n,r,E,k,C,P,!1),c.length>0)return c;var T=void 0,S=n-u+s,D=r-l+s;if(T=this.intersectLineCircle(e,t,n,r,S,D,s+o),T.length>0&&T[0]<=S&&T[1]<=D)return[T[0],T[1]];var I=n+u-s,N=r-l+s;if(T=this.intersectLineCircle(e,t,n,r,I,N,s+o),T.length>0&&T[0]>=I&&T[1]<=N)return[T[0],T[1]];var M=n+u-s,L=r+l-s;if(T=this.intersectLineCircle(e,t,n,r,M,L,s+o),T.length>0&&T[0]>=M&&T[1]>=L)return[T[0],T[1]];var B=n-u+s,O=r+l-s;return T=this.intersectLineCircle(e,t,n,r,B,O,s+o),T.length>0&&T[0]<=B&&T[1]>=O?[T[0],T[1]]:[]},r.inLineVicinity=function(e,t,n,r,i,a,o){var s=o,u=Math.min(n,i),l=Math.max(n,i),c=Math.min(r,a),d=Math.max(r,a);return u-s<=e&&e<=l+s&&c-s<=t&&t<=d+s},r.inBezierVicinity=function(e,t,n,r,i,a,o,s,u){var l={x1:Math.min(n,o,i)-u,x2:Math.max(n,o,i)+u,y1:Math.min(r,s,a)-u,y2:Math.max(r,s,a)+u};return!(e<l.x1||e>l.x2||t<l.y1||t>l.y2)},r.solveQuadratic=function(e,t,n,r){n-=r;var i=t*t-4*e*n;if(i<0)return[];var a=Math.sqrt(i),o=2*e;return[(-t+a)/o,(-t-a)/o]},r.solveCubic=function(e,t,n,r,i){t/=e,n/=e,r/=e;var a=void 0,o=void 0,s=void 0,u=void 0,l=void 0,c=void 0,d=void 0,h=void 0;return o=(3*n-t*t)/9,s=-27*r+t*(9*n-t*t*2),s/=54,a=o*o*o+s*s,i[1]=0,d=t/3,a>0?(l=s+Math.sqrt(a),l=l<0?-Math.pow(-l,1/3):Math.pow(l,1/3),c=s-Math.sqrt(a),c=c<0?-Math.pow(-c,1/3):Math.pow(c,1/3),i[0]=-d+l+c,d+=(l+c)/2,i[4]=i[2]=-d,d=Math.sqrt(3)*(-c+l)/2,i[3]=d,void(i[5]=-d)):(i[5]=i[3]=0,0===a?(h=s<0?-Math.pow(-s,1/3):Math.pow(s,1/3),i[0]=2*h-d,void(i[4]=i[2]=-(h+d))):(o=-o,u=o*o*o,u=Math.acos(s/Math.sqrt(u)),h=2*Math.sqrt(o),i[0]=-d+h*Math.cos(u/3),i[2]=-d+h*Math.cos((u+2*Math.PI)/3),void(i[4]=-d+h*Math.cos((u+4*Math.PI)/3))))},r.sqdistToQuadraticBezier=function(e,t,n,r,i,a,o,s){var u=1*n*n-4*n*i+2*n*o+4*i*i-4*i*o+o*o+r*r-4*r*a+2*r*s+4*a*a-4*a*s+s*s,l=9*n*i-3*n*n-3*n*o-6*i*i+3*i*o+9*r*a-3*r*r-3*r*s-6*a*a+3*a*s,c=3*n*n-6*n*i+n*o-n*e+2*i*i+2*i*e-o*e+3*r*r-6*r*a+r*s-r*t+2*a*a+2*a*t-s*t,d=1*n*i-n*n+n*e-i*e+r*a-r*r+r*t-a*t,h=[];this.solveCubic(u,l,c,d,h);for(var f=[],p=0;p<6;p+=2)Math.abs(h[p+1])<1e-7&&h[p]>=0&&h[p]<=1&&f.push(h[p]);f.push(1),f.push(0);for(var v=-1,g=void 0,y=void 0,m=void 0,b=0;b<f.length;b++)g=Math.pow(1-f[b],2)*n+2*(1-f[b])*f[b]*i+f[b]*f[b]*o,y=Math.pow(1-f[b],2)*r+2*(1-f[b])*f[b]*a+f[b]*f[b]*s,m=Math.pow(g-e,2)+Math.pow(y-t,2),v>=0?m<v&&(v=m):v=m;return v},r.sqdistToFiniteLine=function(e,t,n,r,i,a){var o=[e-n,t-r],s=[i-n,a-r],u=s[0]*s[0]+s[1]*s[1],l=o[0]*o[0]+o[1]*o[1],c=o[0]*s[0]+o[1]*s[1],d=c*c/u;return c<0?l:d>u?(e-i)*(e-i)+(t-a)*(t-a):l-d},r.pointInsidePolygonPoints=function(e,t,n){for(var r=void 0,i=void 0,a=void 0,o=void 0,s=0,u=0;u<n.length/2;u++)if(r=n[2*u],i=n[2*u+1],u+1<n.length/2?(a=n[2*(u+1)],o=n[2*(u+1)+1]):(a=n[2*(u+1-n.length/2)],o=n[2*(u+1-n.length/2)+1]),r==e&&a==e);else{if(!(r>=e&&e>=a||r<=e&&e<=a))continue;(e-r)/(a-r)*(o-i)+i>t&&s++}return s%2!=0},r.pointInsidePolygon=function(e,t,n,i,a,o,s,u,l){var c=new Array(n.length),d=void 0;null!=u[0]?(d=Math.atan(u[1]/u[0]),u[0]<0?d+=Math.PI/2:d=-d-Math.PI/2):d=u;for(var h=Math.cos(-d),f=Math.sin(-d),p=0;p<c.length/2;p++)c[2*p]=o/2*(n[2*p]*h-n[2*p+1]*f),c[2*p+1]=s/2*(n[2*p+1]*h+n[2*p]*f),c[2*p]+=i,c[2*p+1]+=a;var v=void 0;if(l>0){var g=this.expandPolygon(c,-l);v=this.joinLines(g)}else v=c;return r.pointInsidePolygonPoints(e,t,v)},r.joinLines=function(e){for(var t=new Array(e.length/2),n=void 0,r=void 0,i=void 0,a=void 0,o=void 0,s=void 0,u=void 0,l=void 0,c=0;c<e.length/4;c++){n=e[4*c],r=e[4*c+1],i=e[4*c+2],a=e[4*c+3],c<e.length/4-1?(o=e[4*(c+1)],s=e[4*(c+1)+1],u=e[4*(c+1)+2],l=e[4*(c+1)+3]):(o=e[0],s=e[1],u=e[2],l=e[3]);var d=this.finiteLinesIntersect(n,r,i,a,o,s,u,l,!0);t[2*c]=d[0],t[2*c+1]=d[1]}return t},r.expandPolygon=function(e,t){for(var n=new Array(2*e.length),r=void 0,i=void 0,a=void 0,o=void 0,s=0;s<e.length/2;s++){r=e[2*s],i=e[2*s+1],s<e.length/2-1?(a=e[2*(s+1)],o=e[2*(s+1)+1]):(a=e[0],o=e[1]);var u=o-i,l=-(a-r),c=Math.sqrt(u*u+l*l),d=u/c,h=l/c;n[4*s]=r+d*t,n[4*s+1]=i+h*t,n[4*s+2]=a+d*t,n[4*s+3]=o+h*t}return n},r.intersectLineEllipse=function(e,t,n,r,i,a){var o=n-e,s=r-t;o/=i,s/=a;var u=Math.sqrt(o*o+s*s),l=u-1;if(l<0)return[];var c=l/u;return[(n-e)*c+e,(r-t)*c+t]},r.checkInEllipse=function(e,t,n,r,i,a,o){return e-=a,t-=o,e/=r/2+n,t/=i/2+n,e*e+t*t<=1},r.intersectLineCircle=function(e,t,n,r,i,a,o){var s=[n-e,r-t],u=[e-i,t-a],l=s[0]*s[0]+s[1]*s[1],c=2*(u[0]*s[0]+u[1]*s[1]),d=u[0]*u[0]+u[1]*u[1]-o*o,h=c*c-4*l*d;if(h<0)return[];var f=(-c+Math.sqrt(h))/(2*l),p=(-c-Math.sqrt(h))/(2*l),v=Math.min(f,p),g=Math.max(f,p),y=[];if(v>=0&&v<=1&&y.push(v),g>=0&&g<=1&&y.push(g),0===y.length)return[];var m=y[0]*s[0]+e,b=y[0]*s[1]+t;return y.length>1?y[0]==y[1]?[m,b]:[m,b,y[1]*s[0]+e,y[1]*s[1]+t]:[m,b]},r.findCircleNearPoint=function(e,t,n,r,i){var a=r-e,o=i-t,s=Math.sqrt(a*a+o*o);return[e+a/s*n,t+o/s*n]},r.findMaxSqDistanceToOrigin=function(e){for(var t=1e-6,n=void 0,r=0;r<e.length/2;r++)(n=e[2*r]*e[2*r]+e[2*r+1]*e[2*r+1])>t&&(t=n);return t},r.midOfThree=function(e,t,n){return t<=e&&e<=n||n<=e&&e<=t?e:e<=t&&t<=n||n<=t&&t<=e?t:n},r.finiteLinesIntersect=function(e,t,n,r,i,a,o,s,u){var l=e-i,c=n-e,d=o-i,h=t-a,f=r-t,p=s-a,v=d*h-p*l,g=c*h-f*l,y=p*c-d*f;if(0!==y){var m=v/y,b=g/y;return-.001<=m&&m<=1.001&&-.001<=b&&b<=1.001?[e+m*c,t+m*f]:u?[e+m*c,t+m*f]:[]}return 0===v||0===g?this.midOfThree(e,n,o)===o?[o,s]:this.midOfThree(e,n,i)===i?[i,a]:this.midOfThree(i,o,n)===n?[n,r]:[]:[]},r.polygonIntersectLine=function(e,t,n,i,a,o,s,u){var l=[],c=void 0,d=new Array(n.length),h=!0;5===arguments.length&&(h=!1);var f=void 0;if(h){for(var p=0;p<d.length/2;p++)d[2*p]=n[2*p]*o+i,d[2*p+1]=n[2*p+1]*s+a;if(u>0){var v=r.expandPolygon(d,-u);f=r.joinLines(v)}else f=d}else f=n;for(var g=void 0,y=void 0,m=void 0,b=void 0,x=0;x<f.length/2;x++)g=f[2*x],y=f[2*x+1],x<f.length/2-1?(m=f[2*(x+1)],b=f[2*(x+1)+1]):(m=f[0],b=f[1]),c=this.finiteLinesIntersect(e,t,i,a,g,y,m,b),0!==c.length&&l.push(c[0],c[1]);return l},r.shortenIntersection=function(e,t,n){var r=[e[0]-t[0],e[1]-t[1]],i=Math.sqrt(r[0]*r[0]+r[1]*r[1]),a=(i-n)/i;return a<0&&(a=1e-5),[t[0]+a*r[0],t[1]+a*r[1]]},r.generateUnitNgonPointsFitToSquare=function(e,t){var n=r.generateUnitNgonPoints(e,t);return n=r.fitPolygonToSquare(n)},r.fitPolygonToSquare=function(e){for(var t=void 0,n=void 0,r=e.length/2,i=1/0,a=1/0,o=-1/0,s=-1/0,u=0;u<r;u++)t=e[2*u],n=e[2*u+1],i=Math.min(i,t),o=Math.max(o,t),a=Math.min(a,n),s=Math.max(s,n);for(var l=2/(o-i),c=2/(s-a),d=0;d<r;d++)t=e[2*d]=e[2*d]*l,n=e[2*d+1]=e[2*d+1]*c,i=Math.min(i,t),o=Math.max(o,t),a=Math.min(a,n),s=Math.max(s,n);if(a<-1)for(var h=0;h<r;h++)n=e[2*h+1]=e[2*h+1]+(-1-a);return e},r.generateUnitNgonPoints=function(e,t){var n=1/e*2*Math.PI,r=e%2==0?Math.PI/2+n/2:Math.PI/2;r+=t;for(var i=new Array(2*e),a=void 0,o=0;o<e;o++)a=o*n+r,i[2*o]=Math.cos(a),i[2*o+1]=Math.sin(-a);return i},r.getRoundRectangleRadius=function(e,t){return Math.min(e/4,t/4,8)},r.getCutRectangleCornerLength=function(){return 8},r.bezierPtsToQuadCoeff=function(e,t,n){return[e-2*t+n,2*(t-e),e]},r.getBarrelCurveConstants=function(e,t){return{heightOffset:Math.min(15,.05*t),widthOffset:Math.min(100,.25*e),ctrlPtOffsetPct:.05}},e.exports=r},function(e,t,n){"use strict";var r=n(1),i={};[n(73),n(74),n(75)].forEach(function(e){r.assign(i,e)}),e.exports=i},function(e,t,n){"use strict";e.exports="undefined"==typeof window?null:window},function(e,t,n){"use strict";(function(t){var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r=function e(t){return this instanceof e?(this.id="Thenable/1.0.7",this.state=0,this.fulfillValue=void 0,this.rejectReason=void 0,this.onFulfilled=[],this.onRejected=[],this.proxy={then:this.then.bind(this)},"function"==typeof t&&t.call(this,this.fulfill.bind(this),this.reject.bind(this)),void 0):new e(t)};r.prototype={fulfill:function(e){return i(this,1,"fulfillValue",e)},reject:function(e){return i(this,2,"rejectReason",e)},then:function(e,t){var n=this,i=new r;return n.onFulfilled.push(s(e,i,"fulfill")),n.onRejected.push(s(t,i,"reject")),a(n),i.proxy}};var i=function(e,t,n,r){return 0===e.state&&(e.state=t,e[n]=r,a(e)),e},a=function(e){1===e.state?o(e,"onFulfilled",e.fulfillValue):2===e.state&&o(e,"onRejected",e.rejectReason)},o=function(e,n,r){if(0!==e[n].length){var i=e[n];e[n]=[];var a=function(){for(var e=0;e<i.length;e++)i[e](r)};"function"==typeof t?t(a):setTimeout(a,0)}},s=function(e,t,n){return function(r){if("function"!=typeof e)t[n].call(t,r);else{var i;try{i=e(r)}catch(e){return void t.reject(e)}u(t,i)}}},u=function e(t,r){if(t===r||t.proxy===r)return void t.reject(new TypeError("cannot resolve promise with itself"));var i;if("object"===(void 0===r?"undefined":n(r))&&null!==r||"function"==typeof r)try{i=r.then}catch(e){return void t.reject(e)}if("function"!=typeof i)t.fulfill(r);else{var a=!1;try{i.call(r,function(n){a||(a=!0,n===r?t.reject(new TypeError("circular thenable chain")):e(t,n))},function(e){a||(a=!0,t.reject(e))})}catch(e){a||t.reject(e)}}};r.all=function(e){return new r(function(t,n){for(var r=new Array(e.length),i=0,a=function(n,a){r[n]=a,++i===e.length&&t(r)},o=0;o<e.length;o++)!function(t){var r=e[t];null!=r&&null!=r.then?r.then(function(e){a(t,e)},function(e){n(e)}):a(t,r)}(o)})},r.resolve=function(e){return new r(function(t,n){t(e)})},r.reject=function(e){return new r(function(t,n){n(e)})},e.exports="undefined"!=typeof Promise?Promise:r}).call(t,n(142).setImmediate)},function(e,t,n){"use strict";var r=n(0),i=n(1),a=n(11),o=function(e){var t=this;if(t._private={selectorText:e,invalid:!0},null==e||r.string(e)&&e.match(/^\s*$/))t.length=0;else if("*"===e||"edge"===e||"node"===e)t[0]=a(),t[0].group="*"===e?e:e+"s",t[0].groupOnly=!0,t[0].length=1,t._private.invalid=!1,t.length=1;else if(r.elementOrCollection(e)){var n=e.collection();t[0]=a(),t[0].collection=n,t[0].length=1,t.length=1}else if(r.fn(e))t[0]=a(),t[0].filter=e,t[0].length=1,t.length=1;else{if(!r.string(e))return void i.error("A selector must be created from a string; found ",e);if(!t.parse(e))return}t._private.invalid=!1},s=o.prototype;s.valid=function(){return!this._private.invalid},s.invalid=function(){return this._private.invalid},s.text=function(){return this._private.selectorText},s.size=function(){return this.length},s.eq=function(e){return this[e]},s.sameText=function(e){return this.text()===e.text()},s.toString=s.selector=function(){if(null!=this._private.toStringCache)return this._private.toStringCache;var e=void 0,t="",n=function(e){return null==e?"":e},i=function(e){return r.string(e)?'"'+e+'"':n(e)},a=function(e){return" "+e+" "};for(e=0;e<this.length;e++){var o=this[e];t+=function t(r){var o="",s=void 0,u=void 0;r.subject===r&&(o+="$");var l=n(r.group);for(o+=l.substring(0,l.length-1),s=0;s<r.data.length;s++){var c=r.data[s];o+=c.value?"["+c.field+a(n(c.operator))+i(c.value)+"]":"["+n(c.operator)+c.field+"]"}for(s=0;s<r.meta.length;s++){var d=r.meta[s];o+="[["+d.field+a(n(d.operator))+i(d.value)+"]]"}for(s=0;s<r.colonSelectors.length;s++)u=r.colonSelectors[e],o+=u;for(s=0;s<r.ids.length;s++)u="#"+r.ids[e],o+=u;for(s=0;s<r.classes.length;s++)u="."+r.classes[s],o+=u;if(null!=r.source&&null!=r.target&&(o=t(r.source)+" -> "+t(r.target)),null!=r.connectedNodes){var h=r.connectedNodes;o=t(h[0])+" <-> "+t(h[1])}return null!=r.parent&&(o=t(r.parent)+" > "+o),null!=r.ancestor&&(o=t(r.ancestor)+" "+o),null!=r.child&&(o+=" > "+t(r.child)),null!=r.descendant&&(o+=" "+t(r.descendant)),o}(o),this.length>1&&e<this.length-1&&(t+=", ")}return this._private.toStringCache=t,t},[n(121),n(120)].forEach(function(e){return i.assign(s,e)}),e.exports=o},function(e,t,n){"use strict";var r=n(1),i=n(0),a=n(118),o=n(9),s=n(13),u={generate:function(e,t,n){for(var i=null!=n?n:r.uuid();e.hasElementWithId(i);)i=r.uuid();return i}},l=function(e,t,n){if(void 0===e||!i.core(e))return void r.error("A collection must have a reference to the core");var l=new a,c=!1;if(t){if(t.length>0&&i.plainObject(t[0])&&!i.element(t[0])){c=!0;for(var d=[],h=new o,f=0,p=t.length;f<p;f++){var v=t[f];null==v.data&&(v.data={});var g=v.data;if(null==g.id)g.id=u.generate(e,v);else if(e.hasElementWithId(g.id)||h.has(g.id))continue;var y=new s(e,v,!1);d.push(y),h.add(g.id)}t=d}}else t=[];this.length=0;for(var m=0,b=t.length;m<b;m++){var x=t[m];if(null!=x){var w=x._private.data.id;(null==n||n.unique&&!l.has(w))&&(l.set(w,{index:this.length,ele:x}),this[this.length]=x,this.length++)}}this._private={cy:e,map:l},c&&this.restore()},c=s.prototype=l.prototype;c.instanceString=function(){return"collection"},c.spawn=function(e,t,n){return i.core(e)||(n=t,t=e,e=this.cy()),new l(e,t,n)},c.spawnSelf=function(){return this.spawn(this)},c.cy=function(){return this._private.cy},c.renderer=function(){return this._private.cy.renderer()},c.element=function(){return this[0]},c.collection=function(){return i.collection(this)?this:new l(this._private.cy,[this])},c.unique=function(){return new l(this._private.cy,this,{unique:!0})},c.hasElementWithId=function(e){return this._private.map.has(e)},c.getElementById=function(e){var t=this._private.cy,n=this._private.map.get(e);return n?n.ele:new l(t)},c.$id=c.getElementById,c.poolIndex=function(){var e=this._private.cy,t=e._private.elements,n=this._private.data.id;return t._private.map.get(n).index},c.json=function(e){var t=this.element(),n=this.cy();if(null==t&&e)return this;if(null!=t){var a=t._private;if(i.plainObject(e)){n.startBatch(),e.data&&t.data(e.data),e.position&&t.position(e.position);var o=function(n,r,i){var o=e[n];null!=o&&o!==a[n]&&(o?t[r]():t[i]())};return o("removed","remove","restore"),o("selected","select","unselect"),o("selectable","selectify","unselectify"),o("locked","lock","unlock"),o("grabbable","grabify","ungrabify"),null!=e.classes&&t.classes(e.classes),n.endBatch(),this}if(void 0===e){var s={data:r.copy(a.data),position:r.copy(a.position),group:a.group,removed:a.removed,selected:a.selected,selectable:a.selectable,locked:a.locked,grabbable:a.grabbable,classes:null};s.classes="";var u=0;return a.classes.forEach(function(e){return s.classes+=0==u++?e:" "+e}),s}}},c.jsons=function(){for(var e=[],t=0;t<this.length;t++){var n=this[t],r=n.json();e.push(r)}return e},c.clone=function(){for(var e=this.cy(),t=[],n=0;n<this.length;n++){var r=this[n],i=r.json(),a=new s(e,i,!1);t.push(a)}return new l(e,t)},c.copy=c.clone,c.restore=function(e){var t=this,n=t.cy(),o=n._private;void 0===e&&(e=!0);for(var s=[],c=[],d=void 0,h=0,f=t.length;h<f;h++){var p=t[h];p.removed()&&(p.isNode()?s.push(p):c.push(p))}d=s.concat(c);var v=void 0,g=function(){d.splice(v,1),v--};for(v=0;v<d.length;v++){var y=d[v],m=y._private,b=m.data;if(y.clearTraversalCache(),void 0===b.id)b.id=u.generate(n,y);else if(i.number(b.id))b.id=""+b.id;else{if(i.emptyString(b.id)||!i.string(b.id)){r.error("Can not create element with invalid string ID `"+b.id+"`"),g();continue}if(n.hasElementWithId(b.id)){r.error("Can not create second element with ID `"+b.id+"`"),g();continue}}var x=b.id;if(y.isNode()){var w=m.position;null==w.x&&(w.x=0),null==w.y&&(w.y=0)}if(y.isEdge()){for(var _=y,E=["source","target"],k=E.length,C=!1,P=0;P<k;P++){var T=E[P],S=b[T];i.number(S)&&(S=b[T]=""+b[T]),null==S||""===S?(r.error("Can not create edge `"+x+"` with unspecified "+T),C=!0):n.hasElementWithId(S)||(r.error("Can not create edge `"+x+"` with nonexistant "+T+" `"+S+"`"),C=!0)}if(C){g();continue}var D=n.getElementById(b.source),I=n.getElementById(b.target);D._private.edges.push(_),I._private.edges.push(_),_._private.source=D,_._private.target=I}m.map=new a,m.map.set(x,{ele:y,index:0}),m.removed=!1,n.addToPool(y)}for(var N=0;N<s.length;N++){var M=s[N],L=M._private.data;i.number(L.parent)&&(L.parent=""+L.parent);var B=L.parent;if(null!=B){var O=n.getElementById(B);if(O.empty())L.parent=void 0;else{for(var A=!1,z=O;!z.empty();){if(M.same(z)){A=!0,L.parent=void 0;break}z=z.parent()}A||(O[0]._private.children.push(M),M._private.parent=O[0],o.hasCompoundNodes=!0)}}}if(d.length>0){for(var R=new l(n,d),j=0;j<R.length;j++){var F=R[j];F.isNode()||(F.parallelEdges().clearTraversalCache(),F.source().clearTraversalCache(),F.target().clearTraversalCache())}var V=void 0;V=o.hasCompoundNodes?n.collection().merge(R).merge(R.connectedNodes()).merge(R.parent()):R,V.dirtyCompoundBoundsCache().updateStyle(e),e?R.emitAndNotify("add"):R.emit("add")}return t},c.removed=function(){var e=this[0];return e&&e._private.removed},c.inside=function(){var e=this[0];return e&&!e._private.removed},c.remove=function(e){function t(e){for(var t=e._private.edges,n=0;n<t.length;n++)i(t[n])}function n(e){for(var t=e._private.children,n=0;n<t.length;n++)i(t[n])}function i(e){c[e.id()]||(c[e.id()]=!0,e.isNode()?(u.push(e),t(e),n(e)):u.unshift(e))}function a(e,t){var n=e._private.edges;r.removeFromArray(n,t),e.clearTraversalCache()}var o=this,s=[],u=[],c={},d=o._private.cy;void 0===e&&(e=!0);for(var h=0,f=o.length;h<f;h++)i(o[h]);var p=[];p.ids={},o.dirtyCompoundBoundsCache(),d.removeFromPool(u);for(var v=0;v<u.length;v++){var g=u[v];if(g._private.removed=!0,s.push(g),g.isEdge()){var y=g.source()[0],m=g.target()[0];a(y,g),a(m,g),function(e){e.parallelEdges().clearTraversalCache()}(g)}else{var b=g.parent();0!==b.length&&function(e,t){t=t[0],e=e[0];var n=e._private.children,i=e.id();r.removeFromArray(n,t),p.ids[i]||(p.ids[i]=!0,p.push(e))}(b,g)}}var x=d._private.elements;d._private.hasCompoundNodes=!1;for(var w=0;w<x.length;w++)if(x[w].isParent()){d._private.hasCompoundNodes=!0;break}var _=new l(this.cy(),s);_.size()>0&&(e&&this.cy().notify({type:"remove",eles:_}),_.emit("remove"));for(var E=0;E<p.length;E++){var k=p[E];k.removed()||k.updateStyle()}return new l(d,s)},c.move=function(e){var t=this._private.cy;if(void 0!==e.source||void 0!==e.target){var n=e.source,r=e.target,i=t.hasElementWithId(n),a=t.hasElementWithId(r);if(i||a){var o=this.jsons();this.remove();for(var s=0;s<o.length;s++){var u=o[s],l=this[s];"edges"===u.group&&(i&&(u.data.source=n),a&&(u.data.target=r),u.scratch=l._private.scratch)}return t.add(o)}}else if(void 0!==e.parent){var c=e.parent,d=null===c||t.hasElementWithId(c);if(d){var h=this.jsons(),f=this.descendants(),p=f.union(f.union(this).connectedEdges()).jsons();this.remove();for(var v=0;v<h.length;v++){var g=h[v],y=this[v];"nodes"===g.group&&(g.data.parent=null===c?void 0:c,g.scratch=y._private.scratch)}return t.add(h.concat(p))}}return this},[n(33),n(37),n(38),n(39),n(40),n(41),n(42),n(45),n(48),n(49),n(50),n(7),n(51),n(52),n(53),n(54),n(55)].forEach(function(e){r.extend(c,e)}),e.exports=l},function(e,t,n){"use strict";e.exports=n(137)},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),a=("function"==typeof Symbol&&Symbol.iterator,function(){function e(t){if(r(this,e),this._obj=Object.create(null),null!=t){var n=void 0;n=null!=t.instanceString&&t.instanceString()===this.instanceString()?t.toArray():t;for(var i=0;i<n.length;i++)this.add(n[i])}}return i(e,[{key:"instanceString",value:function(){return"set"}},{key:"add",value:function(e){this._obj[e]=1}},{key:"delete",value:function(e){this._obj[e]=0}},{key:"clear",value:function(){this._obj=Object.create(null)}},{key:"has",value:function(e){return 1===this._obj[e]}},{key:"toArray",value:function(){var e=this;return Object.keys(this._obj).filter(function(t){return e.has(t)})}},{key:"forEach",value:function(e,t){return this.toArray().forEach(e,t)}},{key:"size",get:function(){return this.toArray().length}}]),e}());e.exports=a},function(e,t,n){"use strict";function r(e){i.assign(this,u,e),this.listeners=[],this.emitting=0}var i=n(1),a=n(0),o=n(15),s=/(\w+)(\.(?:\w+|\*))?/,u={qualifierCompare:function(e,t){return e===t},eventMatches:function(){return!0},eventFields:function(){return{}},callbackContext:function(e){return e},beforeEmit:function(){},afterEmit:function(){},bubble:function(){return!1},parent:function(){return null},context:void 0},l=r.prototype,c=function(e,t,n,r,o,u,l){a.fn(r)&&(o=r,r=null),l&&(u=null==u?l:i.assign({},u,l));for(var c=n.split(/\s+/),d=0;d<c.length;d++){var h=c[d];if(!a.emptyString(h)){var f=h.match(s);if(f&&!1===t(e,h,f[1],f[2]?f[2]:null,r,o,u))break}}},d=function(e,t){return new o(t.type,i.assign(t,e.eventFields(e.context)))},h=function(e,t,n){if(a.event(n))return void t(e,n);if(a.plainObject(n))return void t(e,d(e,n));for(var r=n.split(/\s+/),i=0;i<r.length;i++){var o=r[i];if(!a.emptyString(o)){var u=o.match(s);if(u){var l=u[1],c=u[2]?u[2]:null;t(e,d(e,{type:l,namespace:c,target:e.context}))}}}};l.on=l.addListener=function(e,t,n,r,i){return c(this,function(e,t,n,r,i,o,s){a.fn(o)&&e.listeners.push({event:t,callback:o,type:n,namespace:r,qualifier:i,conf:s})},e,t,n,r,i),this},l.one=function(e,t,n,r){return this.on(e,t,n,r,{one:!0})},l.removeListener=l.off=function(e,t,n,r){var a=this;0!==this.emitting&&(this.listeners=i.copyArray(this.listeners));for(var o=this.listeners,s=o.length-1;s>=0;s--)!function(i){var s=o[i];c(a,function(e,t,n,r,a,u){if(s.type===n&&(!r||s.namespace===r)&&(!a||e.qualifierCompare(s.qualifier,a))&&(!u||s.callback===u))return o.splice(i,1),!1},e,t,n,r)}(s);return this},l.emit=l.trigger=function(e,t,n){var r=this.listeners,o=r.length;return this.emitting++,a.array(t)||(t=[t]),h(this,function(e,a){null!=n&&(r=[{event:a.event,type:a.type,namespace:a.namespace,callback:n}],o=r.length);for(var s=0;s<o;s++)!function(n){var o=r[n];if(o.type===a.type&&(!o.namespace||o.namespace===a.namespace||".*"===o.namespace)&&e.eventMatches(e.context,o,a)){var s=[a];null!=t&&i.push(s,t),e.beforeEmit(e.context,o,a),o.conf&&o.conf.one&&(e.listeners=e.listeners.filter(function(e){return e!==o}));var u=e.callbackContext(e.context,o,a),l=o.callback.apply(u,s);e.afterEmit(e.context,o,a),!1===l&&(a.stopPropagation(),a.preventDefault())}}(s);e.bubble(e.context)&&!a.isPropagationStopped()&&e.parent(e.context).emit(a,t)},e),this.emitting--,this},e.exports=r},function(e,t,n){"use strict";var r=function(){return{classes:[],colonSelectors:[],data:[],group:null,ids:[],meta:[],collection:null,filter:null,parent:null,ancestor:null,subject:null,child:null,descendant:null}};e.exports=r},function(e,t,n){"use strict";var r=n(4),i=n(1),a=n(7),o=n(0),s=n(5),u=n(3),l=function(e){var t=this;e=i.extend({},e);var n=e.container;n&&!o.htmlElement(n)&&o.htmlElement(n[0])&&(n=n[0]);var u=n?n._cyreg:null;(u=u||{})&&u.cy&&(u.cy.destroy(),u={});var l=u.readies=u.readies||[];n&&(n._cyreg=u),u.cy=t;var c=void 0!==r&&void 0!==n&&!e.headless,d=e;d.layout=i.extend({name:c?"grid":"null"},d.layout),d.renderer=i.extend({name:c?"canvas":"null"},d.renderer);var h=function(e,t,n){return void 0!==t?t:void 0!==n?n:e},f=this._private={container:n,ready:!1,options:d,elements:new a(this),listeners:[],aniEles:new a(this),scratch:{},layout:null,renderer:null,destroyed:!1,notificationsEnabled:!0,minZoom:1e-50,maxZoom:1e50,zoomingEnabled:h(!0,d.zoomingEnabled),userZoomingEnabled:h(!0,d.userZoomingEnabled),panningEnabled:h(!0,d.panningEnabled),userPanningEnabled:h(!0,d.userPanningEnabled),boxSelectionEnabled:h(!0,d.boxSelectionEnabled),autolock:h(!1,d.autolock,d.autolockNodes),autoungrabify:h(!1,d.autoungrabify,d.autoungrabifyNodes),autounselectify:h(!1,d.autounselectify),styleEnabled:void 0===d.styleEnabled?c:d.styleEnabled,zoom:o.number(d.zoom)?d.zoom:1,pan:{x:o.plainObject(d.pan)&&o.number(d.pan.x)?d.pan.x:0,y:o.plainObject(d.pan)&&o.number(d.pan.y)?d.pan.y:0},animation:{current:[],queue:[]},hasCompoundNodes:!1};this.createEmitter();var p=d.selectionType;f.selectionType=void 0===p||"additive"!==p&&"single"!==p?"single":p,o.number(d.minZoom)&&o.number(d.maxZoom)&&d.minZoom<d.maxZoom?(f.minZoom=d.minZoom,f.maxZoom=d.maxZoom):o.number(d.minZoom)&&void 0===d.maxZoom?f.minZoom=d.minZoom:o.number(d.maxZoom)&&void 0===d.minZoom&&(f.maxZoom=d.maxZoom),
f.styleEnabled&&t.setStyle([]),t.initRenderer(i.extend({hideEdgesOnViewport:d.hideEdgesOnViewport,textureOnViewport:d.textureOnViewport,wheelSensitivity:o.number(d.wheelSensitivity)&&d.wheelSensitivity>0?d.wheelSensitivity:1,motionBlur:void 0!==d.motionBlur&&d.motionBlur,motionBlurOpacity:void 0===d.motionBlurOpacity?.05:d.motionBlurOpacity,pixelRatio:o.number(d.pixelRatio)&&d.pixelRatio>0?d.pixelRatio:void 0,desktopTapThreshold:void 0===d.desktopTapThreshold?4:d.desktopTapThreshold,touchTapThreshold:void 0===d.touchTapThreshold?8:d.touchTapThreshold},d.renderer));var v=function(e,n,r){t.notifications(!1);var a=t.mutableElements();a.length>0&&a.remove(),null!=e&&(o.plainObject(e)||o.array(e))&&t.add(e),t.one("layoutready",function(e){t.notifications(!0),t.emit(e),t.notify({type:"load",eles:t.mutableElements()}),t.one("load",n),t.emit("load")}).one("layoutstop",function(){t.one("done",r),t.emit("done")});var s=i.extend({},t._private.options.layout);s.eles=t.elements(),t.layout(s).run()};!function(e,t){return e.some(o.promise)?s.all(e).then(t):void t(e)}([d.style,d.elements],function(e){var n=e[0],r=e[1];f.styleEnabled&&t.style().append(n),v(r,function(){t.startAnimationLoop(),f.ready=!0,o.fn(d.ready)&&t.on("ready",d.ready);for(var e=0;e<l.length;e++){var n=l[e];t.on("ready",n)}u&&(u.readies=[]),t.emit("ready")},d.done)})},c=l.prototype;i.extend(c,{instanceString:function(){return"core"},isReady:function(){return this._private.ready},isDestroyed:function(){return this._private.destroyed},ready:function(e){return this.isReady()?this.emitter().emit("ready",[],e):this.on("ready",e),this},destroy:function(){var e=this;if(!e.isDestroyed())return e.stopAnimationLoop(),e.destroyRenderer(),this.emit("destroy"),e._private.destroyed=!0,e},hasElementWithId:function(e){return this._private.elements.hasElementWithId(e)},getElementById:function(e){return this._private.elements.getElementById(e)},selectionType:function(){return this._private.selectionType},hasCompoundNodes:function(){return this._private.hasCompoundNodes},headless:function(){return"null"===this._private.options.renderer.name},styleEnabled:function(){return this._private.styleEnabled},addToPool:function(e){return this._private.elements.merge(e),this},removeFromPool:function(e){return this._private.elements.unmerge(e),this},container:function(){return this._private.container},options:function(){return i.copy(this._private.options)},json:function(e){var t=this,n=t._private,r=t.mutableElements();if(o.plainObject(e)){if(t.startBatch(),e.elements){var a={},s=function(e,n){for(var r=0;r<e.length;r++){var o=e[r],s=o.data.id,u=t.getElementById(s);a[s]=!0,0!==u.length?u.json(o):n?t.add(i.extend({group:n},o)):t.add(o)}};if(o.array(e.elements))s(e.elements);else for(var u=["nodes","edges"],l=0;l<u.length;l++){var c=u[l],d=e.elements[c];o.array(d)&&s(d,c)}r.stdFilter(function(e){return!a[e.id()]}).remove()}e.style&&t.style(e.style),null!=e.zoom&&e.zoom!==n.zoom&&t.zoom(e.zoom),e.pan&&(e.pan.x===n.pan.x&&e.pan.y===n.pan.y||t.pan(e.pan));for(var h=["minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","panningEnabled","userPanningEnabled","boxSelectionEnabled","autolock","autoungrabify","autounselectify"],f=0;f<h.length;f++){var p=h[f];null!=e[p]&&t[p](e[p])}return t.endBatch(),this}if(void 0===e){var v={};return v.elements={},r.forEach(function(e){var t=e.group();v.elements[t]||(v.elements[t]=[]),v.elements[t].push(e.json())}),this._private.styleEnabled&&(v.style=t.style().json()),v.zoomingEnabled=t._private.zoomingEnabled,v.userZoomingEnabled=t._private.userZoomingEnabled,v.zoom=t._private.zoom,v.minZoom=t._private.minZoom,v.maxZoom=t._private.maxZoom,v.panningEnabled=t._private.panningEnabled,v.userPanningEnabled=t._private.userPanningEnabled,v.pan=i.copy(t._private.pan),v.boxSelectionEnabled=t._private.boxSelectionEnabled,v.renderer=i.copy(t._private.options.renderer),v.hideEdgesOnViewport=t._private.options.hideEdgesOnViewport,v.textureOnViewport=t._private.options.textureOnViewport,v.wheelSensitivity=t._private.options.wheelSensitivity,v.motionBlur=t._private.options.motionBlur,v}},scratch:u.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0}),removeScratch:u.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0})}),c.$id=c.getElementById,[n(56),n(60),n(65),n(66),n(67),n(68),n(69),n(70),n(71),n(72)].forEach(function(e){i.extend(c,e)}),e.exports=l},function(e,t,n){"use strict";var r=n(1),i=n(0),a=n(9),o=function(e,t,n){if(n=!(void 0!==n&&!n),void 0===e||void 0===t||!i.core(e))return void r.error("An element must have a core reference and parameters set");var o=t.group;if(null==o&&(o=t.data&&null!=t.data.source&&null!=t.data.target?"edges":"nodes"),"nodes"!==o&&"edges"!==o)return void r.error("An element must be of type `nodes` or `edges`; you specified `"+o+"`");this.length=1,this[0]=this;var s=this._private={cy:e,single:!0,data:t.data||{},position:t.position||{},autoWidth:void 0,autoHeight:void 0,autoPadding:void 0,compoundBoundsClean:!1,listeners:[],group:o,style:{},rstyle:{},styleCxts:[],removed:!0,selected:!!t.selected,selectable:void 0===t.selectable||!!t.selectable,locked:!!t.locked,grabbed:!1,grabbable:void 0===t.grabbable||!!t.grabbable,active:!1,classes:new a,animation:{current:[],queue:[]},rscratch:{},scratch:t.scratch||{},edges:[],children:[],parent:null,traversalCache:{}};if(t.renderedPosition){var u=t.renderedPosition,l=e.pan(),c=e.zoom();s.position={x:(u.x-l.x)/c,y:(u.y-l.y)/c}}if(i.string(t.classes))for(var d=t.classes.split(/\s+/),h=0,f=d.length;h<f;h++){var p=d[h];p&&""!==p&&s.classes.add(p)}(t.style||t.css)&&e.style().applyBypass(this,t.style||t.css),this.createEmitter(),(void 0===n||n)&&this.restore()};e.exports=o},function(e,t,n){"use strict";var r=n(1),i=function(e,t){function n(e){var t=e.pstyle("z-compound-depth");return"auto"===t.value?o?e.zDepth():0:"bottom"===t.value?-1:"top"===t.value?r.MAX_INT:0}function i(e){return"auto"===e.pstyle("z-index-compare").value&&e.isNode()?1:0}var a=e.cy(),o=a.hasCompoundNodes(),s=n(e)-n(t);if(0!==s)return s;var u=i(e)-i(t);if(0!==u)return u;var l=e.pstyle("z-index").value-t.pstyle("z-index").value;return 0!==l?l:e.poolIndex()-t.poolIndex()};e.exports=i},function(e,t,n){"use strict";function r(){return!1}function i(){return!0}var a=function(e,t){this.recycle(e,t)};a.prototype={instanceString:function(){return"event"},recycle:function(e,t){if(this.isImmediatePropagationStopped=this.isPropagationStopped=this.isDefaultPrevented=r,null!=e&&e.preventDefault?(this.type=e.type,this.isDefaultPrevented=e.defaultPrevented?i:r):null!=e&&e.type?t=e:this.type=e,null!=t&&(this.originalEvent=t.originalEvent,this.type=null!=t.type?t.type:this.type,this.cy=t.cy,this.target=t.target,this.position=t.position,this.renderedPosition=t.renderedPosition,this.namespace=t.namespace,this.layout=t.layout),null!=this.cy&&null!=this.position&&null==this.renderedPosition){var n=this.position,a=this.cy.zoom(),o=this.cy.pan();this.renderedPosition={x:n.x*a+o.x,y:n.y*a+o.y}}this.timeStamp=e&&e.timeStamp||Date.now()},preventDefault:function(){this.isDefaultPrevented=i;var e=this.originalEvent;e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){this.isPropagationStopped=i;var e=this.originalEvent;e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=i,this.stopPropagation()},isDefaultPrevented:r,isPropagationStopped:r,isImmediatePropagationStopped:r},e.exports=a},function(e,t,n){"use strict";var r=n(1);e.exports={setupDequeueing:function(e){return function(){var t=this,n=this.renderer;if(!t.dequeueingSetup){t.dequeueingSetup=!0;var i=r.debounce(function(){n.redrawHint("eles",!0),n.redrawHint("drag",!0),n.redraw()},e.deqRedrawThreshold),a=function(a,o){for(var s=r.performanceNow(),u=n.averageRedrawTime,l=n.lastRedrawTime,c=[],d=n.cy.extent(),h=n.getPixelRatio();;){var f=r.performanceNow(),p=f-s,v=f-o;if(l<1e3/60){var g=1e3/60-(a?u:0);if(v>=e.deqFastCost*g)break}else if(a){if(p>=e.deqCost*l||p>=e.deqAvgCost*u)break}else if(v>=e.deqNoDrawCost*(1e3/60))break;var y=e.deq(t,h,d);if(!(y.length>0))break;for(var m=0;m<y.length;m++)c.push(y[m])}c.length>0&&(e.onDeqd(t,c),!a&&e.shouldRedraw(t,c,h,d)&&i())},o=e.priority||r.noop;n.beforeRender(a,o(t))}}}}},function(e,t,n){"use strict";var r=n(1),i=[{selector:":selected",matches:function(e){return e.selected()}},{selector:":unselected",matches:function(e){return!e.selected()}},{selector:":selectable",matches:function(e){return e.selectable()}},{selector:":unselectable",matches:function(e){return!e.selectable()}},{selector:":locked",matches:function(e){return e.locked()}},{selector:":unlocked",matches:function(e){return!e.locked()}},{selector:":visible",matches:function(e){return e.visible()}},{selector:":hidden",matches:function(e){return!e.visible()}},{selector:":transparent",matches:function(e){return e.transparent()}},{selector:":grabbed",matches:function(e){return e.grabbed()}},{selector:":free",matches:function(e){return!e.grabbed()}},{selector:":removed",matches:function(e){return e.removed()}},{selector:":inside",matches:function(e){return!e.removed()}},{selector:":grabbable",matches:function(e){return e.grabbable()}},{selector:":ungrabbable",matches:function(e){return!e.grabbable()}},{selector:":animated",matches:function(e){return e.animated()}},{selector:":unanimated",matches:function(e){return!e.animated()}},{selector:":parent",matches:function(e){return e.isParent()}},{selector:":childless",matches:function(e){return e.isChildless()}},{selector:":child",matches:function(e){return e.isChild()}},{selector:":orphan",matches:function(e){return e.isOrphan()}},{selector:":nonorphan",matches:function(e){return e.isChild()}},{selector:":loop",matches:function(e){return e.isLoop()}},{selector:":simple",matches:function(e){return e.isSimple()}},{selector:":active",matches:function(e){return e.active()}},{selector:":inactive",matches:function(e){return!e.active()}},{selector:":backgrounding",matches:function(e){return e.backgrounding()}},{selector:":nonbackgrounding",matches:function(e){return!e.backgrounding()}}].sort(function(e,t){return r.sort.descending(e.selector,t.selector)}),a=function e(t,n){return(e.lookup=e.lookup||function(){for(var e={},t=void 0,n=0;n<i.length;n++)t=i[n],e[t.selector]=t.matches;return e}())[t](n)},o="("+i.map(function(e){return e.selector}).join("|")+")";e.exports={stateSelectors:i,stateSelectorMatches:a,stateSelectorRegex:o}},function(e,t,n){"use strict";var r=n(0),i=n(1),a=n(6),o=function e(t){return this instanceof e?r.core(t)?(this._private={cy:t,coreStyle:{}},this.length=0,void this.resetToDefault()):void i.error("A style must have a core reference"):new e(t)},s=o.prototype;s.instanceString=function(){return"style"},s.clear=function(){for(var e=0;e<this.length;e++)this[e]=void 0;return this.length=0,this._private.newStyle=!0,this},s.resetToDefault=function(){return this.clear(),this.addDefaultStylesheet(),this},s.core=function(){return this._private.coreStyle},s.selector=function(e){var t="core"===e?null:new a(e),n=this.length++;return this[n]={selector:t,properties:[],mappedProperties:[],index:n},this},s.css=function(){var e=this,t=arguments;switch(t.length){case 1:for(var n=t[0],r=0;r<e.properties.length;r++){var a=e.properties[r],o=n[a.name];void 0===o&&(o=n[i.dash2camel(a.name)]),void 0!==o&&this.cssRule(a.name,o)}break;case 2:this.cssRule(t[0],t[1])}return this},s.style=s.css,s.cssRule=function(e,t){var n=this.parse(e,t);if(n){var r=this.length-1;this[r].properties.push(n),this[r].properties[n.name]=n,n.name.match(/pie-(\d+)-background-size/)&&n.value&&(this._private.hasPie=!0),n.mapped&&this[r].mappedProperties.push(n),!this[r].selector&&(this._private.coreStyle[n.name]=n)}return this},s.append=function(e){return r.stylesheet(e)?e.appendToStyle(this):r.array(e)?this.appendFromJson(e):r.string(e)&&this.appendFromString(e),this},o.fromJson=function(e,t){var n=new o(e);return n.fromJson(t),n},o.fromString=function(e,t){return new o(e).fromString(t)},[n(123),n(124),n(125),n(126),n(127),n(130),n(129),n(128)].forEach(function(e){i.extend(s,e)}),o.types=s.types,o.properties=s.properties,e.exports=o},function(e,t,n){"use strict";e.exports=function(e,t){t||(t=function(){if(1===arguments.length)return arguments[0];if(0===arguments.length)return"undefined";for(var e=[],t=0;t<arguments.length;t++)e.push(arguments[t]);return e.join("$")});var n=function n(){var r=this,i=arguments,a=void 0,o=t.apply(r,i),s=n.cache;return(a=s[o])||(a=s[o]=e.apply(r,i)),a};return n.cache={},n}},function(e,t){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){"use strict";function r(e,t,n){var r=n,a=function(n){s.error("Can not register `"+t+"` for `"+e+"` since `"+n+"` already exists in the prototype and can not be overridden")};if("core"===e){if(c.prototype[t])return a(t);c.prototype[t]=n}else if("collection"===e){if(l.prototype[t])return a(t);l.prototype[t]=n}else if("layout"===e){for(var o=function(e){this.options=e,n.call(this,e),h.plainObject(this._private)||(this._private={}),this._private.cy=e.cy,this._private.listeners=[],this.createEmitter()},d=o.prototype=Object.create(n.prototype),v=[],g=0;g<v.length;g++){var y=v[g];d[y]=d[y]||function(){return this}}d.start&&!d.run?d.run=function(){return this.start(),this}:!d.start&&d.run&&(d.start=function(){return this.run(),this});var m=n.prototype.stop;d.stop=function(){var e=this.options;if(e&&e.animate){var t=this.animations;if(t)for(var n=0;n<t.length;n++)t[n].stop()}return m?m.call(this):this.emit("layoutstop"),this},d.destroy||(d.destroy=function(){return this}),d.cy=function(){return this._private.cy};var b=function(e){return e._private.cy};s.assign(d,{createEmitter:function(){return this._private.emitter=new f({eventFields:function(e){return{layout:e,cy:b(e),target:e}},bubble:function(){return!0},parent:function(e){return b(e)},context:this}),this},emitter:function(){return this._private.emitter},on:function(e,t){return this.emitter().on(e,t),this},one:function(e,t){return this.emitter().one(e,t),this},once:function(e,t){return this.emitter().one(e,t),this},removeListener:function(e,t){return this.emitter().removeListener(e,t),this},emit:function(e,t){return this.emitter().emit(e,t),this}}),u.eventAliasesOn(d),r=o}else if("renderer"===e&&"null"!==t&&"base"!==t){var x=i("renderer","base"),w=x.prototype,_=n,E=n.prototype,k=function(){x.apply(this,arguments),_.apply(this,arguments)},C=k.prototype;for(var P in w){var T=w[P],S=null!=E[P];if(S)return a(P);C[P]=T}for(var D in E)C[D]=E[D];w.clientFunctions.forEach(function(e){C[e]=C[e]||function(){s.error("Renderer does not implement `renderer."+e+"()` on its prototype")}}),r=k}return s.setMap({map:p,keys:[e,t],value:r})}function i(e,t){return s.getMap({map:p,keys:[e,t]})}function a(e,t,n,r,i){return s.setMap({map:v,keys:[e,t,n,r],value:i})}function o(e,t,n,r){return s.getMap({map:v,keys:[e,t,n,r]})}var s=n(1),u=n(3),l=n(7),c=n(12),d=n(76),h=n(0),f=n(10),p={},v={},g=function(){return 2===arguments.length?i.apply(null,arguments):3===arguments.length?r.apply(null,arguments):4===arguments.length?o.apply(null,arguments):5===arguments.length?a.apply(null,arguments):void s.error("Invalid extension access syntax")};c.prototype.extension=g,d.forEach(function(e){e.extensions.forEach(function(t){r(e.type,t.name,t.impl)})}),e.exports=g},function(e,t,n){"use strict";var r=n(0),i=n(1),a=n(18),o=function e(){return this instanceof e?void(this.length=0):new e},s=o.prototype;s.instanceString=function(){return"stylesheet"},s.selector=function(e){return this[this.length++]={selector:e,properties:[]},this},s.css=function(e,t){var n=this.length-1;if(r.string(e))this[n].properties.push({name:e,value:t});else if(r.plainObject(e))for(var o=e,s=0;s<a.properties.length;s++){var u=a.properties[s],l=o[u.name];if(void 0===l&&(l=o[i.dash2camel(u.name)]),void 0!==l){var c=u.name,d=l;this[n].properties.push({name:c,value:d})}}return this},s.style=s.css,s.generateStyle=function(e){var t=new a(e);return this.appendToStyle(t)},s.appendToStyle=function(e){for(var t=0;t<this.length;t++){var n=this[t],r=n.selector,i=n.properties;e.selector(r);for(var a=0;a<i.length;a++){var o=i[a];e.css(o.name,o.value)}}return e},e.exports=o},function(e,t,n){"use strict";e.exports="3.2.4"},function(e,t,n){"use strict";var r=n(1),i=n(0),a=n(5),o=function(e,t,n){var a=this._private=r.extend({duration:1e3},t,n);a.target=e,a.style=a.style||a.css,a.started=!1,a.playing=!1,a.hooked=!1,a.applying=!1,a.progress=0,a.completes=[],a.frames=[],a.complete&&i.fn(a.complete)&&a.completes.push(a.complete),this.length=1,this[0]=this},s=o.prototype;r.extend(s,{instanceString:function(){return"animation"},hook:function(){var e=this._private;if(!e.hooked){var t=void 0,n=e.target._private.animation;t=e.queue?n.queue:n.current,t.push(this),i.elementOrCollection(e.target)&&e.target.cy().addToAnimationPool(e.target),e.hooked=!0}return this},play:function(){var e=this._private;return 1===e.progress&&(e.progress=0),e.playing=!0,e.started=!1,e.stopped=!1,this.hook(),this},playing:function(){return this._private.playing},apply:function(){var e=this._private;return e.applying=!0,e.started=!1,e.stopped=!1,this.hook(),this},applying:function(){return this._private.applying},pause:function(){var e=this._private;return e.playing=!1,e.started=!1,this},stop:function(){var e=this._private;return e.playing=!1,e.started=!1,e.stopped=!0,this},rewind:function(){return this.progress(0)},fastforward:function(){return this.progress(1)},time:function(e){var t=this._private;return void 0===e?t.progress*t.duration:this.progress(e/t.duration)},progress:function(e){var t=this._private,n=t.playing;return void 0===e?t.progress:(n&&this.pause(),t.progress=e,t.started=!1,n&&this.play(),this)},completed:function(){return 1===this._private.progress},reverse:function(){var e=this._private,t=e.playing;t&&this.pause(),e.progress=1-e.progress,e.started=!1;var n=function(t,n){var r=e[t];null!=r&&(e[t]=e[n],e[n]=r)};if(n("zoom","startZoom"),n("pan","startPan"),n("position","startPosition"),e.style)for(var r=0;r<e.style.length;r++){var i=e.style[r],a=i.name,o=e.startStyle[a];e.startStyle[a]=i,e.style[r]=o}return t&&this.play(),this},promise:function(e){var t=this._private,n=void 0;switch(e){case"frame":n=t.frames;break;default:case"complete":case"completed":n=t.completes}return new a(function(e,t){n.push(function(){e()})})}}),s.complete=s.completed,e.exports=o},function(e,t,n){"use strict";var r=n(0),i={aStar:function(e){var t=this;e=e||{};var n=this._private.cy;if(null!=e&&null!=e.root){var i=r.string(e.root)?this.filter(e.root)[0]:e.root[0];if(null!=e.goal){var a=r.string(e.goal)?this.filter(e.goal)[0]:e.goal[0];if(null!=e.heuristic&&r.fn(e.heuristic))var o=e.heuristic;else var o=function(){return 0};if(null!=e.weight&&r.fn(e.weight))var s=e.weight;else var s=function(e){return 1};if(null!=e.directed)var u=e.directed;else var u=!1;var l=i.id(),c=a.id(),d=[],h=[l],f={},p={},v={},g={};v[l]=0,g[l]=o(i);for(var y=0;h.length>0;){var m=function(e,t){if(0!==e.length){for(var n=0,r=t[e[0]],i=1;i<e.length;i++){var a=t[e[i]];a<r&&(r=a,n=i)}return n}}(h,g),b=n.getElementById(h[m]),x=b.id();if(y++,x==c){var w=function e(t,r,i,a){if(t==r)return a.unshift(n.getElementById(r)),a;if(r in i){var o=i[r],s=p[r];return a.unshift(n.getElementById(s)),a.unshift(n.getElementById(r)),e(t,o,i,a)}}(l,c,f,[]);return{found:!0,distance:v[x],path:t.spawn(w),steps:y}}d.push(x),h.splice(m,1);for(var _=b._private.edges,E=0;E<_.length;E++){var k=_[E];if(this.hasElementWithId(k.id())&&(!u||k.data("source")===x)){var C=k.source(),P=k.target(),T=C.id()!==x?C:P,S=T.id();if(this.hasElementWithId(S)&&-1==d.indexOf(S)){var D=v[x]+s(k);-1!=h.indexOf(S)?D<v[S]&&(v[S]=D,g[S]=D+o(T),f[S]=x):(v[S]=D,g[S]=D+o(T),h.push(S),f[S]=x,p[S]=k.id())}}}}return{found:!1,distance:void 0,path:void 0,steps:y}}}}};e.exports=i},function(e,t,n){"use strict";var r=n(0),i=n(1),a={bellmanFord:function(e){var t=this;if(e=e||{},null!=e.weight&&r.fn(e.weight))var n=e.weight;else var n=function(e){return 1};if(null!=e.directed)var a=e.directed;else var a=!1;if(null!=e.root){if(r.string(e.root))var o=this.filter(e.root)[0];else var o=e.root[0];for(var s=this._private.cy,u=this.edges().stdFilter(function(e){return!e.isLoop()}),l=this.nodes(),c=l.length,d={},h=0;h<c;h++)d[l[h].id()]=h;for(var f=[],p=[],v=[],h=0;h<c;h++)l[h].id()===o.id()?f[h]=0:f[h]=1/0,p[h]=void 0;for(var g=!1,h=1;h<c;h++){g=!1;for(var y=0;y<u.length;y++){var m=d[u[y].source().id()],b=d[u[y].target().id()],x=n(u[y]),w=f[m]+x;if(w<f[b]&&(f[b]=w,p[b]=m,v[b]=u[y],g=!0),!a){var w=f[b]+x;w<f[m]&&(f[m]=w,p[m]=b,v[m]=u[y],g=!0)}}if(!g)break}if(g)for(var y=0;y<u.length;y++){var m=d[u[y].source().id()],b=d[u[y].target().id()],x=n(u[y]);if(f[m]+x<f[b])return i.error("Graph contains a negative weight cycle for Bellman-Ford"),{pathTo:void 0,distanceTo:void 0,hasNegativeWeightCycle:!0}}for(var _=[],h=0;h<c;h++)_.push(l[h].id());return{distanceTo:function(e){if(r.string(e))var t=s.filter(e)[0].id();else var t=e.id();return f[d[t]]},pathTo:function(e){if(r.string(e))var n=s.filter(e)[0].id();else var n=e.id();var i=[],a=function(e,t,n,r,i,a){for(;;){if(i.push(s.getElementById(r[n])),i.push(a[n]),t===n)return i;var o=e[n];if(void 0===o)return;n=o}}(p,d[o.id()],d[n],_,i,v);return null!=a&&a.reverse(),t.spawn(a)},hasNegativeWeightCycle:!1}}}};e.exports=a},function(e,t,n){"use strict";var r=n(0),i=n(8),a={betweennessCentrality:function(e){e=e||{};var t,n;r.fn(e.weight)?(n=e.weight,t=!0):t=!1;for(var a=null!=e.directed&&e.directed,o=this._private.cy,s=this.nodes(),u={},l={},c=0,d={set:function(e,t){l[e]=t,t>c&&(c=t)},get:function(e){return l[e]}},h=0;h<s.length;h++){var f=s[h],p=f.id();u[p]=a?f.outgoers().nodes():f.openNeighborhood().nodes(),d.set(p,0)}for(var v=0;v<s.length;v++){for(var g=s[v].id(),y=[],m={},b={},x={},w=new i(function(e,t){return x[e]-x[t]}),h=0;h<s.length;h++){var p=s[h].id();m[p]=[],b[p]=0,x[p]=1/0}for(b[g]=1,x[g]=0,w.push(g);!w.empty();){var f=w.pop();if(y.push(f),t)for(var _=0;_<u[f].length;_++){var E,k=u[f][_],C=o.getElementById(f);E=C.edgesTo(k).length>0?C.edgesTo(k)[0]:k.edgesTo(C)[0];var P=n(E);k=k.id(),x[k]>x[f]+P&&(x[k]=x[f]+P,w.nodes.indexOf(k)<0?w.push(k):w.updateItem(k),b[k]=0,m[k]=[]),x[k]==x[f]+P&&(b[k]=b[k]+b[f],m[k].push(f))}else for(var _=0;_<u[f].length;_++){var k=u[f][_].id();x[k]==1/0&&(w.push(k),x[k]=x[f]+1),x[k]==x[f]+1&&(b[k]=b[k]+b[f],m[k].push(f))}}for(var T={},h=0;h<s.length;h++)T[s[h].id()]=0;for(;y.length>0;)for(var k=y.pop(),_=0;_<m[k].length;_++){var f=m[k][_];T[f]=T[f]+b[f]/b[k]*(1+T[k]),k!=s[v].id()&&d.set(k,d.get(k)+T[k])}}var S={betweenness:function(e){if(r.string(e))var e=o.filter(e).id();else var e=e.id();return d.get(e)},betweennessNormalized:function(e){if(0==c)return 0;if(r.string(e))var e=o.filter(e).id();else var e=e.id();return d.get(e)/c}};return S.betweennessNormalised=S.betweennessNormalized,S}};a.bc=a.betweennessCentrality,e.exports=a},function(e,t,n){"use strict";var r=n(0),i=function(e){return e={bfs:e.bfs||!e.dfs,dfs:e.dfs||!e.bfs},function(t,n,i){var a;r.plainObject(t)&&!r.elementOrCollection(t)&&(a=t,t=a.roots||a.root,n=a.visit,i=a.directed),i=2!==arguments.length||r.fn(n)?i:n,n=r.fn(n)?n:function(){};for(var o,s=this._private.cy,u=t=r.string(t)?this.filter(t):t,l=[],c=[],d={},h={},f={},p=0,v=this.nodes(),g=this.edges(),y=0;y<u.length;y++)u[y].isNode()&&(l.unshift(u[y]),e.bfs&&(f[u[y].id()]=!0,c.push(u[y])),h[u[y].id()]=0);for(;0!==l.length;){var u=e.bfs?l.shift():l.pop();if(e.dfs){if(f[u.id()])continue;f[u.id()]=!0,c.push(u)}var m,b=h[u.id()],x=d[u.id()],w=null==x?void 0:x.connectedNodes().not(u)[0];if(!0===(m=n(u,x,w,p++,b))){o=u;break}if(!1===m)break;for(var _=u.connectedEdges(i?function(e){return e.data("source")===u.id()}:void 0).intersect(g),y=0;y<_.length;y++){var E=_[y],k=E.connectedNodes(function(e){return e.id()!==u.id()}).intersect(v);0===k.length||f[k.id()]||(k=k[0],l.push(k),e.bfs&&(f[k.id()]=!0,c.push(k)),d[k.id()]=E,h[k.id()]=h[u.id()]+1)}}for(var C=[],y=0;y<c.length;y++){var P=c[y],T=d[P.id()];T&&C.push(T),C.push(P)}return{path:s.collection(C,{unique:!0}),found:s.collection(o)}}},a={breadthFirstSearch:i({bfs:!0}),depthFirstSearch:i({dfs:!0})};a.bfs=a.breadthFirstSearch,a.dfs=a.depthFirstSearch,e.exports=a},function(e,t,n){"use strict";var r=n(0),i={closenessCentralityNormalized:function(e){e=e||{};var t=this.cy(),n=e.harmonic;void 0===n&&(n=!0);for(var i={},a=0,o=this.nodes(),s=this.floydWarshall({weight:e.weight,directed:e.directed}),u=0;u<o.length;u++){for(var l=0,c=0;c<o.length;c++)if(u!=c){var d=s.distance(o[u],o[c]);l+=n?1/d:d}n||(l=1/l),a<l&&(a=l),i[o[u].id()]=l}return{closeness:function(e){if(0==a)return 0;if(r.string(e))var e=t.filter(e)[0].id();else var e=e.id();return i[e]/a}}},closenessCentrality:function(e){if(e=e||{},null!=e.root){if(r.string(e.root))var t=this.filter(e.root)[0];else var t=e.root[0];if(null!=e.weight&&r.fn(e.weight))var n=e.weight;else var n=function(){return 1};if(null!=e.directed&&r.bool(e.directed))var i=e.directed;else var i=!1;var a=e.harmonic;void 0===a&&(a=!0);for(var o=this.dijkstra({root:t,weight:n,directed:i}),s=0,u=this.nodes(),l=0;l<u.length;l++)if(u[l].id()!=t.id()){var c=o.distanceTo(u[l]);s+=a?1/c:c}return a?s:1/s}}};i.cc=i.closenessCentrality,i.ccn=i.closenessCentralityNormalised=i.closenessCentralityNormalized,e.exports=i},function(e,t,n){"use strict";var r=n(0),i=n(1),a={degreeCentralityNormalized:function(e){e=e||{};var t=this.cy();if(null!=e.directed)var n=e.directed;else var n=!1;var a=this.nodes(),o=a.length;if(n){for(var s={},u={},l=0,c=0,d=0;d<o;d++){var h=a[d],f=this.degreeCentrality(i.extend({},e,{root:h}));l<f.indegree&&(l=f.indegree),c<f.outdegree&&(c=f.outdegree),s[h.id()]=f.indegree,u[h.id()]=f.outdegree}return{indegree:function(e){if(0==l)return 0;if(r.string(e))var e=t.filter(e)[0].id();else var e=e.id();return s[e]/l},outdegree:function(e){if(0==c)return 0;if(r.string(e))var e=t.filter(e)[0].id();else var e=e.id();return u[e]/c}}}for(var p={},v=0,d=0;d<o;d++){var h=a[d],f=this.degreeCentrality(i.extend({},e,{root:h}));v<f.degree&&(v=f.degree),p[h.id()]=f.degree}return{degree:function(e){if(0==v)return 0;if(r.string(e))var e=t.filter(e)[0].id();else var e=e.id();return p[e]/v}}},degreeCentrality:function(e){e=e||{};var t=this;if(null!=e&&null!=e.root){var n=r.string(e.root)?this.filter(e.root)[0]:e.root[0];if(null!=e.weight&&r.fn(e.weight))var i=e.weight;else var i=function(e){return 1};if(null!=e.directed)var a=e.directed;else var a=!1;if(null!=e.alpha&&r.number(e.alpha))var o=e.alpha;else o=0;if(a){for(var s=n.connectedEdges('edge[target = "'+n.id()+'"]').intersection(t),u=n.connectedEdges('edge[source = "'+n.id()+'"]').intersection(t),l=s.length,c=u.length,d=0,h=0,f=0;f<s.length;f++){var p=s[f];d+=i(p)}for(var f=0;f<u.length;f++){var p=u[f];h+=i(p)}return{indegree:Math.pow(l,1-o)*Math.pow(d,o),outdegree:Math.pow(c,1-o)*Math.pow(h,o)}}for(var v=n.connectedEdges().intersection(t),g=v.length,y=0,f=0;f<v.length;f++){var p=v[f];y+=i(p)}return{degree:Math.pow(g,1-o)*Math.pow(y,o)}}}};a.dc=a.degreeCentrality,a.dcn=a.degreeCentralityNormalised=a.degreeCentralityNormalized,e.exports=a},function(e,t,n){"use strict";var r=n(0),i=n(8),a={dijkstra:function(e,t,n){var a;r.plainObject(e)&&!r.elementOrCollection(e)&&(a=e,e=a.root,t=a.weight,n=a.directed);var o=this._private.cy;t=r.fn(t)?t:function(){return 1};for(var s=r.string(e)?this.filter(e)[0]:e[0],u={},l={},c={},d=this.edges().filter(function(e){return!e.isLoop()}),h=this.nodes(),f=function(e){return u[e.id()]},p=new i(function(e,t){return f(e)-f(t)}),v=0;v<h.length;v++){var g=h[v];u[g.id()]=g.same(s)?0:1/0,p.push(g)}for(;p.size()>0;){var y=p.pop(),m=f(y),b=y.id();if(c[b]=m,m!==1/0)for(var x=y.neighborhood().intersect(h),v=0;v<x.length;v++){var w=x[v],_=w.id(),E=function(e,r){for(var i,a=(n?e.edgesTo(r):e.edgesWith(r)).intersect(d),o=1/0,s=0;s<a.length;s++){var u=a[s],l=t(u);(l<o||!i)&&(o=l,i=u)}return{edge:i,dist:o}}(y,w),k=m+E.dist;k<f(w)&&(!function(e,t){u[e.id()]=t,p.updateItem(e)}(w,k),l[_]={node:y,edge:E.edge})}}return{distanceTo:function(e){var t=r.string(e)?h.filter(e)[0]:e[0];return c[t.id()]},pathTo:function(e){var t=r.string(e)?h.filter(e)[0]:e[0],n=[],i=t;if(t.length>0)for(n.unshift(t);l[i.id()];){var a=l[i.id()];n.unshift(a.edge),n.unshift(a.node),i=a.node}return o.collection(n)}}}};e.exports=a},function(e,t,n){"use strict";var r=n(0),i={floydWarshall:function(e){e=e||{};var t=this.cy();if(null!=e.weight&&r.fn(e.weight))var n=e.weight;else var n=function(e){return 1};if(null!=e.directed)var i=e.directed;else var i=!1;for(var a=this.edges().stdFilter(function(e){return!e.isLoop()}),o=this.nodes(),s=o.length,u={},l=0;l<s;l++)u[o[l].id()]=l;for(var c=[],l=0;l<s;l++){for(var d=new Array(s),h=0;h<s;h++)d[h]=l==h?0:1/0;c.push(d)}var f=[],p=[],v=function(e){for(var t=0;t<s;t++){for(var n=new Array(s),r=0;r<s;r++)n[r]=void 0;e.push(n)}};v(f),v(p);for(var l=0;l<a.length;l++){var g=u[a[l].source().id()],y=u[a[l].target().id()],m=n(a[l]);c[g][y]>m&&(c[g][y]=m,f[g][y]=y,p[g][y]=a[l])}if(!i)for(var l=0;l<a.length;l++){var g=u[a[l].target().id()],y=u[a[l].source().id()],m=n(a[l]);c[g][y]>m&&(c[g][y]=m,f[g][y]=y,p[g][y]=a[l])}for(var b=0;b<s;b++)for(var l=0;l<s;l++)for(var h=0;h<s;h++)c[l][b]+c[b][h]<c[l][h]&&(c[l][h]=c[l][b]+c[b][h],f[l][h]=f[l][b]);for(var x=[],l=0;l<s;l++)x.push(o[l].id());return{distance:function(e,n){if(r.string(e))var i=t.filter(e)[0].id();else var i=e.id();if(r.string(n))var a=t.filter(n)[0].id();else var a=n.id();return c[u[i]][u[a]]},path:function(e,n){if(r.string(e))var i=t.filter(e)[0].id();else var i=e.id();if(r.string(n))var a=t.filter(n)[0].id();else var a=n.id();var o=function(e,n,r,i,a){if(e===n)return t.getElementById(i[e]);if(void 0!==r[e][n]){for(var o=[t.getElementById(i[e])],s=e;e!==n;){s=e,e=r[e][n];var u=a[s][e];o.push(u),o.push(t.getElementById(i[e]))}return o}}(u[i],u[a],f,x,p);return t.collection(o)}}}};e.exports=i},function(e,t,n){"use strict";var r=n(1),i={};[n(28),n(31),n(35),n(25),n(32),n(26),n(34),n(36),n(30),n(29),n(27)].forEach(function(e){r.extend(i,e)}),e.exports=i},function(e,t,n){"use strict";var r=n(1),i={kargerStein:function(e){var t=this;e=e||{};var n=function(e,t,n){for(var r=n[e],i=r[1],a=r[2],o=t[i],s=t[a],u=n.filter(function(e){return!(t[e[1]]===o&&t[e[2]]===s||t[e[1]]===s&&t[e[2]]===o)}),l=0;l<u.length;l++){var c=u[l];c[1]===s?(u[l]=c.slice(0),u[l][1]=o):c[2]===s&&(u[l]=c.slice(0),u[l][2]=o)}for(var l=0;l<t.length;l++)t[l]===s&&(t[l]=o);return u},i=function e(t,r,i,a){if(i<=a)return r;var o=Math.floor(Math.random()*r.length);return e(t,n(o,t,r),i-1,a)},a=this._private.cy,o=this.edges().stdFilter(function(e){return!e.isLoop()}),s=this.nodes(),u=s.length,l=o.length,c=Math.ceil(Math.pow(Math.log(u)/Math.LN2,2)),d=Math.floor(u/Math.sqrt(2));if(u<2)return void r.error("At least 2 nodes are required for Karger-Stein algorithm");for(var h={},f=0;f<u;f++)h[s[f].id()]=f;for(var p=[],f=0;f<l;f++){var v=o[f];p.push([f,h[v.source().id()],h[v.target().id()]])}for(var g,y=1/0,m=[],f=0;f<u;f++)m.push(f);for(var b=0;b<=c;b++){var x=m.slice(0),w=i(x,p,u,d),_=x.slice(0),E=i(x,w,d,2),k=i(_,w,d,2);E.length<=k.length&&E.length<y?(y=E.length,g=[E,x]):k.length<=E.length&&k.length<y&&(y=k.length,g=[k,_])}for(var C=g[0].map(function(e){return o[e[0]]}),P=[],T=[],S=g[1][0],f=0;f<g[1].length;f++)g[1][f]===S?P.push(s[f]):T.push(s[f]);return{cut:t.spawn(a,C),partition1:t.spawn(P),partition2:t.spawn(T)}}};e.exports=i},function(e,t,n){"use strict";var r=n(0),i={kruskal:function(e){function t(e){for(var t=0;t<a.length;t++){var n=a[t];if(n.anySame(e))return{eles:n,index:t}}}var n=this.cy();e=r.fn(e)?e:function(){return 1};for(var i=n.collection(n,[]),a=[],o=this.nodes(),s=0;s<o.length;s++)a.push(o[s].collection());for(var u=this.edges(),l=u.toArray().sort(function(t,n){
return e(t)-e(n)}),s=0;s<l.length;s++){var c=l[s],d=c.source()[0],h=c.target()[0],f=t(d),p=t(h);f.index!==p.index&&(i=i.add(c),a[f.index]=f.eles.add(p.eles),a.splice(p.index,1))}return o.add(i)}};e.exports=i},function(e,t,n){"use strict";var r=n(0),i={pageRank:function(e){if(e=e||{},null!=e&&null!=e.dampingFactor)var t=e.dampingFactor;else var t=.8;if(null!=e&&null!=e.precision)var n=e.precision;else var n=1e-6;if(null!=e&&null!=e.iterations)var i=e.iterations;else var i=200;if(null!=e&&null!=e.weight&&r.fn(e.weight))var a=e.weight;else var a=function(e){return 1};for(var o=this._private.cy,s=this.edges().stdFilter(function(e){return!e.isLoop()}),u=this.nodes(),l=u.length,c=s.length,d={},h=0;h<l;h++)d[u[h].id()]=h;for(var f=[],p=[],v=(1-t)/l,h=0;h<l;h++){for(var g=[],y=0;y<l;y++)g.push(0);f.push(g),p.push(0)}for(var h=0;h<c;h++){var m=s[h],b=d[m.source().id()],x=d[m.target().id()],w=a(m);f[x][b]+=w,p[b]+=w}for(var _=1/l+v,y=0;y<l;y++)if(0===p[y])for(var h=0;h<l;h++)f[h][y]=_;else for(var h=0;h<l;h++)f[h][y]=f[h][y]/p[y]+v;for(var E,k=[],C=[],h=0;h<l;h++)k.push(1),C.push(0);for(var P=0;P<i;P++){for(var T=C.slice(0),h=0;h<l;h++)for(var y=0;y<l;y++)T[h]+=f[h][y]*k[y];!function(e){for(var t=e.length,n=0,r=0;r<t;r++)n+=e[r];for(var r=0;r<t;r++)e[r]=e[r]/n}(T),E=k,k=T;for(var S=0,h=0;h<l;h++)S+=Math.pow(E[h]-k[h],2);if(S<n)break}return{rank:function(e){if(r.string(e))var t=o.filter(e)[0].id();else var t=e.id();return k[d[t]]}}}};e.exports=i},function(e,t,n){"use strict";var r=n(3),i={animate:r.animate(),animation:r.animation(),animated:r.animated(),clearQueue:r.clearQueue(),delay:r.delay(),delayAnimation:r.delayAnimation(),stop:r.stop()};e.exports=i},function(e,t,n){"use strict";var r=n(9),i={classes:function(e){e=(e||"").match(/\S+/g)||[];for(var t=this,n=[],i=new r(e),a=0;a<t.length;a++)!function(e){var a=t[e],o=a._private,s=o.classes,u=!1;i.forEach(function(e){s.has(e)||(u=!0)}),u||s.forEach(function(e){i.has(e)||(u=!0)}),u&&(o.classes=new r(i),n.push(a))}(a);return n.length>0&&this.spawn(n).updateStyle().emit("class"),t},addClass:function(e){return this.toggleClass(e,!0)},hasClass:function(e){var t=this[0];return null!=t&&t._private.classes.has(e)},toggleClass:function(e,t){for(var n=e.match(/\S+/g)||[],r=this,i=[],a=0,o=r.length;a<o;a++)for(var s=r[a],u=!1,l=0;l<n.length;l++){var c=n[l],d=s._private.classes,h=d.has(c),f=t||void 0===t&&!h;f?(d.add(c),h||u||(i.push(s),u=!0)):(d.delete(c),h&&!u&&(i.push(s),u=!0))}return i.length>0&&this.spawn(i).updateStyle().emit("class"),r},removeClass:function(e){return this.toggleClass(e,!1)},flashClass:function(e,t){var n=this;if(null==t)t=250;else if(0===t)return n;return n.addClass(e),setTimeout(function(){n.removeClass(e)},t),n}};e.exports=i},function(e,t,n){"use strict";var r=(n(0),n(6)),i={allAre:function(e){var t=new r(e);return this.every(function(e){return t.matches(e)})},is:function(e){var t=new r(e);return this.some(function(e){return t.matches(e)})},some:function(e,t){for(var n=0;n<this.length;n++)if(t?e.apply(t,[this[n],n,this]):e(this[n],n,this))return!0;return!1},every:function(e,t){for(var n=0;n<this.length;n++)if(!(t?e.apply(t,[this[n],n,this]):e(this[n],n,this)))return!1;return!0},same:function(e){return e=this.cy().collection(e),this.length===e.length&&this.every(function(t){return e.hasElementWithId(t.id())})},anySame:function(e){return e=this.cy().collection(e),this.some(function(t){return e.hasElementWithId(t.id())})},allAreNeighbors:function(e){e=this.cy().collection(e);var t=this.neighborhood();return e.every(function(e){return t.hasElementWithId(e.id())})},contains:function(e){e=this.cy().collection(e);var t=this;return e.every(function(e){return t.hasElementWithId(e.id())})}};i.allAreNeighbours=i.allAreNeighbors,i.has=i.contains,e.exports=i},function(e,t,n){"use strict";function r(e,t,n,r){for(var i=[],a=new s,o=e.cy(),u=o.hasCompoundNodes(),l=0;l<e.length;l++){var c=e[l];n?i.push(c):u&&r(i,a,c)}for(;i.length>0;){var d=i.shift();t(d),a.add(d.id()),u&&r(i,a,d)}return e}function i(e,t,n){if(n.isParent())for(var r=n._private.children,i=0;i<r.length;i++){var a=r[i];t.has(a.id())||e.push(a)}}function a(e,t,n){if(n.isChild()){var r=n._private.parent;t.has(r.id())||e.push(r)}}function o(e,t,n){a(e,t,n),i(e,t,n)}var s=n(9),u={parent:function(e){var t=[];if(1===this.length){var n=this[0]._private.parent;if(n)return n}for(var r=0;r<this.length;r++){var i=this[r],a=i._private.parent;a&&t.push(a)}return this.spawn(t,{unique:!0}).filter(e)},parents:function(e){for(var t=[],n=this.parent();n.nonempty();){for(var r=0;r<n.length;r++){var i=n[r];t.push(i)}n=n.parent()}return this.spawn(t,{unique:!0}).filter(e)},commonAncestors:function(e){for(var t=void 0,n=0;n<this.length;n++){var r=this[n],i=r.parents();t=t||i,t=t.intersect(i)}return t.filter(e)},orphans:function(e){return this.stdFilter(function(e){return e.isOrphan()}).filter(e)},nonorphans:function(e){return this.stdFilter(function(e){return e.isChild()}).filter(e)},children:function(e){for(var t=[],n=0;n<this.length;n++){var r=this[n];t=t.concat(r._private.children)}return this.spawn(t,{unique:!0}).filter(e)},siblings:function(e){return this.parent().children().not(this).filter(e)},isParent:function(){var e=this[0];if(e)return e.isNode()&&0!==e._private.children.length},isChildless:function(){var e=this[0];if(e)return e.isNode()&&0===e._private.children.length},isChild:function(){var e=this[0];if(e)return e.isNode()&&null!=e._private.parent},isOrphan:function(){var e=this[0];if(e)return e.isNode()&&null==e._private.parent},descendants:function(e){function t(e){for(var r=0;r<e.length;r++){var i=e[r];n.push(i),i.children().nonempty()&&t(i.children())}}var n=[];return t(this.children()),this.spawn(n,{unique:!0}).filter(e)}};u.forEachDown=function(e){return r(this,e,!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i)},u.forEachUp=function(e){return r(this,e,!(arguments.length>1&&void 0!==arguments[1])||arguments[1],a)},u.forEachUpAndDown=function(e){return r(this,e,!(arguments.length>1&&void 0!==arguments[1])||arguments[1],o)},u.ancestors=u.parents,e.exports=u},function(e,t,n){"use strict";var r=n(3),i=void 0,a=void 0;i=a={data:r.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),removeData:r.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),scratch:r.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:r.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),rscratch:r.data({field:"rscratch",allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!0}),removeRscratch:r.removeData({field:"rscratch",triggerEvent:!1}),id:function(){var e=this[0];if(e)return e._private.data.id}},i.attr=i.data,i.removeAttr=i.removeData,e.exports=a},function(e,t,n){"use strict";function r(e){return function(t){var n=this;if(void 0===t&&(t=!0),0!==n.length&&n.isNode()&&!n.removed()){for(var r=0,i=n[0],a=i._private.edges,o=0;o<a.length;o++){var s=a[o];!t&&s.isLoop()||(r+=e(i,s))}return r}}}function i(e,t){return function(n){for(var r=void 0,i=this.nodes(),a=0;a<i.length;a++){var o=i[a],s=o[e](n);void 0===s||void 0!==r&&!t(s,r)||(r=s)}return r}}var a=n(1),o={};a.extend(o,{degree:r(function(e,t){return t.source().same(t.target())?2:1}),indegree:r(function(e,t){return t.target().same(e)?1:0}),outdegree:r(function(e,t){return t.source().same(e)?1:0})}),a.extend(o,{minDegree:i("degree",function(e,t){return e<t}),maxDegree:i("degree",function(e,t){return e>t}),minIndegree:i("indegree",function(e,t){return e<t}),maxIndegree:i("indegree",function(e,t){return e>t}),minOutdegree:i("outdegree",function(e,t){return e<t}),maxOutdegree:i("outdegree",function(e,t){return e>t})}),a.extend(o,{totalDegree:function(e){for(var t=0,n=this.nodes(),r=0;r<n.length;r++)t+=n[r].degree(e);return t}}),e.exports=o},function(e,t,n){"use strict";function r(e){return{includeNodes:a.default(e.includeNodes,b.includeNodes),includeEdges:a.default(e.includeEdges,b.includeEdges),includeLabels:a.default(e.includeLabels,b.includeLabels),includeOverlays:a.default(e.includeOverlays,b.includeOverlays),useCache:a.default(e.useCache,b.useCache)}}var i=n(0),a=n(1),o=n(2),s=void 0,u=void 0;s=u={},u.renderedBoundingBox=function(e){var t=this.boundingBox(e),n=this.cy(),r=n.zoom(),i=n.pan(),a=t.x1*r+i.x,o=t.x2*r+i.x,s=t.y1*r+i.y,u=t.y2*r+i.y;return{x1:a,x2:o,y1:s,y2:u,w:o-a,h:u-s}},u.dirtyCompoundBoundsCache=function(){var e=this.cy();return e.styleEnabled()&&e.hasCompoundNodes()?(this.forEachUp(function(e){e._private.compoundBoundsClean=!1,e.isParent()&&e.emit("bounds")}),this):this},u.updateCompoundBounds=function(){var e=this.cy();if(!e.styleEnabled()||!e.hasCompoundNodes())return this;if(e.batching())return this;for(var t=[],n=0;n<this.length;n++){var r=this[n],i=r._private;i.compoundBoundsClean||(!function(e){function n(e,t,n){var r=0,i=0,a=t+n;return e>0&&a>0&&(r=t/a*e,i=n/a*e),{biasDiff:r,biasComplementDiff:i}}if(e.isParent()){var r=e._private,i=e.children(),a="include"===e.pstyle("compound-sizing-wrt-labels").value,o={width:{val:e.pstyle("min-width").pfValue,left:e.pstyle("min-width-bias-left"),right:e.pstyle("min-width-bias-right")},height:{val:e.pstyle("min-height").pfValue,top:e.pstyle("min-height-bias-top"),bottom:e.pstyle("min-height-bias-bottom")}},s=i.boundingBox({includeLabels:a,includeOverlays:!1,useCache:!1}),u=r.position;0!==s.w&&0!==s.h||(s={w:e.pstyle("width").pfValue,h:e.pstyle("height").pfValue},s.x1=u.x-s.w/2,s.x2=u.x+s.w/2,s.y1=u.y-s.h/2,s.y2=u.y+s.h/2);var l=o.width.left.value;"px"===o.width.left.units&&o.width.val>0&&(l=100*l/o.width.val);var c=o.width.right.value;"px"===o.width.right.units&&o.width.val>0&&(c=100*c/o.width.val);var d=o.height.top.value;"px"===o.height.top.units&&o.height.val>0&&(d=100*d/o.height.val);var h=o.height.bottom.value;"px"===o.height.bottom.units&&o.height.val>0&&(h=100*h/o.height.val);var f=n(o.width.val-s.w,l,c),p=f.biasDiff,v=f.biasComplementDiff,g=n(o.height.val-s.h,d,h),y=g.biasDiff,m=g.biasComplementDiff;r.autoPadding=function(e,t,n,r){if("%"!==n.units)return"px"===n.units?n.pfValue:0;switch(r){case"width":return e>0?n.pfValue*e:0;case"height":return t>0?n.pfValue*t:0;case"average":return e>0&&t>0?n.pfValue*(e+t)/2:0;case"min":return e>0&&t>0?e>t?n.pfValue*t:n.pfValue*e:0;case"max":return e>0&&t>0?e>t?n.pfValue*e:n.pfValue*t:0;default:return 0}}(s.w,s.h,e.pstyle("padding"),e.pstyle("padding-relative-to").value),r.autoWidth=Math.max(s.w,o.width.val),u.x=(-p+s.x1+s.x2+v)/2,r.autoHeight=Math.max(s.h,o.height.val),u.y=(-y+s.y1+s.y2+m)/2,t.push(e)}}(r),e._private.batchingStyle||(i.compoundBoundsClean=!0))}return this};var l=function(e){return e===1/0||e===-1/0?0:e},c=function(e,t,n,r,i){r-t!=0&&i-n!=0&&null!=t&&null!=n&&null!=r&&null!=i&&(e.x1=t<e.x1?t:e.x1,e.x2=r>e.x2?r:e.x2,e.y1=n<e.y1?n:e.y1,e.y2=i>e.y2?i:e.y2)},d=function(e,t){return c(e,t.x1,t.y1,t.x2,t.y2)},h=function(e,t,n){return a.getPrefixedProperty(e,t,n)},f=function(e,t,n){if(!t.cy().headless()){var r=t._private,i=r.rstyle,a=i.arrowWidth/2,o=t.pstyle(n+"-arrow-shape").value,s=void 0,u=void 0;"none"!==o&&("source"===n?(s=i.srcX,u=i.srcY):"target"===n?(s=i.tgtX,u=i.tgtY):(s=i.midX,u=i.midY),c(e,s-a,u-a,s+a,u+a))}},p=function(e,t,n){if(!t.cy().headless()){var r=void 0;r=n?n+"-":"";var i=t._private,a=i.rstyle;if(t.pstyle(r+"label").strValue){var o=t.pstyle("text-halign"),s=t.pstyle("text-valign"),u=h(a,"labelWidth",n),l=h(a,"labelHeight",n),d=h(a,"labelX",n),f=h(a,"labelY",n),p=t.pstyle(r+"text-margin-x").pfValue,v=t.pstyle(r+"text-margin-y").pfValue,g=t.isEdge(),y=t.pstyle(r+"text-rotation"),m=t.pstyle("text-outline-width").pfValue,b=t.pstyle("text-border-width").pfValue,x=b/2,w=t.pstyle("text-background-padding").pfValue,_=l+2*w,E=u+2*w,k=E/2,C=_/2,P=void 0,T=void 0,S=void 0,D=void 0;if(g)P=d-k,T=d+k,S=f-C,D=f+C;else{switch(o.value){case"left":P=d-E,T=d;break;case"center":P=d-k,T=d+k;break;case"right":P=d,T=d+E}switch(s.value){case"top":S=f-_,D=f;break;case"center":S=f-C,D=f+C;break;case"bottom":S=f,D=f+_}}var I=g&&"autorotate"===y.strValue,N=null!=y.pfValue&&0!==y.pfValue;if(I||N){var M=I?h(i.rstyle,"labelAngle",n):y.pfValue,L=Math.cos(M),B=Math.sin(M),O=function(e,t){return e-=d,t-=f,{x:e*L-t*B+d,y:e*B+t*L+f}},A=O(P,S),z=O(P,D),R=O(T,S),j=O(T,D);P=Math.min(A.x,z.x,R.x,j.x),T=Math.max(A.x,z.x,R.x,j.x),S=Math.min(A.y,z.y,R.y,j.y),D=Math.max(A.y,z.y,R.y,j.y)}P+=p-Math.max(m,x),T+=p+Math.max(m,x),S+=v-Math.max(m,x),D+=v+Math.max(m,x),c(e,P,S,T,D)}return e}},v=function(e,t){var n=e._private.cy,r=n.styleEnabled(),i=n.headless(),a={x1:1/0,y1:1/0,x2:-1/0,y2:-1/0},s=e._private,u=r?e.pstyle("display").value:"element",d=e.isNode(),h=e.isEdge(),v=void 0,g=void 0,y=void 0,m=void 0,b=void 0,x=void 0,w="none"!==u;if(w){var _=0;r&&t.includeOverlays&&0!==e.pstyle("overlay-opacity").value&&(_=e.pstyle("overlay-padding").value);var E=0,k=0;if(r&&(E=e.pstyle("width").pfValue,k=E/2),d&&t.includeNodes){var C=e.position();b=C.x,x=C.y;var P=e.outerWidth(),T=P/2,S=e.outerHeight(),D=S/2;v=b-T-_,g=b+T+_,y=x-D-_,m=x+D+_,c(a,v,y,g,m)}else if(h&&t.includeEdges){var I=s.rstyle||{};if(r&&!i&&(v=Math.min(I.srcX,I.midX,I.tgtX),g=Math.max(I.srcX,I.midX,I.tgtX),y=Math.min(I.srcY,I.midY,I.tgtY),m=Math.max(I.srcY,I.midY,I.tgtY),v-=k,g+=k,y-=k,m+=k,c(a,v,y,g,m)),r&&!i&&"haystack"===e.pstyle("curve-style").strValue){var N=I.haystackPts||[];if(v=N[0].x,y=N[0].y,g=N[1].x,m=N[1].y,v>g){var M=v;v=g,g=M}if(y>m){var L=y;y=m,m=L}c(a,v-k,y-k,g+k,m+k)}else{for(var B=I.bezierPts||I.linePts||[],O=0;O<B.length;O++){var A=B[O];v=A.x-k,g=A.x+k,y=A.y-k,m=A.y+k,c(a,v,y,g,m)}if(0===B.length){var z=e.source(),R=z.position(),j=e.target(),F=j.position();if(v=R.x,g=F.x,y=R.y,m=F.y,v>g){var V=v;v=g,g=V}if(y>m){var q=y;y=m,m=q}v-=k,g+=k,y-=k,m+=k,c(a,v,y,g,m)}}}if(r&&t.includeEdges&&h&&(f(a,e,"mid-source"),f(a,e,"mid-target"),f(a,e,"source"),f(a,e,"target")),r&&"yes"===e.pstyle("ghost").value){var Y=e.pstyle("ghost-offset-x").pfValue,X=e.pstyle("ghost-offset-y").pfValue;c(a,a.x1+Y,a.y1+X,a.x2+Y,a.y2+X)}r&&(v=a.x1,g=a.x2,y=a.y1,m=a.y2,c(a,v-_,y-_,g+_,m+_)),r&&t.includeLabels&&(p(a,e,null),h&&(p(a,e,"source"),p(a,e,"target")))}return a.x1=l(a.x1),a.y1=l(a.y1),a.x2=l(a.x2),a.y2=l(a.y2),a.w=l(a.x2-a.x1),a.h=l(a.y2-a.y1),a.w>0&&a.h>0&&w&&o.expandBoundingBox(a,1),a},g=function(e){return e?"t":"f"},y=function(e){var t="";return t+=g(e.incudeNodes),t+=g(e.includeEdges),t+=g(e.includeLabels),t+=g(e.includeOverlays)},m=function(e,t){var n=e._private,r=void 0,i=e.cy().headless(),a=t===b?x:y(t);return t.useCache&&!i&&n.bbCache&&n.bbCache[a]?r=n.bbCache[a]:(r=v(e,t),i||(n.bbCache=n.bbCache||{},n.bbCache[a]=r)),r},b={includeNodes:!0,includeEdges:!0,includeLabels:!0,includeOverlays:!0,useCache:!0},x=y(b);u.boundingBox=function(e){if(1===this.length&&this[0]._private.bbCache&&(void 0===e||void 0===e.useCache||!0===e.useCache))return e=void 0===e?b:r(e),m(this[0],e);var t={x1:1/0,y1:1/0,x2:-1/0,y2:-1/0};e=e||a.staticEmptyObject();var n=r(e),i=this,o=i.cy(),s=o.styleEnabled();s&&this.recalculateRenderedStyle(n.useCache),this.updateCompoundBounds();for(var u={},c=0;c<i.length;c++){var h=i[c];if(s&&h.isEdge()&&"bezier"===h.pstyle("curve-style").strValue&&!u[h.id()]){for(var f=h.parallelEdges(),p=0;p<f.length;p++)u[f[p].id()]=!0;f.recalculateRenderedStyle(n.useCache)}d(t,m(h,n))}return t.x1=l(t.x1),t.y1=l(t.y1),t.x2=l(t.x2),t.y2=l(t.y2),t.w=l(t.x2-t.x1),t.h=l(t.y2-t.y1),t},u.boundingBoxAt=function(e){var t=this.nodes();if(i.plainObject(e)){var n=e;e=function(){return n}}for(var r=0;r<t.length;r++){var a=t[r],o=a._private,s=o.position,u=e.call(a,a,r);o.bbAtOldPos={x:s.x,y:s.y},u&&(s.x=u.x,s.y=u.y)}this.emit("dirty"),t.dirtyCompoundBoundsCache().updateCompoundBounds();for(var l=this.boundingBox({useCache:!1}),c=0;c<t.length;c++){var d=t[c],h=d._private,f=d._private.position,p=h.bbAtOldPos;f.x=p.x,f.y=p.y}return t.dirtyCompoundBoundsCache(),this.emit("dirty"),l},s.boundingbox=s.boundingBox,s.renderedBoundingbox=s.renderedBoundingBox,e.exports=u},function(e,t,n){"use strict";var r=function(e,t){if(e.isEdge())return t(e.renderer())};e.exports={controlPoints:function(){var e=this;return r(this,function(t){return t.getControlPoints(e)})},segmentPoints:function(){var e=this;return r(this,function(t){return t.getSegmentPoints(e)})},sourceEndpoint:function(){var e=this;return r(this,function(t){return t.getSourceEndpoint(e)})},targetEndpoint:function(){var e=this;return r(this,function(t){return t.getTargetEndpoint(e)})},midpoint:function(){var e=this;return r(this,function(t){return t.getEdgeMidpoint(e)})}}},function(e,t,n){"use strict";var r=n(1),i=n(46),a=n(43),o=n(47),s=n(44);e.exports=r.assign({},i,a,o,s)},function(e,t,n){"use strict";var r=n(3),i=n(0),a=n(2),o=void 0,s=void 0,u=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(r.isParent()&&!r.locked()){var i=r._private.position,a={x:t.x-i.x,y:t.y-i.y};e.children().shift(a)}}};o=s={position:r.data({field:"position",bindingEvent:"position",allowBinding:!0,allowSetting:!0,settingEvent:"position",settingTriggersEvent:!0,triggerFnName:"emitAndNotify",allowGetting:!0,validKeys:["x","y"],beforeGet:function(e){e.updateCompoundBounds()},beforeSet:u,onSet:function(e){e.dirtyCompoundBoundsCache()},canSet:function(e){return!e.locked()}}),silentPosition:r.data({field:"position",bindingEvent:"position",allowBinding:!1,allowSetting:!0,settingEvent:"position",settingTriggersEvent:!1,triggerFnName:"trigger",allowGetting:!1,validKeys:["x","y"],beforeSet:u,onSet:function(e){e.dirtyCompoundBoundsCache()},canSet:function(e){return!e.locked()}}),positions:function(e,t){if(i.plainObject(e))t?this.silentPosition(e):this.position(e);else if(i.fn(e)){var n=e,r=this.cy();r.startBatch();for(var a=0;a<this.length;a++){var o=this[a],s=void 0;(s=n(o,a))&&(t?o.silentPosition(s):o.position(s))}r.endBatch()}return this},silentPositions:function(e){return this.positions(e,!0)},shift:function(e,t){var n=void 0;if(i.plainObject(e)?n=e:i.string(e)&&i.number(t)&&(n={x:0,y:0},n[e]=t),null!=n)for(var r=0;r<this.length;r++){var a=this[r],o=a.position();a.position({x:o.x+n.x,y:o.y+n.y})}return this},renderedPosition:function(e,t){var n=this[0],r=this.cy(),o=r.zoom(),s=r.pan(),u=i.plainObject(e)?e:void 0,l=void 0!==u||void 0!==t&&i.string(e);if(n&&n.isNode()){if(!l){var c=n.position();return u=a.modelToRenderedPosition(c,o,s),void 0===e?u:u[e]}for(var d=0;d<this.length;d++){var h=this[d];void 0!==t?h.position(e,(t-s[e])/o):void 0!==u&&h.position(a.renderedToModelPosition(u,o,s))}}else if(!l)return;return this},relativePosition:function(e,t){var n=this[0],r=this.cy(),a=i.plainObject(e)?e:void 0,o=void 0!==a||void 0!==t&&i.string(e),s=r.hasCompoundNodes();if(n&&n.isNode()){if(!o){var u=n.position(),l=s?n.parent():null,c=l&&l.length>0,d=c;c&&(l=l[0]);var h=d?l.position():{x:0,y:0};return a={x:u.x-h.x,y:u.y-h.y},void 0===e?a:a[e]}for(var f=0;f<this.length;f++){var p=this[f],v=s?p.parent():null,g=v&&v.length>0,y=g;g&&(v=v[0]);var m=y?v.position():{x:0,y:0};void 0!==t?p.position(e,t+m[e]):void 0!==a&&p.position({x:a.x+m.x,y:a.y+m.y})}}else if(!o)return;return this}},o.modelPosition=o.point=o.position,o.modelPositions=o.points=o.positions,o.renderedPoint=o.renderedPosition,o.relativePoint=o.relativePosition,e.exports=s},function(e,t,n){"use strict";var r=n(1),i=void 0,a=void 0;i=a={};var o=function(e){e.uppercaseName=r.capitalize(e.name),e.autoName="auto"+e.uppercaseName,e.labelName="label"+e.uppercaseName,e.outerName="outer"+e.uppercaseName,e.uppercaseOuterName=r.capitalize(e.outerName),i[e.name]=function(){var t=this[0],n=t._private,r=n.cy,i=r._private.styleEnabled;if(t){if(!i)return 1;if(t.isParent())return t.updateCompoundBounds(),n[e.autoName]||0;var a=t.pstyle(e.name);switch(a.strValue){case"label":return t.recalculateRenderedStyle(),n.rstyle[e.labelName]||0;default:return a.pfValue}}},i["outer"+e.uppercaseName]=function(){var t=this[0],n=t._private,r=n.cy,i=r._private.styleEnabled;if(t)return i?t[e.name]()+t.pstyle("border-width").pfValue+2*t.padding():1},i["rendered"+e.uppercaseName]=function(){var t=this[0];if(t)return t[e.name]()*this.cy().zoom()},i["rendered"+e.uppercaseOuterName]=function(){var t=this[0];if(t)return t[e.outerName]()*this.cy().zoom()}};o({name:"width"}),o({name:"height"}),a.padding=function(){var e=this[0],t=e._private;return e.isParent()?(e.updateCompoundBounds(),void 0!==t.autoPadding?t.autoPadding:e.pstyle("padding").pfValue):e.pstyle("padding").pfValue},e.exports=a},function(e,t,n){"use strict";var r=n(10),i=n(3),a=n(0),o=n(1),s=n(6),u={qualifierCompare:function(e,t){return null==e||null==t?null==e&&null==t:e.sameText(t)},eventMatches:function(e,t,n){var r=t.qualifier;return null==r||e!==n.target&&a.element(n.target)&&r.matches(n.target)},eventFields:function(e){return{cy:e.cy(),target:e}},callbackContext:function(e,t,n){return null!=t.qualifier?n.target:e},beforeEmit:function(e,t){t.conf&&t.conf.once&&t.conf.onceCollection.removeListener(t.event,t.qualifier,t.callback)},bubble:function(){return!0},parent:function(e){return e.isChild()?e.parent():e.cy()}},l=function(e){return a.string(e)?new s(e):e},c={createEmitter:function(){for(var e=0;e<this.length;e++){var t=this[e],n=t._private;n.emitter||(n.emitter=new r(o.assign({context:t},u)))}return this},emitter:function(){return this._private.emitter},on:function(e,t,n){for(var r=0;r<this.length;r++)this[r].emitter().on(e,l(t),n);return this},removeListener:function(e,t,n){for(var r=0;r<this.length;r++)this[r].emitter().removeListener(e,l(t),n);return this},one:function(e,t,n){for(var r=0;r<this.length;r++)this[r].emitter().one(e,l(t),n);return this},once:function(e,t,n){for(var r=0;r<this.length;r++)this[r].emitter().on(e,l(t),n,{once:!0,onceCollection:this})},emit:function(e,t){for(var n=0;n<this.length;n++)this[n].emitter().emit(e,t);return this},emitAndNotify:function(e,t){if(0!==this.length)return this.cy().notify({type:e,eles:this}),this.emit(e,t),this}};i.eventAliasesOn(c),e.exports=c},function(e,t,n){"use strict";var r=n(0),i=n(6),a={nodes:function(e){return this.filter(function(e){return e.isNode()}).filter(e)},edges:function(e){return this.filter(function(e){return e.isEdge()}).filter(e)},filter:function(e,t){if(void 0===e)return this;if(r.string(e)||r.elementOrCollection(e))return new i(e).filter(this);if(r.fn(e)){for(var n=this.spawn(),a=this,o=0;o<a.length;o++){var s=a[o];(t?e.apply(t,[s,o,a]):e(s,o,a))&&n.merge(s)}return n}return this.spawn()},not:function(e){if(e){r.string(e)&&(e=this.filter(e));for(var t=[],n=e._private.map,i=0;i<this.length;i++){var a=this[i];n.has(a.id())||t.push(a)}return this.spawn(t)}return this},absoluteComplement:function(){return this.cy().mutableElements().not(this)},intersect:function(e){if(r.string(e)){var t=e;return this.filter(t)}for(var n=[],i=this,a=e,o=this.length<e.length,s=o?a._private.map:i._private.map,u=o?i:a,l=0;l<u.length;l++){var c=u[l]._private.data.id,d=s.get(c);d&&n.push(d.ele)}return this.spawn(n)},xor:function(e){var t=this._private.cy;r.string(e)&&(e=t.$(e));var n=[],i=this,a=e,o=function(e,t){for(var r=0;r<e.length;r++){var i=e[r],a=i._private.data.id;t.hasElementWithId(a)||n.push(i)}};return o(i,a),o(a,i),this.spawn(n)},diff:function(e){var t=this._private.cy;r.string(e)&&(e=t.$(e));var n=[],i=[],a=[],o=this,s=e,u=function(e,t,n){for(var r=0;r<e.length;r++){var i=e[r],o=i._private.data.id;t.hasElementWithId(o)?a.push(i):n.push(i)}};return u(o,s,n),u(s,o,i),{left:this.spawn(n,{unique:!0}),right:this.spawn(i,{unique:!0}),both:this.spawn(a,{unique:!0})}},add:function(e){var t=this._private.cy;if(!e)return this;if(r.string(e)){var n=e;e=t.mutableElements().filter(n)}for(var i=[],a=0;a<this.length;a++)i.push(this[a]);for(var o=this._private.map,s=0;s<e.length;s++)!o.has(e[s].id())&&i.push(e[s]);return this.spawn(i)},merge:function(e){var t=this._private,n=t.cy;if(!e)return this;if(e&&r.string(e)){var i=e;e=n.mutableElements().filter(i)}for(var a=t.map,o=0;o<e.length;o++){var s=e[o],u=s._private.data.id;if(a.has(u)){var l=a.get(u).index;this[l]=s,a.set(u,{ele:s,index:l})}else{var c=this.length++;this[c]=s,a.set(u,{ele:s,index:c})}}return this},unmergeOne:function(e){e=e[0];var t=this._private,n=e._private.data.id,r=t.map,i=r.get(n);if(!i)return this;var a=i.index;this[a]=void 0,r.delete(n);var o=a===this.length-1;if(this.length>1&&!o){var s=this.length-1,u=this[s],l=u._private.data.id;this[s]=void 0,this[a]=u,r.set(l,{ele:u,index:a})}return this.length--,this},unmerge:function(e){var t=this._private.cy;if(!e)return this;if(e&&r.string(e)){var n=e;e=t.mutableElements().filter(n)}for(var i=0;i<e.length;i++)this.unmergeOne(e[i]);return this},map:function(e,t){for(var n=[],r=this,i=0;i<r.length;i++){var a=r[i],o=t?e.apply(t,[a,i,r]):e(a,i,r);n.push(o)}return n},reduce:function(e,t){for(var n=t,r=this,i=0;i<r.length;i++)n=e(n,r[i],i,r);return n},max:function(e,t){for(var n=-1/0,r=void 0,i=this,a=0;a<i.length;a++){var o=i[a],s=t?e.apply(t,[o,a,i]):e(o,a,i);s>n&&(n=s,r=o)}return{value:n,ele:r}},min:function(e,t){for(var n=1/0,r=void 0,i=this,a=0;a<i.length;a++){var o=i[a],s=t?e.apply(t,[o,a,i]):e(o,a,i);s<n&&(n=s,r=o)}return{value:n,ele:r}}},o=a;o.u=o["|"]=o["+"]=o.union=o.or=o.add,o["\\"]=o["!"]=o["-"]=o.difference=o.relativeComplement=o.subtract=o.not,o.n=o["&"]=o["."]=o.and=o.intersection=o.intersect,o["^"]=o["(+)"]=o["(-)"]=o.symmetricDifference=o.symdiff=o.xor,o.fnFilter=o.filterFn=o.stdFilter=o.filter,o.complement=o.abscomp=o.absoluteComplement,e.exports=a},function(e,t,n){"use strict";var r={isNode:function(){return"nodes"===this.group()},isEdge:function(){return"edges"===this.group()},isLoop:function(){return this.isEdge()&&this.source().id()===this.target().id()},isSimple:function(){return this.isEdge()&&this.source().id()!==this.target().id()},group:function(){var e=this[0];if(e)return e._private.group}};e.exports=r},function(e,t,n){"use strict";var r=n(0),i=n(14),a=n(1),o={forEach:function(e,t){if(r.fn(e))for(var n=0;n<this.length;n++){var i=this[n],a=t?e.apply(t,[i,n,this]):e(i,n,this);if(!1===a)break}return this},toArray:function(){for(var e=[],t=0;t<this.length;t++)e.push(this[t]);return e},slice:function(e,t){var n=[],r=this.length;null==t&&(t=r),null==e&&(e=0),e<0&&(e=r+e),t<0&&(t=r+t);for(var i=e;i>=0&&i<t&&i<r;i++)n.push(this[i]);return this.spawn(n)},size:function(){return this.length},eq:function(e){return this[e]||this.spawn()},first:function(){return this[0]||this.spawn()},last:function(){return this[this.length-1]||this.spawn()},empty:function(){return 0===this.length},nonempty:function(){return!this.empty()},sort:function(e){if(!r.fn(e))return this;var t=this.toArray().sort(e);return this.spawn(t)},sortByZIndex:function(){return this.sort(i)},zDepth:function(){var e=this[0];if(e){var t=e._private;if("nodes"===t.group){var n=t.data.parent?e.parents().size():0;return e.isParent()?n:a.MAX_INT-1}var r=t.source,i=t.target,o=r.zDepth(),s=i.zDepth();return Math.max(o,s,0)}}};o.each=o.forEach,e.exports=o},function(e,t,n){"use strict";var r=n(0),i=n(1),a=n(5),o=n(2),s={layoutDimensions:function(e){if(e=i.assign({nodeDimensionsIncludeLabels:!0},e),e.nodeDimensionsIncludeLabels){var t=this.boundingBox();return{w:t.w,h:t.h}}return{w:this.outerWidth(),h:this.outerHeight()}},layoutPositions:function(e,t,n){var s=this.nodes(),u=this.cy(),l=t.eles,c=function(e,t){return e.id()+"$"+t},d=i.memoize(n,c);e.emit({type:"layoutstart",layout:e}),e.animations=[];var h=function(e,t,n){var r={x:t.x1+t.w/2,y:t.y1+t.h/2},i={x:(n.x-r.x)*e,y:(n.y-r.y)*e};return{x:r.x+i.x,y:r.y+i.y}},f=t.spacingFactor&&1!==t.spacingFactor,p=function(){if(!f)return null;for(var e=o.makeBoundingBox(),t=0;t<s.length;t++){var n=s[t],r=d(n,t);o.expandBoundingBoxByPoint(e,r.x,r.y)}return e}(),v=i.memoize(function(e,n){var i=d(e,n),a=e.position();if(r.number(a.x)&&r.number(a.y)||e.silentPosition({x:0,y:0}),f){var o=Math.abs(t.spacingFactor);i=h(o,p,i)}return null!=t.transform&&(i=t.transform(e,i)),i},c);if(t.animate){for(var g=0;g<s.length;g++){var y=s[g],m=v(y,g);if(null==t.animateFilter||t.animateFilter(y,g)){var b=y.animation({position:m,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(b),b.play()}else y.position(m)}if(t.fit){var x=u.animation({fit:{boundingBox:l.boundingBoxAt(v),padding:t.padding},duration:t.animationDuration,easing:t.animationEasing});e.animations.push(x),x.play()}else if(void 0!==t.zoom&&void 0!==t.pan){var w=u.animation({zoom:t.zoom,pan:t.pan,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(w),w.play()}e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),a.all(e.animations.map(function(e){return e.promise()})).then(function(){e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e})})}else s.positions(v),t.fit&&u.fit(t.eles,t.padding),null!=t.zoom&&u.zoom(t.zoom),t.pan&&u.pan(t.pan),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e});return this},layout:function(e){return this.cy().makeLayout(i.extend({},e,{eles:this}))}};s.createLayout=s.makeLayout=s.layout,e.exports=s},function(e,t,n){"use strict";function r(e,t,n){var r,i=n._private,a=i.styleCache=i.styleCache||{};return null!=(r=a[e])?r:r=a[e]=t(n)}function i(e,t){return function(n){return r(e,t,n)}}function a(e,t){var n=function(e){return t.call(e)};return function(){var t=this[0];if(t)return r(e,n,t)}}function o(e,t){var n=e._private,r=n.data.parent?e.parents():null;if(r)for(var i=0;i<r.length;i++){var a=r[i];if(!t(a))return!1}return!0}function s(e){var t=e.ok,n=e.edgeOkViaNode||e.ok,r=e.parentOk||e.ok;return function(){var e=this.cy();if(!e.styleEnabled())return!0;var i=this[0],a=e.hasCompoundNodes();if(i){var s=i._private;if(!t(i))return!1;if(i.isNode())return!a||o(i,r);var u=s.source,l=s.target;return n(u)&&(!a||o(u,n))&&(u===l||n(l)&&(!a||o(l,n)))}}}var u=n(0),l={recalculateRenderedStyle:function(e){var t=this.cy(),n=t.renderer(),r=t.styleEnabled();return n&&r&&n.recalculateRenderedStyle(this,e),this},dirtyStyleCache:function(){var e=this.cy(),t=function(e){return e._private.styleCache={}};if(e.hasCompoundNodes()){var n=void 0;n=this.spawnSelf().merge(this.descendants()).merge(this.parents()),n.merge(n.connectedEdges()),n.forEach(t)}else this.forEach(function(e){t(e),e.connectedEdges().forEach(t)});return this},updateStyle:function(e){var t=this._private.cy;if(!t.styleEnabled())return this;if(t._private.batchingStyle)return t._private.batchStyleEles.merge(this),this;var n=t.hasCompoundNodes(),r=t.style(),i=this;e=!(!e&&void 0!==e),n&&(i=this.spawnSelf().merge(this.descendants()).merge(this.parents()));var a=r.apply(i);return a.dirtyStyleCache(),a.dirtyCompoundBoundsCache(),e?a.emitAndNotify("style"):a.emit("style"),this},updateMappers:function(e){var t=this._private.cy,n=t.style();if(e=!(!e&&void 0!==e),!t.styleEnabled())return this;var r=n.updateMappers(this);return r.dirtyStyleCache(),r.dirtyCompoundBoundsCache(),e?r.emitAndNotify("style"):r.emit("style"),this},parsedStyle:function(e){var t=this[0],n=t.cy();if(n.styleEnabled())return t?t._private.style[e]||n.style().getDefaultProperty(e):void 0},numericStyle:function(e){var t=this[0];if(t.cy().styleEnabled()&&t){var n=t.pstyle(e);return void 0!==n.pfValue?n.pfValue:n.value}},numericStyleUnits:function(e){var t=this[0];if(t.cy().styleEnabled())return t?t.pstyle(e).units:void 0},renderedStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var n=this[0];return n?t.style().getRenderedStyle(n,e):void 0},style:function(e,t){var n=this.cy();if(!n.styleEnabled())return this;var r=n.style();if(u.plainObject(e)){var i=e;r.applyBypass(this,i,!1),this.dirtyStyleCache(),this.dirtyCompoundBoundsCache(),this.emitAndNotify("style")}else if(u.string(e)){if(void 0===t){var a=this[0];
return a?r.getStylePropertyValue(a,e):void 0}r.applyBypass(this,e,t,!1),this.dirtyStyleCache(),this.dirtyCompoundBoundsCache(),this.emitAndNotify("style")}else if(void 0===e){var o=this[0];return o?r.getRawStyle(o):void 0}return this},removeStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var n=t.style(),r=this;if(void 0===e)for(var i=0;i<r.length;i++){var a=r[i];n.removeAllBypasses(a,!1)}else{e=e.split(/\s+/);for(var o=0;o<r.length;o++){var s=r[o];n.removeBypasses(s,e,!1)}}return this.dirtyStyleCache(),this.dirtyCompoundBoundsCache(),this.emitAndNotify("style"),this},show:function(){return this.css("display","element"),this},hide:function(){return this.css("display","none"),this},effectiveOpacity:function(){var e=this.cy();if(!e.styleEnabled())return 1;var t=e.hasCompoundNodes(),n=this[0];if(n){var r=n._private,i=n.pstyle("opacity").value;if(!t)return i;var a=r.data.parent?n.parents():null;if(a)for(var o=0;o<a.length;o++){var s=a[o],u=s.pstyle("opacity").value;i*=u}return i}},transparent:function(){if(!this.cy().styleEnabled())return!1;var e=this[0],t=e.cy().hasCompoundNodes();return e?t?0===e.effectiveOpacity():0===e.pstyle("opacity").value:void 0},backgrounding:function(){return!!this.cy().styleEnabled()&&!!this[0]._private.backgrounding}},c=i("eleTakesUpSpace",function(e){return"element"===e.pstyle("display").value&&0!==e.width()&&(!e.isNode()||0!==e.height())});l.takesUpSpace=a("takesUpSpace",s({ok:c}));var d=i("eleInteractive",function(e){return"yes"===e.pstyle("events").value&&"visible"===e.pstyle("visibility").value&&c(e)}),h=i("parentInteractive",function(e){return"visible"===e.pstyle("visibility").value&&c(e)});l.interactive=a("interactive",s({ok:d,parentOk:h,edgeOkViaNode:c})),l.noninteractive=function(){var e=this[0];if(e)return!e.interactive()};var f=i("eleVisible",function(e){return"visible"===e.pstyle("visibility").value&&0!==e.pstyle("opacity").pfValue&&c(e)}),p=c;l.visible=a("visible",s({ok:f,edgeOkViaNode:p})),l.hidden=function(){var e=this[0];if(e)return!e.visible()},l.bypass=l.css=l.style,l.renderedCss=l.renderedStyle,l.removeBypass=l.removeCss=l.removeStyle,l.pstyle=l.parsedStyle,e.exports=l},function(e,t,n){"use strict";function r(e){return function(){var t=arguments,n=[];if(2===t.length){var r=t[0],i=t[1];this.on(e.event,r,i)}else if(1===t.length){var a=t[0];this.on(e.event,a)}else if(0===t.length){for(var o=0;o<this.length;o++){var s=this[o],u=!e.ableField||s._private[e.ableField],l=s._private[e.field]!=e.value;if(e.overrideAble){var c=e.overrideAble(s);if(void 0!==c&&(u=c,!c))return this}u&&(s._private[e.field]=e.value,l&&n.push(s))}var d=this.spawn(n);d.updateStyle(),d.emit(e.event)}return this}}function i(e){a[e.field]=function(){var t=this[0];if(t){if(e.overrideField){var n=e.overrideField(t);if(void 0!==n)return n}return t._private[e.field]}},a[e.on]=r({event:e.on,field:e.field,ableField:e.ableField,overrideAble:e.overrideAble,value:!0}),a[e.off]=r({event:e.off,field:e.field,ableField:e.ableField,overrideAble:e.overrideAble,value:!1})}var a={};i({field:"locked",overrideField:function(e){return!!e.cy().autolock()||void 0},on:"lock",off:"unlock"}),i({field:"grabbable",overrideField:function(e){return!e.cy().autoungrabify()&&void 0},on:"grabify",off:"ungrabify"}),i({field:"selected",ableField:"selectable",overrideAble:function(e){return!e.cy().autounselectify()&&void 0},on:"select",off:"unselect"}),i({field:"selectable",overrideField:function(e){return!e.cy().autounselectify()&&void 0},on:"selectify",off:"unselectify"}),a.deselect=a.unselect,a.grabbed=function(){var e=this[0];if(e)return e._private.grabbed},i({field:"active",on:"activate",off:"unactivate"}),a.inactive=function(){var e=this[0];if(e)return!e._private.active},e.exports=a},function(e,t,n){"use strict";function r(e){return function(t){for(var n=[],r=0;r<this.length;r++){var i=this[r],a=i._private[e.attr];a&&n.push(a)}return this.spawn(n,{unique:!0}).filter(t)}}function i(e){return function(t){var n=[],r=this._private.cy,i=e||{};s.string(t)&&(t=r.$(t));for(var a=0;a<t.length;a++)for(var o=t[a]._private.edges,u=0;u<o.length;u++){var l=o[u],c=l._private.data,d=this.hasElementWithId(c.source)&&t.hasElementWithId(c.target),h=t.hasElementWithId(c.source)&&this.hasElementWithId(c.target),f=d||h;if(f){if(i.thisIsSrc||i.thisIsTgt){if(i.thisIsSrc&&!d)continue;if(i.thisIsTgt&&!h)continue}n.push(l)}}return this.spawn(n,{unique:!0})}}function a(e){var t={codirected:!1};return e=o.extend({},t,e),function(t){for(var n=[],r=this.edges(),i=e,a=0;a<r.length;a++)for(var o=r[a],s=o._private,u=s.source,l=u._private.data.id,c=s.data.target,d=u._private.edges,h=0;h<d.length;h++){var f=d[h],p=f._private.data,v=p.target,g=p.source,y=v===c&&g===l,m=l===v&&c===g;(i.codirected&&y||!i.codirected&&(y||m))&&n.push(f)}return this.spawn(n,{unique:!0}).filter(t)}}var o=n(1),s=n(0),u={},l=function(e,t){return function(n,r,i,a){var o=n,u=this,l=void 0;if(null==o?l="null":s.elementOrCollection(o)&&1===o.length&&(l="#"+o.id()),1===u.length&&l){var c=u[0]._private,d=c.traversalCache=c.traversalCache||{},h=d[t]=d[t]||{},f=h[l];return f||(h[l]=e.call(u,n,r,i,a))}return e.call(u,n,r,i,a)}},c=function(e){return function(t){for(var n=this,r=[],i=0;i<n.length;i++){var a=n[i];if(a.isNode()){for(var o=!1,s=a.connectedEdges(),u=0;u<s.length;u++){var l=s[u],c=l.source(),d=l.target();if(e.noIncomingEdges&&d===a&&c!==a||e.noOutgoingEdges&&c===a&&d!==a){o=!0;break}}o||r.push(a)}}return this.spawn(r,{unique:!0}).filter(t)}},d=function(e){return function(t){for(var n=this,r=[],i=0;i<n.length;i++){var a=n[i];if(a.isNode())for(var o=a.connectedEdges(),s=0;s<o.length;s++){var u=o[s],l=u.source(),c=u.target();e.outgoing&&l===a?(r.push(u),r.push(c)):e.incoming&&c===a&&(r.push(u),r.push(l))}}return this.spawn(r,{unique:!0}).filter(t)}},h=function(e){return function(t){for(var n=this,r=[],i={};;){var a=e.outgoing?n.outgoers():n.incomers();if(0===a.length)break;for(var o=!1,s=0;s<a.length;s++){var u=a[s],l=u.id();i[l]||(i[l]=!0,r.push(u),o=!0)}if(!o)break;n=a}return this.spawn(r,{unique:!0}).filter(t)}};u.clearTraversalCache=function(){for(var e=0;e<this.length;e++)this[e]._private.traversalCache=null},o.extend(u,{roots:c({noIncomingEdges:!0}),leaves:c({noOutgoingEdges:!0}),outgoers:l(d({outgoing:!0}),"outgoers"),successors:h({outgoing:!0}),incomers:l(d({incoming:!0}),"incomers"),predecessors:h({incoming:!0})}),o.extend(u,{neighborhood:l(function(e){for(var t=[],n=this.nodes(),r=0;r<n.length;r++)for(var i=n[r],a=i.connectedEdges(),o=0;o<a.length;o++){var s=a[o],u=s.source(),l=s.target(),c=i===u?l:u;c.length>0&&t.push(c[0]),t.push(s[0])}return this.spawn(t,{unique:!0}).filter(e)},"neighborhood"),closedNeighborhood:function(e){return this.neighborhood().add(this).filter(e)},openNeighborhood:function(e){return this.neighborhood(e)}}),u.neighbourhood=u.neighborhood,u.closedNeighbourhood=u.closedNeighborhood,u.openNeighbourhood=u.openNeighborhood,o.extend(u,{source:l(function(e){var t=this[0],n=void 0;return t&&(n=t._private.source||t.cy().collection()),n&&e?n.filter(e):n},"source"),target:l(function(e){var t=this[0],n=void 0;return t&&(n=t._private.target||t.cy().collection()),n&&e?n.filter(e):n},"target"),sources:r({attr:"source"}),targets:r({attr:"target"})}),o.extend(u,{edgesWith:l(i(),"edgesWith"),edgesTo:l(i({thisIsSrc:!0}),"edgesTo")}),o.extend(u,{connectedEdges:l(function(e){for(var t=[],n=this,r=0;r<n.length;r++){var i=n[r];if(i.isNode())for(var a=i._private.edges,o=0;o<a.length;o++){var s=a[o];t.push(s)}}return this.spawn(t,{unique:!0}).filter(e)},"connectedEdges"),connectedNodes:l(function(e){for(var t=[],n=this,r=0;r<n.length;r++){var i=n[r];i.isEdge()&&(t.push(i.source()[0]),t.push(i.target()[0]))}return this.spawn(t,{unique:!0}).filter(e)},"connectedNodes"),parallelEdges:l(a(),"parallelEdges"),codirectedEdges:l(a({codirected:!0}),"codirectedEdges")}),o.extend(u,{components:function(){var e=this,t=e.cy(),n=e.spawn(),r=e.nodes().spawnSelf(),i=[],a=function(e,t){n.merge(e),r.unmerge(e),t.merge(e)};if(r.empty())return e.spawn();do!function(){var n=t.collection();i.push(n);var o=r[0];a(o,n),e.bfs({directed:!1,roots:o,visit:function(e,t,r,i,o){a(e,n)}})}();while(r.length>0);return i.map(function(e){var t=e.connectedEdges().stdFilter(function(t){return e.anySame(t.source())&&e.anySame(t.target())});return e.union(t)})}}),e.exports=u},function(e,t,n){"use strict";var r=n(0),i=n(1),a=n(7),o=n(13),s={add:function(e){var t=void 0,n=this;if(r.elementOrCollection(e)){var s=e;if(s._private.cy===n)t=s.restore();else{for(var u=[],l=0;l<s.length;l++){var c=s[l];u.push(c.json())}t=new a(n,u)}}else if(r.array(e)){var d=e;t=new a(n,d)}else if(r.plainObject(e)&&(r.array(e.nodes)||r.array(e.edges))){for(var h=e,f=[],p=["nodes","edges"],v=0,g=p.length;v<g;v++){var y=p[v],m=h[y];if(r.array(m))for(var b=0,x=m.length;b<x;b++){var w=i.extend({group:y},m[b]);f.push(w)}}t=new a(n,f)}else{var _=e;t=new o(n,_).collection()}return t},remove:function(e){if(r.elementOrCollection(e));else if(r.string(e)){var t=e;e=this.$(t)}return e.remove()}};e.exports=s},function(e,t,n){"use strict";function r(e,t,n,r){function i(e,t){return 1-3*t+3*e}function a(e,t){return 3*t-6*e}function o(e){return 3*e}function s(e,t,n){return((i(t,n)*e+a(t,n))*e+o(t))*e}function u(e,t,n){return 3*i(t,n)*e*e+2*a(t,n)*e+o(t)}function l(t,r){for(var i=0;i<p;++i){var a=u(r,e,n);if(0===a)return r;r-=(s(r,e,n)-t)/a}return r}function c(){for(var t=0;t<m;++t)_[t]=s(t*b,e,n)}function d(t,r,i){var a=void 0,o=void 0,u=0;do o=r+(i-r)/2,a=s(o,e,n)-t,a>0?i=o:r=o;while(Math.abs(a)>g&&++u<y);return o}function h(t){for(var r=0,i=1,a=m-1;i!==a&&_[i]<=t;++i)r+=b;--i;var o=(t-_[i])/(_[i+1]-_[i]),s=r+o*b,c=u(s,e,n);return c>=v?l(t,s):0===c?s:d(t,r,r+b)}function f(){E=!0,e===t&&n===r||c()}var p=4,v=.001,g=1e-7,y=10,m=11,b=1/(m-1),x="undefined"!=typeof Float32Array;if(4!==arguments.length)return!1;for(var w=0;w<4;++w)if("number"!=typeof arguments[w]||isNaN(arguments[w])||!isFinite(arguments[w]))return!1;e=Math.min(e,1),n=Math.min(n,1),e=Math.max(e,0),n=Math.max(n,0);var _=x?new Float32Array(m):new Array(m),E=!1,k=function(i){return E||f(),e===t&&n===r?i:0===i?0:1===i?1:s(h(i),t,r)};k.getControlPoints=function(){return[{x:e,y:t},{x:n,y:r}]};var C="generateBezier("+[e,t,n,r]+")";return k.toString=function(){return C},k}e.exports=r},function(e,t,n){"use strict";function r(e,t,n,r,i){if(1===r)return n;var a=i(t,n,r);return null==e?a:((e.roundValue||e.color)&&(a=Math.round(a)),void 0!==e.min&&(a=Math.max(a,e.min)),void 0!==e.max&&(a=Math.min(a,e.max)),a)}function i(e,t,n,i,o){var s=null!=o?o.type:null;n<0?n=0:n>1&&(n=1);var u=void 0,l=void 0;if(u=null!=e.pfValue||null!=e.value?null!=e.pfValue?e.pfValue:e.value:e,l=null!=t.pfValue||null!=t.value?null!=t.pfValue?t.pfValue:t.value:t,a.number(u)&&a.number(l))return r(s,u,l,n,i);if(a.array(u)&&a.array(l)){for(var c=[],d=0;d<l.length;d++){var h=u[d],f=l[d];if(null!=h&&null!=f){var p=r(s,h,f,n,i);c.push(p)}else c.push(f)}return c}}var a=n(0);e.exports=i},function(e,t,n){"use strict";var r=n(57),i=n(61),a=function(e,t,n,i){var a=r(e,t,n,i);return function(e,t,n){return e+(t-e)*a(n)}},o={linear:function(e,t,n){return e+(t-e)*n},ease:a(.25,.1,.25,1),"ease-in":a(.42,0,1,1),"ease-out":a(0,0,.58,1),"ease-in-out":a(.42,0,.58,1),"ease-in-sine":a(.47,0,.745,.715),"ease-out-sine":a(.39,.575,.565,1),"ease-in-out-sine":a(.445,.05,.55,.95),"ease-in-quad":a(.55,.085,.68,.53),"ease-out-quad":a(.25,.46,.45,.94),"ease-in-out-quad":a(.455,.03,.515,.955),"ease-in-cubic":a(.55,.055,.675,.19),"ease-out-cubic":a(.215,.61,.355,1),"ease-in-out-cubic":a(.645,.045,.355,1),"ease-in-quart":a(.895,.03,.685,.22),"ease-out-quart":a(.165,.84,.44,1),"ease-in-out-quart":a(.77,0,.175,1),"ease-in-quint":a(.755,.05,.855,.06),"ease-out-quint":a(.23,1,.32,1),"ease-in-out-quint":a(.86,0,.07,1),"ease-in-expo":a(.95,.05,.795,.035),"ease-out-expo":a(.19,1,.22,1),"ease-in-out-expo":a(1,0,0,1),"ease-in-circ":a(.6,.04,.98,.335),"ease-out-circ":a(.075,.82,.165,1),"ease-in-out-circ":a(.785,.135,.15,.86),spring:function(e,t,n){if(0===n)return o.linear;var r=i(e,t,n);return function(e,t,n){return e+(t-e)*r(n)}},"cubic-bezier":a};e.exports=o},function(e,t,n){"use strict";var r=n(3),i=n(1),a=n(63),o={animate:r.animate(),animation:r.animation(),animated:r.animated(),clearQueue:r.clearQueue(),delay:r.delay(),delayAnimation:r.delayAnimation(),stop:r.stop(),addToAnimationPool:function(e){var t=this;t.styleEnabled()&&t._private.aniEles.merge(e)},stopAnimationLoop:function(){this._private.animationsRunning=!1},startAnimationLoop:function(){function e(){t._private.animationsRunning&&i.requestAnimationFrame(function(n){a(n,t),e()})}var t=this;if(t._private.animationsRunning=!0,t.styleEnabled()){var n=t.renderer();n&&n.beforeRender?n.beforeRender(function(e,n){a(n,t)},n.beforeRenderPriorities.animations):e()}}};e.exports=o},function(e,t,n){"use strict";var r=function(){function e(e){return-e.tension*e.x-e.friction*e.v}function t(t,n,r){var i={x:t.x+r.dx*n,v:t.v+r.dv*n,tension:t.tension,friction:t.friction};return{dx:i.v,dv:e(i)}}function n(n,r){var i={dx:n.v,dv:e(n)},a=t(n,.5*r,i),o=t(n,.5*r,a),s=t(n,r,o),u=1/6*(i.dx+2*(a.dx+o.dx)+s.dx),l=1/6*(i.dv+2*(a.dv+o.dv)+s.dv);return n.x=n.x+u*r,n.v=n.v+l*r,n}return function e(t,r,i){var a={x:-1,v:0,tension:null,friction:null},o=[0],s=0,u=void 0,l=void 0,c=void 0;for(t=parseFloat(t)||500,r=parseFloat(r)||20,i=i||null,a.tension=t,a.friction=r,u=null!==i,u?(s=e(t,r),l=s/i*.016):l=.016;c=n(c||a,l),o.push(1+c.x),s+=16,Math.abs(c.x)>1e-4&&Math.abs(c.v)>1e-4;);return u?function(e){return o[e*(o.length-1)|0]}:s}}();e.exports=r},function(e,t,n){"use strict";function r(e,t,n,r){var i=!r,a=e,o=t._private,s=r?e:e.cy(),u=s.style();if(i){var l=a.position();o.startPosition=o.startPosition||{x:l.x,y:l.y},o.startStyle=o.startStyle||u.getAnimationStartStyle(a,o.style)}if(r){var c=s._private.pan;o.startPan=o.startPan||{x:c.x,y:c.y},o.startZoom=null!=o.startZoom?o.startZoom:s._private.zoom}o.started=!0,o.startTime=n-o.progress*o.duration}e.exports=r},function(e,t,n){"use strict";function r(e,t){function n(t,n){var r=t._private,s=r.animation.current,u=r.animation.queue,l=!1;if(!n&&"none"===t.pstyle("display").value){s=s.splice(0,s.length).concat(u.splice(0,u.length));for(var c=0;c<s.length;c++)s[c].stop()}if(0===s.length){var d=u.shift();d&&s.push(d)}for(var h=function(e){for(var t=e.length-1;t>=0;t--)(0,e[t])();e.splice(0,e.length)},f=s.length-1;f>=0;f--){var p=s[f],v=p._private;v.stopped?(s.splice(f,1),v.hooked=!1,v.playing=!1,v.started=!1,h(v.frames)):(v.playing||v.applying)&&(v.playing&&v.applying&&(v.applying=!1),v.started||a(t,p,e,n),i(t,p,e,n),v.applying&&(v.applying=!1),h(v.frames),p.completed()&&(s.splice(f,1),v.hooked=!1,v.playing=!1,v.started=!1,h(v.completes)),l=!0)}return n||0!==s.length||0!==u.length||o.push(t),l}for(var r=t._private.aniEles,o=[],s=!1,u=0;u<r.length;u++){var l=r[u],c=n(l);s=s||c}var d=n(t,!0);(s||d)&&(r.length>0?(r.dirtyCompoundBoundsCache(),t.notify({type:"draw",eles:r})):t.notify({type:"draw"})),r.unmerge(o),t.emit("step")}var i=n(64),a=n(62);e.exports=r},function(e,t,n){"use strict";function r(e,t,n,r){var u=!r,l=e._private,c=t._private,d=c.easing,h=c.startTime,f=r?e:e.cy(),p=f.style();if(!c.easingImpl)if(null==d)c.easingImpl=a.linear;else{var v=void 0;if(s.string(d)){var g=p.parse("transition-timing-function",d);v=g.value}else v=d;var y=void 0,m=void 0;s.string(v)?(y=v,m=[]):(y=v[1],m=v.slice(2).map(function(e){return+e})),m.length>0?("spring"===y&&m.push(c.duration),c.easingImpl=a[y].apply(null,m)):c.easingImpl=a[y]}var b=c.easingImpl,x=void 0;if(x=0===c.duration?1:(n-h)/c.duration,c.applying&&(x=c.progress),x<0?x=0:x>1&&(x=1),null==c.delay){var w=c.startPosition,_=c.position;if(_&&u&&!e.locked()){var E=e.position();i(w.x,_.x)&&(E.x=o(w.x,_.x,x,b)),i(w.y,_.y)&&(E.y=o(w.y,_.y,x,b)),e.emit("position")}var k=c.startPan,C=c.pan,P=l.pan,T=null!=C&&r;T&&(i(k.x,C.x)&&(P.x=o(k.x,C.x,x,b)),i(k.y,C.y)&&(P.y=o(k.y,C.y,x,b)),e.emit("pan"));var S=c.startZoom,D=c.zoom,I=null!=D&&r;I&&(i(S,D)&&(l.zoom=o(S,D,x,b)),e.emit("zoom")),(T||I)&&e.emit("viewport");var N=c.style;if(N&&N.length>0&&u){for(var M=0;M<N.length;M++){var L=N[M],B=L.name,O=L,A=c.startStyle[B],z=p.properties[A.name],R=o(A,O,x,b,z);p.overrideBypass(e,B,R)}e.emit("style")}}return c.progress=x,x}function i(e,t){return!!(null!=e&&null!=t&&(s.number(e)&&s.number(t)||e&&t))}var a=n(59),o=n(58),s=n(0);e.exports=r},function(e,t,n){"use strict";var r=n(10),i=n(3),a=n(0),o=n(1),s=n(6),u={qualifierCompare:function(e,t){return null==e||null==t?null==e&&null==t:e.sameText(t)},eventMatches:function(e,t,n){var r=t.qualifier;return null==r||e!==n.target&&a.element(n.target)&&r.matches(n.target)},eventFields:function(e){return{cy:e,target:e}},callbackContext:function(e,t,n){return null!=t.qualifier?n.target:e}},l=function(e){return a.string(e)?new s(e):e},c={createEmitter:function(){var e=this._private;return e.emitter||(e.emitter=new r(o.assign({context:this},u))),this},emitter:function(){return this._private.emitter},on:function(e,t,n){return this.emitter().on(e,l(t),n),this},removeListener:function(e,t,n){return this.emitter().removeListener(e,l(t),n),this},one:function(e,t,n){return this.emitter().one(e,l(t),n),this},once:function(e,t,n){return this.emitter().one(e,l(t),n),this},emit:function(e,t){return this.emitter().emit(e,t),this}};i.eventAliasesOn(c),e.exports=c},function(e,t,n){"use strict";var r={png:function(e){var t=this._private.renderer;return e=e||{},t.png(e)},jpg:function(e){var t=this._private.renderer;return e=e||{},e.bg=e.bg||"#fff",t.jpg(e)}};r.jpeg=r.jpg,e.exports=r},function(e,t,n){"use strict";var r=n(1),i=n(0),a={layout:function(e){var t=this;if(null==e)return void r.error("Layout options must be specified to make a layout");if(null==e.name)return void r.error("A `name` must be specified to make a layout");var n=e.name,a=t.extension("layout",n);if(null==a)return void r.error("Can not apply layout: No such layout `"+n+"` found; did you include its JS file?");var o=void 0;return o=i.string(e.eles)?t.$(e.eles):null!=e.eles?e.eles:t.$(),new a(r.extend({},e,{cy:t,eles:o}))}};a.createLayout=a.makeLayout=a.layout,e.exports=a},function(e,t,n){"use strict";var r={notify:function(e){var t=this._private;if(t.batchingNotify){var n=t.batchNotifyEles,r=t.batchNotifyTypes;return e.eles&&n.merge(e.eles),void(r.ids[e.type]||(r.push(e.type),r.ids[e.type]=!0))}if(t.notificationsEnabled){var i=this.renderer();!this.isDestroyed()&&i&&i.notify(e)}},notifications:function(e){var t=this._private;return void 0===e?t.notificationsEnabled:void(t.notificationsEnabled=!!e)},noNotifications:function(e){this.notifications(!1),e(),this.notifications(!0)},batching:function(){return this._private.batchCount>0},startBatch:function(){var e=this._private;return null==e.batchCount&&(e.batchCount=0),0===e.batchCount&&(e.batchingStyle=e.batchingNotify=!0,e.batchStyleEles=this.collection(),e.batchNotifyEles=this.collection(),e.batchNotifyTypes=[],e.batchNotifyTypes.ids={}),e.batchCount++,this},endBatch:function(){var e=this._private;return e.batchCount--,0===e.batchCount&&(e.batchingStyle=!1,e.batchStyleEles.updateStyle(),e.batchingNotify=!1,this.notify({type:e.batchNotifyTypes,eles:e.batchNotifyEles})),this},batch:function(e){return this.startBatch(),e(),this.endBatch(),this},batchData:function(e){var t=this;return this.batch(function(){for(var n=Object.keys(e),r=0;r<n.length;r++){var i=n[r],a=e[i];t.getElementById(i).data(a)}})}};e.exports=r},function(e,t,n){"use strict";var r=n(1),i={renderTo:function(e,t,n,r){return this._private.renderer.renderTo(e,t,n,r),this},renderer:function(){return this._private.renderer},forceRender:function(){return this.notify({type:"draw"}),this},resize:function(){return this.invalidateSize(),this.notify({type:"resize"}),this.emit("resize"),this},initRenderer:function(e){var t=this,n=t.extension("renderer",e.name);return null==n?void r.error("Can not initialise: No such renderer `%s` found; did you include its JS file?",e.name):(t._private.renderer=new n(r.extend({},e,{cy:t})),void this.notify({type:"init"}))},destroyRenderer:function(){var e=this;e.notify({type:"destroy"});var t=e.container();if(t)for(t._cyreg=null;t.childNodes.length>0;)t.removeChild(t.childNodes[0]);e._private.renderer=null},onRender:function(e){return this.on("render",e)},offRender:function(e){return this.off("render",e)}};i.invalidateDimensions=i.resize,e.exports=i},function(e,t,n){"use strict";var r=n(0),i=n(7),a={collection:function(e,t){return r.string(e)?this.$(e):r.elementOrCollection(e)?e.collection():r.array(e)?new i(this,e,t):new i(this)},nodes:function(e){var t=this.$(function(e){return e.isNode()});return e?t.filter(e):t},edges:function(e){var t=this.$(function(e){return e.isEdge()});return e?t.filter(e):t},$:function(e){var t=this._private.elements;return e?t.filter(e):t.spawnSelf()},mutableElements:function(){return this._private.elements}};a.elements=a.filter=a.$,e.exports=a},function(e,t,n){"use strict";var r=n(0),i=n(18),a={style:function(e){return e&&this.setStyle(e).update(),this._private.style},setStyle:function(e){var t=this._private;return r.stylesheet(e)?t.style=e.generateStyle(this):r.array(e)?t.style=i.fromJson(this,e):r.string(e)?t.style=i.fromString(this,e):t.style=i(this),t.style}};e.exports=a},function(e,t,n){"use strict";var r=n(0),i=n(4),a=n(2),o={autolock:function(e){return void 0===e?this._private.autolock:(this._private.autolock=!!e,this)},autoungrabify:function(e){return void 0===e?this._private.autoungrabify:(this._private.autoungrabify=!!e,this)},autounselectify:function(e){return void 0===e?this._private.autounselectify:(this._private.autounselectify=!!e,this)},panningEnabled:function(e){return void 0===e?this._private.panningEnabled:(this._private.panningEnabled=!!e,this)},userPanningEnabled:function(e){return void 0===e?this._private.userPanningEnabled:(this._private.userPanningEnabled=!!e,this)},zoomingEnabled:function(e){return void 0===e?this._private.zoomingEnabled:(this._private.zoomingEnabled=!!e,this)},userZoomingEnabled:function(e){return void 0===e?this._private.userZoomingEnabled:(this._private.userZoomingEnabled=!!e,this)},boxSelectionEnabled:function(e){return void 0===e?this._private.boxSelectionEnabled:(this._private.boxSelectionEnabled=!!e,this)},pan:function(){var e=arguments,t=this._private.pan,n=void 0,i=void 0,a=void 0,o=void 0,s=void 0;switch(e.length){case 0:return t;case 1:if(r.string(e[0]))return n=e[0],t[n];if(r.plainObject(e[0])){if(!this._private.panningEnabled)return this;a=e[0],o=a.x,s=a.y,r.number(o)&&(t.x=o),r.number(s)&&(t.y=s),this.emit("pan viewport")}break;case 2:if(!this._private.panningEnabled)return this;n=e[0],i=e[1],"x"!==n&&"y"!==n||!r.number(i)||(t[n]=i),this.emit("pan viewport")}return this.notify({type:"viewport"}),this},panBy:function(e,t){var n=arguments,i=this._private.pan,a=void 0,o=void 0,s=void 0,u=void 0,l=void 0;if(!this._private.panningEnabled)return this;switch(n.length){case 1:r.plainObject(e)&&(s=n[0],u=s.x,l=s.y,r.number(u)&&(i.x+=u),r.number(l)&&(i.y+=l),this.emit("pan viewport"));break;case 2:a=e,o=t,"x"!==a&&"y"!==a||!r.number(o)||(i[a]+=o),this.emit("pan viewport")}return this.notify({type:"viewport"}),this},fit:function(e,t){var n=this.getFitViewport(e,t);if(n){var r=this._private;r.zoom=n.zoom,r.pan=n.pan,this.emit("pan zoom viewport"),this.notify({type:"viewport"})}return this},getFitViewport:function(e,t){if(r.number(e)&&void 0===t&&(t=e,e=void 0),this._private.panningEnabled&&this._private.zoomingEnabled){var n=void 0;if(r.string(e)){var i=e;e=this.$(i)}else if(r.boundingBox(e)){var a=e;n={x1:a.x1,y1:a.y1,x2:a.x2,y2:a.y2},n.w=n.x2-n.x1,n.h=n.y2-n.y1}else r.elementOrCollection(e)||(e=this.mutableElements());if(!r.elementOrCollection(e)||!e.empty()){n=n||e.boundingBox();var o=this.width(),s=this.height(),u=void 0;if(t=r.number(t)?t:0,!isNaN(o)&&!isNaN(s)&&o>0&&s>0&&!isNaN(n.w)&&!isNaN(n.h)&&n.w>0&&n.h>0)return u=Math.min((o-2*t)/n.w,(s-2*t)/n.h),u=u>this._private.maxZoom?this._private.maxZoom:u,u=u<this._private.minZoom?this._private.minZoom:u,{zoom:u,pan:{x:(o-u*(n.x1+n.x2))/2,y:(s-u*(n.y1+n.y2))/2}}}}},minZoom:function(e){return void 0===e?this._private.minZoom:(r.number(e)&&(this._private.minZoom=e),this)},maxZoom:function(e){return void 0===e?this._private.maxZoom:(r.number(e)&&(this._private.maxZoom=e),this)},getZoomedViewport:function(e){var t=this._private,n=t.pan,i=t.zoom,o=void 0,s=void 0,u=!1;if(t.zoomingEnabled||(u=!0),r.number(e)?s=e:r.plainObject(e)&&(s=e.level,null!=e.position?o=a.modelToRenderedPosition(e.position,i,n):null!=e.renderedPosition&&(o=e.renderedPosition),null==o||t.panningEnabled||(u=!0)),s=s>t.maxZoom?t.maxZoom:s,s=s<t.minZoom?t.minZoom:s,u||!r.number(s)||s===i||null!=o&&(!r.number(o.x)||!r.number(o.y)))return null;if(null!=o){var l=n,c=i,d=s;return{zoomed:!0,panned:!0,zoom:d,pan:{x:-d/c*(o.x-l.x)+o.x,y:-d/c*(o.y-l.y)+o.y}}}return{zoomed:!0,panned:!1,zoom:s,pan:n}},zoom:function(e){if(void 0===e)return this._private.zoom;var t=this.getZoomedViewport(e),n=this._private;return null!=t&&t.zoomed?(n.zoom=t.zoom,t.panned&&(n.pan.x=t.pan.x,n.pan.y=t.pan.y),this.emit("zoom"+(t.panned?" pan":"")+" viewport"),this.notify({type:"viewport"}),this):this},viewport:function(e){var t=this._private,n=!0,i=!0,a=[],o=!1,s=!1;if(!e)return this;if(r.number(e.zoom)||(n=!1),r.plainObject(e.pan)||(i=!1),!n&&!i)return this;if(n){var u=e.zoom;u<t.minZoom||u>t.maxZoom||!t.zoomingEnabled?o=!0:(t.zoom=u,a.push("zoom"))}if(i&&(!o||!e.cancelOnFailedZoom)&&t.panningEnabled){var l=e.pan;r.number(l.x)&&(t.pan.x=l.x,s=!1),r.number(l.y)&&(t.pan.y=l.y,s=!1),s||a.push("pan")}return a.length>0&&(a.push("viewport"),this.emit(a.join(" ")),this.notify({type:"viewport"})),this},center:function(e){var t=this.getCenterPan(e);return t&&(this._private.pan=t,this.emit("pan viewport"),this.notify({type:"viewport"})),this},getCenterPan:function(e,t){if(this._private.panningEnabled){if(r.string(e)){var n=e;e=this.mutableElements().filter(n)}else r.elementOrCollection(e)||(e=this.mutableElements());if(0!==e.length){var i=e.boundingBox(),a=this.width(),o=this.height();return t=void 0===t?this._private.zoom:t,{x:(a-t*(i.x1+i.x2))/2,y:(o-t*(i.y1+i.y2))/2}}}},reset:function(){return this._private.panningEnabled&&this._private.zoomingEnabled?(this.viewport({pan:{x:0,y:0},zoom:1}),this):this},invalidateSize:function(){this._private.sizeCache=null},size:function(){var e=this._private,t=e.container;return e.sizeCache=e.sizeCache||(t?function(){var e=i.getComputedStyle(t),n=function(t){return parseFloat(e.getPropertyValue(t))};return{width:t.clientWidth-n("padding-left")-n("padding-right"),height:t.clientHeight-n("padding-top")-n("padding-bottom")}}():{width:1,height:1})},width:function(){return this.size().width},height:function(){return this.size().height},extent:function(){var e=this._private.pan,t=this._private.zoom,n=this.renderedExtent(),r={x1:(n.x1-e.x)/t,x2:(n.x2-e.x)/t,y1:(n.y1-e.y)/t,y2:(n.y2-e.y)/t};return r.w=r.x2-r.x1,r.h=r.y2-r.y1,r},renderedExtent:function(){var e=this.width(),t=this.height();return{x1:0,y1:0,x2:e,y2:t,w:e,h:t}}};o.centre=o.center,o.autolockNodes=o.autolock,o.autoungrabifyNodes=o.autoungrabify,e.exports=o},function(e,t,n){"use strict";var r=n(1),i=n(24),a=n(2),o=n(0),s={animated:function(){return function(){var e=this,t=void 0!==e.length,n=t?e:[e];if(!(this._private.cy||this).styleEnabled())return!1;var r=n[0];return r?r._private.animation.current.length>0:void 0}},clearQueue:function(){return function(){var e=this,t=void 0!==e.length,n=t?e:[e];if(!(this._private.cy||this).styleEnabled())return this;for(var r=0;r<n.length;r++)n[r]._private.animation.queue=[];return this}},delay:function(){return function(e,t){return(this._private.cy||this).styleEnabled()?this.animate({delay:e,duration:e,complete:t}):this}},delayAnimation:function(){return function(e,t){return(this._private.cy||this).styleEnabled()?this.animation({delay:e,duration:e,complete:t}):this}},animation:function(){return function(e,t){var n=this,s=void 0!==n.length,u=s?n:[n],l=this._private.cy||this,c=!s,d=!c;if(!l.styleEnabled())return this;var h=l.style();if(e=r.assign({},e,t),0===Object.keys(e).length)return new i(u[0],e);switch(void 0===e.duration&&(e.duration=400),e.duration){case"slow":e.duration=600;break;case"fast":e.duration=200}if(d&&(e.style=h.getPropsList(e.style||e.css),e.css=void 0),d&&null!=e.renderedPosition){var f=e.renderedPosition,p=l.pan(),v=l.zoom();e.position=a.renderedToModelPosition(f,v,p)}if(c&&null!=e.panBy){var g=e.panBy,y=l.pan();e.pan={x:y.x+g.x,y:y.y+g.y}}var m=e.center||e.centre;if(c&&null!=m){var b=l.getCenterPan(m.eles,e.zoom);null!=b&&(e.pan=b)}if(c&&null!=e.fit){var x=e.fit,w=l.getFitViewport(x.eles||x.boundingBox,x.padding);null!=w&&(e.pan=w.pan,e.zoom=w.zoom)}if(c&&o.plainObject(e.zoom)){var _=l.getZoomedViewport(e.zoom);null!=_&&(_.zoomed&&(e.zoom=_.zoom),_.panned&&(e.pan=_.pan))}return new i(u[0],e)}},animate:function(){return function(e,t){var n=this,i=void 0!==n.length,a=i?n:[n];if(!(this._private.cy||this).styleEnabled())return this;t&&(e=r.extend({},e,t));for(var o=0;o<a.length;o++){var s=a[o],u=s.animated()&&(void 0===e.queue||e.queue);s.animation(e,u?{queue:!0}:void 0).play()}return this}},stop:function(){return function(e,t){var n=this,r=void 0!==n.length,i=r?n:[n],a=this._private.cy||this;if(!a.styleEnabled())return this;for(var o=0;o<i.length;o++){for(var s=i[o],u=s._private,l=u.animation.current,c=0;c<l.length;c++){var d=l[c],h=d._private;t&&(h.duration=0)}e&&(u.animation.queue=[]),t||(u.animation.current=[])}return a.notify({eles:this,type:"draw"}),this}}};e.exports=s},function(e,t,n){"use strict";function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var i=n(1),a=n(0),o={data:function(e){var t={field:"data",bindingEvent:"data",allowBinding:!1,allowSetting:!1,allowGetting:!1,settingEvent:"data",settingTriggersEvent:!1,triggerFnName:"trigger",immutableKeys:{},updateStyle:!1,beforeGet:function(e){},beforeSet:function(e,t){},onSet:function(e){},canSet:function(e){return!0}};return e=i.extend({},t,e),function(t,n){var i=e,o=this,s=void 0!==o.length,u=s?o:[o],l=s?o[0]:o;if(a.string(t)){if(i.allowGetting&&void 0===n){var c=void 0;return l&&(i.beforeGet(l),c=l._private[i.field][t]),c}if(i.allowSetting&&void 0!==n&&!i.immutableKeys[t]){var d=r({},t,n);i.beforeSet(o,d);for(var h=0,f=u.length;h<f;h++){var p=u[h];i.canSet(p)&&(p._private[i.field][t]=n)}i.updateStyle&&o.updateStyle(),i.onSet(o),i.settingTriggersEvent&&o[i.triggerFnName](i.settingEvent)}}else if(i.allowSetting&&a.plainObject(t)){var v=t,g=void 0,y=void 0,m=Object.keys(v);i.beforeSet(o,v);for(var b=0;b<m.length;b++){g=m[b],y=v[g];var x=!i.immutableKeys[g];if(x)for(var w=0;w<u.length;w++){var _=u[w];i.canSet(_)&&(_._private[i.field][g]=y)}}i.updateStyle&&o.updateStyle(),i.onSet(o),i.settingTriggersEvent&&o[i.triggerFnName](i.settingEvent)}else if(i.allowBinding&&a.fn(t)){var E=t;o.on(i.bindingEvent,E)}else if(i.allowGetting&&void 0===t){var k=void 0;return l&&(i.beforeGet(l),k=l._private[i.field]),k}return o}},removeData:function(e){var t={field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!1,immutableKeys:{}};return e=i.extend({},t,e),function(t){var n=e,r=this,i=void 0!==r.length,o=i?r:[r];if(a.string(t)){for(var s=t.split(/\s+/),u=s.length,l=0;l<u;l++){var c=s[l];if(!a.emptyString(c)&&!n.immutableKeys[c])for(var d=0,h=o.length;d<h;d++)o[d]._private[n.field][c]=void 0}n.triggerEvent&&r[n.triggerFnName](n.event)}else if(void 0===t){for(var f=0,p=o.length;f<p;f++)for(var v=o[f]._private[n.field],g=Object.keys(v),y=0;y<g.length;y++){var m=g[y],b=!n.immutableKeys[m];b&&(v[m]=void 0)}n.triggerEvent&&r[n.triggerFnName](n.event)}return r}}};e.exports=o},function(e,t,n){"use strict";var r=n(5),i={eventAliasesOn:function(e){var t=e;t.addListener=t.listen=t.bind=t.on,t.unlisten=t.unbind=t.off=t.removeListener,t.trigger=t.emit,t.pon=t.promiseOn=function(e,t){var n=this,i=Array.prototype.slice.call(arguments,0);return new r(function(e,t){
var r=function(t){n.off.apply(n,o),e(t)},a=i.concat([r]),o=a.concat([]);n.on.apply(n,a)})}}};e.exports=i},function(e,t,n){"use strict";e.exports=[{type:"layout",extensions:n(82)},{type:"renderer",extensions:n(115)}]},function(e,t,n){"use strict";function r(e){this.options=i.extend({},s,e)}var i=n(1),a=n(2),o=n(0),s={fit:!0,directed:!1,padding:30,circle:!1,spacingFactor:1.75,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,roots:void 0,maximalAdjustments:0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};r.prototype.run=function(){var e=this.options,t=e,n=e.cy,r=t.eles,i=r.nodes().not(":parent"),s=r,u=a.makeBoundingBox(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:n.width(),h:n.height()}),l=void 0;if(o.elementOrCollection(t.roots))l=t.roots;else if(o.array(t.roots)){for(var c=[],d=0;d<t.roots.length;d++){var h=t.roots[d],f=n.getElementById(h);c.push(f)}l=n.collection(c)}else if(o.string(t.roots))l=n.$(t.roots);else if(t.directed)l=i.roots();else{for(var p=[],v=i;v.length>0;)!function(){var e=n.collection();r.bfs({roots:v[0],visit:function(t,n,r,i,a){e=e.add(t)},directed:!1}),v=v.not(e),p.push(e)}();l=n.collection();for(var g=0;g<p.length;g++)!function(e){var t=p[e],n=t.maxDegree(!1),r=t.filter(function(e){return e.degree(!1)===n});l=l.add(r)}(g)}var y=[],m={},b={},x={},w={},_={};s.bfs({roots:l,directed:t.directed,visit:function(e,t,n,r,i){var a=e[0],o=a.id();if(y[i]||(y[i]=[]),y[i].push(a),m[o]=!0,b[o]=i,x[o]=n,w[o]=t,n){var s=n.id();(_[s]=_[s]||[]).push(e)}}});for(var E=[],k=0;k<i.length;k++){var C=i[k];m[C.id()]||E.push(C)}for(var P=3*E.length,T=0;0!==E.length&&T<P;){for(var S=E.shift(),D=S.neighborhood().nodes(),I=!1,N=0;N<D.length;N++){var M=b[D[N].id()];if(void 0!==M){y[M].push(S),I=!0;break}}I||E.push(S),T++}for(;0!==E.length;){var L=E.shift();0===y.length&&y.push([]),y[0].push(L)}var B=function(){for(var e=0;e<y.length;e++)for(var t=y[e],n=0;n<t.length;n++){var r=t[n];r._private.scratch.breadthfirst={depth:e,index:n}}};B();for(var O=0;O<t.maximalAdjustments;O++){for(var A=y.length,z=[],R=0;R<A;R++)for(var j=y[R],F=j.length,V=0;V<F;V++){var q=j[V],Y=q._private.scratch.breadthfirst,X=function(e){for(var t=e.connectedEdges(function(t){return t.data("target")===e.id()}),n=e._private.scratch.breadthfirst,r=0,i=void 0,a=0;a<t.length;a++){var o=t[a],s=o.source()[0],u=s._private.scratch.breadthfirst;n.depth<=u.depth&&r<u.depth&&(r=u.depth,i=s)}return i}(q);X&&(Y.intEle=X,z.push(q))}for(var W=0;W<z.length;W++){var U=z[W],H=U._private.scratch.breadthfirst,$=H.intEle,G=$._private.scratch.breadthfirst;y[H.depth].splice(H.index,1);for(var Z=G.depth+1;Z>y.length-1;)y.push([]);y[Z].push(U),H.depth=Z,H.index=y[Z].length-1}B()}var Q=0;if(t.avoidOverlap)for(var K=0;K<i.length;K++){var J=i[K],ee=J.layoutDimensions(t),te=ee.w,ne=ee.h;Q=Math.max(Q,te,ne)}for(var re={},ie=function(e){if(re[e.id()])return re[e.id()];for(var t=e._private.scratch.breadthfirst.depth,n=e.neighborhood().nodes().not(":parent").intersection(i),r=0,a=0,o=0;o<n.length;o++){var s=n[o],u=s._private.scratch.breadthfirst,l=u.index,c=u.depth,d=y[c].length;(t>c||0===t)&&(r+=l/d,a++)}return a=Math.max(1,a),r/=a,0===a&&(r=void 0),re[e.id()]=r,r},ae=function(e,t){return ie(e)-ie(t)},oe=0;oe<3;oe++){for(var se=0;se<y.length;se++)y[se]=y[se].sort(ae);B()}for(var ue=0,le=0;le<y.length;le++)ue=Math.max(y[le].length,ue);for(var ce={x:u.x1+u.w/2,y:u.x1+u.h/2},de={},he=y.length-1;he>=0;he--)for(var fe=y[he],pe=0;pe<fe.length;pe++){var ve=fe[pe];de[ve.id()]=function(e,n){var r=e._private.scratch.breadthfirst,i=r.depth,a=r.index,o=y[i].length,s=Math.max(u.w/(o+1),Q),l=Math.max(u.h/(y.length+1),Q),c=Math.min(u.w/2/y.length,u.h/2/y.length);if(c=Math.max(c,Q),t.circle){if(t.circle){var d=c*i+c-(y.length>0&&y[0].length<=3?c/2:0),h=2*Math.PI/y[i].length*a;return 0===i&&1===y[0].length&&(d=1),{x:ce.x+d*Math.cos(h),y:ce.y+d*Math.sin(h)}}return{x:ce.x+(a+1-(o+1)/2)*s,y:(i+1)*l}}var f={x:ce.x+(a+1-(o+1)/2)*s,y:(i+1)*l};return f}(ve,y.length)}return i.layoutPositions(this,t,function(e){return de[e.id()]}),this},e.exports=r},function(e,t,n){"use strict";function r(e){this.options=i.extend({},s,e)}var i=n(1),a=n(2),o=n(0),s={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,radius:void 0,startAngle:1.5*Math.PI,sweep:void 0,clockwise:!0,sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};r.prototype.run=function(){var e=this.options,t=e,n=e.cy,r=t.eles,i=void 0!==t.counterclockwise?!t.counterclockwise:t.clockwise,s=r.nodes().not(":parent");t.sort&&(s=s.sort(t.sort));for(var u=a.makeBoundingBox(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:n.width(),h:n.height()}),l={x:u.x1+u.w/2,y:u.y1+u.h/2},c=void 0===t.sweep?2*Math.PI-2*Math.PI/s.length:t.sweep,d=c/Math.max(1,s.length-1),h=void 0,f=0,p=0;p<s.length;p++){var v=s[p],g=v.layoutDimensions(t),y=g.w,m=g.h;f=Math.max(f,y,m)}if(h=o.number(t.radius)?t.radius:s.length<=1?0:Math.min(u.h,u.w)/2-f,s.length>1&&t.avoidOverlap){f*=1.75;var b=Math.cos(d)-Math.cos(0),x=Math.sin(d)-Math.sin(0),w=Math.sqrt(f*f/(b*b+x*x));h=Math.max(w,h)}var _=function(e,n){var r=t.startAngle+n*d*(i?1:-1),a=h*Math.cos(r),o=h*Math.sin(r);return{x:l.x+a,y:l.y+o}};return s.layoutPositions(this,t,_),this},e.exports=r},function(e,t,n){"use strict";function r(e){this.options=i.extend({},o,e)}var i=n(1),a=n(2),o={fit:!0,padding:30,startAngle:1.5*Math.PI,sweep:void 0,clockwise:!0,equidistant:!1,minNodeSpacing:10,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,height:void 0,width:void 0,spacingFactor:void 0,concentric:function(e){return e.degree()},levelWidth:function(e){return e.maxDegree()/4},animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};r.prototype.run=function(){for(var e=this.options,t=e,n=void 0!==t.counterclockwise?!t.counterclockwise:t.clockwise,r=e.cy,i=t.eles,o=i.nodes().not(":parent"),s=a.makeBoundingBox(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()}),u={x:s.x1+s.w/2,y:s.y1+s.h/2},l=[],c=(t.startAngle,0),d=0;d<o.length;d++){var h=o[d],f=void 0;f=t.concentric(h),l.push({value:f,node:h}),h._private.scratch.concentric=f}o.updateStyle();for(var p=0;p<o.length;p++){var v=o[p],g=v.layoutDimensions(t);c=Math.max(c,g.w,g.h)}l.sort(function(e,t){return t.value-e.value});for(var y=t.levelWidth(o),m=[[]],b=m[0],x=0;x<l.length;x++){var w=l[x];b.length>0&&Math.abs(b[0].value-w.value)>=y&&(b=[],m.push(b)),b.push(w)}var _=c+t.minNodeSpacing;if(!t.avoidOverlap){var E=m.length>0&&m[0].length>1,k=Math.min(s.w,s.h)/2-_,C=k/(m.length+E?1:0);_=Math.min(_,C)}for(var P=0,T=0;T<m.length;T++){var S=m[T],D=void 0===t.sweep?2*Math.PI-2*Math.PI/S.length:t.sweep,I=S.dTheta=D/Math.max(1,S.length-1);if(S.length>1&&t.avoidOverlap){var N=Math.cos(I)-Math.cos(0),M=Math.sin(I)-Math.sin(0),L=Math.sqrt(_*_/(N*N+M*M));P=Math.max(L,P)}S.r=P,P+=_}if(t.equidistant){for(var B=0,O=0,A=0;A<m.length;A++){var z=m[A],R=z.r-O;B=Math.max(B,R)}O=0;for(var j=0;j<m.length;j++){var F=m[j];0===j&&(O=F.r),F.r=O,O+=B}}for(var V={},q=0;q<m.length;q++)for(var Y=m[q],X=Y.dTheta,W=Y.r,U=0;U<Y.length;U++){var H=Y[U],$=t.startAngle+(n?1:-1)*X*U,G={x:u.x+W*Math.cos($),y:u.y+W*Math.sin($)};V[H.node.id()]=G}return o.layoutPositions(this,t,function(e){var t=e.id();return V[t]}),this},e.exports=r},function(e,t,n){"use strict";function r(e){this.options=a.extend({},l,e),this.options.layout=this}var i,a=n(1),o=n(2),s=n(0),u=n(5),l={ready:function(){},stop:function(){},animate:!0,animationEasing:void 0,animationDuration:void 0,animateFilter:function(e,t){return!0},animationThreshold:250,refresh:20,fit:!0,padding:30,boundingBox:void 0,nodeDimensionsIncludeLabels:!1,randomize:!1,componentSpacing:40,nodeRepulsion:function(e){return 2048},nodeOverlap:4,idealEdgeLength:function(e){return 32},edgeElasticity:function(e){return 32},nestingFactor:1.2,gravity:1,numIter:1e3,initialTemp:1e3,coolingFactor:.99,minTemp:1,weaver:!1};r.prototype.run=function(){function e(e){var t={type:"message",message:e};d.trigger(t)}var t=this.options,n=t.cy,r=this,o=this.thread,l=t.weaver?t.weaver.Thread:null,d={listeners:[],on:function(e,t){return this.listeners.push({event:e,callback:t}),this},trigger:function(e){s.string(e)&&(e={type:e});var t=function(t){return t.event===e.type},n=function(t){t.callback(e)};return this.listeners.filter(t).forEach(n),this},pass:function(e){return this.pass=e,this},run:function(e){var t=this.pass;return new u(function(n){n(e(t))})},stop:function(){return this}};o&&!o.stopped()||(o=this.thread=l?new l:d),r.stopped=!1,!0!==t.animate&&!1!==t.animate||r.emit({type:"layoutstart",layout:r}),i=!0===t.debug;var h=c(n,r,t);i&&f(h),t.randomize&&p(h,n);var g=Date.now(),y=!1,m=function(e){e=e||{},y&&!e.next||!e.force&&Date.now()-g<t.animationThreshold||(y=!0,a.requestAnimationFrame(function(){v(h,n,t),!0===t.fit&&n.fit(t.padding),y=!1,e.next&&e.next()}))};o.on("message",function(e){var t=e.message;h.layoutNodes=t,m()}),o.pass({layoutInfo:h,options:{animate:t.animate,refresh:t.refresh,componentSpacing:t.componentSpacing,nodeOverlap:t.nodeOverlap,nestingFactor:t.nestingFactor,gravity:t.gravity,numIter:t.numIter,initialTemp:t.initialTemp,coolingFactor:t.coolingFactor,minTemp:t.minTemp}}).run(function(t){var n,r=t.layoutInfo,i=t.options,a=function(e,t,n){o(e,t),d(e,t),h(e,t),f(e,t),p(e,t)},o=function(e,t){for(var n=0;n<e.graphSet.length;n++)for(var r=e.graphSet[n],i=r.length,a=0;a<i;a++)for(var o=e.layoutNodes[e.idToIndex[r[a]]],s=a+1;s<i;s++){var l=e.layoutNodes[e.idToIndex[r[s]]];u(o,l,e,t)}},s=function(e){return-e+2*e*Math.random()},u=function(e,t,n,r){if(e.cmptId===t.cmptId||n.isCompound){var i=t.positionX-e.positionX,a=t.positionY-e.positionY;0===i&&0===a&&(i=s(1),a=s(1));var o=l(e,t,i,a);if(o>0)var u=r.nodeOverlap*o,d=Math.sqrt(i*i+a*a),h=u*i/d,f=u*a/d;else var p=c(e,i,a),v=c(t,-1*i,-1*a),g=v.x-p.x,y=v.y-p.y,m=g*g+y*y,d=Math.sqrt(m),u=(e.nodeRepulsion+t.nodeRepulsion)/m,h=u*g/d,f=u*y/d;e.isLocked||(e.offsetX-=h,e.offsetY-=f),t.isLocked||(t.offsetX+=h,t.offsetY+=f)}},l=function(e,t,n,r){if(n>0)var i=e.maxX-t.minX;else var i=t.maxX-e.minX;if(r>0)var a=e.maxY-t.minY;else var a=t.maxY-e.minY;return i>=0&&a>=0?Math.sqrt(i*i+a*a):0},c=function(e,t,n){var r=e.positionX,i=e.positionY,a=e.height||1,o=e.width||1,s=n/t,u=a/o,l={};return 0===t&&0<n?(l.x=r,l.y=i+a/2,l):0===t&&0>n?(l.x=r,l.y=i+a/2,l):0<t&&-1*u<=s&&s<=u?(l.x=r+o/2,l.y=i+o*n/2/t,l):0>t&&-1*u<=s&&s<=u?(l.x=r-o/2,l.y=i-o*n/2/t,l):0<n&&(s<=-1*u||s>=u)?(l.x=r+a*t/2/n,l.y=i+a/2,l):0>n&&(s<=-1*u||s>=u)?(l.x=r-a*t/2/n,l.y=i-a/2,l):l},d=function(e,t){for(var n=0;n<e.edgeSize;n++){var r=e.layoutEdges[n],i=e.idToIndex[r.sourceId],a=e.layoutNodes[i],o=e.idToIndex[r.targetId],s=e.layoutNodes[o],u=s.positionX-a.positionX,l=s.positionY-a.positionY;if(0!==u||0!==l){var d=c(a,u,l),h=c(s,-1*u,-1*l),f=h.x-d.x,p=h.y-d.y,v=Math.sqrt(f*f+p*p),g=Math.pow(r.idealLength-v,2)/r.elasticity;if(0!==v)var y=g*f/v,m=g*p/v;else var y=0,m=0;a.isLocked||(a.offsetX+=y,a.offsetY+=m),s.isLocked||(s.offsetX-=y,s.offsetY-=m)}}},h=function(e,t){for(var n=0;n<e.graphSet.length;n++){var r=e.graphSet[n],i=r.length;if(0===n)var a=e.clientHeight/2,o=e.clientWidth/2;else var s=e.layoutNodes[e.idToIndex[r[0]]],u=e.layoutNodes[e.idToIndex[s.parentId]],a=u.positionX,o=u.positionY;for(var l=0;l<i;l++){var c=e.layoutNodes[e.idToIndex[r[l]]];if(!c.isLocked){var d=a-c.positionX,h=o-c.positionY,f=Math.sqrt(d*d+h*h);if(f>1){var p=t.gravity*d/f,v=t.gravity*h/f;c.offsetX+=p,c.offsetY+=v}}}}},f=function(e,t){var n=[],r=0,i=-1;for(n.push.apply(n,e.graphSet[0]),i+=e.graphSet[0].length;r<=i;){var a=n[r++],o=e.idToIndex[a],s=e.layoutNodes[o],u=s.children;if(0<u.length&&!s.isLocked){for(var l=s.offsetX,c=s.offsetY,d=0;d<u.length;d++){var h=e.layoutNodes[e.idToIndex[u[d]]];h.offsetX+=l,h.offsetY+=c,n[++i]=u[d]}s.offsetX=0,s.offsetY=0}}},p=function(e,t){for(var n=0;n<e.nodeSize;n++){var r=e.layoutNodes[n];0<r.children.length&&(r.maxX=void 0,r.minX=void 0,r.maxY=void 0,r.minY=void 0)}for(var n=0;n<e.nodeSize;n++){var r=e.layoutNodes[n];if(!(0<r.children.length||r.isLocked)){var i=v(r.offsetX,r.offsetY,e.temperature);r.positionX+=i.x,r.positionY+=i.y,r.offsetX=0,r.offsetY=0,r.minX=r.positionX-r.width,r.maxX=r.positionX+r.width,r.minY=r.positionY-r.height,r.maxY=r.positionY+r.height,g(r,e)}}for(var n=0;n<e.nodeSize;n++){var r=e.layoutNodes[n];0<r.children.length&&!r.isLocked&&(r.positionX=(r.maxX+r.minX)/2,r.positionY=(r.maxY+r.minY)/2,r.width=r.maxX-r.minX,r.height=r.maxY-r.minY)}},v=function(e,t,n){var r=Math.sqrt(e*e+t*t);if(r>n)var i={x:n*e/r,y:n*t/r};else var i={x:e,y:t};return i},g=function e(t,n){var r=t.parentId;if(null!=r){var i=n.layoutNodes[n.idToIndex[r]],a=!1;return(null==i.maxX||t.maxX+i.padRight>i.maxX)&&(i.maxX=t.maxX+i.padRight,a=!0),(null==i.minX||t.minX-i.padLeft<i.minX)&&(i.minX=t.minX-i.padLeft,a=!0),(null==i.maxY||t.maxY+i.padBottom>i.maxY)&&(i.maxY=t.maxY+i.padBottom,a=!0),(null==i.minY||t.minY-i.padTop<i.minY)&&(i.minY=t.minY-i.padTop,a=!0),a?e(i,n):void 0}},y=0;do{for(var m=0;m<i.refresh&&y<i.numIter;){var n=function(e){return a(r,i),r.temperature=r.temperature*i.coolingFactor,!(r.temperature<i.minTemp)}();if(!n)break;m++,y++}!0===i.animate&&e(r.layoutNodes)}while(n&&y+1<i.numIter);return function(e,t){for(var n=r.layoutNodes,i=[],a=0;a<n.length;a++){var o=n[a],s=o.cmptId;(i[s]=i[s]||[]).push(o)}for(var u=0,a=0;a<i.length;a++){var l=i[a];if(l){l.x1=1/0,l.x2=-1/0,l.y1=1/0,l.y2=-1/0;for(var c=0;c<l.length;c++){var d=l[c];l.x1=Math.min(l.x1,d.positionX-d.width/2),l.x2=Math.max(l.x2,d.positionX+d.width/2),l.y1=Math.min(l.y1,d.positionY-d.height/2),l.y2=Math.max(l.y2,d.positionY+d.height/2)}l.w=l.x2-l.x1,l.h=l.y2-l.y1,u+=l.w*l.h}}i.sort(function(e,t){return t.w*t.h-e.w*e.h});for(var h=0,f=0,p=0,v=0,g=Math.sqrt(u)*r.clientWidth/r.clientHeight,a=0;a<i.length;a++){var l=i[a];if(l){for(var c=0;c<l.length;c++){var d=l[c];d.isLocked||(d.positionX+=h,d.positionY+=f)}h+=l.w+t.componentSpacing,p+=l.w+t.componentSpacing,v=Math.max(v,l.h),p>g&&(f+=v+t.componentSpacing,h=0,p=0,v=0)}}}(0,i),r}).then(function(e){h.layoutNodes=e.layoutNodes,o.stop(),b()});var b=function(){!0===t.animate||!1===t.animate?m({force:!0,next:function(){r.one("layoutstop",t.stop),r.emit({type:"layoutstop",layout:r})}}):t.eles.nodes().layoutPositions(r,t,function(e){var t=h.layoutNodes[h.idToIndex[e.data("id")]];return{x:t.positionX,y:t.positionY}})};return this},r.prototype.stop=function(){return this.stopped=!0,this.thread&&this.thread.stop(),this.emit("layoutstop"),this},r.prototype.destroy=function(){return this.thread&&this.thread.stop(),this};var c=function(e,t,n){for(var r=n.eles.edges(),i=n.eles.nodes(),a={isCompound:e.hasCompoundNodes(),layoutNodes:[],idToIndex:{},nodeSize:i.size(),graphSet:[],indexToGraph:[],layoutEdges:[],edgeSize:r.size(),temperature:n.initialTemp,clientWidth:e.width(),clientHeight:e.width(),boundingBox:o.makeBoundingBox(n.boundingBox?n.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()})},u=n.eles.components(),l={},c=0;c<u.length;c++)for(var h=u[c],f=0;f<h.length;f++){var p=h[f];l[p.id()]=c}for(var c=0;c<a.nodeSize;c++){var v=i[c],g=v.layoutDimensions(n),y={};y.isLocked=v.locked(),y.id=v.data("id"),y.parentId=v.data("parent"),y.cmptId=l[v.id()],y.children=[],y.positionX=v.position("x"),y.positionY=v.position("y"),y.offsetX=0,y.offsetY=0,y.height=g.w,y.width=g.h,y.maxX=y.positionX+y.width/2,y.minX=y.positionX-y.width/2,y.maxY=y.positionY+y.height/2,y.minY=y.positionY-y.height/2,y.padLeft=parseFloat(v.style("padding")),y.padRight=parseFloat(v.style("padding")),y.padTop=parseFloat(v.style("padding")),y.padBottom=parseFloat(v.style("padding")),y.nodeRepulsion=s.fn(n.nodeRepulsion)?n.nodeRepulsion(v):n.nodeRepulsion,a.layoutNodes.push(y),a.idToIndex[y.id]=c}for(var m=[],b=0,x=-1,w=[],c=0;c<a.nodeSize;c++){var v=a.layoutNodes[c],_=v.parentId;null!=_?a.layoutNodes[a.idToIndex[_]].children.push(v.id):(m[++x]=v.id,w.push(v.id))}for(a.graphSet.push(w);b<=x;){var E=m[b++],k=a.idToIndex[E],p=a.layoutNodes[k],C=p.children;if(C.length>0){a.graphSet.push(C);for(var c=0;c<C.length;c++)m[++x]=C[c]}}for(var c=0;c<a.graphSet.length;c++)for(var P=a.graphSet[c],f=0;f<P.length;f++){var T=a.idToIndex[P[f]];a.indexToGraph[T]=c}for(var c=0;c<a.edgeSize;c++){var S=r[c],D={};D.id=S.data("id"),D.sourceId=S.data("source"),D.targetId=S.data("target");var I=s.fn(n.idealEdgeLength)?n.idealEdgeLength(S):n.idealEdgeLength,N=s.fn(n.edgeElasticity)?n.edgeElasticity(S):n.edgeElasticity,M=a.idToIndex[D.sourceId],L=a.idToIndex[D.targetId];if(a.indexToGraph[M]!=a.indexToGraph[L]){for(var B=d(D.sourceId,D.targetId,a),O=a.graphSet[B],A=0,y=a.layoutNodes[M];-1===O.indexOf(y.id);)y=a.layoutNodes[a.idToIndex[y.parentId]],A++;for(y=a.layoutNodes[L];-1===O.indexOf(y.id);)y=a.layoutNodes[a.idToIndex[y.parentId]],A++;I*=A*n.nestingFactor}D.idealLength=I,D.elasticity=N,a.layoutEdges.push(D)}return a},d=function(e,t,n){var r=h(e,t,0,n);return 2>r.count?0:r.graph},h=function e(t,n,r,i){var a=i.graphSet[r];if(-1<a.indexOf(t)&&-1<a.indexOf(n))return{count:2,graph:r};for(var o=0,s=0;s<a.length;s++){var u=a[s],l=i.idToIndex[u],c=i.layoutNodes[l].children;if(0!==c.length){var d=i.indexToGraph[i.idToIndex[c[0]]],h=e(t,n,d,i);if(0!==h.count){if(1!==h.count)return h;if(2===++o)break}}}return{count:o,graph:r}},f=function(e){if(i){console.debug("layoutNodes:");for(var t=0;t<e.nodeSize;t++){var n=e.layoutNodes[t],r="\nindex: "+t+"\nId: "+n.id+"\nChildren: "+n.children.toString()+"\nparentId: "+n.parentId+"\npositionX: "+n.positionX+"\npositionY: "+n.positionY+"\nOffsetX: "+n.offsetX+"\nOffsetY: "+n.offsetY+"\npadLeft: "+n.padLeft+"\npadRight: "+n.padRight+"\npadTop: "+n.padTop+"\npadBottom: "+n.padBottom;console.debug(r)}console.debug("idToIndex");for(var t in e.idToIndex)console.debug("Id: "+t+"\nIndex: "+e.idToIndex[t]);console.debug("Graph Set");for(var a=e.graphSet,t=0;t<a.length;t++)console.debug("Set : "+t+": "+a[t].toString());for(var r="IndexToGraph",t=0;t<e.indexToGraph.length;t++)r+="\nIndex : "+t+" Graph: "+e.indexToGraph[t];console.debug(r),r="Layout Edges";for(var t=0;t<e.layoutEdges.length;t++){var o=e.layoutEdges[t];r+="\nEdge Index: "+t+" ID: "+o.id+" SouceID: "+o.sourceId+" TargetId: "+o.targetId+" Ideal Length: "+o.idealLength}console.debug(r),r="nodeSize: "+e.nodeSize,r+="\nedgeSize: "+e.edgeSize,r+="\ntemperature: "+e.temperature,console.debug(r)}},p=function(e,t){for(var n=e.clientWidth,r=e.clientHeight,i=0;i<e.nodeSize;i++){var a=e.layoutNodes[i];0!==a.children.length||a.isLocked||(a.positionX=Math.random()*n,a.positionY=Math.random()*r)}},v=function(e,t,n){var r=n.layout,i=n.eles.nodes(),a=e.boundingBox,o={x1:1/0,x2:-1/0,y1:1/0,y2:-1/0};n.boundingBox&&(i.forEach(function(t){var n=e.layoutNodes[e.idToIndex[t.data("id")]];o.x1=Math.min(o.x1,n.positionX),o.x2=Math.max(o.x2,n.positionX),o.y1=Math.min(o.y1,n.positionY),o.y2=Math.max(o.y2,n.positionY)}),o.w=o.x2-o.x1,o.h=o.y2-o.y1),i.positions(function(t,r){var i=e.layoutNodes[e.idToIndex[t.data("id")]];if(n.boundingBox){var s=(i.positionX-o.x1)/o.w,u=(i.positionY-o.y1)/o.h;return{x:a.x1+s*a.w,y:a.y1+u*a.h}}return{x:i.positionX,y:i.positionY}}),!0!==e.ready&&(e.ready=!0,r.one("layoutready",n.ready),r.emit({type:"layoutready",layout:this}))};e.exports=r},function(e,t,n){"use strict";function r(e){this.options=i.extend({},o,e)}var i=n(1),a=n(2),o={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,avoidOverlapPadding:10,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,condense:!1,rows:void 0,cols:void 0,position:function(e){},sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};r.prototype.run=function(){var e=this.options,t=e,n=e.cy,r=t.eles,i=r.nodes().not(":parent");t.sort&&(i=i.sort(t.sort));var o=a.makeBoundingBox(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:n.width(),h:n.height()});if(0===o.h||0===o.w)i.layoutPositions(this,t,function(e){return{x:o.x1,y:o.y1}});else{var s=i.size(),u=Math.sqrt(s*o.h/o.w),l=Math.round(u),c=Math.round(o.w/o.h*u),d=function(e){return null==e?Math.min(l,c):void(Math.min(l,c)==l?l=e:c=e)},h=function(e){return null==e?Math.max(l,c):void(Math.max(l,c)==l?l=e:c=e)},f=t.rows,p=null!=t.cols?t.cols:t.columns;if(null!=f&&null!=p)l=f,c=p;else if(null!=f&&null==p)l=f,c=Math.ceil(s/l);else if(null==f&&null!=p)c=p,l=Math.ceil(s/c);else if(c*l>s){var v=d(),g=h();(v-1)*g>=s?d(v-1):(g-1)*v>=s&&h(g-1)}else for(;c*l<s;){var y=d(),m=h();(m+1)*y>=s?h(m+1):d(y+1)}var b=o.w/c,x=o.h/l;if(t.condense&&(b=0,x=0),t.avoidOverlap)for(var w=0;w<i.length;w++){var _=i[w],E=_._private.position;null!=E.x&&null!=E.y||(E.x=0,E.y=0);var k=_.layoutDimensions(t),C=t.avoidOverlapPadding,P=k.w+C,T=k.h+C;b=Math.max(b,P),x=Math.max(x,T)}for(var S={},D=function(e,t){return!!S["c-"+e+"-"+t]},I=function(e,t){S["c-"+e+"-"+t]=!0},N=0,M=0,L=function(){++M>=c&&(M=0,N++)},B={},O=0;O<i.length;O++){var A=i[O],z=t.position(A);if(z&&(void 0!==z.row||void 0!==z.col)){var R={row:z.row,col:z.col};if(void 0===R.col)for(R.col=0;D(R.row,R.col);)R.col++;else if(void 0===R.row)for(R.row=0;D(R.row,R.col);)R.row++;B[A.id()]=R,I(R.row,R.col)}}var j=function(e,t){var n=void 0,r=void 0;if(e.locked()||e.isParent())return!1;var i=B[e.id()];if(i)n=i.col*b+b/2+o.x1,r=i.row*x+x/2+o.y1;else{for(;D(N,M);)L();n=M*b+b/2+o.x1,r=N*x+x/2+o.y1,I(N,M),L()}return{x:n,y:r}};i.layoutPositions(this,t,j)}return this},e.exports=r},function(e,t,n){"use strict";e.exports=[{name:"breadthfirst",impl:n(77)},{name:"circle",impl:n(78)},{name:"concentric",impl:n(79)},{name:"cose",impl:n(80)},{name:"grid",impl:n(81)},{name:"null",impl:n(83)},{name:"preset",impl:n(84)},{name:"random",impl:n(85)}]},function(e,t,n){"use strict";function r(e){this.options=i.extend({},a,e)}var i=n(1),a={ready:function(){},stop:function(){}};r.prototype.run=function(){var e=this.options,t=e.eles,n=this;return e.cy,n.emit("layoutstart"),t.nodes().positions(function(){return{x:0,y:0}}),n.one("layoutready",e.ready),n.emit("layoutready"),n.one("layoutstop",e.stop),n.emit("layoutstop"),this},r.prototype.stop=function(){return this},e.exports=r},function(e,t,n){"use strict";function r(e){this.options=i.extend({},o,e)}var i=n(1),a=n(0),o={positions:void 0,zoom:void 0,pan:void 0,fit:!0,padding:30,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};r.prototype.run=function(){function e(e){if(null==t.positions)return null;if(i)return t.positions(e);var n=t.positions[e._private.data.id];return null==n?null:n}var t=this.options,n=t.eles,r=n.nodes(),i=a.fn(t.positions);return r.layoutPositions(this,t,function(t,n){var r=e(t);return!t.locked()&&null!=r&&r}),this},e.exports=r},function(e,t,n){"use strict";function r(e){this.options=i.extend({},o,e)}var i=n(1),a=n(2),o={fit:!0,padding:30,boundingBox:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};r.prototype.run=function(){var e=this.options,t=e.cy,n=e.eles,r=n.nodes().not(":parent"),i=a.makeBoundingBox(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()}),o=function(e,t){return{x:i.x1+Math.round(Math.random()*i.w),y:i.y1+Math.round(Math.random()*i.h)}};return r.layoutPositions(this,e,o),this},e.exports=r},function(e,t,n){"use strict";var r=n(2),i=n(0),a=n(1),o={};o.arrowShapeWidth=.3,o.registerArrowShapes=function(){var e=this.arrowShapes={},t=this,n=function(e,t,n,r,i,a,o){var s=i.x-n/2-o,u=i.x+n/2+o,l=i.y-n/2-o,c=i.y+n/2+o;return s<=e&&e<=u&&l<=t&&t<=c},o=function(e,t,n,r,i){var a=e*Math.cos(r)-t*Math.sin(r),o=e*Math.sin(r)+t*Math.cos(r),s=a*n,u=o*n;return{x:s+i.x,y:u+i.y}},s=function(e,t,n,r){for(var i=[],a=0;a<e.length;a+=2){var s=e[a],u=e[a+1];i.push(o(s,u,t,n,r))}return i},u=function(e){for(var t=[],n=0;n<e.length;n++){var r=e[n];t.push(r.x,r.y)}return t},l=function(e){return e.pstyle("width").pfValue*e.pstyle("arrow-scale").pfValue*2},c=function(o,c){i.string(c)&&(c=e[c]),e[o]=a.extend({name:o,points:[-.15,-.3,.15,-.3,.15,.3,-.15,.3],collide:function(e,t,n,i,a,o){var l=u(s(this.points,n+2*o,i,a));return r.pointInsidePolygonPoints(e,t,l)},roughCollide:n,draw:function(e,n,r,i){var a=s(this.points,n,r,i);t.arrowShapeImpl("polygon")(e,a)},spacing:function(e){return 0},gap:l},c)};c("none",{collide:a.falsify,roughCollide:a.falsify,draw:a.noop,spacing:a.zeroify,gap:a.zeroify}),c("triangle",{points:[-.15,-.3,0,0,.15,-.3]}),c("arrow","triangle"),c("triangle-backcurve",{points:e.triangle.points,controlPoint:[0,-.15],roughCollide:n,draw:function(e,n,r,i,a){var u=s(this.points,n,r,i),l=this.controlPoint,c=o(l[0],l[1],n,r,i);t.arrowShapeImpl(this.name)(e,u,c)},gap:function(e){return.985*l(e)}}),c("triangle-tee",{points:[-.15,-.3,0,0,.15,-.3,-.15,-.3],pointsTee:[-.15,-.4,-.15,-.5,.15,-.5,.15,-.4],collide:function(e,t,n,i,a,o,l){var c=u(s(this.points,n+2*l,i,a)),d=u(s(this.pointsTee,n+2*l,i,a));return r.pointInsidePolygonPoints(e,t,c)||r.pointInsidePolygonPoints(e,t,d)},draw:function(e,n,r,i,a){var o=s(this.points,n,r,i),u=s(this.pointsTee,n,r,i);t.arrowShapeImpl(this.name)(e,o,u)}}),c("triangle-cross",{points:[-.15,-.3,0,0,.15,-.3,-.15,-.3],baseCrossLinePts:[-.15,-.4,-.15,-.4,.15,-.4,.15,-.4],crossLinePts:function(e,t){var n=this.baseCrossLinePts.slice(),r=t/e;return n[3]=n[3]-r,n[5]=n[5]-r,n},collide:function(e,t,n,i,a,o,l){var c=u(s(this.points,n+2*l,i,a)),d=u(s(this.crossLinePts(n,o),n+2*l,i,a));return r.pointInsidePolygonPoints(e,t,c)||r.pointInsidePolygonPoints(e,t,d)},draw:function(e,n,r,i,a){var o=s(this.points,n,r,i),u=s(this.crossLinePts(n,a),n,r,i);t.arrowShapeImpl(this.name)(e,o,u)}}),c("vee",{points:[-.15,-.3,0,0,.15,-.3,0,-.15],gap:function(e){return.985*l(e)}}),c("circle",{radius:.15,collide:function(e,t,n,r,i,a,o){var s=i;return Math.pow(s.x-e,2)+Math.pow(s.y-t,2)<=Math.pow((n+2*o)*this.radius,2)},draw:function(e,n,r,i,a){t.arrowShapeImpl(this.name)(e,i.x,i.y,this.radius*n)},spacing:function(e){return t.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.radius}}),c("tee",{points:[-.15,0,-.15,-.1,.15,-.1,.15,0],spacing:function(e){return 1},gap:function(e){return 1}}),c("square",{points:[-.15,0,.15,0,.15,-.3,-.15,-.3]}),c("diamond",{points:[-.15,-.15,0,-.3,.15,-.15,0,0],gap:function(e){return e.pstyle("width").pfValue*e.pstyle("arrow-scale").value}})},e.exports=o},function(e,t,n){"use strict";var r=n(4),i=n(2),a=n(1),r=n(4),o={};o.projectIntoViewport=function(e,t){var n=this.cy,r=this.findContainerClientCoords(),i=r[0],a=r[1],o=r[4],s=n.pan(),u=n.zoom();return[((e-i)/o-s.x)/u,((t-a)/o-s.y)/u]},o.findContainerClientCoords=function(){if(this.containerBB)return this.containerBB;var e=this.container,t=e.getBoundingClientRect(),n=r.getComputedStyle(e),i=function(e){return parseFloat(n.getPropertyValue(e))},a={left:i("padding-left"),right:i("padding-right"),top:i("padding-top"),bottom:i("padding-bottom")},o={left:i("border-left-width"),right:i("border-right-width"),top:i("border-top-width"),bottom:i("border-bottom-width")},s=e.clientWidth,u=e.clientHeight,l=a.left+a.right,c=a.top+a.bottom,d=o.left+o.right,h=(o.top+o.bottom,t.width/(s+d)),f=s-l,p=u-c,v=(t.width,t.height,t.left+a.left+o.left),g=t.top+a.top+o.top;return this.containerBB=[v,g,f,p,h]},o.invalidateContainerClientCoordsCache=function(){this.containerBB=null},o.findNearestElement=function(e,t,n,r){return this.findNearestElements(e,t,n,r)[0]},o.findNearestElements=function(e,t,n,r){function o(e,t){if(e.isNode()){if(d)return;d=e,v.push(e)}if(e.isEdge()&&(null==t||t<w))if(c){if(c.pstyle("z-index").value===e.pstyle("z-index").value)for(var n=0;n<v.length;n++)if(v[n].isEdge()){v[n]=e,c=e,w=null!=t?t:w;break}}else v.push(e),c=e,w=null!=t?t:w}function s(n){var r=n.outerWidth()+2*b,i=n.outerHeight()+2*b,a=r/2,s=i/2,u=n.position();if(u.x-a<=e&&e<=u.x+a&&u.y-s<=t&&t<=u.y+s&&f.nodeShapes[h.getNodeShape(n)].checkPoint(e,t,0,r,i,u.x,u.y))return o(n,0),!0}function u(e,t,n){return a.getPrefixedProperty(e,t,n)}function l(n,r){var a,s=n._private,l=x;a=r?r+"-":"";var c=n.pstyle(a+"label").value;if("yes"===n.pstyle("text-events").strValue&&c){var d=s.rstyle,h=n.pstyle("text-border-width").pfValue,f=n.pstyle("text-background-padding").pfValue,p=u(d,"labelWidth",r)+h+2*l+2*f,v=u(d,"labelHeight",r)+h+2*l+2*f,g=u(d,"labelX",r),y=u(d,"labelY",r),m=u(s.rscratch,"labelAngle",r),b=g-p/2,w=g+p/2,_=y-v/2,E=y+v/2;if(m){var k=Math.cos(m),C=Math.sin(m),P=function(e,t){return e-=g,t-=y,{x:e*k-t*C+g,y:e*C+t*k+y}},T=P(b,_),S=P(b,E),D=P(w,_),I=P(w,E),N=[T.x,T.y,D.x,D.y,I.x,I.y,S.x,S.y];if(i.pointInsidePolygonPoints(e,t,N))return o(n),!0}else{var M={w:p,h:v,x1:b,x2:w,y1:_,y2:E};if(i.inBoundingBox(M,e,t))return o(n),!0}}}var c,d,h=this,f=this,p=f.getCachedZSortedEles(),v=[],g=f.cy.zoom(),y=f.cy.hasCompoundNodes(),m=(r?24:8)/g,b=(r?8:2)/g,x=(r?8:2)/g,w=1/0;n&&(p=p.interactive);for(var _=p.length-1;_>=0;_--){var E=p[_];E.isNode()?s(E)||l(E):function(n){var r,a=n._private,u=a.rscratch,l=n.pstyle("width").pfValue,c=n.pstyle("arrow-scale").value,d=l/2+m,p=d*d,g=2*d,b=a.source,x=a.target;if("segments"===u.edgeType||"straight"===u.edgeType||"haystack"===u.edgeType){for(var w=u.allpts,_=0;_+3<w.length;_+=2)if(i.inLineVicinity(e,t,w[_],w[_+1],w[_+2],w[_+3],g)&&p>(r=i.sqdistToFiniteLine(e,t,w[_],w[_+1],w[_+2],w[_+3])))return o(n,r),!0}else if("bezier"===u.edgeType||"multibezier"===u.edgeType||"self"===u.edgeType||"compound"===u.edgeType)for(var w=u.allpts,_=0;_+5<u.allpts.length;_+=4)if(i.inBezierVicinity(e,t,w[_],w[_+1],w[_+2],w[_+3],w[_+4],w[_+5],g)&&p>(r=i.sqdistToQuadraticBezier(e,t,w[_],w[_+1],w[_+2],w[_+3],w[_+4],w[_+5])))return o(n,r),!0;for(var b=b||a.source,x=x||a.target,E=h.getArrowWidth(l,c),k=[{name:"source",x:u.arrowStartX,y:u.arrowStartY,angle:u.srcArrowAngle},{name:"target",x:u.arrowEndX,y:u.arrowEndY,angle:u.tgtArrowAngle},{name:"mid-source",x:u.midX,y:u.midY,angle:u.midsrcArrowAngle},{name:"mid-target",x:u.midX,y:u.midY,angle:u.midtgtArrowAngle}],_=0;_<k.length;_++){var C=k[_],P=f.arrowShapes[n.pstyle(C.name+"-arrow-shape").value],T=n.pstyle("width").pfValue;if(P.roughCollide(e,t,E,C.angle,{x:C.x,y:C.y},T,m)&&P.collide(e,t,E,C.angle,{x:C.x,y:C.y},T,m))return o(n),!0}y&&v.length>0&&(s(b),s(x))}(E)||l(E)||l(E,"source")||l(E,"target")}return v},o.getAllInBox=function(e,t,n,r){var a=this.getCachedZSortedEles().interactive,o=[],s=Math.min(e,n),u=Math.max(e,n),l=Math.min(t,r),c=Math.max(t,r);e=s,n=u,t=l,r=c;for(var d=i.makeBoundingBox({x1:e,y1:t,x2:n,y2:r}),h=0;h<a.length;h++){var f=a[h];if(f.isNode()){var p=f,v=p.boundingBox({includeNodes:!0,includeEdges:!1,includeLabels:!1});i.boundingBoxesIntersect(d,v)&&!i.boundingBoxInBoundingBox(v,d)&&o.push(p)}else{var g=f,y=g._private,m=y.rscratch;if(null!=m.startX&&null!=m.startY&&!i.inBoundingBox(d,m.startX,m.startY))continue;if(null!=m.endX&&null!=m.endY&&!i.inBoundingBox(d,m.endX,m.endY))continue;if("bezier"===m.edgeType||"multibezier"===m.edgeType||"self"===m.edgeType||"compound"===m.edgeType||"segments"===m.edgeType||"haystack"===m.edgeType){for(var b=y.rstyle.bezierPts||y.rstyle.linePts||y.rstyle.haystackPts,x=!0,w=0;w<b.length;w++)if(!i.pointInBoundingBox(d,b[w])){x=!1;break}x&&o.push(g)}else"haystack"!==m.edgeType&&"straight"!==m.edgeType||o.push(g)}}return o},e.exports=o},function(e,t,n){"use strict";var r=n(2),i={};i.calculateArrowAngles=function(e){var t,n,i,a,o,s,u,l,c=e._private.rscratch,d="haystack"===c.edgeType,h="bezier"===c.edgeType,f="multibezier"===c.edgeType,p="segments"===c.edgeType,v="compound"===c.edgeType,g="self"===c.edgeType;if(d?(i=c.haystackPts[0],a=c.haystackPts[1],o=c.haystackPts[2],s=c.haystackPts[3]):(i=c.arrowStartX,a=c.arrowStartY,o=c.arrowEndX,s=c.arrowEndY),u=c.midX,l=c.midY,p)t=i-c.segpts[0],n=a-c.segpts[1];else if(f||v||g||h){var y=c.allpts,m=r.qbezierAt(y[0],y[2],y[4],.1),b=r.qbezierAt(y[1],y[3],y[5],.1);
t=i-m,n=a-b}else t=i-u,n=a-l;c.srcArrowAngle=r.getAngleFromDisp(t,n);var u=c.midX,l=c.midY;if(d&&(u=(i+o)/2,l=(a+s)/2),t=o-i,n=s-a,p){var y=c.allpts;if(y.length/2%2==0){var x=y.length/2,w=x-2;t=y[x]-y[w],n=y[x+1]-y[w+1]}else{var x=y.length/2-1,w=x-2,_=x+2;t=y[x]-y[w],n=y[x+1]-y[w+1]}}else if(f||v||g){var E,k,C,P,y=c.allpts,T=c.ctrlpts;if(T.length/2%2==0){var S=y.length/2-1,D=S+2,I=D+2;E=r.qbezierAt(y[S],y[D],y[I],0),k=r.qbezierAt(y[S+1],y[D+1],y[I+1],0),C=r.qbezierAt(y[S],y[D],y[I],1e-4),P=r.qbezierAt(y[S+1],y[D+1],y[I+1],1e-4)}else{var D=y.length/2-1,S=D-2,I=D+2;E=r.qbezierAt(y[S],y[D],y[I],.4999),k=r.qbezierAt(y[S+1],y[D+1],y[I+1],.4999),C=r.qbezierAt(y[S],y[D],y[I],.5),P=r.qbezierAt(y[S+1],y[D+1],y[I+1],.5)}t=C-E,n=P-k}if(c.midtgtArrowAngle=r.getAngleFromDisp(t,n),c.midDispX=t,c.midDispY=n,t*=-1,n*=-1,p){var y=c.allpts;if(y.length/2%2==0);else{var x=y.length/2-1,_=x+2;t=-(y[_]-y[x]),n=-(y[_+1]-y[x+1])}}if(c.midsrcArrowAngle=r.getAngleFromDisp(t,n),p)t=o-c.segpts[c.segpts.length-2],n=s-c.segpts[c.segpts.length-1];else if(f||v||g||h){var y=c.allpts,N=y.length,m=r.qbezierAt(y[N-6],y[N-4],y[N-2],.9),b=r.qbezierAt(y[N-5],y[N-3],y[N-1],.9);t=o-m,n=s-b}else t=o-u,n=s-l;c.tgtArrowAngle=r.getAngleFromDisp(t,n)},i.getArrowWidth=i.getArrowHeight=function(e,t){var n=this.arrowWidthCache=this.arrowWidthCache||{},r=n[e+", "+t];return r||(r=Math.max(Math.pow(13.37*e,.9),29)*t,n[e+", "+t]=r,r)},e.exports=i},function(e,t,n){"use strict";function r(e){var t=[];if(null!=e){for(var n=0;n<e.length;n+=2){var r=e[n],i=e[n+1];t.push({x:r,y:i})}return t}}var i=n(2),a=n(0),o={};o.findEdgeControlPoints=function(e){if(e&&0!==e.length){for(var t,n=this,r=n.cy,o=r.hasCompoundNodes(),s={},u=[],l=[],c=0;c<e.length;c++){var d=e[c],h=d._private,f=h.data,p=d.pstyle("curve-style").value,v="unbundled-bezier"===p||"segments"===p,g="unbundled-bezier"===p||"bezier"===p;if("none"!==d.pstyle("display").value)if("haystack"!==p){var y=f.source,m=f.target;t=y>m?m+"$-$"+y:y+"$-$"+m,v&&(t="unbundled$-$"+f.id);var b=s[t];null==b&&(b=s[t]=[],u.push(t)),b.push(d),v&&(b.hasUnbundled=!0),g&&(b.hasBezier=!0)}else l.push(d)}for(var x,w,_,E,k,C,P,T,S,D,I,N,M=0;M<u.length;M++){t=u[M];var L=s[t];if(L.sort(function(e,t){return e.poolIndex()-t.poolIndex()}),x=L[0]._private.source,w=L[0]._private.target,!L.hasUnbundled&&x.id()>w.id()){var B=x;x=w,w=B}_=x.position(),E=w.position(),k=x.outerWidth(),C=x.outerHeight(),P=w.outerWidth(),T=w.outerHeight(),S=n.nodeShapes[this.getNodeShape(x)],D=n.nodeShapes[this.getNodeShape(w)],N=!1;for(var d,O,A,z={north:0,west:0,south:0,east:0,northwest:0,southwest:0,northeast:0,southeast:0},R=_.x,j=_.y,F=k,V=C,q=E.x,Y=E.y,X=P,W=T,U=L.length,c=0;c<L.length;c++){d=L[c],O=d._private,A=O.rscratch;var H=A.lastEdgeIndex,$=c,G=A.lastNumEdges,p=d.pstyle("curve-style").value,v="unbundled-bezier"===p||"segments"===p,Z=x.id()!==d.source().id(),Q=d.pstyle("control-point-distances"),K=d.pstyle("loop-direction").pfValue,J=d.pstyle("loop-sweep").pfValue,ee=d.pstyle("control-point-weights"),te=Q&&ee?Math.min(Q.value.length,ee.value.length):1,ne=d.pstyle("control-point-step-size").pfValue,re=Q?Q.pfValue[0]:void 0,ie=ee.value[0],ae=d.pstyle("edge-distances").value,oe=d.pstyle("segment-weights"),se=d.pstyle("segment-distances"),ue=Math.min(oe.pfValue.length,se.pfValue.length),le=d.pstyle("source-endpoint").value,ce=d.pstyle("target-endpoint").value,de=d.pstyle("source-arrow-shape").value,he=d.pstyle("target-arrow-shape").value,fe=d.pstyle("arrow-scale").value,pe=d.pstyle("width").pfValue,ve=A.lastSrcCtlPtX,ge=A.lastSrcCtlPtY,ye=A.lastSrcCtlPtW,me=A.lastSrcCtlPtH,be=A.lastTgtCtlPtX,xe=A.lastTgtCtlPtY,we=A.lastTgtCtlPtW,_e=A.lastTgtCtlPtH,Ee=A.lastCurveStyle,ke=p,Ce=A.lastCtrlptDists,Pe=Q?Q.strValue:null,Te=A.lastCtrlptWs,Se=ee.strValue,De=A.lastSegmentWs,Ie=oe.strValue,Ne=A.lastSegmentDs,Me=se.strValue,Le=A.lastStepSize,Be=ne,Oe=A.lastLoopDir,Ae=K,ze=A.lastLoopSwp,Re=J,je=A.lastEdgeDistances,Fe=ae,Ve=A.lastSrcEndpt,qe=le,Ye=A.lastTgtEndpt,Xe=ce,We=A.lastSrcArr,Ue=de,He=A.lastTgtArr,$e=he,Ge=A.lastLineW,Ze=pe,Qe=A.lastArrScl,Ke=fe;A.badBezier=!!N;var Je;if(ve===R&&ge===j&&ye===F&&me===V&&be===q&&xe===Y&&we===X&&_e===W&&Ee===ke&&Ce===Pe&&Te===Se&&De===Ie&&Ne===Me&&Le===Be&&Oe===Ae&&ze===Re&&je===Fe&&Ve===qe&&Ye===Xe&&We===Ue&&He===$e&&Ge===Ze&&Qe===Ke&&(H===$&&G===U||v)?Je=!0:(Je=!1,A.lastSrcCtlPtX=R,A.lastSrcCtlPtY=j,A.lastSrcCtlPtW=F,A.lastSrcCtlPtH=V,A.lastTgtCtlPtX=q,A.lastTgtCtlPtY=Y,A.lastTgtCtlPtW=X,A.lastTgtCtlPtH=W,A.lastEdgeIndex=$,A.lastNumEdges=U,A.lastCurveStyle=ke,A.lastCtrlptDists=Pe,A.lastCtrlptWs=Se,A.lastSegmentDs=Me,A.lastSegmentWs=Ie,A.lastStepSize=Be,A.lastLoopDir=Ae,A.lastLoopSwp=Re,A.lastEdgeDistances=Fe,A.lastSrcEndpt=qe,A.lastTgtEndpt=Xe,A.lastSrcArr=Ue,A.lastTgtArr=$e,A.lastLineW=Ze,A.lastArrScl=Ke),!Je){if(!L.calculatedIntersection&&x!==w&&(L.hasBezier||L.hasUnbundled)){L.calculatedIntersection=!0;var et=S.intersectLine(_.x,_.y,k,C,E.x,E.y,0);L.srcIntn=et;var tt=D.intersectLine(E.x,E.y,P,T,_.x,_.y,0);L.tgtIntn=tt;var nt={x1:et[0],x2:tt[0],y1:et[1],y2:tt[1]},rt={x1:_.x,x2:E.x,y1:_.y,y2:E.y},it=tt[1]-et[1],at=tt[0]-et[0],ot=Math.sqrt(at*at+it*it),st={x:at,y:it},ut={x:st.x/ot,y:st.y/ot};I={x:-ut.y,y:ut.x},D.checkPoint(et[0],et[1],0,P,T,E.x,E.y)&&S.checkPoint(tt[0],tt[1],0,k,C,_.x,_.y)&&(I={},N=!0)}if(Z?(A.srcIntn=L.tgtIntn,A.tgtIntn=L.srcIntn):(A.srcIntn=L.srcIntn,A.tgtIntn=L.tgtIntn),x===w){A.edgeType="self";var lt=c,ct=ne;v&&(lt=0,ct=re);var dt=K-Math.PI/2,ht=dt-J/2,ft=dt+J/2,pt=String(K+"_"+J);lt=void 0===z[pt]?z[pt]=0:++z[pt],A.ctrlpts=[_.x+1.4*Math.cos(ht)*ct*(lt/3+1),_.y+1.4*Math.sin(ht)*ct*(lt/3+1),_.x+1.4*Math.cos(ft)*ct*(lt/3+1),_.y+1.4*Math.sin(ft)*ct*(lt/3+1)]}else if(o&&(x.isParent()||x.isChild()||w.isParent()||w.isChild())&&(x.parents().anySame(w)||w.parents().anySame(x))){A.edgeType="compound",A.badBezier=!1;var lt=c,ct=ne;v&&(lt=0,ct=re);var vt={x:_.x-k/2,y:_.y-C/2},gt={x:E.x-P/2,y:E.y-T/2},yt={x:Math.min(vt.x,gt.x),y:Math.min(vt.y,gt.y)},mt=Math.max(.5,Math.log(.01*k)),bt=Math.max(.5,Math.log(.01*P));A.ctrlpts=[yt.x,yt.y-(1+Math.pow(50,1.12)/100)*ct*(lt/3+1)*mt,yt.x-(1+Math.pow(50,1.12)/100)*ct*(lt/3+1)*bt,yt.y]}else if("segments"===p){A.edgeType="segments",A.segpts=[];for(var xt=0;xt<ue;xt++){var wt=oe.pfValue[xt],_t=se.pfValue[xt],Et=1-wt,kt=wt,Ct="node-position"===ae?rt:nt,Pt={x:Ct.x1*Et+Ct.x2*kt,y:Ct.y1*Et+Ct.y2*kt};A.segpts.push(Pt.x+I.x*_t,Pt.y+I.y*_t)}}else if(L.length%2!=1||c!==Math.floor(L.length/2)||v){var Tt=v;A.edgeType=Tt?"multibezier":"bezier",A.ctrlpts=[];for(var St=0;St<te;St++){var Dt,It=(.5-L.length/2+c)*ne,Nt=i.signum(It);Tt&&(re=Q?Q.pfValue[St]:ne,ie=ee.value[St]),Dt=v?re:void 0!==re?Nt*re:void 0;var Mt=void 0!==Dt?Dt:It,Et=1-ie,kt=ie;if(Z){var B=Et;Et=kt,kt=B}var Ct="node-position"===ae?rt:nt,Pt={x:Ct.x1*Et+Ct.x2*kt,y:Ct.y1*Et+Ct.y2*kt};A.ctrlpts.push(Pt.x+I.x*Mt,Pt.y+I.y*Mt)}}else A.edgeType="straight";this.findEndpoints(d);var Lt=!a.number(A.startX)||!a.number(A.startY),Bt=!a.number(A.arrowStartX)||!a.number(A.arrowStartY),Ot=!a.number(A.endX)||!a.number(A.endY),At=!a.number(A.arrowEndX)||!a.number(A.arrowEndY),zt=this.getArrowWidth(d.pstyle("width").pfValue,d.pstyle("arrow-scale").value)*this.arrowShapeWidth,Rt=3*zt;if("bezier"===A.edgeType){var jt=i.dist({x:A.ctrlpts[0],y:A.ctrlpts[1]},{x:A.startX,y:A.startY}),Ft=jt<Rt,Vt=i.dist({x:A.ctrlpts[0],y:A.ctrlpts[1]},{x:A.endX,y:A.endY}),qt=Vt<Rt,Yt=!1;if(Lt||Bt||Ft){Yt=!0;var Xt={x:A.ctrlpts[0]-_.x,y:A.ctrlpts[1]-_.y},Wt=Math.sqrt(Xt.x*Xt.x+Xt.y*Xt.y),Ut={x:Xt.x/Wt,y:Xt.y/Wt},Ht=Math.max(k,C),$t={x:A.ctrlpts[0]+2*Ut.x*Ht,y:A.ctrlpts[1]+2*Ut.y*Ht},Gt=S.intersectLine(_.x,_.y,k,C,$t.x,$t.y,0);Ft?(A.ctrlpts[0]=A.ctrlpts[0]+Ut.x*(Rt-jt),A.ctrlpts[1]=A.ctrlpts[1]+Ut.y*(Rt-jt)):(A.ctrlpts[0]=Gt[0]+Ut.x*Rt,A.ctrlpts[1]=Gt[1]+Ut.y*Rt)}if(Ot||At||qt){Yt=!0;var Xt={x:A.ctrlpts[0]-E.x,y:A.ctrlpts[1]-E.y},Wt=Math.sqrt(Xt.x*Xt.x+Xt.y*Xt.y),Ut={x:Xt.x/Wt,y:Xt.y/Wt},Ht=Math.max(k,C),$t={x:A.ctrlpts[0]+2*Ut.x*Ht,y:A.ctrlpts[1]+2*Ut.y*Ht},Zt=D.intersectLine(E.x,E.y,P,T,$t.x,$t.y,0);qt?(A.ctrlpts[0]=A.ctrlpts[0]+Ut.x*(Rt-Vt),A.ctrlpts[1]=A.ctrlpts[1]+Ut.y*(Rt-Vt)):(A.ctrlpts[0]=Zt[0]+Ut.x*Rt,A.ctrlpts[1]=Zt[1]+Ut.y*Rt)}Yt&&this.findEndpoints(d)}if("multibezier"===A.edgeType||"bezier"===A.edgeType||"self"===A.edgeType||"compound"===A.edgeType){A.allpts=[],A.allpts.push(A.startX,A.startY);for(var St=0;St+1<A.ctrlpts.length;St+=2)A.allpts.push(A.ctrlpts[St],A.ctrlpts[St+1]),St+3<A.ctrlpts.length&&A.allpts.push((A.ctrlpts[St]+A.ctrlpts[St+2])/2,(A.ctrlpts[St+1]+A.ctrlpts[St+3])/2);A.allpts.push(A.endX,A.endY);var Qt,Kt;A.ctrlpts.length/2%2==0?(Qt=A.allpts.length/2-1,A.midX=A.allpts[Qt],A.midY=A.allpts[Qt+1]):(Qt=A.allpts.length/2-3,Kt=.5,A.midX=i.qbezierAt(A.allpts[Qt],A.allpts[Qt+2],A.allpts[Qt+4],Kt),A.midY=i.qbezierAt(A.allpts[Qt+1],A.allpts[Qt+3],A.allpts[Qt+5],Kt))}else if("straight"===A.edgeType)A.allpts=[A.startX,A.startY,A.endX,A.endY],A.midX=(A.startX+A.endX+A.arrowStartX+A.arrowEndX)/4,A.midY=(A.startY+A.endY+A.arrowStartY+A.arrowEndY)/4;else if("segments"===A.edgeType)if(A.allpts=[],A.allpts.push(A.startX,A.startY),A.allpts.push.apply(A.allpts,A.segpts),A.allpts.push(A.endX,A.endY),A.segpts.length%4==0){var Jt=A.segpts.length/2,en=Jt-2;A.midX=(A.segpts[en]+A.segpts[Jt])/2,A.midY=(A.segpts[en+1]+A.segpts[Jt+1])/2}else{var en=A.segpts.length/2-1;A.midX=A.segpts[en],A.midY=A.segpts[en+1]}this.storeEdgeProjections(d),this.calculateArrowAngles(d)}this.recalculateEdgeLabelProjections(d),this.calculateLabelAngles(d)}}for(var c=0;c<l.length;c++){var d=l[c],h=d._private,tn=h.rscratch,A=tn;if(!tn.haystack){var nn=2*Math.random()*Math.PI;tn.source={x:Math.cos(nn),y:Math.sin(nn)};var nn=2*Math.random()*Math.PI;tn.target={x:Math.cos(nn),y:Math.sin(nn)}}var x=h.source,w=h.target,_=x.position(),E=w.position(),k=x.width(),P=w.width(),C=x.height(),T=w.height(),Ht=d.pstyle("haystack-radius").value,rn=Ht/2;A.haystackPts=A.allpts=[A.source.x*k*rn+_.x,A.source.y*C*rn+_.y,A.target.x*P*rn+E.x,A.target.y*T*rn+E.y],A.midX=(A.allpts[0]+A.allpts[2])/2,A.midY=(A.allpts[1]+A.allpts[3])/2,tn.edgeType=tn.lastCurveStyle="haystack",tn.haystack=!0,this.storeEdgeProjections(d),this.calculateArrowAngles(d),this.recalculateEdgeLabelProjections(d),this.calculateLabelAngles(d)}}},o.getSegmentPoints=function(e){var t=e[0]._private.rscratch;if("segments"===t.edgeType)return r(t.segpts)},o.getControlPoints=function(e){var t=e[0]._private.rscratch,n=t.edgeType;if("bezier"===n||"multibezier"===n)return r(t.ctrlpts)},o.getEdgeMidpoint=function(e){var t=e[0]._private.rscratch;return{x:t.midX,y:t.midY}},e.exports=o},function(e,t,n){"use strict";var r=n(2),i=n(0),a={};a.manualEndptToPx=function(e,t){var n=this,r=e.position(),i=e.outerWidth(),a=e.outerHeight();if(2===t.value.length){var o=[t.pfValue[0],t.pfValue[1]];return"%"===t.units[0]&&(o[0]=o[0]*i),"%"===t.units[1]&&(o[1]=o[1]*a),o[0]+=r.x,o[1]+=r.y,o}var s=t.pfValue[0];s=-Math.PI/2+s;var u=2*Math.max(i,a),l=[r.x+Math.cos(s)*u,r.y+Math.sin(s)*u];return n.nodeShapes[this.getNodeShape(e)].intersectLine(r.x,r.y,i,a,l[0],l[1],0)},a.findEndpoints=function(e){var t=this,n=void 0,a=e.source()[0],o=e.target()[0],s=a.position(),u=o.position(),l=e.pstyle("target-arrow-shape").value,c=e.pstyle("source-arrow-shape").value,d=e.pstyle("target-distance-from-node").pfValue,h=e.pstyle("source-distance-from-node").pfValue,f=e._private.rscratch,p=f.edgeType,v="self"===p||"compound"===p,g="bezier"===p||"multibezier"===p||v,y="bezier"!==p,m="straight"===p||"segments"===p,b="segments"===p,x=g||y||m,w=e.pstyle("source-endpoint"),_=v?"outside-to-node":w.value,E=e.pstyle("target-endpoint"),k=v?"outside-to-node":E.value;f.srcManEndpt=w,f.tgtManEndpt=E;var C=void 0,P=void 0,T=void 0,S=void 0;if(g){var D=[f.ctrlpts[0],f.ctrlpts[1]];C=y?[f.ctrlpts[f.ctrlpts.length-2],f.ctrlpts[f.ctrlpts.length-1]]:D,P=D}else if(m){var I=b?f.segpts.slice(0,2):[u.x,u.y],N=b?f.segpts.slice(f.segpts.length-2):[s.x,s.y];C=N,P=I}"inside-to-node"===k?n=[u.x,u.y]:E.units?n=this.manualEndptToPx(o,E):"outside-to-line"===k?n=f.tgtIntn:("outside-to-node"===k?T=C:"outside-to-line"===k&&(T=[s.x,s.y]),n=t.nodeShapes[this.getNodeShape(o)].intersectLine(u.x,u.y,o.outerWidth(),o.outerHeight(),T[0],T[1],0));var M=r.shortenIntersection(n,C,t.arrowShapes[l].spacing(e)+d),L=r.shortenIntersection(n,C,t.arrowShapes[l].gap(e)+d);f.endX=L[0],f.endY=L[1],f.arrowEndX=M[0],f.arrowEndY=M[1],"inside-to-node"===_?n=[s.x,s.y]:w.units?n=this.manualEndptToPx(a,w):"outside-to-line"===_?n=f.srcIntn:("outside-to-node"===_?S=P:"outside-to-line"===_&&(S=[u.x,u.y]),n=t.nodeShapes[this.getNodeShape(a)].intersectLine(s.x,s.y,a.outerWidth(),a.outerHeight(),S[0],S[1],0));var B=r.shortenIntersection(n,P,t.arrowShapes[c].spacing(e)+h),O=r.shortenIntersection(n,P,t.arrowShapes[c].gap(e)+h);f.startX=O[0],f.startY=O[1],f.arrowStartX=B[0],f.arrowStartY=B[1],x&&(i.number(f.startX)&&i.number(f.startY)&&i.number(f.endX)&&i.number(f.endY)?f.badLine=!1:f.badLine=!0)},a.getSourceEndpoint=function(e){var t=e[0]._private.rscratch;switch(t.edgeType){case"haystack":return{x:t.haystackPts[0],y:t.haystackPts[1]};default:return{x:t.arrowStartX,y:t.arrowStartY}}},a.getTargetEndpoint=function(e){var t=e[0]._private.rscratch;switch(t.edgeType){case"haystack":return{x:t.haystackPts[2],y:t.haystackPts[3]};default:return{x:t.arrowEndX,y:t.arrowEndY}}},e.exports=a},function(e,t,n){"use strict";function r(e,t,n){for(var r=function(e,t,n,r){return i.qbezierAt(e,t,n,r)},a=t._private,o=a.rstyle.bezierPts,s=0;s<e.bezierProjPcts.length;s++){var u=e.bezierProjPcts[s];o.push({x:r(n[0],n[2],n[4],u),y:r(n[1],n[3],n[5],u)})}}var i=n(2),a={};a.storeEdgeProjections=function(e){var t=e._private,n=t.rscratch,i=n.edgeType;if(t.rstyle.bezierPts=null,t.rstyle.linePts=null,t.rstyle.haystackPts=null,"multibezier"===i||"bezier"===i||"self"===i||"compound"===i)for(var a=(t.rstyle.bezierPts=[],0);a+5<n.allpts.length;a+=4)r(this,e,n.allpts.slice(a,a+6));else if("segments"===i)for(var o=t.rstyle.linePts=[],a=0;a+1<n.allpts.length;a+=2)o.push({x:n.allpts[a],y:n.allpts[a+1]});else if("haystack"===i){var s=n.haystackPts;t.rstyle.haystackPts=[{x:s[0],y:s[1]},{x:s[2],y:s[3]}]}t.rstyle.arrowWidth=this.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.arrowShapeWidth},a.recalculateEdgeProjections=function(e){this.findEdgeControlPoints(e)},e.exports=a},function(e,t,n){"use strict";var r=n(1),i={};[n(87),n(88),n(89),n(90),n(91),n(93),n(94),n(95),n(96)].forEach(function(e){r.extend(i,e)}),e.exports=i},function(e,t,n){"use strict";var r=n(2),i=n(0),a=n(1),o={};o.recalculateNodeLabelProjection=function(e){var t=e.pstyle("label").strValue;if(!i.emptyString(t)){var n,r,a=e._private,o=e.width(),s=e.height(),u=e.padding(),l=e.position(),c=e.pstyle("text-halign").strValue,d=e.pstyle("text-valign").strValue,h=a.rscratch,f=a.rstyle;switch(c){case"left":n=l.x-o/2-u;break;case"right":n=l.x+o/2+u;break;default:n=l.x}switch(d){case"top":r=l.y-s/2-u;break;case"bottom":r=l.y+s/2+u;break;default:r=l.y}h.labelX=n,h.labelY=r,f.labelX=n,f.labelY=r,this.applyLabelDimensions(e)}},o.recalculateEdgeLabelProjections=function(e){var t,n=e._private,i=n.rscratch,o=this,s={mid:e.pstyle("label").strValue,source:e.pstyle("source-label").strValue,target:e.pstyle("target-label").strValue};if(s.mid||s.source||s.target){t={x:i.midX,y:i.midY};var u=function(e,t,r){a.setPrefixedProperty(n.rscratch,e,t,r),a.setPrefixedProperty(n.rstyle,e,t,r)};u("labelX",null,t.x),u("labelY",null,t.y);var l=function e(){function t(e,t,n,i,a){var o=r.dist(t,n),s=e.segments[e.segments.length-1],u={p0:t,p1:n,t0:i,t1:a,startDist:s?s.startDist+s.length:0,length:o};e.segments.push(u),e.length+=o}if(e.cache)return e.cache;for(var a=[],s=0;s+5<i.allpts.length;s+=4){var u={x:i.allpts[s],y:i.allpts[s+1]},l={x:i.allpts[s+2],y:i.allpts[s+3]},c={x:i.allpts[s+4],y:i.allpts[s+5]};a.push({p0:u,p1:l,p2:c,startDist:0,length:0,segments:[]})}for(var d=n.rstyle.bezierPts,h=o.bezierProjPcts.length,s=0;s<a.length;s++){var f=a[s],p=a[s-1];p&&(f.startDist=p.startDist+p.length),t(f,f.p0,d[s*h],0,o.bezierProjPcts[0]);for(var v=0;v<h-1;v++)t(f,d[s*h+v],d[s*h+v+1],o.bezierProjPcts[v],o.bezierProjPcts[v+1]);t(f,d[s*h+h-1],f.p2,o.bezierProjPcts[h-1],1)}return e.cache=a},c=function(n){var a,o="source"===n;if(s[n]){var c=e.pstyle(n+"-text-offset").pfValue,d=function(e,t){var n=t.x-e.x,r=t.y-e.y;return Math.atan(r/n)};switch(i.edgeType){case"self":case"compound":case"bezier":case"multibezier":for(var h,f=l(),p=0,v=0,g=0;g<f.length;g++){for(var y=f[o?g:f.length-1-g],m=0;m<y.segments.length;m++){var b=y.segments[o?m:y.segments.length-1-m],x=g===f.length-1&&m===y.segments.length-1;if(p=v,(v+=b.length)>=c||x){h={cp:y,segment:b};break}}if(h)break}var y=h.cp,b=h.segment,w=(c-p)/b.length,_=b.t1-b.t0,E=o?b.t0+_*w:b.t1-_*w;E=r.bound(0,E,1),t=r.qbezierPtAt(y.p0,y.p1,y.p2,E),a=function(e,t,n,i){var a=r.bound(0,i-.001,1),o=r.bound(0,i+.001,1),s=r.qbezierPtAt(e,t,n,a),u=r.qbezierPtAt(e,t,n,o);return d(s,u)}(y.p0,y.p1,y.p2,E);break;case"straight":case"segments":case"haystack":for(var k,C,P,T,S=0,D=i.allpts.length,g=0;g+3<D&&(o?(P={x:i.allpts[g],y:i.allpts[g+1]},T={x:i.allpts[g+2],y:i.allpts[g+3]}):(P={x:i.allpts[D-2-g],y:i.allpts[D-1-g]},T={x:i.allpts[D-4-g],y:i.allpts[D-3-g]}),k=r.dist(P,T),C=S,!((S+=k)>=c));g+=2);var I=c-C,E=I/k;E=r.bound(0,E,1),t=r.lineAt(P,T,E),a=d(P,T)}u("labelX",n,t.x),u("labelY",n,t.y),u("labelAutoAngle",n,a)}};c("source"),c("target"),this.applyLabelDimensions(e)}},o.applyLabelDimensions=function(e){this.applyPrefixedLabelDimensions(e),e.isEdge()&&(this.applyPrefixedLabelDimensions(e,"source"),this.applyPrefixedLabelDimensions(e,"target"))},o.applyPrefixedLabelDimensions=function(e,t){var n=e._private,r=this.getLabelText(e,t),i=this.calculateLabelDimensions(e,r);a.setPrefixedProperty(n.rstyle,"labelWidth",t,i.width),a.setPrefixedProperty(n.rscratch,"labelWidth",t,i.width),a.setPrefixedProperty(n.rstyle,"labelHeight",t,i.height),a.setPrefixedProperty(n.rscratch,"labelHeight",t,i.height)},o.getLabelText=function(e,t){var n=e._private,r=t?t+"-":"",i=e.pstyle(r+"label").strValue,o=e.pstyle("text-transform").value,s=function(e,r){return r?(a.setPrefixedProperty(n.rscratch,e,t,r),r):a.getPrefixedProperty(n.rscratch,e,t)};"none"==o||("uppercase"==o?i=i.toUpperCase():"lowercase"==o&&(i=i.toLowerCase()));var u=e.pstyle("text-wrap").value;if("wrap"===u){var l=s("labelKey");if(l&&s("labelWrapKey")===l)return s("labelWrapCachedText");for(var c=i.split("\n"),d=e.pstyle("text-max-width").pfValue,h=[],f=0;f<c.length;f++){var p=c[f];if(this.calculateLabelDimensions(e,p,"line="+p).width>d){for(var v=p.split(/\s+/),g="",y=0;y<v.length;y++){var m=v[y],b=0===g.length?m:g+" "+m;this.calculateLabelDimensions(e,b,"testLine="+b).width<=d?g+=m+" ":(h.push(g),g=m+" ")}g.match(/^\s+$/)||h.push(g)}else h.push(p)}s("labelWrapCachedLines",h),i=s("labelWrapCachedText",h.join("\n")),s("labelWrapKey",l)}else if("ellipsis"===u){for(var d=e.pstyle("text-max-width").pfValue,x="",w=!1,_=0;_<i.length;_++){var E=this.calculateLabelDimensions(e,x+i[_]+"…").width;if(E>d)break;x+=i[_],_===i.length-1&&(w=!0)}return w||(x+="…"),x}return i},o.calculateLabelDimensions=function(e,t,n){var r=this,i=e._private.labelStyleKey+"$@$"+t;n&&(i+="$@$"+n);var a=r.labelDimCache||(r.labelDimCache={});if(a[i])return a[i];var o=e.pstyle("font-style").strValue,s=1*e.pstyle("font-size").pfValue+"px",u=e.pstyle("font-family").strValue,l=e.pstyle("font-weight").strValue,c=this.labelCalcDiv;c||(c=this.labelCalcDiv=document.createElement("div"),document.body.appendChild(c));var d=c.style;return d.fontFamily=u,d.fontStyle=o,d.fontSize=s,d.fontWeight=l,d.position="absolute",d.left="-9999px",d.top="-9999px",d.zIndex="-1",d.visibility="hidden",d.pointerEvents="none",d.padding="0",d.lineHeight="1","wrap"===e.pstyle("text-wrap").value?d.whiteSpace="pre":d.whiteSpace="normal",c.textContent=t,a[i]={width:Math.ceil(c.clientWidth/1),height:Math.ceil(c.clientHeight/1)},a[i]},o.calculateLabelAngles=function(e){var t=e._private,n=t.rscratch,r=e.isEdge(),i=e.pstyle("text-rotation"),a=i.strValue;"none"===a?n.labelAngle=n.sourceLabelAngle=n.targetLabelAngle=0:r&&"autorotate"===a?(n.labelAngle=Math.atan(n.midDispY/n.midDispX),n.sourceLabelAngle=n.sourceLabelAutoAngle,n.targetLabelAngle=n.targetLabelAutoAngle):n.labelAngle=n.sourceLabelAngle=n.targetLabelAngle="autorotate"===a?0:i.pfValue},e.exports=o},function(e,t,n){"use strict";var r={};r.getNodeShape=function(e){var t=this,n=e.pstyle("shape").value;if(e.isParent())return"rectangle"===n||"roundrectangle"===n||"cutrectangle"===n||"barrel"===n?n:"rectangle";if("polygon"===n){var r=e.pstyle("shape-polygon-points").value;return t.nodeShapes.makePolygon(r).name}return n},e.exports=r},function(e,t,n){"use strict";var r={};r.registerCalculationListeners=function(){var e=this.cy,t=e.collection(),n=this,r=function(e,n,r){if(t.merge(e),!0===r||void 0===r)for(var i=0;i<e.length;i++){var a=e[i],o=a._private,s=o.rstyle;s.clean=!1,o.bbCache=null;var u=s.dirtyEvents=s.dirtyEvents||{length:0};u[n.type]||(u[n.type]=!0,u.length++)}};n.binder(e).on("position.* style.* free.* bounds.*","node",function(e){var t=e.target;r(t,e),r(t.connectedEdges(),e)}).on("add.*","node",function(e){var t=e.target;r(t,e)}).on("background.*","node",function(e){var t=e.target;r(t,e,!1)}).on("add.* style.*","edge",function(e){var t=e.target;r(t,e),r(t.parallelEdges(),e)}).on("remove.*","edge",function(e){for(var t=e.target,n=t.parallelEdges(),i=0;i<n.length;i++){var a=n[i];a.removed()||r(a,e)}}).on("dirty.*","node",function(e){var t=e.target;r(t,e)});var i=function(r){if(r){var i=n.onUpdateEleCalcsFns;if(i)for(var a=0;a<i.length;a++){var o=i[a];o(r,t)}n.recalculateRenderedStyle(t,!1);for(var a=0;a<t.length;a++)t[a]._private.rstyle.dirtyEvents=null;t=e.collection()}};n.beforeRender(i,n.beforeRenderPriorities.eleCalcs)},r.onUpdateEleCalcs=function(e){(this.onUpdateEleCalcsFns=this.onUpdateEleCalcsFns||[]).push(e)},r.recalculateRenderedStyle=function(e,t){var n=[],r=[];if(!this.destroyed){void 0===t&&(t=!0);for(var i=0;i<e.length;i++){var a=e[i],o=a._private,s=o.rstyle;t&&s.clean||a.removed()||"none"!==a.pstyle("display").value&&("nodes"===o.group?r.push(a):n.push(a),s.clean=!0)}for(var i=0;i<r.length;i++){var a=r[i],o=a._private,s=o.rstyle,u=a.position();this.recalculateNodeLabelProjection(a),s.nodeX=u.x,s.nodeY=u.y,s.nodeW=a.pstyle("width").pfValue,s.nodeH=a.pstyle("height").pfValue}this.recalculateEdgeProjections(n);for(var i=0;i<n.length;i++){var a=n[i],o=a._private,s=o.rstyle,l=o.rscratch;this.recalculateEdgeLabelProjections(a),s.srcX=l.arrowStartX,s.srcY=l.arrowStartY,s.tgtX=l.arrowEndX,s.tgtY=l.arrowEndY,s.midX=l.midX,s.midY=l.midY,s.labelAngle=l.labelAngle,s.sourceLabelAngle=l.sourceLabelAngle,s.targetLabelAngle=l.targetLabelAngle}}},e.exports=r},function(e,t,n){"use strict";var r=n(14),i={};i.updateCachedGrabbedEles=function(){var e=this.cachedZSortedEles;if(e){e.drag=[],e.nondrag=[];for(var t=[],n=0;n<e.length;n++){var r=e[n],i=r._private.rscratch;r.grabbed()&&!r.isParent()?t.push(r):i.inDragLayer?e.drag.push(r):e.nondrag.push(r)}for(var n=0;n<t.length;n++){var r=t[n];e.drag.push(r)}}},i.invalidateCachedZSortedEles=function(){this.cachedZSortedEles=null},i.getCachedZSortedEles=function(e){if(e||!this.cachedZSortedEles){var t=this.cy.mutableElements().toArray();t.sort(r),t.interactive=t.filter(function(e){return e.interactive()}),this.cachedZSortedEles=t,this.updateCachedGrabbedEles()}else t=this.cachedZSortedEles;return t},e.exports=i},function(e,t,n){"use strict";var r={};r.getCachedImage=function(e,t,n){var r=this,i=r.imageCache=r.imageCache||{},a=i[e];if(a)return a.image.complete||a.image.addEventListener("load",n),a.image;a=i[e]=i[e]||{};var o=a.image=new Image;return o.addEventListener("load",n),o.addEventListener("error",function(){o.error=!0}),"data:"===e.substring(0,"data:".length).toLowerCase()||(o.crossOrigin=t),o.src=e,o},e.exports=r},function(e,t,n){"use strict";var r=n(0),i=n(1),a=n(4),o=function(e){this.init(e)},s=o,u=s.prototype;u.clientFunctions=["redrawHint","render","renderTo","matchCanvasSize","nodeShapeImpl","arrowShapeImpl"],u.init=function(e){var t=this;t.options=e,t.cy=e.cy;var n=t.container=e.cy.container();if(a){var r=a.document,o=r.head,s="__________cytoscape_container",u=null!=r.getElementById("__________cytoscape_stylesheet");if(n.className.indexOf(s)<0&&(n.className=(n.className||"")+" "+s),!u){var l=r.createElement("style");l.id="__________cytoscape_stylesheet",l.innerHTML="."+s+" { position: relative; }",o.insertBefore(l,o.children[0])}"static"===a.getComputedStyle(n).getPropertyValue("position")&&i.error("A Cytoscape container has style position:static and so can not use UI extensions properly")}t.selection=[void 0,void 0,void 0,void 0,0],t.bezierProjPcts=[.05,.225,.4,.5,.6,.775,.95],t.hoverData={down:null,last:null,downTime:null,triggerMode:null,dragging:!1,initialPan:[null,null],capture:!1},t.dragData={possibleDragElements:[]},t.touchData={start:null,capture:!1,startPosition:[null,null,null,null,null,null],singleTouchStartTime:null,singleTouchMoved:!0,now:[null,null,null,null,null,null],earlier:[null,null,null,null,null,null]},t.redraws=0,t.showFps=e.showFps,t.debug=e.debug,t.hideEdgesOnViewport=e.hideEdgesOnViewport,t.hideLabelsOnViewport=e.hideLabelsOnViewport,t.textureOnViewport=e.textureOnViewport,t.wheelSensitivity=e.wheelSensitivity,t.motionBlurEnabled=e.motionBlur,t.forcedPixelRatio=e.pixelRatio,t.motionBlur=e.motionBlur,t.motionBlurOpacity=e.motionBlurOpacity,t.motionBlurTransparency=1-t.motionBlurOpacity,t.motionBlurPxRatio=1,t.mbPxRBlurry=1,t.minMbLowQualFrames=4,t.fullQualityMb=!1,t.clearedForMotionBlur=[],t.desktopTapThreshold=e.desktopTapThreshold,t.desktopTapThreshold2=e.desktopTapThreshold*e.desktopTapThreshold,t.touchTapThreshold=e.touchTapThreshold,t.touchTapThreshold2=e.touchTapThreshold*e.touchTapThreshold,t.tapholdDuration=500,t.bindings=[],t.beforeRenderCallbacks=[],t.beforeRenderPriorities={animations:400,eleCalcs:300,eleTxrDeq:200,lyrTxrDeq:100},t.registerNodeShapes(),t.registerArrowShapes(),t.registerCalculationListeners()},u.notify=function(e){var t,n=this;if(!this.destroyed){t=r.array(e.type)?e.type:[e.type];for(var i={},a=0;a<t.length;a++)i[t[a]]=!0;if(i.init)return void n.load();if(i.destroy)return void n.destroy();(i.add||i.remove||i.load||i.zorder)&&n.invalidateCachedZSortedEles(),i.viewport&&n.redrawHint("select",!0),(i.load||i.resize)&&(n.invalidateContainerClientCoordsCache(),n.matchCanvasSize(n.container)),n.redrawHint("eles",!0),n.redrawHint("drag",!0),this.startRenderLoop(),this.redraw()}},u.destroy=function(){var e=this;e.destroyed=!0,e.cy.stopAnimationLoop();for(var t=0;t<e.bindings.length;t++){var n=e.bindings[t],r=n,i=r.target;(i.off||i.removeEventListener).apply(i,r.args)}if(e.bindings=[],e.beforeRenderCallbacks=[],e.onUpdateEleCalcsFns=[],e.removeObserver&&e.removeObserver.disconnect(),e.styleObserver&&e.styleObserver.disconnect(),e.labelCalcDiv)try{document.body.removeChild(e.labelCalcDiv)}catch(e){}},[n(86),n(92),n(97),n(99),n(100),n(101)].forEach(function(e){i.extend(u,e)}),e.exports=s},function(e,t,n){"use strict";var r=n(0),i=n(1),a=n(2),o=(n(15),{});o.registerBinding=function(e,t,n,r){var i=Array.prototype.slice.apply(arguments,[1]),a=this.binder(e);return a.on.apply(a,i)},o.binder=function(e){var t=this,n=e===window||e===document||e===document.body||r.domElement(e);if(null==t.supportsPassiveEvents){var i=!1;try{var a=Object.defineProperty({},"passive",{get:function(){i=!0}});window.addEventListener("test",null,a)}catch(e){}t.supportsPassiveEvents=i}var o=function(r,i,a){var o=Array.prototype.slice.call(arguments);return n&&t.supportsPassiveEvents&&(o[2]={capture:null!=a&&a,passive:!1,once:!1}),t.bindings.push({target:e,args:o}),(e.addEventListener||e.on).apply(e,o),this};return{on:o,addEventListener:o,addListener:o,bind:o}},o.nodeIsDraggable=function(e){return e&&e.isNode()&&!e.locked()&&e.grabbable()},o.nodeIsGrabbable=function(e){return this.nodeIsDraggable(e)&&e.interactive()},o.load=function(){var e=this,t=function(t,n,r,a){null==t&&(t=e.cy);for(var o=0;o<n.length;o++){var s=n[o];t.emit(i.extend({originalEvent:r,type:s},a))}},n=function(e){return e.shiftKey||e.metaKey||e.ctrlKey},o=function(t,n){var r=!0;if(e.cy.hasCompoundNodes()&&t&&t.isEdge())for(var i=0;n&&i<n.length;i++){var t=n[i];if(t.isNode()&&t.isParent()){r=!1;break}}else r=!0;return r},s=function(t){var n;if(t.addToList&&e.cy.hasCompoundNodes()){if(!t.addToList.hasId){t.addToList.hasId={};for(var r=0;r<t.addToList.length;r++){var i=t.addToList[r];t.addToList.hasId[i.id()]=!0}}n=t.addToList.hasId}return n||{}},u=function(e){e[0]._private.grabbed=!0},l=function(e){e[0]._private.grabbed=!1},c=function(e){e[0]._private.rscratch.inDragLayer=!0},d=function(e){e[0]._private.rscratch.inDragLayer=!1},h=function(e){e[0]._private.rscratch.isGrabTarget=!0},f=function(e){e[0]._private.rscratch.isGrabTarget=!1},p=function(e,t){var n=s(t);n[e.id()]||(t.addToList.push(e),n[e.id()]=!0,u(e))},v=function(e,t){if(e.cy().hasCompoundNodes()&&(null!=t.inDragLayer||null!=t.addToList)){var n=e.descendants();t.inDragLayer&&(n.forEach(c),n.connectedEdges().forEach(c)),t.addToList&&n.forEach(function(e){p(e,t)})}},g=function(t,n){n=n||{};var r=t.cy().hasCompoundNodes();n.inDragLayer&&(t.forEach(c),t.neighborhood().stdFilter(function(e){return!r||e.isEdge()}).forEach(c)),n.addToList&&t.forEach(function(e){p(e,n)}),v(t,n),b(t,{inDragLayer:n.inDragLayer}),e.updateCachedGrabbedEles()},y=g,m=function(t){t&&(t.hasId={},e.getCachedZSortedEles().forEach(function(e){l(e),d(e),f(e)}),e.updateCachedGrabbedEles())},b=function(e,t){if((null!=t.inDragLayer||null!=t.addToList)&&e.cy().hasCompoundNodes()){var n=e.ancestors().orphans();if(!n.same(e)){var r=n.descendants().spawnSelf().merge(n).unmerge(e).unmerge(e.descendants()),i=r.connectedEdges();t.inDragLayer&&(i.forEach(c),r.forEach(c)),t.addToList&&r.forEach(function(e){p(e,t)})}}},x="undefined"!=typeof MutationObserver;x?(e.removeObserver=new MutationObserver(function(t){for(var n=0;n<t.length;n++){var r=t[n],i=r.removedNodes;if(i)for(var a=0;a<i.length;a++){var o=i[a];if(o===e.container){e.destroy();break}}}}),e.container.parentNode&&e.removeObserver.observe(e.container.parentNode,{childList:!0})):e.registerBinding(e.container,"DOMNodeRemoved",function(t){e.destroy()});var w=i.debounce(function(){e.cy.resize()},100);x&&(e.styleObserver=new MutationObserver(w),e.styleObserver.observe(e.container,{attributes:!0})),e.registerBinding(window,"resize",w);var _=function(){e.invalidateContainerClientCoordsCache()};!function(e,t){for(;null!=e;)t(e),e=e.parentNode}(e.container,function(t){e.registerBinding(t,"transitionend",_),e.registerBinding(t,"animationend",_),e.registerBinding(t,"scroll",_)}),e.registerBinding(e.container,"contextmenu",function(e){e.preventDefault()});var E=function(){return 0!==e.selection[4]},k=function(t){for(var n=e.findContainerClientCoords(),r=n[0],i=n[1],a=n[2],o=n[3],s=t.touches?t.touches:[t],u=!1,l=0;l<s.length;l++){var c=s[l];if(r<=c.clientX&&c.clientX<=r+a&&i<=c.clientY&&c.clientY<=i+o){u=!0;break}}if(!u)return!1;for(var d=e.container,h=t.target,f=h.parentNode,p=!1;f;){if(f===d){p=!0;break}f=f.parentNode}return!!p};e.registerBinding(e.container,"mousedown",function(n){if(k(n)){n.preventDefault(),e.hoverData.capture=!0,e.hoverData.which=n.which;var r=e.cy,i=[n.clientX,n.clientY],a=e.projectIntoViewport(i[0],i[1]),o=e.selection,s=e.findNearestElements(a[0],a[1],!0,!1),u=s[0],l=e.dragData.possibleDragElements;if(e.hoverData.mdownPos=a,e.hoverData.mdownGPos=i,3==n.which){e.hoverData.cxtStarted=!0;var c={originalEvent:n,type:"cxttapstart",position:{x:a[0],y:a[1]}};u?(u.activate(),u.emit(c),e.hoverData.down=u):r.emit(c),e.hoverData.downTime=(new Date).getTime(),e.hoverData.cxtDragged=!1}else if(1==n.which){if(u&&u.activate(),null!=u&&e.nodeIsGrabbable(u)){var d=function(e){return{originalEvent:n,type:e,position:{x:a[0],y:a[1]}}},f=function(e){e.emit(d("grab"))};if(h(u),u.selected()){l=e.dragData.possibleDragElements=[];var p=r.$(function(t){return t.isNode()&&t.selected()&&e.nodeIsGrabbable(t)});g(p,{addToList:l}),
u.emit(d("grabon")),p.forEach(f)}else l=e.dragData.possibleDragElements=[],y(u,{addToList:l}),u.emit(d("grabon")).emit(d("grab"));e.redrawHint("eles",!0),e.redrawHint("drag",!0)}e.hoverData.down=u,e.hoverData.downs=s,e.hoverData.downTime=(new Date).getTime(),t(u,["mousedown","tapstart","vmousedown"],n,{position:{x:a[0],y:a[1]}}),null==u?(o[4]=1,e.data.bgActivePosistion={x:a[0],y:a[1]},e.redrawHint("select",!0),e.redraw()):u.isEdge()&&(o[4]=1),function(){e.hoverData.tapholdCancelled=!1,clearTimeout(e.hoverData.tapholdTimeout),e.hoverData.tapholdTimeout=setTimeout(function(){if(!e.hoverData.tapholdCancelled){var t=e.hoverData.down;t?t.emit({originalEvent:n,type:"taphold",position:{x:a[0],y:a[1]}}):r.emit({originalEvent:n,type:"taphold",position:{x:a[0],y:a[1]}})}},e.tapholdDuration)}()}o[0]=o[2]=a[0],o[1]=o[3]=a[1]}},!1),e.registerBinding(window,"mousemove",function(i){if(e.hoverData.capture||k(i)){var s=!1,u=e.cy,l=u.zoom(),c=[i.clientX,i.clientY],d=e.projectIntoViewport(c[0],c[1]),h=e.hoverData.mdownPos,f=e.hoverData.mdownGPos,p=e.selection,v=null;e.hoverData.draggingEles||e.hoverData.dragging||e.hoverData.selecting||(v=e.findNearestElement(d[0],d[1],!0,!1));var y,b=e.hoverData.last,x=e.hoverData.down,w=[d[0]-p[2],d[1]-p[3]],_=e.dragData.possibleDragElements;if(f){var E=c[0]-f[0],C=E*E,P=c[1]-f[1],T=P*P,S=C+T;e.hoverData.isOverThresholdDrag=y=S>=e.desktopTapThreshold2}var D=n(i);y&&(e.hoverData.tapholdCancelled=!0),s=!0,t(v,["mousemove","vmousemove","tapdrag"],i,{position:{x:d[0],y:d[1]}});var I=function(){e.data.bgActivePosistion=void 0,e.hoverData.selecting||u.emit("boxstart"),p[4]=1,e.hoverData.selecting=!0,e.redrawHint("select",!0),e.redraw()};if(3===e.hoverData.which){if(y){var N={originalEvent:i,type:"cxtdrag",position:{x:d[0],y:d[1]}};x?x.emit(N):u.emit(N),e.hoverData.cxtDragged=!0,e.hoverData.cxtOver&&v===e.hoverData.cxtOver||(e.hoverData.cxtOver&&e.hoverData.cxtOver.emit({originalEvent:i,type:"cxtdragout",position:{x:d[0],y:d[1]}}),e.hoverData.cxtOver=v,v&&v.emit({originalEvent:i,type:"cxtdragover",position:{x:d[0],y:d[1]}}))}}else if(e.hoverData.dragging){if(s=!0,u.panningEnabled()&&u.userPanningEnabled()){var M;if(e.hoverData.justStartedPan){var L=e.hoverData.mdownPos;M={x:(d[0]-L[0])*l,y:(d[1]-L[1])*l},e.hoverData.justStartedPan=!1}else M={x:w[0]*l,y:w[1]*l};u.panBy(M),e.hoverData.dragged=!0}d=e.projectIntoViewport(i.clientX,i.clientY)}else if(1!=p[4]||null!=x&&!x.isEdge()){if(x&&x.isEdge()&&x.active()&&x.unactivate(),x&&x.grabbed()||v==b||(b&&t(b,["mouseout","tapdragout"],i,{position:{x:d[0],y:d[1]}}),v&&t(v,["mouseover","tapdragover"],i,{position:{x:d[0],y:d[1]}}),e.hoverData.last=v),x&&e.nodeIsDraggable(x))if(y)if(u.boxSelectionEnabled()&&D)x&&x.grabbed()&&(m(_),x.emit("free")),I();else{var B=!e.dragData.didDrag;B&&e.redrawHint("eles",!0),e.dragData.didDrag=!0;var O=[];e.hoverData.draggingEles||g(u.collection(_),{inDragLayer:!0});for(var A=0;A<_.length;A++){var z=_[A];if(e.nodeIsDraggable(z)&&z.grabbed()){var R=z.position();if(O.push(z),r.number(w[0])&&r.number(w[1])&&(R.x+=w[0],R.y+=w[1],B)){var j=e.hoverData.dragDelta;j&&r.number(j[0])&&r.number(j[1])&&(R.x+=j[0],R.y+=j[1])}}}e.hoverData.draggingEles=!0;var F=u.collection(O);F.dirtyCompoundBoundsCache(),F.emit("position drag"),e.redrawHint("drag",!0),e.redraw()}else!function(){var t=e.hoverData.dragDelta=e.hoverData.dragDelta||[];0===t.length?(t.push(w[0]),t.push(w[1])):(t[0]+=w[0],t[1]+=w[1])}();s=!0}else if(y){if(e.hoverData.dragging||!u.boxSelectionEnabled()||!D&&u.panningEnabled()&&u.userPanningEnabled()){if(!e.hoverData.selecting&&u.panningEnabled()&&u.userPanningEnabled()){var V=o(x,e.hoverData.downs);V&&(e.hoverData.dragging=!0,e.hoverData.justStartedPan=!0,p[4]=0,e.data.bgActivePosistion=a.array2point(h),e.redrawHint("select",!0),e.redraw())}}else I();x&&x.isEdge()&&x.active()&&x.unactivate()}return p[2]=d[0],p[3]=d[1],s?(i.stopPropagation&&i.stopPropagation(),i.preventDefault&&i.preventDefault(),!1):void 0}},!1),e.registerBinding(window,"mouseup",function(r){if(e.hoverData.capture){e.hoverData.capture=!1;var i=e.cy,a=e.projectIntoViewport(r.clientX,r.clientY),o=e.selection,s=e.findNearestElement(a[0],a[1],!0,!1),u=e.dragData.possibleDragElements,l=e.hoverData.down,c=n(r);if(e.data.bgActivePosistion&&(e.redrawHint("select",!0),e.redraw()),e.hoverData.tapholdCancelled=!0,e.data.bgActivePosistion=void 0,l&&l.unactivate(),3===e.hoverData.which){var d={originalEvent:r,type:"cxttapend",position:{x:a[0],y:a[1]}};if(l?l.emit(d):i.emit(d),!e.hoverData.cxtDragged){var h={originalEvent:r,type:"cxttap",position:{x:a[0],y:a[1]}};l?l.emit(h):i.emit(h)}e.hoverData.cxtDragged=!1,e.hoverData.which=null}else if(1===e.hoverData.which){if(null!=l||e.dragData.didDrag||e.hoverData.selecting||e.hoverData.dragged||n(r)||(i.$(function(e){return e.selected()}).unselect(),u.length>0&&e.redrawHint("eles",!0),e.dragData.possibleDragElements=u=[]),t(s,["mouseup","tapend","vmouseup"],r,{position:{x:a[0],y:a[1]}}),e.dragData.didDrag||e.hoverData.dragged||e.hoverData.selecting||e.hoverData.isOverThresholdDrag||t(l,["click","tap","vclick"],r,{position:{x:a[0],y:a[1]}}),s!=l||e.dragData.didDrag||e.hoverData.selecting||null!=s&&s._private.selectable&&(e.hoverData.dragging||("additive"===i.selectionType()||c?s.selected()?s.unselect():s.select():c||(i.$(":selected").unmerge(s).unselect(),s.select())),e.redrawHint("eles",!0)),e.hoverData.selecting){var f=i.collection(e.getAllInBox(o[0],o[1],o[2],o[3]));e.redrawHint("select",!0),f.length>0&&e.redrawHint("eles",!0),i.emit("boxend");var p=function(e){return e.selectable()&&!e.selected()};"additive"===i.selectionType()?f.emit("box").stdFilter(p).select().emit("boxselect"):(c||i.$(":selected").unmerge(f).unselect(),f.emit("box").stdFilter(p).select().emit("boxselect")),e.redraw()}if(e.hoverData.dragging&&(e.hoverData.dragging=!1,e.redrawHint("select",!0),e.redrawHint("eles",!0),e.redraw()),!o[4]){e.redrawHint("drag",!0),e.redrawHint("eles",!0);var v=l&&l.grabbed();m(u),v&&l.emit("free")}}o[4]=0,e.hoverData.down=null,e.hoverData.cxtStarted=!1,e.hoverData.draggingEles=!1,e.hoverData.selecting=!1,e.hoverData.isOverThresholdDrag=!1,e.dragData.didDrag=!1,e.hoverData.dragged=!1,e.hoverData.dragDelta=[],e.hoverData.mdownPos=null,e.hoverData.mdownGPos=null}},!1);var C=function(t){if(!e.scrollingPage){var n=e.cy,r=e.projectIntoViewport(t.clientX,t.clientY),i=[r[0]*n.zoom()+n.pan().x,r[1]*n.zoom()+n.pan().y];if(e.hoverData.draggingEles||e.hoverData.dragging||e.hoverData.cxtStarted||E())return void t.preventDefault();if(n.panningEnabled()&&n.userPanningEnabled()&&n.zoomingEnabled()&&n.userZoomingEnabled()){t.preventDefault(),e.data.wheelZooming=!0,clearTimeout(e.data.wheelTimeout),e.data.wheelTimeout=setTimeout(function(){e.data.wheelZooming=!1,e.redrawHint("eles",!0),e.redraw()},150);var a;a=null!=t.deltaY?t.deltaY/-250:null!=t.wheelDeltaY?t.wheelDeltaY/1e3:t.wheelDelta/1e3,a*=e.wheelSensitivity,1===t.deltaMode&&(a*=33),n.zoom({level:n.zoom()*Math.pow(10,a),renderedPosition:{x:i[0],y:i[1]}})}}};e.registerBinding(e.container,"wheel",C,!0),e.registerBinding(window,"scroll",function(t){e.scrollingPage=!0,clearTimeout(e.scrollingPageTimeout),e.scrollingPageTimeout=setTimeout(function(){e.scrollingPage=!1},250)},!0),e.registerBinding(e.container,"mouseout",function(t){var n=e.projectIntoViewport(t.clientX,t.clientY);e.cy.emit({originalEvent:t,type:"mouseout",position:{x:n[0],y:n[1]}})},!1),e.registerBinding(e.container,"mouseover",function(t){var n=e.projectIntoViewport(t.clientX,t.clientY);e.cy.emit({originalEvent:t,type:"mouseover",position:{x:n[0],y:n[1]}})},!1);var P,T,S,D,I,N,M,L,B,O,A,z,R,j,F=function(e,t,n,r){return Math.sqrt((n-e)*(n-e)+(r-t)*(r-t))},V=function(e,t,n,r){return(n-e)*(n-e)+(r-t)*(r-t)};e.registerBinding(e.container,"touchstart",j=function(n){if(k(n)){e.touchData.capture=!0,e.data.bgActivePosistion=void 0;var r=e.cy,i=e.touchData.now,a=e.touchData.earlier;if(n.touches[0]){var o=e.projectIntoViewport(n.touches[0].clientX,n.touches[0].clientY);i[0]=o[0],i[1]=o[1]}if(n.touches[1]){var o=e.projectIntoViewport(n.touches[1].clientX,n.touches[1].clientY);i[2]=o[0],i[3]=o[1]}if(n.touches[2]){var o=e.projectIntoViewport(n.touches[2].clientX,n.touches[2].clientY);i[4]=o[0],i[5]=o[1]}if(n.touches[1]){m(e.dragData.touchDragEles);var s=e.findContainerClientCoords();B=s[0],O=s[1],A=s[2],z=s[3],P=n.touches[0].clientX-B,T=n.touches[0].clientY-O,S=n.touches[1].clientX-B,D=n.touches[1].clientY-O,R=0<=P&&P<=A&&0<=S&&S<=A&&0<=T&&T<=z&&0<=D&&D<=z;var u=r.pan(),l=r.zoom();if(I=F(P,T,S,D),N=V(P,T,S,D),M=[(P+S)/2,(T+D)/2],L=[(M[0]-u.x)/l,(M[1]-u.y)/l],N<4e4&&!n.touches[2]){var c=e.findNearestElement(i[0],i[1],!0,!0),d=e.findNearestElement(i[2],i[3],!0,!0);return c&&c.isNode()?(c.activate().emit({originalEvent:n,type:"cxttapstart",position:{x:i[0],y:i[1]}}),e.touchData.start=c):d&&d.isNode()?(d.activate().emit({originalEvent:n,type:"cxttapstart",position:{x:i[0],y:i[1]}}),e.touchData.start=d):r.emit({originalEvent:n,type:"cxttapstart",position:{x:i[0],y:i[1]}}),e.touchData.start&&(e.touchData.start._private.grabbed=!1),e.touchData.cxt=!0,e.touchData.cxtDragged=!1,e.data.bgActivePosistion=void 0,void e.redraw()}}if(n.touches[2]);else if(n.touches[1]);else if(n.touches[0]){var f=e.findNearestElements(i[0],i[1],!0,!0),p=f[0];if(null!=p&&(p.activate(),e.touchData.start=p,e.touchData.starts=f,e.nodeIsGrabbable(p))){var v=e.dragData.touchDragEles=[],b=null;e.redrawHint("eles",!0),e.redrawHint("drag",!0),p.selected()?(b=r.$(function(t){return t.selected()&&e.nodeIsGrabbable(t)}),g(b,{addToList:v})):y(p,{addToList:v}),h(p);var x=function(e){return{originalEvent:n,type:e,position:{x:i[0],y:i[1]}}};p.emit(x("grabon")),b?b.forEach(function(e){e.emit(x("grab"))}):p.emit(x("grab"))}t(p,["touchstart","tapstart","vmousedown"],n,{position:{x:i[0],y:i[1]}}),null==p&&(e.data.bgActivePosistion={x:o[0],y:o[1]},e.redrawHint("select",!0),e.redraw()),e.touchData.singleTouchMoved=!1,e.touchData.singleTouchStartTime=+new Date,clearTimeout(e.touchData.tapholdTimeout),e.touchData.tapholdTimeout=setTimeout(function(){!1!==e.touchData.singleTouchMoved||e.pinching||e.touchData.selecting||(t(e.touchData.start,["taphold"],n,{position:{x:i[0],y:i[1]}}),e.touchData.start||r.$(":selected").unselect())},e.tapholdDuration)}if(n.touches.length>=1){for(var w=e.touchData.startPosition=[],_=0;_<i.length;_++)w[_]=a[_]=i[_];var E=n.touches[0];e.touchData.startGPosition=[E.clientX,E.clientY]}}},!1);var q;e.registerBinding(window,"touchmove",q=function(n){var i=e.touchData.capture;if(i||k(n)){var s=e.selection,u=e.cy,l=e.touchData.now,c=e.touchData.earlier,d=u.zoom();if(n.touches[0]){var h=e.projectIntoViewport(n.touches[0].clientX,n.touches[0].clientY);l[0]=h[0],l[1]=h[1]}if(n.touches[1]){var h=e.projectIntoViewport(n.touches[1].clientX,n.touches[1].clientY);l[2]=h[0],l[3]=h[1]}if(n.touches[2]){var h=e.projectIntoViewport(n.touches[2].clientX,n.touches[2].clientY);l[4]=h[0],l[5]=h[1]}var f,p=e.touchData.startGPosition;if(i&&n.touches[0]&&p){for(var v=[],y=0;y<l.length;y++)v[y]=l[y]-c[y];var b=n.touches[0].clientX-p[0],x=b*b,w=n.touches[0].clientY-p[1];f=x+w*w>=e.touchTapThreshold2}if(i&&e.touchData.cxt){n.preventDefault();var _=n.touches[0].clientX-B,E=n.touches[0].clientY-O,C=n.touches[1].clientX-B,M=n.touches[1].clientY-O,A=V(_,E,C,M),z=A/N;if(z>=2.25||A>=22500){e.touchData.cxt=!1,e.data.bgActivePosistion=void 0,e.redrawHint("select",!0);var j={originalEvent:n,type:"cxttapend",position:{x:l[0],y:l[1]}};e.touchData.start?(e.touchData.start.unactivate().emit(j),e.touchData.start=null):u.emit(j)}}if(i&&e.touchData.cxt){var j={originalEvent:n,type:"cxtdrag",position:{x:l[0],y:l[1]}};e.data.bgActivePosistion=void 0,e.redrawHint("select",!0),e.touchData.start?e.touchData.start.emit(j):u.emit(j),e.touchData.start&&(e.touchData.start._private.grabbed=!1),e.touchData.cxtDragged=!0;var q=e.findNearestElement(l[0],l[1],!0,!0);e.touchData.cxtOver&&q===e.touchData.cxtOver||(e.touchData.cxtOver&&e.touchData.cxtOver.emit({originalEvent:n,type:"cxtdragout",position:{x:l[0],y:l[1]}}),e.touchData.cxtOver=q,q&&q.emit({originalEvent:n,type:"cxtdragover",position:{x:l[0],y:l[1]}}))}else if(i&&n.touches[2]&&u.boxSelectionEnabled())n.preventDefault(),e.data.bgActivePosistion=void 0,this.lastThreeTouch=+new Date,e.touchData.selecting||u.emit("boxstart"),e.touchData.selecting=!0,e.redrawHint("select",!0),s&&0!==s.length&&void 0!==s[0]?(s[2]=(l[0]+l[2]+l[4])/3,s[3]=(l[1]+l[3]+l[5])/3):(s[0]=(l[0]+l[2]+l[4])/3,s[1]=(l[1]+l[3]+l[5])/3,s[2]=(l[0]+l[2]+l[4])/3+1,s[3]=(l[1]+l[3]+l[5])/3+1),s[4]=1,e.touchData.selecting=!0,e.redraw();else if(i&&n.touches[1]&&u.zoomingEnabled()&&u.panningEnabled()&&u.userZoomingEnabled()&&u.userPanningEnabled()){n.preventDefault(),e.data.bgActivePosistion=void 0,e.redrawHint("select",!0);var Y=e.dragData.touchDragEles;if(Y){e.redrawHint("drag",!0);for(var X=0;X<Y.length;X++){var W=Y[X]._private;W.grabbed=!1,W.rscratch.inDragLayer=!1}}var _=n.touches[0].clientX-B,E=n.touches[0].clientY-O,C=n.touches[1].clientX-B,M=n.touches[1].clientY-O,U=F(_,E,C,M),H=U/I;if(R){var $=_-P,G=E-T,Z=C-S,Q=M-D,K=($+Z)/2,J=(G+Q)/2,ee=u.zoom(),te=ee*H,ne=u.pan(),re=L[0]*ee+ne.x,ie=L[1]*ee+ne.y,ae={x:-te/ee*(re-ne.x-K)+re,y:-te/ee*(ie-ne.y-J)+ie};if(e.touchData.start&&e.touchData.start.active()){var Y=e.dragData.touchDragEles;m(Y),e.redrawHint("drag",!0),e.redrawHint("eles",!0),e.touchData.start.unactivate().emit("free")}u.viewport({zoom:te,pan:ae,cancelOnFailedZoom:!0}),I=U,P=_,T=E,S=C,D=M,e.pinching=!0}if(n.touches[0]){var h=e.projectIntoViewport(n.touches[0].clientX,n.touches[0].clientY);l[0]=h[0],l[1]=h[1]}if(n.touches[1]){var h=e.projectIntoViewport(n.touches[1].clientX,n.touches[1].clientY);l[2]=h[0],l[3]=h[1]}if(n.touches[2]){var h=e.projectIntoViewport(n.touches[2].clientX,n.touches[2].clientY);l[4]=h[0],l[5]=h[1]}}else if(n.touches[0]){var q,oe=e.touchData.start,se=e.touchData.last;if(e.hoverData.draggingEles||e.swipePanning||(q=e.findNearestElement(l[0],l[1],!0,!0)),i&&null!=oe&&n.preventDefault(),i&&null!=oe&&e.nodeIsDraggable(oe))if(f){var Y=e.dragData.touchDragEles,ue=!e.dragData.didDrag;ue&&g(u.collection(Y),{inDragLayer:!0});for(var le=0;le<Y.length;le++){var ce=Y[le];if(e.nodeIsDraggable(ce)&&ce.grabbed()){e.dragData.didDrag=!0;var de=ce.position();if(r.number(v[0])&&r.number(v[1])&&(de.x+=v[0],de.y+=v[1]),ue){e.redrawHint("eles",!0);var he=e.touchData.dragDelta;he&&r.number(he[0])&&r.number(he[1])&&(de.x+=he[0],de.y+=he[1])}}}var fe=u.collection(Y);fe.dirtyCompoundBoundsCache(),fe.emit("position drag"),e.hoverData.draggingEles=!0,e.redrawHint("drag",!0),e.touchData.startPosition[0]==c[0]&&e.touchData.startPosition[1]==c[1]&&e.redrawHint("eles",!0),e.redraw()}else{var he=e.touchData.dragDelta=e.touchData.dragDelta||[];0===he.length?(he.push(v[0]),he.push(v[1])):(he[0]+=v[0],he[1]+=v[1])}if(t(oe||q,["touchmove","tapdrag","vmousemove"],n,{position:{x:l[0],y:l[1]}}),oe&&oe.grabbed()||q==se||(se&&se.emit({originalEvent:n,type:"tapdragout",position:{x:l[0],y:l[1]}}),q&&q.emit({originalEvent:n,type:"tapdragover",position:{x:l[0],y:l[1]}})),e.touchData.last=q,i)for(var X=0;X<l.length;X++)l[X]&&e.touchData.startPosition[X]&&f&&(e.touchData.singleTouchMoved=!0);if(i&&(null==oe||oe.isEdge())&&u.panningEnabled()&&u.userPanningEnabled()){var pe=o(oe,e.touchData.starts);pe&&(n.preventDefault(),e.swipePanning?u.panBy({x:v[0]*d,y:v[1]*d}):f&&(e.swipePanning=!0,u.panBy({x:b*d,y:w*d}),oe&&(oe.unactivate(),e.data.bgActivePosistion||(e.data.bgActivePosistion=a.array2point(e.touchData.startPosition)),e.redrawHint("select",!0),e.touchData.start=null)));var h=e.projectIntoViewport(n.touches[0].clientX,n.touches[0].clientY);l[0]=h[0],l[1]=h[1]}}for(var y=0;y<l.length;y++)c[y]=l[y]}},!1);var Y;e.registerBinding(window,"touchcancel",Y=function(t){var n=e.touchData.start;e.touchData.capture=!1,n&&n.unactivate()});var X;if(e.registerBinding(window,"touchend",X=function(n){var r=e.touchData.start;if(e.touchData.capture){e.touchData.capture=!1,n.preventDefault();var i=e.selection;e.swipePanning=!1,e.hoverData.draggingEles=!1;var a=e.cy,o=a.zoom(),s=e.touchData.now,u=e.touchData.earlier;if(n.touches[0]){var l=e.projectIntoViewport(n.touches[0].clientX,n.touches[0].clientY);s[0]=l[0],s[1]=l[1]}if(n.touches[1]){var l=e.projectIntoViewport(n.touches[1].clientX,n.touches[1].clientY);s[2]=l[0],s[3]=l[1]}if(n.touches[2]){var l=e.projectIntoViewport(n.touches[2].clientX,n.touches[2].clientY);s[4]=l[0],s[5]=l[1]}r&&r.unactivate();var c;if(e.touchData.cxt){if(c={originalEvent:n,type:"cxttapend",position:{x:s[0],y:s[1]}},r?r.emit(c):a.emit(c),!e.touchData.cxtDragged){var d={originalEvent:n,type:"cxttap",position:{x:s[0],y:s[1]}};r?r.emit(d):a.emit(d)}return e.touchData.start&&(e.touchData.start._private.grabbed=!1),e.touchData.cxt=!1,e.touchData.start=null,void e.redraw()}if(!n.touches[2]&&a.boxSelectionEnabled()&&e.touchData.selecting){e.touchData.selecting=!1;var h=a.collection(e.getAllInBox(i[0],i[1],i[2],i[3]));i[0]=void 0,i[1]=void 0,i[2]=void 0,i[3]=void 0,i[4]=0,e.redrawHint("select",!0),a.emit("boxend");var f=function(e){return e.selectable()&&!e.selected()};h.emit("box").stdFilter(f).select().emit("boxselect"),h.nonempty()&&e.redrawHint("eles",!0),e.redraw()}if(null!=r&&r.unactivate(),n.touches[2])e.data.bgActivePosistion=void 0,e.redrawHint("select",!0);else if(n.touches[1]);else if(n.touches[0]);else if(!n.touches[0]){e.data.bgActivePosistion=void 0,e.redrawHint("select",!0);var p=e.dragData.touchDragEles;if(null!=r){var v=r._private.grabbed;m(p),e.redrawHint("drag",!0),e.redrawHint("eles",!0),v&&r.emit("free"),t(r,["touchend","tapend","vmouseup","tapdragout"],n,{position:{x:s[0],y:s[1]}}),r.unactivate(),e.touchData.start=null}else{var g=e.findNearestElement(s[0],s[1],!0,!0);t(g,["touchend","tapend","vmouseup","tapdragout"],n,{position:{x:s[0],y:s[1]}})}var y=e.touchData.startPosition[0]-s[0],b=y*y,x=e.touchData.startPosition[1]-s[1],w=x*x,_=b+w,E=_*o*o;null!=r&&!e.dragData.didDrag&&r._private.selectable&&E<e.touchTapThreshold2&&!e.pinching&&("single"===a.selectionType()?(a.$(":selected").unmerge(r).unselect(),r.select()):r.selected()?r.unselect():r.select(),e.redrawHint("eles",!0)),e.touchData.singleTouchMoved||t(r,["tap","vclick"],n,{position:{x:s[0],y:s[1]}}),e.touchData.singleTouchMoved=!0}for(var k=0;k<s.length;k++)u[k]=s[k];e.dragData.didDrag=!1,0===n.touches.length&&(e.touchData.dragDelta=[],e.touchData.startPosition=null,e.touchData.startGPosition=null),n.touches.length<2&&(e.pinching=!1,e.redrawHint("eles",!0),e.redraw())}},!1),"undefined"==typeof TouchEvent){var W=[],U=function(e){return{clientX:e.clientX,clientY:e.clientY,force:1,identifier:e.pointerId,pageX:e.pageX,pageY:e.pageY,radiusX:e.width/2,radiusY:e.height/2,screenX:e.screenX,screenY:e.screenY,target:e.target}},H=function(e){return{event:e,touch:U(e)}},$=function(e){W.push(H(e))},G=function(e){for(var t=0;t<W.length;t++)if(W[t].event.pointerId===e.pointerId)return void W.splice(t,1)},Z=function(e){var t=W.filter(function(t){return t.event.pointerId===e.pointerId})[0];t.event=e,t.touch=U(e)},Q=function(e){e.touches=W.map(function(e){return e.touch})},K=function(e){return"mouse"===e.pointerType||4===e.pointerType};e.registerBinding(e.container,"pointerdown",function(e){K(e)||(e.preventDefault(),$(e),Q(e),j(e))}),e.registerBinding(e.container,"pointerup",function(e){K(e)||(G(e),Q(e),X(e))}),e.registerBinding(e.container,"pointercancel",function(e){K(e)||(G(e),Q(e),Y(e))}),e.registerBinding(e.container,"pointermove",function(e){K(e)||(e.preventDefault(),Z(e),Q(e),q(e))})}},e.exports=o},function(e,t,n){"use strict";var r=n(2),i={};i.generatePolygon=function(e,t){return this.nodeShapes[e]={renderer:this,name:e,points:t,draw:function(e,t,n,r,i){this.renderer.nodeShapeImpl("polygon",e,t,n,r,i,this.points)},intersectLine:function(e,t,n,i,a,o,s){return r.polygonIntersectLine(a,o,this.points,e,t,n/2,i/2,s)},checkPoint:function(e,t,n,i,a,o,s){return r.pointInsidePolygon(e,t,this.points,o,s,i,a,[0,-1],n)}}},i.generateEllipse=function(){return this.nodeShapes.ellipse={renderer:this,name:"ellipse",draw:function(e,t,n,r,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i)},intersectLine:function(e,t,n,i,a,o,s){return r.intersectLineEllipse(a,o,e,t,n/2+s,i/2+s)},checkPoint:function(e,t,n,i,a,o,s){return r.checkInEllipse(e,t,n,i,a,o,s)}}},i.generateRoundRectangle=function(){return this.nodeShapes.roundrectangle={renderer:this,name:"roundrectangle",points:r.generateUnitNgonPointsFitToSquare(4,0),draw:function(e,t,n,r,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i)},intersectLine:function(e,t,n,i,a,o,s){return r.roundRectangleIntersectLine(a,o,e,t,n,i,s)},checkPoint:function(e,t,n,i,a,o,s){var u=r.getRoundRectangleRadius(i,a);return!!(r.pointInsidePolygon(e,t,this.points,o,s,i,a-2*u,[0,-1],n)||r.pointInsidePolygon(e,t,this.points,o,s,i-2*u,a,[0,-1],n)||r.checkInEllipse(e,t,o-i/2+u,s-a/2+u,2*u,2*u,n)||r.checkInEllipse(e,t,o+i/2-u,s-a/2+u,2*u,2*u,n)||r.checkInEllipse(e,t,o+i/2-u,s+a/2-u,2*u,2*u,n)||r.checkInEllipse(e,t,o-i/2+u,s+a/2-u,2*u,2*u,n))}}},i.generateCutRectangle=function(){return this.nodeShapes.cutrectangle={renderer:this,name:"cutrectangle",cornerLength:r.getCutRectangleCornerLength(),points:r.generateUnitNgonPointsFitToSquare(4,0),draw:function(e,t,n,r,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i)},generateCutTrianglePts:function(e,t,n,r){var i=this.cornerLength,a=t/2,o=e/2,s=n-o,u=n+o,l=r-a,c=r+a;return{topLeft:[s,l+i,s+i,l,s+i,l+i],topRight:[u-i,l,u,l+i,u-i,l+i],bottomRight:[u,c-i,u-i,c,u-i,c-i],bottomLeft:[s+i,c,s,c-i,s+i,c-i]}},intersectLine:function(e,t,n,i,a,o,s){var u=this.generateCutTrianglePts(n+2*s,i+2*s,e,t),l=[].concat.apply([],[u.topLeft.splice(0,4),u.topRight.splice(0,4),u.bottomRight.splice(0,4),u.bottomLeft.splice(0,4)]);return r.polygonIntersectLine(a,o,l,e,t)},checkPoint:function(e,t,n,i,a,o,s){if(r.pointInsidePolygon(e,t,this.points,o,s,i,a-2*this.cornerLength,[0,-1],n))return!0;if(r.pointInsidePolygon(e,t,this.points,o,s,i-2*this.cornerLength,a,[0,-1],n))return!0;var u=this.generateCutTrianglePts(i,a,o,s);return r.pointInsidePolygonPoints(e,t,u.topLeft)||r.pointInsidePolygonPoints(e,t,u.topRight)||r.pointInsidePolygonPoints(e,t,u.bottomRight)||r.pointInsidePolygonPoints(e,t,u.bottomLeft)}}},i.generateBarrel=function(){return this.nodeShapes.barrel={renderer:this,name:"barrel",points:r.generateUnitNgonPointsFitToSquare(4,0),draw:function(e,t,n,r,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i)},intersectLine:function(e,t,n,i,a,o,s){var u=this.generateBarrelBezierPts(n+2*s,i+2*s,e,t),l=[].concat(u.topLeft,u.topRight,u.bottomRight,u.bottomLeft);return r.polygonIntersectLine(a,o,l,e,t)},generateBarrelBezierPts:function(e,t,n,i){var a=t/2,o=e/2,s=n-o,u=n+o,l=i-a,c=i+a,d=r.getBarrelCurveConstants(e,t),h=d.heightOffset,f=d.widthOffset,p=d.ctrlPtOffsetPct*e,v={topLeft:[s,l+h,s+p,l,s+f,l],topRight:[u-f,l,u-p,l,u,l+h],bottomRight:[u,c-h,u-p,c,u-f,c],bottomLeft:[s+f,c,s+p,c,s,c-h]};return v.topLeft.isTop=!0,v.topRight.isTop=!0,v.bottomLeft.isBottom=!0,v.bottomRight.isBottom=!0,v},checkPoint:function(e,t,n,i,a,o,s){var u=r.getBarrelCurveConstants(i,a),l=u.heightOffset,c=u.widthOffset;if(r.pointInsidePolygon(e,t,this.points,o,s,i,a-2*l,[0,-1],n))return!0;if(r.pointInsidePolygon(e,t,this.points,o,s,i-2*c,a,[0,-1],n))return!0;for(var d=this.generateBarrelBezierPts(i,a,o,s),h=Object.keys(d),f=0;f<h.length;f++){var p=h[f],v=d[p],g=function(e,t,n){var i=n[4],a=n[2],o=n[0],s=n[5],u=n[1],l=Math.min(i,o),c=Math.max(i,o),d=Math.min(s,u),h=Math.max(s,u);if(l<=e&&e<=c&&d<=t&&t<=h){var f=r.bezierPtsToQuadCoeff(i,a,o),p=r.solveQuadratic(f[0],f[1],f[2],e),v=p.filter(function(e){return 0<=e&&e<=1});if(v.length>0)return v[0]}return null}(e,t,v);if(null!=g){var y=v[5],m=v[3],b=v[1],x=r.qbezierAt(y,m,b,g);if(v.isTop&&x<=t)return!0;if(v.isBottom&&t<=x)return!0}}return!1}}},i.generateBottomRoundrectangle=function(){return this.nodeShapes.bottomroundrectangle={renderer:this,name:"bottomroundrectangle",points:r.generateUnitNgonPointsFitToSquare(4,0),draw:function(e,t,n,r,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i)},intersectLine:function(e,t,n,i,a,o,s){var u=e-(n/2+s),l=t-(i/2+s),c=l,d=e+(n/2+s),h=r.finiteLinesIntersect(a,o,e,t,u,l,d,c,!1);return h.length>0?h:r.roundRectangleIntersectLine(a,o,e,t,n,i,s)},checkPoint:function(e,t,n,i,a,o,s){var u=r.getRoundRectangleRadius(i,a);if(r.pointInsidePolygon(e,t,this.points,o,s,i,a-2*u,[0,-1],n))return!0;if(r.pointInsidePolygon(e,t,this.points,o,s,i-2*u,a,[0,-1],n))return!0;var l=i/2+2*n,c=a/2+2*n,d=[o-l,s-c,o-l,s,o+l,s,o+l,s-c];return!!r.pointInsidePolygonPoints(e,t,d)||!!r.checkInEllipse(e,t,o+i/2-u,s+a/2-u,2*u,2*u,n)||!!r.checkInEllipse(e,t,o-i/2+u,s+a/2-u,2*u,2*u,n)}}},i.registerNodeShapes=function(){var e=this.nodeShapes={},t=this;this.generateEllipse(),this.generatePolygon("triangle",r.generateUnitNgonPointsFitToSquare(3,0)),this.generatePolygon("rectangle",r.generateUnitNgonPointsFitToSquare(4,0)),e.square=e.rectangle,this.generateRoundRectangle(),this.generateCutRectangle(),this.generateBarrel(),this.generateBottomRoundrectangle(),this.generatePolygon("diamond",[0,1,1,0,0,-1,-1,0]),this.generatePolygon("pentagon",r.generateUnitNgonPointsFitToSquare(5,0)),this.generatePolygon("hexagon",r.generateUnitNgonPointsFitToSquare(6,0)),this.generatePolygon("heptagon",r.generateUnitNgonPointsFitToSquare(7,0)),this.generatePolygon("octagon",r.generateUnitNgonPointsFitToSquare(8,0));var n=new Array(20),i=r.generateUnitNgonPoints(5,0),a=r.generateUnitNgonPoints(5,Math.PI/5),o=.5*(3-Math.sqrt(5));o*=1.57;for(var s=0;s<a.length/2;s++)a[2*s]*=o,a[2*s+1]*=o;for(var s=0;s<5;s++)n[4*s]=i[2*s],n[4*s+1]=i[2*s+1],n[4*s+2]=a[2*s],n[4*s+3]=a[2*s+1];n=r.fitPolygonToSquare(n),this.generatePolygon("star",n),this.generatePolygon("vee",[-1,-1,0,-.333,1,-1,0,1]),this.generatePolygon("rhomboid",[-1,-1,.333,-1,1,1,-.333,1]),this.generatePolygon("concavehexagon",[-1,-.95,-.75,0,-1,.95,1,.95,.75,0,1,-.95]),this.generatePolygon("tag",[-1,-1,.25,-1,1,0,.25,1,-1,1]),e.makePolygon=function(e){var n,r=e.join("$"),i="polygon-"+r;return(n=this[i])?n:t.generatePolygon(i,e)}},e.exports=i},function(e,t,n){"use strict";var r=n(1),i={};i.timeToRender=function(){return this.redrawTotalTime/this.redrawCount},i.redraw=function(e){e=e||r.staticEmptyObject();var t=this;void 0===t.averageRedrawTime&&(t.averageRedrawTime=0),void 0===t.lastRedrawTime&&(t.lastRedrawTime=0),void 0===t.lastDrawTime&&(t.lastDrawTime=0),t.requestedFrame=!0,t.renderOptions=e},i.beforeRender=function(e,t){if(!this.destroyed){t=t||0;var n=this.beforeRenderCallbacks;n.push({fn:e,priority:t}),n.sort(function(e,t){return t.priority-e.priority})}};var a=function(e,t,n){for(var r=e.beforeRenderCallbacks,i=0;i<r.length;i++)r[i].fn(t,n)};i.startRenderLoop=function(){var e=this;if(!e.renderLoopStarted){e.renderLoopStarted=!0;var t=function t(n){if(!e.destroyed){if(e.requestedFrame&&!e.skipFrame){a(e,!0,n);var i=r.performanceNow();e.render(e.renderOptions);var o=e.lastDrawTime=r.performanceNow();void 0===e.averageRedrawTime&&(e.averageRedrawTime=o-i),void 0===e.redrawCount&&(e.redrawCount=0),e.redrawCount++,void 0===e.redrawTotalTime&&(e.redrawTotalTime=0);var s=o-i;e.redrawTotalTime+=s,e.lastRedrawTime=s,e.averageRedrawTime=e.averageRedrawTime/2+s/2,e.requestedFrame=!1}else a(e,!1,n);e.skipFrame=!1,r.requestAnimationFrame(t)}};r.requestAnimationFrame(t)}},e.exports=i},function(e,t,n){"use strict";var r,i={};i.arrowShapeImpl=function(e){return(r||(r={polygon:function(e,t){for(var n=0;n<t.length;n++){var r=t[n];e.lineTo(r.x,r.y)}},"triangle-backcurve":function(e,t,n){for(var r,i=0;i<t.length;i++){var a=t[i];0===i&&(r=a),e.lineTo(a.x,a.y)}e.quadraticCurveTo(n.x,n.y,r.x,r.y)},"triangle-tee":function(e,t,n){e.beginPath&&e.beginPath();for(var r=t,i=0;i<r.length;i++){var a=r[i];e.lineTo(a.x,a.y)}e.closePath&&e.closePath(),e.beginPath&&e.beginPath();var o=n,s=n[0];e.moveTo(s.x,s.y);for(var i=0;i<o.length;i++){var a=o[i];e.lineTo(a.x,a.y)}e.closePath&&e.closePath()},"triangle-cross":function(e,t,n){e.beginPath&&e.beginPath();for(var r=t,i=0;i<r.length;i++){var a=r[i];e.lineTo(a.x,a.y)}e.closePath&&e.closePath(),e.beginPath&&e.beginPath();var o=n,s=n[0];e.moveTo(s.x,s.y);for(var i=0;i<o.length;i++){var a=o[i];e.lineTo(a.x,a.y)}e.closePath&&e.closePath()},circle:function(e,t,n,r){e.arc(t,n,r,0,2*Math.PI,!1)}}))[e]},e.exports=i},function(e,t,n){"use strict";var r={};r.drawEdge=function(e,t,n,r){var i=this,a=t._private.rscratch,o=i.usePaths();if(!a.badLine&&!isNaN(a.allpts[0])&&t.visible()){var s=void 0;n&&(s=n,e.translate(-s.x1,-s.y1));var u=t.pstyle("overlay-padding").pfValue,l=2*u,c=t.pstyle("overlay-opacity").value,d=t.pstyle("overlay-color").value,h=t.pstyle("line-color").value,f=t.pstyle("opacity").value,p=t.pstyle("line-style").value,v=t.pstyle("width").pfValue,g=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f;e.lineWidth=v,e.lineCap="butt",i.strokeStyle(e,h[0],h[1],h[2],n),i.drawEdgePath(t,e,a.allpts,p)},y=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f;i.drawArrowheads(e,t,n)};if(e.lineJoin="round","yes"===t.pstyle("ghost").value){var m=t.pstyle("ghost-offset-x").pfValue,b=t.pstyle("ghost-offset-y").pfValue,x=t.pstyle("ghost-opacity").value,w=f*x;e.translate(m,b),g(w),y(w),e.translate(-m,-b)}g(),y(),function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c;e.lineWidth=l,"self"!==a.edgeType||o?e.lineCap="round":e.lineCap="butt",i.strokeStyle(e,d[0],d[1],d[2],n),i.drawEdgePath(t,e,a.allpts,"solid")}(),function(){i.drawElementText(e,t,r)}(),n&&e.translate(s.x1,s.y1)}},r.drawEdgePath=function(e,t,n,r){var i=e._private.rscratch,a=t,o=void 0,s=!1,u=this.usePaths();if(u){var l=n.join("$");i.pathCacheKey&&i.pathCacheKey===l?(o=t=i.pathCache,s=!0):(o=t=new Path2D,i.pathCacheKey=l,i.pathCache=o)}if(a.setLineDash)switch(r){case"dotted":a.setLineDash([1,1]);break;case"dashed":a.setLineDash([6,3]);break;case"solid":a.setLineDash([])}if(!s&&!i.badLine)switch(t.beginPath&&t.beginPath(),t.moveTo(n[0],n[1]),i.edgeType){case"bezier":case"self":case"compound":case"multibezier":for(var c=2;c+3<n.length;c+=4)t.quadraticCurveTo(n[c],n[c+1],n[c+2],n[c+3]);break;case"straight":case"segments":case"haystack":for(var d=2;d+1<n.length;d+=2)t.lineTo(n[d],n[d+1])}t=a,u?t.stroke(o):t.stroke(),t.setLineDash&&t.setLineDash([])},r.drawArrowheads=function(e,t,n){var r=t._private.rscratch,i="haystack"===r.edgeType;i||this.drawArrowhead(e,t,"source",r.arrowStartX,r.arrowStartY,r.srcArrowAngle,n),this.drawArrowhead(e,t,"mid-target",r.midX,r.midY,r.midtgtArrowAngle,n),this.drawArrowhead(e,t,"mid-source",r.midX,r.midY,r.midsrcArrowAngle,n),i||this.drawArrowhead(e,t,"target",r.arrowEndX,r.arrowEndY,r.tgtArrowAngle,n)},r.drawArrowhead=function(e,t,n,r,i,a,o){if(!(isNaN(r)||null==r||isNaN(i)||null==i||isNaN(a)||null==a)){var s=this,u=t.pstyle(n+"-arrow-shape").value;if("none"!==u){var l="hollow"===t.pstyle(n+"-arrow-fill").value?"both":"filled",c=t.pstyle(n+"-arrow-fill").value,d=t.pstyle("width").pfValue,h=t.pstyle("opacity").value;void 0===o&&(o=h);var f=e.globalCompositeOperation;1===o&&"hollow"!==c||(e.globalCompositeOperation="destination-out",s.fillStyle(e,255,255,255,1),s.strokeStyle(e,255,255,255,1),s.drawArrowShape(t,n,e,l,d,u,r,i,a),e.globalCompositeOperation=f);var p=t.pstyle(n+"-arrow-color").value;s.fillStyle(e,p[0],p[1],p[2],o),s.strokeStyle(e,p[0],p[1],p[2],o),s.drawArrowShape(t,n,e,c,d,u,r,i,a)}}},r.drawArrowShape=function(e,t,n,r,i,a,o,s,u){var l=this,c=this.usePaths(),d=e._private.rscratch,h=!1,f=void 0,p=n,v={x:o,y:s},g=e.pstyle("arrow-scale").value,y=this.getArrowWidth(i,g),m=l.arrowShapes[a];if(c){var b=y+"$"+a+"$"+u+"$"+o+"$"+s;d.arrowPathCacheKey=d.arrowPathCacheKey||{},d.arrowPathCache=d.arrowPathCache||{},d.arrowPathCacheKey[t]===b?(f=n=d.arrowPathCache[t],h=!0):(f=n=new Path2D,d.arrowPathCacheKey[t]=b,d.arrowPathCache[t]=f)}n.beginPath&&n.beginPath(),h||m.draw(n,y,u,v,i),!m.leavePathOpen&&n.closePath&&n.closePath(),n=p,"filled"!==r&&"both"!==r||(c?n.fill(f):n.fill()),"hollow"!==r&&"both"!==r||(n.lineWidth=m.matchEdgeWidth?i:1,
n.lineJoin="miter",c?n.stroke(f):n.stroke())},e.exports=r},function(e,t,n){"use strict";var r=n(2),i={};i.drawElement=function(e,t,n,r){var i=this;t.isNode()?i.drawNode(e,t,n,r):i.drawEdge(e,t,n,r)},i.drawCachedElement=function(e,t,n,i){var a=this,o=t.boundingBox();if(0!==o.w&&0!==o.h&&(!i||r.boundingBoxesIntersect(o,i))){var s=a.data.eleTxrCache.getElement(t,o,n);null!=s?e.drawImage(s.texture.canvas,s.x,0,s.width,s.height,o.x1,o.y1,o.w,o.h):a.drawElement(e,t)}},i.drawElements=function(e,t){for(var n=this,r=0;r<t.length;r++){var i=t[r];n.drawElement(e,i)}},i.drawCachedElements=function(e,t,n,r){for(var i=this,a=0;a<t.length;a++){var o=t[a];i.drawCachedElement(e,o,n,r)}},i.drawCachedNodes=function(e,t,n,r){for(var i=this,a=0;a<t.length;a++){var o=t[a];o.isNode()&&i.drawCachedElement(e,o,n,r)}},i.drawLayeredElements=function(e,t,n,r){var i=this,a=i.data.lyrTxrCache.getLayers(t,n);if(a)for(var o=0;o<a.length;o++){var s=a[o],u=s.bb;0!==u.w&&0!==u.h&&e.drawImage(s.canvas,u.x1,u.y1,u.w,u.h)}else i.drawCachedElements(e,t,n,r)},i.drawDebugPoints=function(e,t){for(var n=function(t,n,r){e.fillStyle=r,e.fillRect(t-1,n-1,3,3)},r=0;r<t.length;r++){var i=t[r],a=i._private.rscratch;if(i.isNode()){var o=i.position();n(o.x,o.y,"magenta")}else{for(var s=a.allpts,u=0;u+1<s.length;u+=2)n(s[u],s[u+1],"cyan");n(a.midX,a.midY,"yellow")}}},e.exports=i},function(e,t,n){"use strict";var r={};r.safeDrawImage=function(e,t,n,r,i,a,o,s,u,l){i<=0||a<=0||u<=0||l<=0||e.drawImage(t,n,r,i,a,o,s,u,l)},r.drawInscribedImage=function(e,t,n,r,i){var a=this,o=n.position(),s=o.x,u=o.y,l=n.cy().style(),c=l.getIndexedStyle.bind(l),d=c(n,"background-fit","value",r),h=c(n,"background-repeat","value",r),f=n.width(),p=n.height(),v=2*n.padding(),g=f+("inner"===c(n,"background-width-relative-to","value",r)?0:v),y=p+("inner"===c(n,"background-height-relative-to","value",r)?0:v),m=n._private.rscratch,b=n.pstyle("background-clip").value,x="node"===b,w=c(n,"background-image-opacity","value",r)*i,_=t.width||t.cachedW,E=t.height||t.cachedH;null!=_&&null!=E||(document.body.appendChild(t),_=t.cachedW=t.width||t.offsetWidth,E=t.cachedH=t.height||t.offsetHeight,document.body.removeChild(t));var k=_,C=E;if("auto"!==c(n,"background-width","value",r)&&(k="%"===c(n,"background-width","units",r)?c(n,"background-width","pfValue",r)*g:c(n,"background-width","pfValue",r)),"auto"!==c(n,"background-height","value",r)&&(C="%"===c(n,"background-height","units",r)?c(n,"background-height","pfValue",r)*y:c(n,"background-height","pfValue",r)),0!==k&&0!==C){if("contain"===d){var P=Math.min(g/k,y/C);k*=P,C*=P}else if("cover"===d){var P=Math.max(g/k,y/C);k*=P,C*=P}var T=s-g/2;T+="%"===c(n,"background-position-x","units",r)?(g-k)*c(n,"background-position-x","pfValue",r):c(n,"background-position-x","pfValue",r);var S=u-y/2;S+="%"===c(n,"background-position-y","units",r)?(y-C)*c(n,"background-position-y","pfValue",r):c(n,"background-position-y","pfValue",r),m.pathCache&&(T-=s,S-=u,s=0,u=0);var D=e.globalAlpha;if(e.globalAlpha=w,"no-repeat"===h)x&&(e.save(),m.pathCache?e.clip(m.pathCache):(a.nodeShapes[a.getNodeShape(n)].draw(e,s,u,g,y),e.clip())),a.safeDrawImage(e,t,0,0,_,E,T,S,k,C),x&&e.restore();else{var I=e.createPattern(t,h);e.fillStyle=I,a.nodeShapes[a.getNodeShape(n)].draw(e,s,u,g,y),e.translate(T,S),e.fill(),e.translate(-T,-S)}e.globalAlpha=D}},e.exports=r},function(e,t,n){"use strict";function r(e,t,n,r,i,a){var a=a||5;e.beginPath(),e.moveTo(t+a,n),e.lineTo(t+r-a,n),e.quadraticCurveTo(t+r,n,t+r,n+a),e.lineTo(t+r,n+i-a),e.quadraticCurveTo(t+r,n+i,t+r-a,n+i),e.lineTo(t+a,n+i),e.quadraticCurveTo(t,n+i,t,n+i-a),e.lineTo(t,n+a),e.quadraticCurveTo(t,n,t+a,n),e.closePath(),e.fill()}var i=n(1),a=n(2),o={};o.eleTextBiggerThanMin=function(e,t){if(!t){var n=e.cy().zoom(),r=this.getPixelRatio(),i=Math.ceil(a.log2(n*r));t=Math.pow(2,i)}return!(e.pstyle("font-size").pfValue*t<e.pstyle("min-zoomed-font-size").pfValue)},o.drawElementText=function(e,t,n){var r=this;if(void 0===n){if(!r.eleTextBiggerThanMin(t))return}else if(!n)return;if(t.isNode()){var i=t.pstyle("label");if(!i||!i.value)return;var a=t.pstyle("text-halign").strValue;switch(t.pstyle("text-valign").strValue,a){case"left":e.textAlign="right";break;case"right":e.textAlign="left";break;default:e.textAlign="center"}e.textBaseline="bottom"}else{var i=t.pstyle("label"),o=t.pstyle("source-label"),s=t.pstyle("target-label");if(!(i&&i.value||o&&o.value||s&&s.value))return;e.textAlign="center",e.textBaseline="bottom"}r.drawText(e,t),t.isEdge()&&(r.drawText(e,t,"source"),r.drawText(e,t,"target"))},o.drawNodeText=o.drawEdgeText=o.drawElementText,o.getFontCache=function(e){var t;this.fontCaches=this.fontCaches||[];for(var n=0;n<this.fontCaches.length;n++)if(t=this.fontCaches[n],t.context===e)return t;return t={context:e},this.fontCaches.push(t),t},o.setupTextStyle=function(e,t){var n=t.effectiveOpacity(),r=t.pstyle("font-style").strValue,i=t.pstyle("font-size").pfValue+"px",a=t.pstyle("font-family").strValue,o=t.pstyle("font-weight").strValue,s=t.pstyle("text-opacity").value*t.pstyle("opacity").value*n,u=t.pstyle("text-outline-opacity").value*s,l=t.pstyle("color").value,c=t.pstyle("text-outline-color").value,d=t._private.fontKey,h=this.getFontCache(e);h.key!==d&&(e.font=r+" "+o+" "+i+" "+a,h.key=d),e.lineJoin="round",this.fillStyle(e,l[0],l[1],l[2],s),this.strokeStyle(e,c[0],c[1],c[2],u)},o.drawText=function(e,t,n){var a=t._private,o=a.rscratch,s=t.effectiveOpacity();if(0!==s&&0!==t.pstyle("text-opacity").value){var u=i.getPrefixedProperty(o,"labelX",n),l=i.getPrefixedProperty(o,"labelY",n),c=this.getLabelText(t,n);if(null!=c&&""!==c&&!isNaN(u)&&!isNaN(l)){this.setupTextStyle(e,t);var d=n?n+"-":"",h=i.getPrefixedProperty(o,"labelWidth",n),f=i.getPrefixedProperty(o,"labelHeight",n),p=i.getPrefixedProperty(o,"labelAngle",n),v=t.pstyle(d+"text-margin-x").pfValue,g=t.pstyle(d+"text-margin-y").pfValue,y=t.isEdge(),m=(t.isNode(),t.pstyle("text-halign").value),b=t.pstyle("text-valign").value;y&&(m="center",b="center"),u+=v,l+=g;var x,w=t.pstyle("text-rotation");if(0!==(x="autorotate"===w.strValue?y?p:0:"none"===w.strValue?0:w.pfValue)){var _=u,E=l;e.translate(_,E),e.rotate(x),u=0,l=0}switch(b){case"top":break;case"center":l+=f/2;break;case"bottom":l+=f}var k=t.pstyle("text-background-opacity").value,C=t.pstyle("text-border-opacity").value,P=t.pstyle("text-border-width").pfValue,T=t.pstyle("text-background-padding").pfValue;if(k>0||P>0&&C>0){var S=u-T;switch(m){case"left":S-=h;break;case"center":S-=h/2}var D=l-f-T,I=h+2*T,N=f+2*T;if(k>0){var M=e.fillStyle,L=t.pstyle("text-background-color").value;e.fillStyle="rgba("+L[0]+","+L[1]+","+L[2]+","+k*s+")","roundrectangle"==t.pstyle("text-background-shape").strValue?r(e,S,D,I,N,2):e.fillRect(S,D,I,N),e.fillStyle=M}if(P>0&&C>0){var B=e.strokeStyle,O=e.lineWidth,A=t.pstyle("text-border-color").value,z=t.pstyle("text-border-style").value;if(e.strokeStyle="rgba("+A[0]+","+A[1]+","+A[2]+","+C*s+")",e.lineWidth=P,e.setLineDash)switch(z){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash([4,2]);break;case"double":e.lineWidth=P/4,e.setLineDash([]);break;case"solid":e.setLineDash([])}if(e.strokeRect(S,D,I,N),"double"===z){var R=P/2;e.strokeRect(S+R,D+R,I-2*R,N-2*R)}e.setLineDash&&e.setLineDash([]),e.lineWidth=O,e.strokeStyle=B}}var j=2*t.pstyle("text-outline-width").pfValue;if(j>0&&(e.lineWidth=j),"wrap"===t.pstyle("text-wrap").value){var F=i.getPrefixedProperty(o,"labelWrapCachedLines",n),V=f/F.length;switch(b){case"top":l-=(F.length-1)*V;break;case"center":case"bottom":l-=(F.length-1)*V}for(var q=0;q<F.length;q++)j>0&&e.strokeText(F[q],u,l),e.fillText(F[q],u,l),l+=V}else j>0&&e.strokeText(c,u,l),e.fillText(c,u,l);0!==x&&(e.rotate(-x),e.translate(-_,-E))}}},e.exports=o},function(e,t,n){"use strict";var r=n(0),i={};i.drawNode=function(e,t,n,i){var a=this,o=void 0,s=void 0,u=t._private,l=u.rscratch,c=t.position();if(r.number(c.x)&&r.number(c.y)&&t.visible()){var d=t.effectiveOpacity(),h=a.usePaths(),f=void 0,p=!1,v=t.padding();o=t.width()+2*v,s=t.height()+2*v,e.lineWidth=t.pstyle("border-width").pfValue;var g=void 0;n&&(g=n,e.translate(-g.x1,-g.y1));for(var y=t.pstyle("background-image"),m=y.value,b=void 0,x=[],w=[],_=m.length,E=0;E<_;E++)if(b=m[E],x[E]=null!=b&&"none"!==b,x[E]){var k=t.cy().style().getIndexedStyle(t,"background-image-crossorigin","value",E);w[E]=a.getCachedImage(b,k,function(){t.emitAndNotify("background")})}var C=t.pstyle("background-blacken").value,P=t.pstyle("border-width").pfValue,T=t.pstyle("background-color").value,S=t.pstyle("background-opacity").value*d,D=t.pstyle("border-color").value,I=t.pstyle("border-style").value,N=t.pstyle("border-opacity").value*d;if(e.lineJoin="miter",e.setLineDash)switch(I){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash([4,2]);break;case"solid":case"double":e.setLineDash([])}var M=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:S;a.fillStyle(e,T[0],T[1],T[2],t)},L=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:N;a.strokeStyle(e,D[0],D[1],D[2],t)},B=t.pstyle("shape").strValue,O=t.pstyle("shape-polygon-points").pfValue;if(h){var A=B+"$"+o+"$"+s+("polygon"===B?"$"+O.join("$"):"");e.translate(c.x,c.y),l.pathCacheKey===A?(f=l.pathCache,p=!0):(f=new Path2D,l.pathCacheKey=A,l.pathCache=f)}var z=function(){if(!p){var n=c;h&&(n={x:0,y:0}),a.nodeShapes[a.getNodeShape(t)].draw(f||e,n.x,n.y,o,s)}h?e.fill(f):e.fill()},R=function(){for(var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:d,r=u.backgrounding,i=0,o=0;o<_;o++)x[o]&&w[o].complete&&!w[o].error&&(i++,a.drawInscribedImage(e,w[o],t,o,n));u.backgrounding=!(i===_),r!==u.backgrounding&&t.updateStyle(!1)},j=function(){var n=arguments.length>0&&void 0!==arguments[0]&&arguments[0],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:d;a.hasPie(t)&&(a.drawPie(e,t,r),n&&(h||a.nodeShapes[a.getNodeShape(t)].draw(e,c.x,c.y,o,s)))},F=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:d,n=(C>0?C:-C)*t,r=C>0?0:255;0!==C&&(a.fillStyle(e,r,r,r,n),h?e.fill(f):e.fill())},V=function(){if(P>0&&(h?e.stroke(f):e.stroke(),"double"===I)){e.lineWidth=P/3;var t=e.globalCompositeOperation;e.globalCompositeOperation="destination-out",h?e.stroke(f):e.stroke(),e.globalCompositeOperation=t}};if("yes"===t.pstyle("ghost").value){var q=t.pstyle("ghost-offset-x").pfValue,Y=t.pstyle("ghost-offset-y").pfValue,X=t.pstyle("ghost-opacity").value,W=X*d;e.translate(q,Y),M(X*S),z(),R(W),j(0!==C||0!==P),F(W),L(X*N),V(),e.translate(-q,-Y)}M(),z(),R(),j(0!==C||0!==P),F(),L(),V(),h&&e.translate(-c.x,-c.y),function(){a.drawElementText(e,t,i)}(),function(){var n=t.pstyle("overlay-padding").pfValue,r=t.pstyle("overlay-opacity").value,i=t.pstyle("overlay-color").value;r>0&&(a.fillStyle(e,i[0],i[1],i[2],r),a.nodeShapes.roundrectangle.draw(e,c.x,c.y,o+2*n,s+2*n),e.fill())}(),e.setLineDash&&e.setLineDash([]),n&&e.translate(g.x1,g.y1)}},i.hasPie=function(e){return e=e[0],e._private.hasPie},i.drawPie=function(e,t,n,r){t=t[0],r=r||t.position();var i=t.cy().style(),a=t.pstyle("pie-size"),o=r.x,s=r.y,u=t.width(),l=t.height(),c=Math.min(u,l)/2,d=0;this.usePaths()&&(o=0,s=0),"%"===a.units?c*=a.pfValue:void 0!==a.pfValue&&(c=a.pfValue/2);for(var h=1;h<=i.pieBackgroundN;h++){var f=t.pstyle("pie-"+h+"-background-size").value,p=t.pstyle("pie-"+h+"-background-color").value,v=t.pstyle("pie-"+h+"-background-opacity").value*n,g=f/100;g+d>1&&(g=1-d);var y=1.5*Math.PI+2*Math.PI*d,m=2*Math.PI*g,b=y+m;0===f||d>=1||d+g>1||(e.beginPath(),e.moveTo(o,s),e.arc(o,s,c,y,b),e.closePath(),this.fillStyle(e,p[0],p[1],p[2],v),e.fill(),d+=g)}},e.exports=i},function(e,t,n){"use strict";var r={},i=n(1);r.getPixelRatio=function(){var e=this.data.contexts[0];if(null!=this.forcedPixelRatio)return this.forcedPixelRatio;var t=e.backingStorePixelRatio||e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/t},r.paintCache=function(e){for(var t,n=this.paintCaches=this.paintCaches||[],r=!0,i=0;i<n.length;i++)if(t=n[i],t.context===e){r=!1;break}return r&&(t={context:e},n.push(t)),t},r.fillStyle=function(e,t,n,r,i){e.fillStyle="rgba("+t+","+n+","+r+","+i+")"},r.strokeStyle=function(e,t,n,r,i){e.strokeStyle="rgba("+t+","+n+","+r+","+i+")"},r.matchCanvasSize=function(e){var t=this,n=t.data,r=t.findContainerClientCoords(),i=r[2],a=r[3],o=t.getPixelRatio(),s=t.motionBlurPxRatio;e!==t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_NODE]&&e!==t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_DRAG]||(o=s);var u,l=i*o,c=a*o;if(l!==t.canvasWidth||c!==t.canvasHeight){t.fontCaches=null;var d=n.canvasContainer;d.style.width=i+"px",d.style.height=a+"px";for(var h=0;h<t.CANVAS_LAYERS;h++)u=n.canvases[h],u.width=l,u.height=c,u.style.width=i+"px",u.style.height=a+"px";for(var h=0;h<t.BUFFER_COUNT;h++)u=n.bufferCanvases[h],u.width=l,u.height=c,u.style.width=i+"px",u.style.height=a+"px";t.textureMult=1,o<=1&&(u=n.bufferCanvases[t.TEXTURE_BUFFER],t.textureMult=2,u.width=l*t.textureMult,u.height=c*t.textureMult),t.canvasWidth=l,t.canvasHeight=c}},r.renderTo=function(e,t,n,r){this.render({forcedContext:e,forcedZoom:t,forcedPan:n,drawAllLayers:!0,forcedPxRatio:r})},r.render=function(e){function t(e,t,n,r,i){var a=e.globalCompositeOperation;e.globalCompositeOperation="destination-out",l.fillStyle(e,255,255,255,l.motionBlurTransparency),e.fillRect(t,n,r,i),e.globalCompositeOperation=a}function n(e,n){var i,o,c,d;l.clearingMotionBlur||e!==h.bufferContexts[l.MOTIONBLUR_BUFFER_NODE]&&e!==h.bufferContexts[l.MOTIONBLUR_BUFFER_DRAG]?(i=C,o=E,c=l.canvasWidth,d=l.canvasHeight):(i={x:k.x*g,y:k.y*g},o=_*g,c=l.canvasWidth*g,d=l.canvasHeight*g),e.setTransform(1,0,0,1,0,0),"motionBlur"===n?t(e,0,0,c,d):r||void 0!==n&&!n||e.clearRect(0,0,c,d),a||(e.translate(i.x,i.y),e.scale(o,o)),u&&e.translate(u.x,u.y),s&&e.scale(s,s)}e=e||i.staticEmptyObject();var r=e.forcedContext,a=e.drawAllLayers,o=e.drawOnlyNodeLayer,s=e.forcedZoom,u=e.forcedPan,l=this,c=void 0===e.forcedPxRatio?this.getPixelRatio():e.forcedPxRatio,d=l.cy,h=l.data,f=h.canvasNeedsRedraw,p=l.textureOnViewport&&!r&&(l.pinching||l.hoverData.dragging||l.swipePanning||l.data.wheelZooming),v=void 0!==e.motionBlur?e.motionBlur:l.motionBlur,g=l.motionBlurPxRatio,y=d.hasCompoundNodes(),m=l.hoverData.draggingEles,b=!(!l.hoverData.selecting&&!l.touchData.selecting);v=v&&!r&&l.motionBlurEnabled&&!b;var x=v;r||(l.prevPxRatio!==c&&(l.invalidateContainerClientCoordsCache(),l.matchCanvasSize(l.container),l.redrawHint("eles",!0),l.redrawHint("drag",!0)),l.prevPxRatio=c),!r&&l.motionBlurTimeout&&clearTimeout(l.motionBlurTimeout),v&&(null==l.mbFrames&&(l.mbFrames=0),l.mbFrames++,l.mbFrames<3&&(x=!1),l.mbFrames>l.minMbLowQualFrames&&(l.motionBlurPxRatio=l.mbPxRBlurry)),l.clearingMotionBlur&&(l.motionBlurPxRatio=1),l.textureDrawLastFrame&&!p&&(f[l.NODE]=!0,f[l.SELECT_BOX]=!0);var w=d.style()._private.coreStyle,_=d.zoom(),E=void 0!==s?s:_,k=d.pan(),C={x:k.x,y:k.y},P={zoom:_,pan:{x:k.x,y:k.y}},T=l.prevViewport;void 0===T||P.zoom!==T.zoom||P.pan.x!==T.pan.x||P.pan.y!==T.pan.y||m&&!y||(l.motionBlurPxRatio=1),u&&(C=u),E*=c,C.x*=c,C.y*=c;var S=l.getCachedZSortedEles();if(p||(l.textureDrawLastFrame=!1),p){if(l.textureDrawLastFrame=!0,!l.textureCache){l.textureCache={},l.textureCache.bb=d.mutableElements().boundingBox(),l.textureCache.texture=l.data.bufferCanvases[l.TEXTURE_BUFFER];var D=l.data.bufferContexts[l.TEXTURE_BUFFER];D.setTransform(1,0,0,1,0,0),D.clearRect(0,0,l.canvasWidth*l.textureMult,l.canvasHeight*l.textureMult),l.render({forcedContext:D,drawOnlyNodeLayer:!0,forcedPxRatio:c*l.textureMult});var P=l.textureCache.viewport={zoom:d.zoom(),pan:d.pan(),width:l.canvasWidth,height:l.canvasHeight};P.mpan={x:(0-P.pan.x)/P.zoom,y:(0-P.pan.y)/P.zoom}}f[l.DRAG]=!1,f[l.NODE]=!1;var I=h.contexts[l.NODE],N=l.textureCache.texture,P=l.textureCache.viewport;l.textureCache.bb,I.setTransform(1,0,0,1,0,0),v?t(I,0,0,P.width,P.height):I.clearRect(0,0,P.width,P.height);var M=w["outside-texture-bg-color"].value,L=w["outside-texture-bg-opacity"].value;l.fillStyle(I,M[0],M[1],M[2],L),I.fillRect(0,0,P.width,P.height);var _=d.zoom();n(I,!1),I.clearRect(P.mpan.x,P.mpan.y,P.width/P.zoom/c,P.height/P.zoom/c),I.drawImage(N,P.mpan.x,P.mpan.y,P.width/P.zoom/c,P.height/P.zoom/c)}else l.textureOnViewport&&!r&&(l.textureCache=null);var B=d.extent(),O=l.pinching||l.hoverData.dragging||l.swipePanning||l.data.wheelZooming||l.hoverData.draggingEles,A=l.hideEdgesOnViewport&&O,z=[];if(z[l.NODE]=!f[l.NODE]&&v&&!l.clearedForMotionBlur[l.NODE]||l.clearingMotionBlur,z[l.NODE]&&(l.clearedForMotionBlur[l.NODE]=!0),z[l.DRAG]=!f[l.DRAG]&&v&&!l.clearedForMotionBlur[l.DRAG]||l.clearingMotionBlur,z[l.DRAG]&&(l.clearedForMotionBlur[l.DRAG]=!0),f[l.NODE]||a||o||z[l.NODE]){var R=v&&!z[l.NODE]&&1!==g,I=r||(R?l.data.bufferContexts[l.MOTIONBLUR_BUFFER_NODE]:h.contexts[l.NODE]);n(I,v&&!R?"motionBlur":void 0),A?l.drawCachedNodes(I,S.nondrag,c,B):l.drawLayeredElements(I,S.nondrag,c,B),l.debug&&l.drawDebugPoints(I,S.nondrag),a||v||(f[l.NODE]=!1)}if(!o&&(f[l.DRAG]||a||z[l.DRAG])){var R=v&&!z[l.DRAG]&&1!==g,I=r||(R?l.data.bufferContexts[l.MOTIONBLUR_BUFFER_DRAG]:h.contexts[l.DRAG]);n(I,v&&!R?"motionBlur":void 0),A?l.drawCachedNodes(I,S.drag,c,B):l.drawCachedElements(I,S.drag,c,B),l.debug&&l.drawDebugPoints(I,S.drag),a||v||(f[l.DRAG]=!1)}if(l.showFps||!o&&f[l.SELECT_BOX]&&!a){var I=r||h.contexts[l.SELECT_BOX];if(n(I),1==l.selection[4]&&(l.hoverData.selecting||l.touchData.selecting)){var _=l.cy.zoom(),j=w["selection-box-border-width"].value/_;I.lineWidth=j,I.fillStyle="rgba("+w["selection-box-color"].value[0]+","+w["selection-box-color"].value[1]+","+w["selection-box-color"].value[2]+","+w["selection-box-opacity"].value+")",I.fillRect(l.selection[0],l.selection[1],l.selection[2]-l.selection[0],l.selection[3]-l.selection[1]),j>0&&(I.strokeStyle="rgba("+w["selection-box-border-color"].value[0]+","+w["selection-box-border-color"].value[1]+","+w["selection-box-border-color"].value[2]+","+w["selection-box-opacity"].value+")",I.strokeRect(l.selection[0],l.selection[1],l.selection[2]-l.selection[0],l.selection[3]-l.selection[1]))}if(h.bgActivePosistion&&!l.hoverData.selecting){var _=l.cy.zoom(),F=h.bgActivePosistion;I.fillStyle="rgba("+w["active-bg-color"].value[0]+","+w["active-bg-color"].value[1]+","+w["active-bg-color"].value[2]+","+w["active-bg-opacity"].value+")",I.beginPath(),I.arc(F.x,F.y,w["active-bg-size"].pfValue/_,0,2*Math.PI),I.fill()}var V=l.lastRedrawTime;if(l.showFps&&V){V=Math.round(V);var q=Math.round(1e3/V);I.setTransform(1,0,0,1,0,0),I.fillStyle="rgba(255, 0, 0, 0.75)",I.strokeStyle="rgba(255, 0, 0, 0.75)",I.lineWidth=1,I.fillText("1 frame = "+V+" ms = "+q+" fps",0,20),I.strokeRect(0,30,250,20),I.fillRect(0,30,250*Math.min(q/60,1),20)}a||(f[l.SELECT_BOX]=!1)}if(v&&1!==g){var Y=h.contexts[l.NODE],X=l.data.bufferCanvases[l.MOTIONBLUR_BUFFER_NODE],W=h.contexts[l.DRAG],U=l.data.bufferCanvases[l.MOTIONBLUR_BUFFER_DRAG],H=function(e,n,r){e.setTransform(1,0,0,1,0,0),r||!x?e.clearRect(0,0,l.canvasWidth,l.canvasHeight):t(e,0,0,l.canvasWidth,l.canvasHeight);var i=g;e.drawImage(n,0,0,l.canvasWidth*i,l.canvasHeight*i,0,0,l.canvasWidth,l.canvasHeight)};(f[l.NODE]||z[l.NODE])&&(H(Y,X,z[l.NODE]),f[l.NODE]=!1),(f[l.DRAG]||z[l.DRAG])&&(H(W,U,z[l.DRAG]),f[l.DRAG]=!1)}l.prevViewport=P,l.clearingMotionBlur&&(l.clearingMotionBlur=!1,l.motionBlurCleared=!0,l.motionBlur=!0),v&&(l.motionBlurTimeout=setTimeout(function(){l.motionBlurTimeout=null,l.clearedForMotionBlur[l.NODE]=!1,l.clearedForMotionBlur[l.DRAG]=!1,l.motionBlur=!1,l.clearingMotionBlur=!p,l.mbFrames=0,f[l.NODE]=!0,f[l.DRAG]=!0,l.redraw()},100)),r||d.emit("render")},e.exports=r},function(e,t,n){"use strict";var r=n(2),i={};i.drawPolygonPath=function(e,t,n,r,i,a){var o=r/2,s=i/2;e.beginPath&&e.beginPath(),e.moveTo(t+o*a[0],n+s*a[1]);for(var u=1;u<a.length/2;u++)e.lineTo(t+o*a[2*u],n+s*a[2*u+1]);e.closePath()},i.drawRoundRectanglePath=function(e,t,n,i,a){var o=i/2,s=a/2,u=r.getRoundRectangleRadius(i,a);e.beginPath&&e.beginPath(),e.moveTo(t,n-s),e.arcTo(t+o,n-s,t+o,n,u),e.arcTo(t+o,n+s,t,n+s,u),e.arcTo(t-o,n+s,t-o,n,u),e.arcTo(t-o,n-s,t,n-s,u),e.lineTo(t,n-s),e.closePath()},i.drawBottomRoundRectanglePath=function(e,t,n,i,a){var o=i/2,s=a/2,u=r.getRoundRectangleRadius(i,a);e.beginPath&&e.beginPath(),e.moveTo(t,n-s),e.lineTo(t+o,n-s),e.lineTo(t+o,n),e.arcTo(t+o,n+s,t,n+s,u),e.arcTo(t-o,n+s,t-o,n,u),e.lineTo(t-o,n-s),e.lineTo(t,n-s),e.closePath()},i.drawCutRectanglePath=function(e,t,n,i,a){var o=i/2,s=a/2,u=r.getCutRectangleCornerLength();e.beginPath&&e.beginPath(),e.moveTo(t-o+u,n-s),e.lineTo(t+o-u,n-s),e.lineTo(t+o,n-s+u),e.lineTo(t+o,n+s-u),e.lineTo(t+o-u,n+s),e.lineTo(t-o+u,n+s),e.lineTo(t-o,n+s-u),e.lineTo(t-o,n-s+u),e.closePath()},i.drawBarrelPath=function(e,t,n,i,a){var o=i/2,s=a/2,u=t-o,l=t+o,c=n-s,d=n+s,h=r.getBarrelCurveConstants(i,a),f=h.widthOffset,p=h.heightOffset,v=h.ctrlPtOffsetPct*f;e.beginPath&&e.beginPath(),e.moveTo(u,c+p),e.lineTo(u,d-p),e.quadraticCurveTo(u+v,d,u+f,d),e.lineTo(l-f,d),e.quadraticCurveTo(l-v,d,l,d-p),e.lineTo(l,c+p),e.quadraticCurveTo(l-v,c,l-f,c),e.lineTo(u+f,c),e.quadraticCurveTo(u+v,c,u,c+p),e.closePath()};for(var a=Math.sin(0),o=Math.cos(0),s={},u={},l=Math.PI/40,c=0*Math.PI;c<2*Math.PI;c+=l)s[c]=Math.sin(c),u[c]=Math.cos(c);i.drawEllipsePath=function(e,t,n,r,i){if(e.beginPath&&e.beginPath(),e.ellipse)e.ellipse(t,n,r/2,i/2,0,0,2*Math.PI);else for(var c,d,h=r/2,f=i/2,p=0*Math.PI;p<2*Math.PI;p+=l)c=t-h*s[p]*a+h*u[p]*o,d=n+f*u[p]*a+f*s[p]*o,0===p?e.moveTo(c,d):e.lineTo(c,d);e.closePath()},e.exports=i},function(e,t,n){"use strict";var r=n(2),i=n(1),a=n(8),o=n(16),s={dequeue:"dequeue",downscale:"downscale",highQuality:"highQuality"},u=function(e){var t=this;t.renderer=e,t.onDequeues=[],t.setupDequeueing()},l=u.prototype;l.reasons=s,l.getTextureQueue=function(e){var t=this;return t.eleImgCaches=t.eleImgCaches||{},t.eleImgCaches[e]=t.eleImgCaches[e]||[]},l.getRetiredTextureQueue=function(e){var t=this,n=t.eleImgCaches.retired=t.eleImgCaches.retired||{};return n[e]=n[e]||[]},l.getElementQueue=function(){var e=this;return e.eleCacheQueue=e.eleCacheQueue||new a(function(e,t){return t.reqs-e.reqs})},l.getElementIdToQueue=function(){var e=this;return e.eleIdToCacheQueue=e.eleIdToCacheQueue||{}},l.getElement=function(e,t,n,i,a){var o=this,u=this.renderer,l=e._private.rscratch,c=u.cy.zoom();if(0===t.w||0===t.h||!e.visible())return null;if(null==i&&(i=Math.ceil(r.log2(c*n))),i<-4)i=-4;else if(c>=3.99||i>2)return null;var d=Math.pow(2,i),h=t.h*d,f=t.w*d,p=l.imgCaches=l.imgCaches||{},v=p[i];if(v)return v;var g;if(g=h<=25?25:h<=50?50:50*Math.ceil(h/50),h>1024||f>1024||e.isEdge()||e.isParent())return null;var y=o.getTextureQueue(g),m=y[y.length-2],b=function(){return o.recycleTexture(g,f)||o.addTexture(g,f)};m||(m=y[y.length-1]),m||(m=b()),m.width-m.usedWidth<f&&(m=b());for(var x,w=u.eleTextBiggerThanMin(e,d),_=function(e){return e&&e.scaledLabelShown===w},E=a&&a===s.dequeue,k=a&&a===s.highQuality,C=a&&a===s.downscale,P=i+1;P<=2;P++){var T=p[P];if(T){x=T;break}}var S=x&&x.level===i+1?x:null,D=function(){m.context.drawImage(S.texture.canvas,S.x,0,S.width,S.height,m.usedWidth,0,f,h)};if(m.context.setTransform(1,0,0,1,0,0),m.context.clearRect(m.usedWidth,0,f,g),_(S))D();else if(_(x)){if(!k)return o.queueElement(e,t,x.level-1),x;for(var P=x.level;P>i;P--)S=o.getElement(e,t,n,P,s.downscale);D()}else{var I;if(!E&&!k&&!C)for(var P=i-1;P>=-4;P--){var T=p[P];if(T){I=T;break}}if(_(I))return o.queueElement(e,t,i),I;m.context.translate(m.usedWidth,0),m.context.scale(d,d),u.drawElement(m.context,e,t,w),m.context.scale(1/d,1/d),m.context.translate(-m.usedWidth,0)}return v=p[i]={ele:e,x:m.usedWidth,texture:m,level:i,scale:d,width:f,height:h,scaledLabelShown:w},m.usedWidth+=Math.ceil(f+8),m.eleCaches.push(v),o.checkTextureFullness(m),v},l.invalidateElement=function(e){var t=this,n=e._private.rscratch.imgCaches;if(n)for(var r=-4;r<=2;r++){var a=n[r];if(a){var o=a.texture;o.invalidatedWidth+=a.width,n[r]=null,i.removeFromArray(o.eleCaches,a),t.checkTextureUtility(o)}}},l.checkTextureUtility=function(e){e.invalidatedWidth>=.5*e.width&&this.retireTexture(e)},l.checkTextureFullness=function(e){var t=this,n=t.getTextureQueue(e.height);e.usedWidth/e.width>.8&&e.fullnessChecks>=10?i.removeFromArray(n,e):e.fullnessChecks++},l.retireTexture=function(e){var t=this,n=e.height,r=t.getTextureQueue(n);i.removeFromArray(r,e),e.retired=!0;for(var a=e.eleCaches,o=0;o<a.length;o++){var s=a[o],u=s.ele,l=s.level,c=u._private.rscratch.imgCaches;c&&(c[l]=null)}i.clearArray(a),t.getRetiredTextureQueue(n).push(e)},l.addTexture=function(e,t){var n=this,r=n.getTextureQueue(e),i={};return r.push(i),i.eleCaches=[],i.height=e,i.width=Math.max(1024,t),i.usedWidth=0,i.invalidatedWidth=0,i.fullnessChecks=0,i.canvas=document.createElement("canvas"),i.canvas.width=i.width,i.canvas.height=i.height,i.context=i.canvas.getContext("2d"),i},l.recycleTexture=function(e,t){for(var n=this,r=n.getTextureQueue(e),a=n.getRetiredTextureQueue(e),o=0;o<a.length;o++){var s=a[o];if(s.width>=t)return s.retired=!1,s.usedWidth=0,s.invalidatedWidth=0,s.fullnessChecks=0,i.clearArray(s.eleCaches),s.context.setTransform(1,0,0,1,0,0),s.context.clearRect(0,0,s.width,s.height),i.removeFromArray(a,s),r.push(s),s}},l.queueElement=function(e,t,n){var i=this,a=i.getElementQueue(),o=i.getElementIdToQueue(),s=e.id(),u=o[s];if(u)u.level=Math.max(u.level,n),u.reqs++,a.updateItem(u);else{var l={ele:e,bb:t,position:r.copyPosition(e.position()),level:n,reqs:1};e.isEdge()&&(l.positions={source:r.copyPosition(e.source().position()),target:r.copyPosition(e.target().position())}),a.push(l),o[s]=l}},l.dequeue=function(e){for(var t=this,n=t.getElementQueue(),i=t.getElementIdToQueue(),a=[],o=0;o<1&&n.size()>0;o++){var u=n.pop();i[u.ele.id()]=null,a.push(u);var l,c=u.ele;l=(!c.isEdge()||r.arePositionsSame(c.source().position(),u.positions.source)&&r.arePositionsSame(c.target().position(),u.positions.target))&&r.arePositionsSame(c.position(),u.position)?u.bb:c.boundingBox(),t.getElement(u.ele,l,e,u.level,s.dequeue)}return a},l.onDequeue=function(e){this.onDequeues.push(e)},l.offDequeue=function(e){i.removeFromArray(this.onDequeues,e)},l.setupDequeueing=o.setupDequeueing({deqRedrawThreshold:100,deqCost:.15,deqAvgCost:.1,deqNoDrawCost:.9,deqFastCost:.9,deq:function(e,t,n){return e.dequeue(t,n)},onDeqd:function(e,t){for(var n=0;n<e.onDequeues.length;n++)(0,e.onDequeues[n])(t)},shouldRedraw:function(e,t,n,i){for(var a=0;a<t.length;a++){var o=t[a].bb;if(r.boundingBoxesIntersect(o,i))return!0}return!1},priority:function(e){return e.renderer.beforeRenderPriorities.eleTxrDeq}}),e.exports=u},function(e,t,n){"use strict";function r(e,t){for(var n=atob(e),r=new ArrayBuffer(n.length),i=new Uint8Array(r),a=0;a<n.length;a++)i[a]=n.charCodeAt(a);return new Blob([r],{type:t})}function i(e){var t=e.indexOf(",");return e.substr(t+1)}function a(e,t,n){var a=t.toDataURL(n,e.quality);switch(e.output){case"blob":return r(i(a),n);case"base64":return i(a);case"base64uri":default:return a}}var o=n(0),s={};s.createBuffer=function(e,t){var n=document.createElement("canvas");return n.width=e,n.height=t,[n,n.getContext("2d")]},s.bufferCanvasImage=function(e){var t=this.cy,n=t.mutableElements(),r=n.boundingBox(),i=this.findContainerClientCoords(),a=e.full?Math.ceil(r.w):i[2],s=e.full?Math.ceil(r.h):i[3],u=o.number(e.maxWidth)||o.number(e.maxHeight),l=this.getPixelRatio(),c=1;if(void 0!==e.scale)a*=e.scale,s*=e.scale,c=e.scale;else if(u){var d=1/0,h=1/0;o.number(e.maxWidth)&&(d=c*e.maxWidth/a),o.number(e.maxHeight)&&(h=c*e.maxHeight/s),c=Math.min(d,h),a*=c,s*=c}u||(a*=l,s*=l,c*=l);var f=document.createElement("canvas");f.width=a,f.height=s,f.style.width=a+"px",f.style.height=s+"px";var p=f.getContext("2d");if(a>0&&s>0){p.clearRect(0,0,a,s),p.globalCompositeOperation="source-over";var v=this.getCachedZSortedEles();if(e.full)p.translate(-r.x1*c,-r.y1*c),p.scale(c,c),this.drawElements(p,v),p.scale(1/c,1/c),p.translate(r.x1*c,r.y1*c);else{var g=t.pan(),y={x:g.x*c,y:g.y*c};c*=t.zoom(),p.translate(y.x,y.y),p.scale(c,c),this.drawElements(p,v),p.scale(1/c,1/c),p.translate(-y.x,-y.y)}e.bg&&(p.globalCompositeOperation="destination-over",p.fillStyle=e.bg,p.rect(0,0,a,s),p.fill())}return f},s.png=function(e){return a(e,this.bufferCanvasImage(e),"image/png")},s.jpg=function(e){return a(e,this.bufferCanvasImage(e),"image/jpeg")},e.exports=s},function(e,t,n){"use strict";function r(e){var t=this;t.data={canvases:new Array(l.CANVAS_LAYERS),contexts:new Array(l.CANVAS_LAYERS),canvasNeedsRedraw:new Array(l.CANVAS_LAYERS),bufferCanvases:new Array(l.BUFFER_COUNT),bufferContexts:new Array(l.CANVAS_LAYERS)};var n="-webkit-tap-highlight-color: rgba(0,0,0,0);";t.data.canvasContainer=document.createElement("div");var r=t.data.canvasContainer.style;t.data.canvasContainer.setAttribute("style",n),r.position="relative",r.zIndex="0",r.overflow="hidden";var i=e.cy.container();i.appendChild(t.data.canvasContainer),(i.getAttribute("style")||"").indexOf(n)<0&&i.setAttribute("style",(i.getAttribute("style")||"")+n);for(var u=0;u<l.CANVAS_LAYERS;u++){var c=t.data.canvases[u]=document.createElement("canvas");t.data.contexts[u]=c.getContext("2d"),c.setAttribute("style","-webkit-user-select: none; -moz-user-select: -moz-none; user-select: none; -webkit-tap-highlight-color: rgba(0,0,0,0); outline-style: none;"+(a.ms()?" -ms-touch-action: none; touch-action: none; ":"")),c.style.position="absolute",c.setAttribute("data-id","layer"+u),c.style.zIndex=String(l.CANVAS_LAYERS-u),t.data.canvasContainer.appendChild(c),t.data.canvasNeedsRedraw[u]=!1}t.data.topCanvas=t.data.canvases[0],t.data.canvases[l.NODE].setAttribute("data-id","layer"+l.NODE+"-node"),t.data.canvases[l.SELECT_BOX].setAttribute("data-id","layer"+l.SELECT_BOX+"-selectbox"),t.data.canvases[l.DRAG].setAttribute("data-id","layer"+l.DRAG+"-drag");for(var u=0;u<l.BUFFER_COUNT;u++)t.data.bufferCanvases[u]=document.createElement("canvas"),t.data.bufferContexts[u]=t.data.bufferCanvases[u].getContext("2d"),t.data.bufferCanvases[u].style.position="absolute",t.data.bufferCanvases[u].setAttribute("data-id","buffer"+u),t.data.bufferCanvases[u].style.zIndex=String(-u-1),t.data.bufferCanvases[u].style.visibility="hidden";t.pathsEnabled=!0,t.data.eleTxrCache=new o(t),t.data.lyrTxrCache=new s(t,t.data.eleTxrCache),t.onUpdateEleCalcs(function(e,n){for(var r=0;r<n.length;r++){var i=n[r],a=i._private.rstyle,o=a.dirtyEvents;i.isNode()&&o&&1===o.length&&o.position||t.data.eleTxrCache.invalidateElement(i)}n.length>0&&t.data.lyrTxrCache.invalidateElements(n)})}var i=n(1),a=n(0),o=n(110),s=n(113),u=r,l=r.prototype;l.CANVAS_LAYERS=3,l.SELECT_BOX=0,l.DRAG=1,l.NODE=2,l.BUFFER_COUNT=3,l.TEXTURE_BUFFER=0,l.MOTIONBLUR_BUFFER_NODE=1,l.MOTIONBLUR_BUFFER_DRAG=2,l.redrawHint=function(e,t){var n=this;switch(e){case"eles":n.data.canvasNeedsRedraw[l.NODE]=t;break;case"drag":n.data.canvasNeedsRedraw[l.DRAG]=t;break;case"select":n.data.canvasNeedsRedraw[l.SELECT_BOX]=t}};var c="undefined"!=typeof Path2D;l.path2dEnabled=function(e){return void 0===e?this.pathsEnabled:void(this.pathsEnabled=!!e)},l.usePaths=function(){return c&&this.pathsEnabled},[n(102),n(104),n(103),n(105),n(106),n(107),n(108),n(109),n(111),n(114)].forEach(function(e){i.extend(l,e)}),e.exports=u},function(e,t,n){"use strict";function r(e,t){null!=e.imageSmoothingEnabled?e.imageSmoothingEnabled=t:(e.webkitImageSmoothingEnabled=t,e.mozImageSmoothingEnabled=t,e.msImageSmoothingEnabled=t)}var i=n(1),a=n(2),o=n(8),s=n(0),u=n(16),l=function(e,t){var n=this,r=n.renderer=e;n.layersByLevel={},n.firstGet=!0,n.lastInvalidationTime=i.performanceNow()-500,n.skipping=!1,r.beforeRender(function(e,t){t-n.lastInvalidationTime<=250?n.skipping=!0:n.skipping=!1});var a=function(e,t){return t.reqs-e.reqs};n.layersQueue=new o(a),n.eleTxrCache=t,n.setupEleCacheInvalidation(),n.setupDequeueing()},c=l.prototype,d=0,h=Math.pow(2,53)-1;c.makeLayer=function(e,t){var n=Math.pow(2,t),r=Math.ceil(e.w*n),i=Math.ceil(e.h*n),a=document.createElement("canvas");a.width=r,a.height=i;var o={
id:d=++d%h,bb:e,level:t,width:r,height:i,canvas:a,context:a.getContext("2d"),eles:[],elesQueue:[],reqs:0},s=o.context,u=-o.bb.x1,l=-o.bb.y1;return s.scale(n,n),s.translate(u,l),o},c.getLayers=function(e,t,n){var r=this,o=r.renderer,s=o.cy,u=s.zoom(),l=r.firstGet;if(r.firstGet=!1,null==n)if((n=Math.ceil(a.log2(u*t)))<-4)n=-4;else if(u>=3.99||n>2)return null;r.validateLayersElesOrdering(n,e);var c,d,h=r.layersByLevel,f=Math.pow(2,n),p=h[n]=h[n]||[],v=r.levelIsComplete(n,e);if(v)return p;!function(){var t=function(t){if(r.validateLayersElesOrdering(t,e),r.levelIsComplete(t,e))return d=h[t],!0},a=function(e){if(!d)for(var r=n+e;-4<=r&&r<=2&&!t(r);r+=e);};a(1),a(-1);for(var o=p.length-1;o>=0;o--){var s=p[o];s.invalid&&i.removeFromArray(p,s)}}();var g=function(){if(!c){c=a.makeBoundingBox();for(var t=0;t<e.length;t++)a.updateBoundingBox(c,e[t].boundingBox())}return c};if(r.skipping&&!l)return null;for(var y=null,m=e.length/1,b=!l,x=0;x<e.length;x++){var w=e[x],_=w._private.rscratch,E=_.imgLayerCaches=_.imgLayerCaches||{},k=E[n];if(k)y=k;else{if((!y||y.eles.length>=m||!a.boundingBoxInBoundingBox(y.bb,w.boundingBox()))&&!(y=function(e){e=e||{};var t=e.after;if(g(),c.w*f*(c.h*f)>16e6)return null;var i=r.makeLayer(c,n);if(null!=t){var a=p.indexOf(t)+1;p.splice(a,0,i)}else(void 0===e.insert||e.insert)&&p.unshift(i);return i}({insert:!0,after:y})))return null;d||b?r.queueLayer(y,w):r.drawEleInLayer(y,w,n,t),y.eles.push(w),E[n]=y}}return d||(b?null:p)},c.getEleLevelForLayerLevel=function(e,t){return e},c.drawEleInLayer=function(e,t,n,i){var a=this,o=this.renderer,s=e.context,u=t.boundingBox();if(0!==u.w&&0!==u.h&&t.visible()){var l=a.eleTxrCache,c=l.reasons.highQuality;n=a.getEleLevelForLayerLevel(n,i);var d=l.getElement(t,u,null,n,c);d?(r(s,!1),s.drawImage(d.texture.canvas,d.x,0,d.width,d.height,u.x1,u.y1,u.w,u.h),r(s,!0)):o.drawElement(s,t)}},c.levelIsComplete=function(e,t){var n=this,r=n.layersByLevel[e];if(!r||0===r.length)return!1;for(var i=0,a=0;a<r.length;a++){var o=r[a];if(o.reqs>0)return!1;if(o.invalid)return!1;i+=o.eles.length}return i===t.length},c.validateLayersElesOrdering=function(e,t){var n=this.layersByLevel[e];if(n)for(var r=0;r<n.length;r++){for(var i=n[r],a=-1,o=0;o<t.length;o++)if(i.eles[0]===t[o]){a=o;break}if(a<0)this.invalidateLayer(i);else for(var s=a,o=0;o<i.eles.length;o++)if(i.eles[o]!==t[s+o]){this.invalidateLayer(i);break}}},c.updateElementsInLayers=function(e,t){for(var n=this,r=s.element(e[0]),i=0;i<e.length;i++)for(var a=r?null:e[i],o=r?e[i]:e[i].ele,u=o._private.rscratch,l=u.imgLayerCaches=u.imgLayerCaches||{},c=-4;c<=2;c++){var d=l[c];d&&(a&&n.getEleLevelForLayerLevel(d.level)!==a.level||t(d,o,a))}},c.haveLayers=function(){for(var e=this,t=!1,n=-4;n<=2;n++){var r=e.layersByLevel[n];if(r&&r.length>0){t=!0;break}}return t},c.invalidateElements=function(e){var t=this;t.lastInvalidationTime=i.performanceNow(),0!==e.length&&t.haveLayers()&&t.updateElementsInLayers(e,function(e,n,r){t.invalidateLayer(e)})},c.invalidateLayer=function(e){if(this.lastInvalidationTime=i.performanceNow(),!e.invalid){var t=e.level,n=e.eles,r=this.layersByLevel[t];i.removeFromArray(r,e),e.elesQueue=[],e.invalid=!0,e.replacement&&(e.replacement.invalid=!0);for(var a=0;a<n.length;a++){var o=n[a]._private.rscratch.imgLayerCaches;o&&(o[t]=null)}}},c.refineElementTextures=function(e){var t=this;t.updateElementsInLayers(e,function(e,n,r){var i=e.replacement;if(i||(i=e.replacement=t.makeLayer(e.bb,e.level),i.replaces=e,i.eles=e.eles),!i.reqs)for(var a=0;a<i.eles.length;a++)t.queueLayer(i,i.eles[a])})},c.setupEleCacheInvalidation=function(){var e=this,t=[],n=i.debounce(function(){e.refineElementTextures(t),t=[]},50);e.eleTxrCache.onDequeue(function(e){for(var r=0;r<e.length;r++)t.push(e[r]);n()})},c.queueLayer=function(e,t){var n=this,r=n.layersQueue,i=e.elesQueue,a=i.hasId=i.hasId||{};if(!e.replacement){if(t){if(a[t.id()])return;i.push(t),a[t.id()]=!0}e.reqs?(e.reqs++,r.updateItem(e)):(e.reqs=1,r.push(e))}},c.dequeue=function(e){for(var t=this,n=t.layersQueue,r=[],i=0;i<1&&0!==n.size();){var a=n.peek();if(a.replacement)n.pop();else if(a.replaces&&a!==a.replaces.replacement)n.pop();else if(a.invalid)n.pop();else{var o=a.elesQueue.shift();o&&(t.drawEleInLayer(a,o,a.level,e),i++),0===r.length&&r.push(!0),0===a.elesQueue.length&&(n.pop(),a.reqs=0,a.replaces&&t.applyLayerReplacement(a),t.requestRedraw())}}return r},c.applyLayerReplacement=function(e){var t=this,n=t.layersByLevel[e.level],r=e.replaces,i=n.indexOf(r);if(!(i<0||r.invalid)){n[i]=e;for(var a=0;a<e.eles.length;a++){var o=e.eles[a]._private,s=o.imgLayerCaches=o.imgLayerCaches||{};s&&(s[e.level]=e)}t.requestRedraw()}},c.requestRedraw=i.debounce(function(){var e=this.renderer;e.redrawHint("eles",!0),e.redrawHint("drag",!0),e.redraw()},100),c.setupDequeueing=u.setupDequeueing({deqRedrawThreshold:50,deqCost:.15,deqAvgCost:.1,deqNoDrawCost:.9,deqFastCost:.9,deq:function(e,t){return e.dequeue(t)},onDeqd:i.noop,shouldRedraw:i.trueify,priority:function(e){return e.renderer.beforeRenderPriorities.lyrTxrDeq}}),e.exports=l},function(e,t,n){"use strict";var r={};r.nodeShapeImpl=function(e,t,n,r,i,a,o){switch(e){case"ellipse":return this.drawEllipsePath(t,n,r,i,a);case"polygon":return this.drawPolygonPath(t,n,r,i,a,o);case"roundrectangle":return this.drawRoundRectanglePath(t,n,r,i,a);case"cutrectangle":return this.drawCutRectanglePath(t,n,r,i,a);case"bottomroundrectangle":return this.drawBottomRoundRectanglePath(t,n,r,i,a);case"barrel":return this.drawBarrelPath(t,n,r,i,a)}},e.exports=r},function(e,t,n){"use strict";e.exports=[{name:"null",impl:n(116)},{name:"base",impl:n(98)},{name:"canvas",impl:n(112)}]},function(e,t,n){"use strict";function r(e){this.options=e,this.notifications=0}var i=function(){};r.prototype={recalculateRenderedStyle:i,notify:function(){this.notifications++},init:i},e.exports=r},function(e,t,n){"use strict";var r=n(0),i=n(12),a=n(21),o=n(22),s=function(e){return void 0===e&&(e={}),r.plainObject(e)?new i(e):r.string(e)?a.apply(a,arguments):void 0};s.use=function(e){var t=Array.prototype.slice.call(arguments,1);return t.unshift(s),e.apply(null,t),this},s.version=n(23),s.stylesheet=s.Stylesheet=o,e.exports=s},function(e,t,n){"use strict";function r(){this._obj={}}var i=r.prototype;i.set=function(e,t){this._obj[e]=t},i.delete=function(e){this._obj[e]=null},i.has=function(e){return null!=this._obj[e]},i.get=function(e){return this._obj[e]},e.exports=r},function(e,t,n){"use strict";var r=function(){function e(e,t){var n=[],r=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(r=(o=s.next()).done)&&(n.push(o.value),!t||n.length!==t);r=!0);}catch(e){i=!0,a=e}finally{try{!r&&s.return&&s.return()}finally{if(i)throw a}}return n}return function(t,n){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,n);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),i=n(17),a=i.stateSelectorRegex,o=n(122),s=n(1),u=n(11),l=function(e){return e.replace(new RegExp("\\\\("+o.metaChar+")","g"),function(e,t){return t})},c=function(e,t,n){t===e[e.length-1]&&(e[e.length-1]=n)},d=[{name:"group",query:!0,regex:"("+o.group+")",populate:function(e,t,n){var i=r(n,1),a=i[0];t.group="*"===a?a:a+"s"}},{name:"state",query:!0,regex:a,populate:function(e,t,n){var i=r(n,1),a=i[0];t.colonSelectors.push(a)}},{name:"id",query:!0,regex:"\\#("+o.id+")",populate:function(e,t,n){var i=r(n,1),a=i[0];t.ids.push(l(a))}},{name:"className",query:!0,regex:"\\.("+o.className+")",populate:function(e,t,n){var i=r(n,1),a=i[0];t.classes.push(l(a))}},{name:"dataExists",query:!0,regex:"\\[\\s*("+o.variable+")\\s*\\]",populate:function(e,t,n){var i=r(n,1),a=i[0];t.data.push({field:l(a)})}},{name:"dataCompare",query:!0,regex:"\\[\\s*("+o.variable+")\\s*("+o.comparatorOp+")\\s*("+o.value+")\\s*\\]",populate:function(e,t,n){var i=r(n,3),a=i[0],s=i[1],u=i[2];u=null!=new RegExp("^"+o.string+"$").exec(u)?u.substring(1,u.length-1):parseFloat(u),t.data.push({field:l(a),operator:s,value:u})}},{name:"dataBool",query:!0,regex:"\\[\\s*("+o.boolOp+")\\s*("+o.variable+")\\s*\\]",populate:function(e,t,n){var i=r(n,2),a=i[0],o=i[1];t.data.push({field:l(o),operator:a})}},{name:"metaCompare",query:!0,regex:"\\[\\[\\s*("+o.meta+")\\s*("+o.comparatorOp+")\\s*("+o.number+")\\s*\\]\\]",populate:function(e,t,n){var i=r(n,3),a=i[0],o=i[1],s=i[2];t.meta.push({field:l(a),operator:o,value:parseFloat(s)})}},{name:"nextQuery",separator:!0,regex:o.separator,populate:function(e){var t=e[e.length++]=u();return e.currentSubject=null,t}},{name:"directedEdge",separator:!0,regex:o.directedEdge,populate:function(e,t){var n=u(),r=t,i=u();return n.group="edges",n.target=i,n.source=r,n.subject=e.currentSubject,c(e,t,n),i}},{name:"undirectedEdge",separator:!0,regex:o.undirectedEdge,populate:function(e,t){var n=u(),r=t,i=u();return n.group="edges",n.connectedNodes=[r,i],n.subject=e.currentSubject,c(e,t,n),i}},{name:"child",separator:!0,regex:o.child,populate:function(e,t){var n=u();return n.parent=t,n.subject=e.currentSubject,c(e,t,n),n}},{name:"descendant",separator:!0,regex:o.descendant,populate:function(e,t){var n=u();return n.ancestor=t,n.subject=e.currentSubject,c(e,t,n),n}},{name:"subject",modifier:!0,regex:o.subject,populate:function(e,t){return null!=e.currentSubject&&t.subject!=t?(s.error("Redefinition of subject in selector `"+e.toString()+"`"),!1):(e.currentSubject=t,t.subject=t,e[e.length-1].subject=t,void 0)}}];d.forEach(function(e){return e.regexObj=new RegExp("^"+e.regex)}),e.exports=d},function(e,t,n){"use strict";var r=n(17),i=r.stateSelectorMatches,a=n(0),o=function(e,t){for(var n=!0,r=0;r<e[t.name].length;r++){var i=e[t.name][r],o=i.operator,s=i.value,u=i.field,l=void 0,c=t.fieldValue(u);if(null!=o&&null!=s){var d=a.string(c)||a.number(c)?""+c:"",h=""+s,f=!1;o.indexOf("@")>=0&&(d=d.toLowerCase(),h=h.toLowerCase(),o=o.replace("@",""),f=!0);var p=!1;o.indexOf("!")>=0&&(o=o.replace("!",""),p=!0),f&&(s=h.toLowerCase(),c=d.toLowerCase());var v=!1;switch(o){case"*=":l=d.indexOf(h)>=0;break;case"$=":l=d.indexOf(h,d.length-h.length)>=0;break;case"^=":l=0===d.indexOf(h);break;case"=":l=c===s;break;case">":v=!0,l=c>s;break;case">=":v=!0,l=c>=s;break;case"<":v=!0,l=c<s;break;case"<=":v=!0,l=c<=s;break;default:l=!1}!p||null==c&&v||(l=!l)}else if(null!=o)switch(o){case"?":l=!!c;break;case"!":l=!c;break;case"^":l=void 0===c}else l=void 0!==c;if(!l){n=!1;break}}return n},s=function(e,t,n){if(null!=e){var r=!1;if(!t)return!1;n=n();for(var i=0;i<n.length;i++)if(u(e,n[i])){r=!0;break}return r}return!0},u=function(e,t){if(e.groupOnly)return"*"===e.group||e.group===t.group();if(null!=e.group&&"*"!=e.group&&e.group!=t.group())return!1;var n=t.cy(),r=void 0,a=!0;for(r=0;r<e.colonSelectors.length;r++){var u=e.colonSelectors[r];if(!(a=i(u,t)))break}if(!a)return!1;var l=!0;for(r=0;r<e.ids.length;r++){var c=e.ids[r],d=t.id();if(!(l=l&&c==d))break}if(!l)return!1;var h=!0;for(r=0;r<e.classes.length;r++){var f=e.classes[r];if(!(h=h&&t.hasClass(f)))break}if(!h)return!1;if(!o(e,{name:"data",fieldValue:function(e){return t.data(e)}}))return!1;if(!o(e,{name:"meta",fieldValue:function(e){return t[e]()}}))return!1;if(null!=e.collection&&!e.collection.hasElementWithId(t.id()))return!1;if(null!=e.filter&&t.collection().some(e.filter))return!1;var p=n.hasCompoundNodes(),v=function(){return t.source()},g=function(){return t.target()};if(!s(e.parent,p,function(){return t.parent()}))return!1;if(!s(e.ancestor,p,function(){return t.parents()}))return!1;if(!s(e.child,p,function(){return t.children()}))return!1;if(!s(e.descendant,p,function(){return t.descendants()}))return!1;if(!s(e.source,!0,v))return!1;if(!s(e.target,!0,g))return!1;if(e.connectedNodes){var y=e.connectedNodes[0],m=e.connectedNodes[1];if(s(y,!0,v)&&s(m,!0,g));else if(!s(y,!0,g)||!s(m,!0,v))return!1}return!0},l=function(e){var t=this,n=e.cy();if(t.invalid())return n.collection();if(1===t.length&&1===t[0].length&&1===t[0].ids.length)return e.getElementById(t[0].ids[0]).collection();var r=function(e){for(var n=0;n<t.length;n++){var r=t[n];if(u(r,e))return!0}return!1};return null==t.text()&&(r=function(){return!0}),e.filter(r)},c=function(e){var t=this;if(t.invalid())return!1;for(var n=0;n<t.length;n++){var r=t[n];if(u(r,e))return!0}return!1};e.exports={matches:c,filter:l}},function(e,t,n){"use strict";var r=n(1),i=n(119),a=n(11),o=function(e){for(var t=void 0,n=void 0,r=void 0,a=0;a<i.length;a++){var o=i[a],s=o.name,u=e.match(o.regexObj);if(null!=u){n=u,t=o,r=s;var l=u[0];e=e.substring(l.length);break}}return{expr:t,match:n,name:r,remaining:e}},s=function(e){var t=e.match(/^\s+/);if(t){var n=t[0];e=e.substring(n.length)}return e},u=function(e){var t=this,n=t._private.selectorText=e,i=t[0]=a();for(t.length=1,n=s(n);;){var u=o(n);if(null==u.expr)return r.error("The selector `"+e+"`is invalid"),!1;var l=u.match.slice(1),c=u.expr.populate(t,i,l);if(!1===c)return!1;if(null!=c&&(i=c),n=u.remaining,n.match(/^\s*$/))break}for(var d=0;d<t.length;d++){var h=t[d];if(null!=h.subject){for(;h.subject!==h;)if(null!=h.parent){var f=h.parent,p=h;p.parent=null,f.child=p,h=f}else{if(null==h.ancestor)return h.source||h.target||h.connectedNodes?(r.error("The selector `"+t.text()+"` can not contain a subject selector that applies to the source or target of an edge selector"),!1):(r.error("When adjusting references for the selector `"+t.text()+"`, neither parent nor ancestor was found"),!1);var v=h.ancestor,g=h;g.ancestor=null,v.descendant=g,h=v}t[d]=h.subject}}return!0};e.exports={parse:u}},function(e,t,n){"use strict";var r=n(1),i={metaChar:"[\\!\\\"\\#\\$\\%\\&\\'\\(\\)\\*\\+\\,\\.\\/\\:\\;\\<\\=\\>\\?\\@\\[\\]\\^\\`\\{\\|\\}\\~]",comparatorOp:"=|\\!=|>|>=|<|<=|\\$=|\\^=|\\*=",boolOp:"\\?|\\!|\\^",string:'"(?:\\\\"|[^"])*"|'+"'(?:\\\\'|[^'])*'",number:r.regex.number,meta:"degree|indegree|outdegree",separator:"\\s*,\\s*",descendant:"\\s+",child:"\\s+>\\s+",subject:"\\$",group:"node|edge|\\*",directedEdge:"\\s+->\\s+",undirectedEdge:"\\s+<->\\s+"};i.variable="(?:[\\w-]|(?:\\\\"+i.metaChar+"))+",i.value=i.string+"|"+i.number,i.className=i.variable,i.id=i.variable,function(){var e=void 0,t=void 0,n=void 0;for(e=i.comparatorOp.split("|"),n=0;n<e.length;n++)t=e[n],i.comparatorOp+="|@"+t;for(e=i.comparatorOp.split("|"),n=0;n<e.length;n++)t=e[n],t.indexOf("!")>=0||"="!==t&&(i.comparatorOp+="|\\!"+t)}(),e.exports=i},function(e,t,n){"use strict";var r=n(1),i=n(0),a=n(5),o={};o.apply=function(e){var t=this,n=t._private,r=n.cy,i=r.collection();n.newStyle&&(n.contextStyles={},n.propDiffs={},t.cleanElements(e,!0));for(var a=0;a<e.length;a++){var o=e[a],s=t.getContextMeta(o);if(!s.empty){i.merge(o);var u=t.getContextStyle(s),l=t.applyContextStyle(s,u,o);n.newStyle||t.updateTransitions(o,l.diffProps),t.updateStyleHints(o)}}return n.newStyle=!1,i},o.getPropertiesDiff=function(e,t){var n=this,r=n._private.propDiffs=n._private.propDiffs||{},i=e+"-"+t,a=r[i];if(a)return a;for(var o=[],s={},u=0;u<n.length;u++){var l=n[u],c="t"===e[u],d="t"===t[u],h=c!==d,f=l.mappedProperties.length>0;if(h||f){var p=void 0;h&&f?p=l.properties:h?p=l.properties:f&&(p=l.mappedProperties);for(var v=0;v<p.length;v++){for(var g=p[v],y=g.name,m=!1,b=u+1;b<n.length;b++){var x=n[b];if("t"===t[b]&&(m=null!=x.properties[g.name]))break}s[y]||m||(s[y]=!0,o.push(y))}}}return r[i]=o,o},o.getContextMeta=function(e){var t=this,n="",r=void 0,i=e._private.styleCxtKey||"";t._private.newStyle&&(i="");for(var a=0;a<t.length;a++){var o=t[a];n+=o.selector&&o.selector.matches(e)?"t":"f"}return r=t.getPropertiesDiff(i,n),e._private.styleCxtKey=n,{key:n,diffPropNames:r,empty:0===r.length}},o.getContextStyle=function(e){var t=e.key,n=this,r=this._private.contextStyles=this._private.contextStyles||{};if(r[t])return r[t];for(var i={_private:{key:t}},a=0;a<n.length;a++){var o=n[a];if("t"===t[a])for(var s=0;s<o.properties.length;s++){var u=o.properties[s];i[u.name]=u}}return r[t]=i,i},o.applyContextStyle=function(e,t,n){for(var r=this,i=e.diffPropNames,a={},o=0;o<i.length;o++){var s=i[o],u=t[s],l=n.pstyle(s);if(!u){if(!l)continue;u=l.bypass?{name:s,deleteBypassed:!0}:{name:s,delete:!0}}if(l!==u){var c=a[s]={prev:l};r.applyParsedProperty(n,u),c.next=n.pstyle(s),c.next&&c.next.bypass&&(c.next=c.next.bypassed)}}return{diffProps:a}},o.updateStyleHints=function(e){var t=e._private,n=this;if(!e.removed()){var r=!1;if("nodes"===t.group)for(var i=1;i<=n.pieBackgroundN;i++){var a=e.pstyle("pie-"+i+"-background-size").value;if(a>0){r=!0;break}}t.hasPie=r;var o=e.pstyle("text-transform").strValue,s=e.pstyle("label").strValue,u=e.pstyle("source-label").strValue,l=e.pstyle("target-label").strValue,c=e.pstyle("font-style").strValue,d=e.pstyle("font-size").pfValue+"px",h=e.pstyle("font-family").strValue,f=e.pstyle("font-weight").strValue,p=e.pstyle("text-valign").strValue,v=e.pstyle("text-valign").strValue,g=e.pstyle("text-outline-width").pfValue,y=e.pstyle("text-wrap").strValue,m=e.pstyle("text-max-width").pfValue,b=c+"$"+d+"$"+h+"$"+f+"$"+o+"$"+p+"$"+v+"$"+g+"$"+y+"$"+m;t.labelStyleKey=b,t.sourceLabelKey=b+"$"+u,t.targetLabelKey=b+"$"+l,t.labelKey=b+"$"+s,t.fontKey=c+"$"+f+"$"+d+"$"+h,t.styleKey=Date.now()}},o.applyParsedProperty=function(e,t){var n=this,a=t,o=e._private.style,s=void 0,u=n.types,l=n.properties[a.name].type,c=a.bypass,d=o[a.name],h=d&&d.bypass,f=e._private,p=function(){n.checkZOrderTrigger(e,a.name,d?d.value:null,a.value)};if("curve-style"===t.name&&"haystack"===t.value&&e.isEdge()&&(e.isLoop()||e.source().isParent()||e.target().isParent())&&(a=t=this.parse(t.name,"bezier",c)),a.delete)return o[a.name]=void 0,p(),!0;if(a.deleteBypassed)return d?!!d.bypass&&(d.bypassed=void 0,p(),!0):(p(),!0);if(a.deleteBypass)return d?!!d.bypass&&(o[a.name]=d.bypassed,p(),!0):(p(),!0);var v=function(){r.error("Do not assign mappings to elements without corresponding data (e.g. ele `"+e.id()+"` for property `"+a.name+"` with data field `"+a.field+"`); try a `["+a.field+"]` selector to limit scope to elements with `"+a.field+"` defined")};switch(a.mapped){case u.mapData:for(var g=a.field.split("."),y=f.data,m=0;m<g.length&&y;m++)y=y[g[m]];var b=void 0;if(b=i.number(y)?(y-a.fieldMin)/(a.fieldMax-a.fieldMin):0,b<0?b=0:b>1&&(b=1),l.color){var x=a.valueMin[0],w=a.valueMax[0],_=a.valueMin[1],E=a.valueMax[1],k=a.valueMin[2],C=a.valueMax[2],P=null==a.valueMin[3]?1:a.valueMin[3],T=null==a.valueMax[3]?1:a.valueMax[3],S=[Math.round(x+(w-x)*b),Math.round(_+(E-_)*b),Math.round(k+(C-k)*b),Math.round(P+(T-P)*b)];s={bypass:a.bypass,name:a.name,value:S,strValue:"rgb("+S[0]+", "+S[1]+", "+S[2]+")"}}else{if(!l.number)return!1;var D=a.valueMin+(a.valueMax-a.valueMin)*b;s=this.parse(a.name,D,a.bypass,"mapping")}s||(s=this.parse(a.name,d.strValue,a.bypass,"mapping")),s||v(),s.mapping=a,a=s;break;case u.data:var I=a.field.split("."),N=f.data;if(N)for(var M=0;M<I.length;M++){var L=I[M];N=N[L]}if(!(s=this.parse(a.name,N,a.bypass,"mapping"))){var B=d?d.strValue:"";s=this.parse(a.name,B,a.bypass,"mapping")}s||v(),s.mapping=a,a=s;break;case u.fn:var O=a.value,A=O(e);s=this.parse(a.name,A,a.bypass,"mapping"),s.mapping=a,a=s;break;case void 0:break;default:return!1}return c?(a.bypassed=h?d.bypassed:d,o[a.name]=a):h?d.bypassed=a:o[a.name]=a,p(),!0},o.cleanElements=function(e,t){for(var n=this,r=n.properties,i=0;i<e.length;i++){var a=e[i];if(t)for(var o=a._private.style,s=0;s<r.length;s++){var u=r[s],l=o[u.name];l&&(l.bypass?l.bypassed=null:o[u.name]=null)}else a._private.style={}}},o.update=function(){this._private.cy.mutableElements().updateStyle()},o.updateMappers=function(e){for(var t=this,n=this._private.cy,r=n.collection(),i=0;i<e.length;i++){for(var a=e[i],o=a._private.style,s=!1,u=0;u<t.properties.length;u++){var l=t.properties[u],c=o[l.name];if(c&&c.mapping){var d=c.mapping;this.applyParsedProperty(a,d),s=!0}}s&&(this.updateStyleHints(a),r.merge(a))}return r},o.updateTransitions=function(e,t,n){var r=this,o=e._private,s=e.pstyle("transition-property").value,u=e.pstyle("transition-duration").pfValue,l=e.pstyle("transition-delay").pfValue;if(s.length>0&&u>0){for(var c={},d=!1,h=0;h<s.length;h++){var f=s[h],p=e.pstyle(f),v=t[f];if(v){var g=v.prev,y=g,m=null!=v.next?v.next:p,b=!1,x=void 0;y&&(i.number(y.pfValue)&&i.number(m.pfValue)?(b=m.pfValue-y.pfValue,x=y.pfValue+1e-6*b):i.number(y.value)&&i.number(m.value)?(b=m.value-y.value,x=y.value+1e-6*b):i.array(y.value)&&i.array(m.value)&&(b=y.value[0]!==m.value[0]||y.value[1]!==m.value[1]||y.value[2]!==m.value[2],x=y.strValue),b&&(c[f]=m.strValue,this.applyBypass(e,f,x),d=!0))}}if(!d)return;o.transitioning=!0,new a(function(t){l>0?e.delayAnimation(l).play().promise().then(t):t()}).then(function(){return e.animation({style:c,duration:u,easing:e.pstyle("transition-timing-function").value,queue:!1}).play().promise()}).then(function(){r.removeBypasses(e,s),e.emitAndNotify("style"),o.transitioning=!1})}else o.transitioning&&(this.removeBypasses(e,s),e.emitAndNotify("style"),o.transitioning=!1)},o.checkZOrderTrigger=function(e,t,n,r){var i=this.properties[t];null==i.triggersZOrder||null!=n&&!i.triggersZOrder(n,r)||this._private.cy.notify({type:"zorder",eles:e})},e.exports=o},function(e,t,n){"use strict";var r=n(0),i=n(1),a={};a.applyBypass=function(e,t,n,a){var o=this,s=[];if("*"===t||"**"===t){if(void 0!==n)for(var u=0;u<o.properties.length;u++){var l=o.properties[u],c=l.name,d=this.parse(c,n,!0);d&&s.push(d)}}else if(r.string(t)){var h=this.parse(t,n,!0);h&&s.push(h)}else{if(!r.plainObject(t))return!1;var f=t;a=n;for(var p=Object.keys(f),v=0;v<p.length;v++){var g=p[v],y=(o.properties[g],f[g]);if(void 0===y&&(y=f[i.dash2camel(g)]),void 0!==y){var m=this.parse(g,y,!0);m&&s.push(m)}}}if(0===s.length)return!1;for(var b=!1,x=0;x<e.length;x++){for(var w=e[x],_={},E=void 0,k=0;k<s.length;k++){var C=s[k];if(a){var P=w.pstyle(C.name);E=_[C.name]={prev:P}}b=this.applyParsedProperty(w,C)||b,a&&(E.next=w.pstyle(C.name))}b&&this.updateStyleHints(w),a&&this.updateTransitions(w,_,!0)}return b},a.overrideBypass=function(e,t,n){t=i.camel2dash(t);for(var r=0;r<e.length;r++){var a=e[r],o=a._private.style[t],s=this.properties[t].type,u=s.color,l=s.mutiple;if(o&&o.bypass){var c=null!=o.pfValue?o.pfValue:o.value;o.value=n,null!=o.pfValue&&(o.pfValue=n),o.strValue=u?"rgb("+n.join(",")+")":l?n.join(" "):""+n,this.checkZOrderTrigger(a,t,c,n)}else this.applyBypass(a,t,n)}},a.removeAllBypasses=function(e,t){return this.removeBypasses(e,this.propertyNames,t)},a.removeBypasses=function(e,t,n){for(var r=0;r<e.length;r++){for(var i=e[r],a={},o=0;o<t.length;o++){var s=t[o],u=this.properties[s],l=i.pstyle(u.name);if(l&&l.bypass){var c=this.parse(s,"",!0),d=a[u.name]={prev:l};this.applyParsedProperty(i,c),d.next=i.pstyle(u.name)}}this.updateStyleHints(i),n&&this.updateTransitions(i,a,!0)}},e.exports=a},function(e,t,n){"use strict";var r=n(4),i={};i.getEmSizeInPixels=function(){var e=this.containerCss("font-size");return null!=e?parseFloat(e):1},i.containerCss=function(e){var t=this._private.cy,n=t.container();if(r&&n&&r.getComputedStyle)return r.getComputedStyle(n).getPropertyValue(e)},e.exports=i},function(e,t,n){"use strict";var r=n(1),i=n(0),a={};a.getRenderedStyle=function(e,t){return t?this.getStylePropertyValue(e,t,!0):this.getRawStyle(e,!0)},a.getRawStyle=function(e,t){var n=this;if(e=e[0]){for(var i={},a=0;a<n.properties.length;a++){var o=n.properties[a],s=n.getStylePropertyValue(e,o.name,t);null!=s&&(i[o.name]=s,i[r.dash2camel(o.name)]=s)}return i}},a.getIndexedStyle=function(e,t,n,r){var i=e.pstyle(t)[n][r];return null!=i?i:e.cy().style().getDefaultProperty(t)[n][0]},a.getStylePropertyValue=function(e,t,n){var r=this;if(e=e[0]){var i=r.properties[t];i.alias&&(i=i.pointsTo);var a=i.type,o=e.pstyle(i.name),s=e.cy().zoom();if(o){var u=o.units?a.implicitUnits||"px":null;return u?[].concat(o.pfValue).map(function(e){return e*(n?s:1)+u}).join(" "):o.strValue}}},a.getAnimationStartStyle=function(e,t){for(var n={},r=0;r<t.length;r++){var a=t[r],o=a.name,s=e.pstyle(o);void 0!==s&&(s=i.plainObject(s)?this.parse(o,s.strValue):this.parse(o,s)),s&&(n[o]=s)}return n},a.getPropsList=function(e){var t=this,n=[],i=e,a=t.properties;if(i)for(var o=Object.keys(i),s=0;s<o.length;s++){var u=o[s],l=i[u],c=a[u]||a[r.camel2dash(u)],d=this.parse(c.name,l);d&&n.push(d)}return n},e.exports=a},function(e,t,n){"use strict";var r={};r.appendFromJson=function(e){for(var t=this,n=0;n<e.length;n++){var r=e[n],i=r.selector,a=r.style||r.css,o=Object.keys(a);t.selector(i);for(var s=0;s<o.length;s++){var u=o[s],l=a[u];t.css(u,l)}}return t},r.fromJson=function(e){var t=this;return t.resetToDefault(),t.appendFromJson(e),t},r.json=function(){for(var e=[],t=this.defaultLength;t<this.length;t++){for(var n=this[t],r=n.selector,i=n.properties,a={},o=0;o<i.length;o++){var s=i[o];a[s.name]=s.strValue}e.push({selector:r?r.toString():"core",style:a})}return e},e.exports=r},function(e,t,n){"use strict";var r=n(1),i=n(0),a=n(2),o={};o.parse=function(e,t,n,a){var o=this;if(i.fn(t))return o.parseImplWarn(e,t,n,a);var s="mapping"===a||!0===a||!1===a||null==a?"dontcare":a,u=[e,t,n,s].join("$"),l=o.propCache=o.propCache||{},c=void 0;return(c=l[u])||(c=l[u]=o.parseImplWarn(e,t,n,a)),(n||"mapping"===a)&&(c=r.copy(c))&&(c.value=r.copy(c.value)),c},o.parseImplWarn=function(e,t,n,i){var a=this.parseImpl(e,t,n,i);return a||null==t||r.error("The style property `%s: %s` is invalid",e,t),a},o.parseImpl=function(e,t,n,o){var s=this;e=r.camel2dash(e);var u=s.properties[e],l=t,c=s.types;if(!u)return null;if(void 0===t||null===t)return null;u.alias&&(u=u.pointsTo,e=u.name);var d=i.string(t);d&&(t=t.trim());var h=u.type;if(!h)return null;if(n&&(""===t||null===t))return{name:e,value:t,bypass:!0,deleteBypass:!0};if(i.fn(t))return{name:e,value:t,strValue:"fn",mapped:c.fn,bypass:n};var f=void 0,p=void 0;if(!d||o);else{if(f=new RegExp(c.data.regex).exec(t)){if(n)return!1;var v=c.data;return{name:e,value:f,strValue:""+t,mapped:v,field:f[1],bypass:n}}if(p=new RegExp(c.mapData.regex).exec(t)){if(n)return!1;if(h.multiple)return!1;var g=c.mapData;if(!h.color&&!h.number)return!1;var y=this.parse(e,p[4]);if(!y||y.mapped)return!1;var m=this.parse(e,p[5]);if(!m||m.mapped)return!1;if(y.value===m.value)return!1;if(h.color){var b=y.value,x=m.value;if(!(b[0]!==x[0]||b[1]!==x[1]||b[2]!==x[2]||b[3]!==x[3]&&(null!=b[3]&&1!==b[3]||null!=x[3]&&1!==x[3])))return!1}return{name:e,value:p,strValue:""+t,mapped:g,field:p[1],fieldMin:parseFloat(p[2]),fieldMax:parseFloat(p[3]),valueMin:y.value,valueMax:m.value,bypass:n}}}if(h.multiple&&"multiple"!==o){var w=void 0;if(w=d?t.split(/\s+/):i.array(t)?t:[t],h.evenMultiple&&w.length%2!=0)return null;for(var _=[],E=[],k=[],C=!1,P=0;P<w.length;P++){var T=s.parse(e,w[P],n,"multiple");C=C||i.string(T.value),_.push(T.value),k.push(null!=T.pfValue?T.pfValue:T.value),E.push(T.units)}return h.validate&&!h.validate(_,E)?null:h.singleEnum&&C?1===_.length&&i.string(_[0])?{name:e,value:_[0],strValue:_[0],bypass:n}:null:{name:e,value:_,pfValue:k,strValue:_.join(" "),bypass:n,units:E}}var S=function(){for(var r=0;r<h.enums.length;r++)if(h.enums[r]===t)return{name:e,value:t,strValue:""+t,bypass:n};return null};if(h.number){var D=void 0,I="px";if(h.units&&(D=h.units),h.implicitUnits&&(I=h.implicitUnits),!h.unitless)if(d){var N="px|em"+(h.allowPercent?"|\\%":"");D&&(N=D);var M=t.match("^("+r.regex.number+")("+N+")?$");M&&(t=M[1],D=M[2]||I)}else D&&!h.implicitUnits||(D=I);if(t=parseFloat(t),isNaN(t)&&void 0===h.enums)return null;if(isNaN(t)&&void 0!==h.enums)return t=l,S();if(h.integer&&!i.integer(t))return null;if(void 0!==h.min&&(t<h.min||h.strictMin&&t===h.min)||void 0!==h.max&&(t>h.max||h.strictMax&&t===h.max))return null;var L={name:e,value:t,strValue:""+t+(D||""),units:D,bypass:n};return h.unitless||"px"!==D&&"em"!==D?L.pfValue=t:L.pfValue="px"!==D&&D?this.getEmSizeInPixels()*t:t,"ms"!==D&&"s"!==D||(L.pfValue="ms"===D?t:1e3*t),"deg"!==D&&"rad"!==D||(L.pfValue="rad"===D?t:a.deg2rad(t)),"%"===D&&(L.pfValue=t/100),L}if(h.propList){var B=[],O=""+t;if("none"===O);else{for(var A=O.split(","),z=0;z<A.length;z++){var R=A[z].trim();s.properties[R]&&B.push(R)}if(0===B.length)return null}return{name:e,value:B,strValue:0===B.length?"none":B.join(", "),bypass:n}}if(h.color){var j=r.color2tuple(t);return j?{name:e,value:j,pfValue:j,strValue:""+t,bypass:n}:null}if(h.regex||h.regexes){if(h.enums){var F=S();if(F)return F}for(var V=h.regexes?h.regexes:[h.regex],q=0;q<V.length;q++){var Y=new RegExp(V[q]),X=Y.exec(t);if(X)return{name:e,value:h.singleRegexMatchValue?X[1]:X,strValue:""+t,bypass:n}}return null}return h.string?{name:e,value:""+t,strValue:""+t,bypass:n}:h.enums?S():null},e.exports=o},function(e,t,n){"use strict";var r=n(1),i=n(0),a={};!function(){var e=r.regex.number,t=r.regex.rgbaNoBackRefs,n=r.regex.hslaNoBackRefs,o=r.regex.hex3,s=r.regex.hex6,u=function(e){return"^"+e+"\\s*\\(\\s*([\\w\\.]+)\\s*\\)$"},l=function(r){var i=e+"|\\w+|"+t+"|"+n+"|"+o+"|"+s;return"^"+r+"\\s*\\(([\\w\\.]+)\\s*\\,\\s*("+e+")\\s*\\,\\s*("+e+")\\s*,\\s*("+i+")\\s*\\,\\s*("+i+")\\)$"},c=["^url\\s*\\(\\s*['\"]?(.+?)['\"]?\\s*\\)$","^(none)$","^(.+)$"];a.types={time:{number:!0,min:0,units:"s|ms",implicitUnits:"ms"},percent:{number:!0,min:0,max:100,units:"%",implicitUnits:"%"},zeroOneNumber:{number:!0,min:0,max:1,unitless:!0},zeroOneNumbers:{number:!0,min:0,max:1,unitless:!0,multiple:!0},nOneOneNumber:{number:!0,min:-1,max:1,unitless:!0},nonNegativeInt:{number:!0,min:0,integer:!0,unitless:!0},position:{enums:["parent","origin"]},nodeSize:{number:!0,min:0,enums:["label"]},number:{number:!0,unitless:!0},numbers:{number:!0,unitless:!0,multiple:!0},positiveNumber:{number:!0,unitless:!0,min:0,strictMin:!0},size:{number:!0,min:0},bidirectionalSize:{number:!0},bidirectionalSizes:{number:!0,multiple:!0},sizeMaybePercent:{number:!0,min:0,allowPercent:!0},paddingRelativeTo:{enums:["width","height","average","min","max"]},bgWH:{number:!0,min:0,allowPercent:!0,enums:["auto"],multiple:!0},bgPos:{number:!0,allowPercent:!0,multiple:!0},bgRelativeTo:{enums:["inner","include-padding"],multiple:!0},bgRepeat:{enums:["repeat","repeat-x","repeat-y","no-repeat"],multiple:!0},bgFit:{enums:["none","contain","cover"],multiple:!0},bgCrossOrigin:{enums:["anonymous","use-credentials"],multiple:!0},bgClip:{enums:["none","node"]},color:{color:!0},bool:{enums:["yes","no"]},lineStyle:{enums:["solid","dotted","dashed"]},borderStyle:{enums:["solid","dotted","dashed","double"]},curveStyle:{enums:["bezier","unbundled-bezier","haystack","segments"]},fontFamily:{regex:'^([\\w- \\"]+(?:\\s*,\\s*[\\w- \\"]+)*)$'},fontletiant:{enums:["small-caps","normal"]},fontStyle:{enums:["italic","normal","oblique"]},fontWeight:{enums:["normal","bold","bolder","lighter","100","200","300","400","500","600","800","900",100,200,300,400,500,600,700,800,900]},textDecoration:{enums:["none","underline","overline","line-through"]},textTransform:{enums:["none","uppercase","lowercase"]},textWrap:{enums:["none","wrap","ellipsis"]},textBackgroundShape:{enums:["rectangle","roundrectangle"]},nodeShape:{enums:["rectangle","roundrectangle","cutrectangle","bottomroundrectangle","barrel","ellipse","triangle","square","pentagon","hexagon","concavehexagon","heptagon","octagon","tag","star","diamond","vee","rhomboid","polygon"]},compoundIncludeLabels:{enums:["include","exclude"]},arrowShape:{enums:["tee","triangle","triangle-tee","triangle-cross","triangle-backcurve","half-triangle-overshot","vee","square","circle","diamond","none"]},arrowFill:{enums:["filled","hollow"]},display:{enums:["element","none"]},visibility:{enums:["hidden","visible"]},zCompoundDepth:{enums:["bottom","orphan","auto","top"]},zIndexCompare:{enums:["auto","manual"]},valign:{enums:["top","center","bottom"]},halign:{enums:["left","center","right"]},text:{string:!0},data:{mapping:!0,regex:u("data")},layoutData:{mapping:!0,regex:u("layoutData")},scratch:{mapping:!0,regex:u("scratch")},mapData:{mapping:!0,regex:l("mapData")},mapLayoutData:{mapping:!0,regex:l("mapLayoutData")},mapScratch:{mapping:!0,regex:l("mapScratch")},fn:{mapping:!0,fn:!0},url:{regexes:c,singleRegexMatchValue:!0},urls:{regexes:c,singleRegexMatchValue:!0,multiple:!0},propList:{propList:!0},angle:{
number:!0,units:"deg|rad",implicitUnits:"rad"},textRotation:{number:!0,units:"deg|rad",implicitUnits:"rad",enums:["none","autorotate"]},polygonPointList:{number:!0,multiple:!0,evenMultiple:!0,min:-1,max:1,unitless:!0},edgeDistances:{enums:["intersection","node-position"]},edgeEndpoint:{number:!0,multiple:!0,units:"%|px|em|deg|rad",implicitUnits:"px",enums:["inside-to-node","outside-to-node","outside-to-line"],singleEnum:!0,validate:function(e,t){switch(e.length){case 2:return"deg"!==t[0]&&"rad"!==t[0]&&"deg"!==t[1]&&"rad"!==t[1];case 1:return i.string(e[0])||"deg"===t[0]||"rad"===t[0];default:return!1}}},easing:{regexes:["^(spring)\\s*\\(\\s*("+e+")\\s*,\\s*("+e+")\\s*\\)$","^(cubic-bezier)\\s*\\(\\s*("+e+")\\s*,\\s*("+e+")\\s*,\\s*("+e+")\\s*,\\s*("+e+")\\s*\\)$"],enums:["linear","ease","ease-in","ease-out","ease-in-out","ease-in-sine","ease-out-sine","ease-in-out-sine","ease-in-quad","ease-out-quad","ease-in-out-quad","ease-in-cubic","ease-out-cubic","ease-in-out-cubic","ease-in-quart","ease-out-quart","ease-in-out-quart","ease-in-quint","ease-out-quint","ease-in-out-quint","ease-in-expo","ease-out-expo","ease-in-out-expo","ease-in-circ","ease-out-circ","ease-in-out-circ"]}};var d={zeroNonZero:function(e,t){return 0===e&&0!==t||0!==e&&0===t},anyDiff:function(e,t){return e!==t}},h=d,f=a.types,p=a.properties=[{name:"label",type:f.text},{name:"text-rotation",type:f.textRotation},{name:"text-margin-x",type:f.bidirectionalSize},{name:"text-margin-y",type:f.bidirectionalSize},{name:"source-label",type:f.text},{name:"source-text-rotation",type:f.textRotation},{name:"source-text-margin-x",type:f.bidirectionalSize},{name:"source-text-margin-y",type:f.bidirectionalSize},{name:"source-text-offset",type:f.size},{name:"target-label",type:f.text},{name:"target-text-rotation",type:f.textRotation},{name:"target-text-margin-x",type:f.bidirectionalSize},{name:"target-text-margin-y",type:f.bidirectionalSize},{name:"target-text-offset",type:f.size},{name:"text-valign",type:f.valign},{name:"text-halign",type:f.halign},{name:"color",type:f.color},{name:"text-outline-color",type:f.color},{name:"text-outline-width",type:f.size},{name:"text-outline-opacity",type:f.zeroOneNumber},{name:"text-opacity",type:f.zeroOneNumber},{name:"text-background-color",type:f.color},{name:"text-background-opacity",type:f.zeroOneNumber},{name:"text-background-padding",type:f.size},{name:"text-border-opacity",type:f.zeroOneNumber},{name:"text-border-color",type:f.color},{name:"text-border-width",type:f.size},{name:"text-border-style",type:f.borderStyle},{name:"text-background-shape",type:f.textBackgroundShape},{name:"text-transform",type:f.textTransform},{name:"text-wrap",type:f.textWrap},{name:"text-max-width",type:f.size},{name:"text-events",type:f.bool},{name:"font-family",type:f.fontFamily},{name:"font-style",type:f.fontStyle},{name:"font-weight",type:f.fontWeight},{name:"font-size",type:f.size},{name:"min-zoomed-font-size",type:f.size},{name:"events",type:f.bool},{name:"display",type:f.display,triggersZOrder:h.anyDiff},{name:"visibility",type:f.visibility,triggersZOrder:h.anyDiff},{name:"opacity",type:f.zeroOneNumber,triggersZOrder:h.zeroNonZero},{name:"z-compound-depth",type:f.zCompoundDepth,triggersZOrder:h.anyDiff},{name:"z-index-compare",type:f.zIndexCompare,triggersZOrder:h.anyDiff},{name:"z-index",type:f.nonNegativeInt,triggersZOrder:h.anyDiff},{name:"overlay-padding",type:f.size},{name:"overlay-color",type:f.color},{name:"overlay-opacity",type:f.zeroOneNumber},{name:"transition-property",type:f.propList},{name:"transition-duration",type:f.time},{name:"transition-delay",type:f.time},{name:"transition-timing-function",type:f.easing},{name:"height",type:f.nodeSize},{name:"width",type:f.nodeSize},{name:"shape",type:f.nodeShape},{name:"shape-polygon-points",type:f.polygonPointList},{name:"background-color",type:f.color},{name:"background-opacity",type:f.zeroOneNumber},{name:"background-blacken",type:f.nOneOneNumber},{name:"padding",type:f.sizeMaybePercent},{name:"padding-relative-to",type:f.paddingRelativeTo},{name:"border-color",type:f.color},{name:"border-opacity",type:f.zeroOneNumber},{name:"border-width",type:f.size},{name:"border-style",type:f.borderStyle},{name:"background-image",type:f.urls},{name:"background-image-crossorigin",type:f.bgCrossOrigin},{name:"background-image-opacity",type:f.zeroOneNumbers},{name:"background-position-x",type:f.bgPos},{name:"background-position-y",type:f.bgPos},{name:"background-width-relative-to",type:f.bgRelativeTo},{name:"background-height-relative-to",type:f.bgRelativeTo},{name:"background-repeat",type:f.bgRepeat},{name:"background-fit",type:f.bgFit},{name:"background-clip",type:f.bgClip},{name:"background-width",type:f.bgWH},{name:"background-height",type:f.bgWH},{name:"position",type:f.position},{name:"compound-sizing-wrt-labels",type:f.compoundIncludeLabels},{name:"min-width",type:f.size},{name:"min-width-bias-left",type:f.sizeMaybePercent},{name:"min-width-bias-right",type:f.sizeMaybePercent},{name:"min-height",type:f.size},{name:"min-height-bias-top",type:f.sizeMaybePercent},{name:"min-height-bias-bottom",type:f.sizeMaybePercent},{name:"line-style",type:f.lineStyle},{name:"line-color",type:f.color},{name:"curve-style",type:f.curveStyle},{name:"haystack-radius",type:f.zeroOneNumber},{name:"source-endpoint",type:f.edgeEndpoint},{name:"target-endpoint",type:f.edgeEndpoint},{name:"control-point-step-size",type:f.size},{name:"control-point-distances",type:f.bidirectionalSizes},{name:"control-point-weights",type:f.numbers},{name:"segment-distances",type:f.bidirectionalSizes},{name:"segment-weights",type:f.numbers},{name:"edge-distances",type:f.edgeDistances},{name:"arrow-scale",type:f.positiveNumber},{name:"loop-direction",type:f.angle},{name:"loop-sweep",type:f.angle},{name:"source-distance-from-node",type:f.size},{name:"target-distance-from-node",type:f.size},{name:"ghost",type:f.bool},{name:"ghost-offset-x",type:f.bidirectionalSize},{name:"ghost-offset-y",type:f.bidirectionalSize},{name:"ghost-opacity",type:f.zeroOneNumber},{name:"selection-box-color",type:f.color},{name:"selection-box-opacity",type:f.zeroOneNumber},{name:"selection-box-border-color",type:f.color},{name:"selection-box-border-width",type:f.size},{name:"active-bg-color",type:f.color},{name:"active-bg-opacity",type:f.zeroOneNumber},{name:"active-bg-size",type:f.size},{name:"outside-texture-bg-color",type:f.color},{name:"outside-texture-bg-opacity",type:f.zeroOneNumber}],v=a.aliases=[{name:"content",pointsTo:"label"},{name:"control-point-distance",pointsTo:"control-point-distances"},{name:"control-point-weight",pointsTo:"control-point-weights"},{name:"edge-text-rotation",pointsTo:"text-rotation"},{name:"padding-left",pointsTo:"padding"},{name:"padding-right",pointsTo:"padding"},{name:"padding-top",pointsTo:"padding"},{name:"padding-bottom",pointsTo:"padding"}];a.pieBackgroundN=16,p.push({name:"pie-size",type:f.sizeMaybePercent});for(var g=1;g<=a.pieBackgroundN;g++)p.push({name:"pie-"+g+"-background-color",type:f.color}),p.push({name:"pie-"+g+"-background-size",type:f.percent}),p.push({name:"pie-"+g+"-background-opacity",type:f.zeroOneNumber});var y=a.arrowPrefixes=["source","mid-source","target","mid-target"];[{name:"arrow-shape",type:f.arrowShape},{name:"arrow-color",type:f.color},{name:"arrow-fill",type:f.arrowFill}].forEach(function(e){y.forEach(function(t){var n=t+"-"+e.name,r=e.type;p.push({name:n,type:r})})},{}),a.propertyNames=p.map(function(e){return e.name});for(var m=0;m<p.length;m++){var b=p[m];p[b.name]=b}for(var x=0;x<v.length;x++){var w=v[x],_=p[w.pointsTo],E={name:w.name,alias:!0,pointsTo:_};p.push(E),p[w.name]=E}}(),a.getDefaultProperty=function(e){return this.getDefaultProperties()[e]},a.getDefaultProperties=r.memoize(function(){for(var e=r.extend({events:"yes","text-events":"no","text-valign":"top","text-halign":"center",color:"#000","text-outline-color":"#000","text-outline-width":0,"text-outline-opacity":1,"text-opacity":1,"text-decoration":"none","text-transform":"none","text-wrap":"none","text-max-width":9999,"text-background-color":"#000","text-background-opacity":0,"text-background-shape":"rectangle","text-background-padding":0,"text-border-opacity":0,"text-border-width":0,"text-border-style":"solid","text-border-color":"#000","font-family":"Helvetica Neue, Helvetica, sans-serif","font-style":"normal","font-weight":"normal","font-size":16,"min-zoomed-font-size":0,"text-rotation":"none","source-text-rotation":"none","target-text-rotation":"none",visibility:"visible",display:"element",opacity:1,"z-compound-depth":"auto","z-index-compare":"auto","z-index":0,label:"","text-margin-x":0,"text-margin-y":0,"source-label":"","source-text-offset":0,"source-text-margin-x":0,"source-text-margin-y":0,"target-label":"","target-text-offset":0,"target-text-margin-x":0,"target-text-margin-y":0,"overlay-opacity":0,"overlay-color":"#000","overlay-padding":10,"transition-property":"none","transition-duration":0,"transition-delay":0,"transition-timing-function":"linear","background-blacken":0,"background-color":"#999","background-opacity":1,"background-image":"none","background-image-crossorigin":"anonymous","background-image-opacity":1,"background-position-x":"50%","background-position-y":"50%","background-width-relative-to":"include-padding","background-height-relative-to":"include-padding","background-repeat":"no-repeat","background-fit":"none","background-clip":"node","background-width":"auto","background-height":"auto","border-color":"#000","border-opacity":1,"border-width":0,"border-style":"solid",height:30,width:30,shape:"ellipse","shape-polygon-points":"-1, -1,   1, -1,   1, 1,   -1, 1",ghost:"no","ghost-offset-y":0,"ghost-offset-x":0,"ghost-opacity":0,padding:0,"padding-relative-to":"width",position:"origin","compound-sizing-wrt-labels":"include","min-width":0,"min-width-bias-left":0,"min-width-bias-right":0,"min-height":0,"min-height-bias-top":0,"min-height-bias-bottom":0},{"pie-size":"100%"},[{name:"pie-{{i}}-background-color",value:"black"},{name:"pie-{{i}}-background-size",value:"0%"},{name:"pie-{{i}}-background-opacity",value:1}].reduce(function(e,t){for(var n=1;n<=a.pieBackgroundN;n++){var r=t.name.replace("{{i}}",n),i=t.value;e[r]=i}return e},{}),{"line-style":"solid","line-color":"#999","control-point-step-size":40,"control-point-weights":.5,"segment-weights":.5,"segment-distances":20,"edge-distances":"intersection","curve-style":"bezier","haystack-radius":0,"arrow-scale":1,"loop-direction":"-45deg","loop-sweep":"-90deg","source-distance-from-node":0,"target-distance-from-node":0,"source-endpoint":"outside-to-node","target-endpoint":"outside-to-node"},[{name:"arrow-shape",value:"none"},{name:"arrow-color",value:"#999"},{name:"arrow-fill",value:"filled"}].reduce(function(e,t){return a.arrowPrefixes.forEach(function(n){var r=n+"-"+t.name,i=t.value;e[r]=i}),e},{})),t={},n=0;n<this.properties.length;n++){var i=this.properties[n];if(!i.pointsTo){var o=i.name,s=e[o],u=this.parse(o,s);t[o]=u}}return t}),a.addDefaultStylesheet=function(){this.selector("$node > node").css({shape:"rectangle",padding:10,"background-color":"#eee","border-color":"#ccc","border-width":1}).selector("edge").css({width:3,"curve-style":"haystack"}).selector(":parent <-> node").css({"curve-style":"bezier","source-endpoint":"outside-to-line","target-endpoint":"outside-to-line"}).selector(":selected").css({"background-color":"#0169D9","line-color":"#0169D9","source-arrow-color":"#0169D9","target-arrow-color":"#0169D9","mid-source-arrow-color":"#0169D9","mid-target-arrow-color":"#0169D9"}).selector("node:parent:selected").css({"background-color":"#CCE1F9","border-color":"#aec8e5"}).selector(":active").css({"overlay-color":"black","overlay-padding":10,"overlay-opacity":.25}).selector("core").css({"selection-box-color":"#ddd","selection-box-opacity":.65,"selection-box-border-color":"#aaa","selection-box-border-width":1,"active-bg-color":"black","active-bg-opacity":.15,"active-bg-size":30,"outside-texture-bg-color":"#000","outside-texture-bg-opacity":.125}),this.defaultLength=this.length},e.exports=a},function(e,t,n){"use strict";var r=n(1),i=n(6),a={};a.appendFromString=function(e){function t(){s=s.length>u.length?s.substr(u.length):""}function n(){l=l.length>c.length?l.substr(c.length):""}var a=this,o=this,s=""+e,u=void 0,l=void 0,c=void 0;for(s=s.replace(/[\/][*](\s|.)+?[*][\/]/g,"");!s.match(/^\s*$/);){var d=s.match(/^\s*((?:.|\s)+?)\s*\{((?:.|\s)+?)\}/);if(!d){r.error("Halting stylesheet parsing: String stylesheet contains more to parse but no selector and block found in: "+s);break}u=d[0];var h=d[1];if("core"!==h&&new i(h)._private.invalid)r.error("Skipping parsing of block: Invalid selector found in string stylesheet: "+h),t();else{var f=d[2],p=!1;l=f;for(var v=[];!l.match(/^\s*$/);){var g=l.match(/^\s*(.+?)\s*:\s*(.+?)\s*;/);if(!g){r.error("Skipping parsing of block: Invalid formatting of style property and value definitions found in:"+f),p=!0;break}c=g[0];var y=g[1],m=g[2];a.properties[y]?o.parse(y,m)?(v.push({name:y,val:m}),n()):(r.error("Skipping property: Invalid property definition in: "+c),n()):(r.error("Skipping property: Invalid property name in: "+c),n())}if(p){t();break}o.selector(h);for(var b=0;b<v.length;b++){var x=v[b];o.css(x.name,x.val)}t()}}return o},a.fromString=function(e){var t=this;return t.resetToDefault(),t.appendFromString(e),t},e.exports=a},function(e,t,n){"use strict";var r=n(0);e.exports={hex2tuple:function(e){if((4===e.length||7===e.length)&&"#"===e[0]){var t=4===e.length,n=void 0,r=void 0,i=void 0;return t?(n=parseInt(e[1]+e[1],16),r=parseInt(e[2]+e[2],16),i=parseInt(e[3]+e[3],16)):(n=parseInt(e[1]+e[2],16),r=parseInt(e[3]+e[4],16),i=parseInt(e[5]+e[6],16)),[n,r,i]}},hsl2tuple:function(e){function t(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}var n=void 0,r=void 0,i=void 0,a=void 0,o=void 0,s=void 0,u=void 0,l=void 0,c=new RegExp("^"+this.regex.hsla+"$").exec(e);if(c){if(r=parseInt(c[1]),r<0?r=(360- -1*r%360)%360:r>360&&(r%=360),r/=360,(i=parseFloat(c[2]))<0||i>100)return;if(i/=100,(a=parseFloat(c[3]))<0||a>100)return;if(a/=100,void 0!==(o=c[4])&&((o=parseFloat(o))<0||o>1))return;if(0===i)s=u=l=Math.round(255*a);else{var d=a<.5?a*(1+i):a+i-a*i,h=2*a-d;s=Math.round(255*t(h,d,r+1/3)),u=Math.round(255*t(h,d,r)),l=Math.round(255*t(h,d,r-1/3))}n=[s,u,l,o]}return n},rgb2tuple:function(e){var t=void 0,n=new RegExp("^"+this.regex.rgba+"$").exec(e);if(n){t=[];for(var r=[],i=1;i<=3;i++){var a=n[i];if("%"===a[a.length-1]&&(r[i]=!0),a=parseFloat(a),r[i]&&(a=a/100*255),a<0||a>255)return;t.push(Math.floor(a))}var o=r[1]||r[2]||r[3],s=r[1]&&r[2]&&r[3];if(o&&!s)return;var u=n[4];if(void 0!==u){if((u=parseFloat(u))<0||u>1)return;t.push(u)}}return t},colorname2tuple:function(e){return this.colors[e.toLowerCase()]},color2tuple:function(e){return(r.array(e)?e:null)||this.colorname2tuple(e)||this.hex2tuple(e)||this.rgb2tuple(e)||this.hsl2tuple(e)},colors:{transparent:[0,0,0,0],aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}}},function(e,t,n){"use strict";var r=n(0);e.exports={mapEmpty:function(e){return null==e||0===Object.keys(e).length},pushMap:function(e){var t=this.getMap(e);null==t?this.setMap(this.extend({},e,{value:[e.value]})):t.push(e.value)},setMap:function(e){for(var t=e.map,n=e.keys,i=n.length,a=0;a<i;a++){var o=n[a];r.plainObject(o)&&this.error("Tried to set map with object key"),a<n.length-1?(null==t[o]&&(t[o]={}),t=t[o]):t[o]=e.value}},getMap:function(e){for(var t=e.map,n=e.keys,i=n.length,a=0;a<i;a++){var o=n[a];if(r.plainObject(o)&&this.error("Tried to get map with object key"),null==(t=t[o]))return t}return t},deleteMap:function(e){for(var t=e.map,n=e.keys,i=n.length,a=e.keepChildren,o=0;o<i;o++){var s=n[o];if(r.plainObject(s)&&this.error("Tried to delete map with object key"),o===e.keys.length-1)if(a)for(var u=Object.keys(t),l=0;l<u.length;l++){var c=u[l];a[c]||(t[c]=void 0)}else t[s]=void 0;else t=t[s]}}}},function(e,t,n){"use strict";var r="(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))";e.exports={regex:{number:r,rgba:"rgb[a]?\\(((?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))[%]?)\\s*,\\s*((?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))[%]?)\\s*,\\s*((?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))[%]?)(?:\\s*,\\s*((?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))))?\\)",rgbaNoBackRefs:"rgb[a]?\\((?:(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))[%]?)\\s*,\\s*(?:(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))[%]?)\\s*,\\s*(?:(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))[%]?)(?:\\s*,\\s*(?:(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))))?\\)",hsla:"hsl[a]?\\(((?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?)))\\s*,\\s*((?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))[%])\\s*,\\s*((?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))[%])(?:\\s*,\\s*((?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))))?\\)",hslaNoBackRefs:"hsl[a]?\\((?:(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?)))\\s*,\\s*(?:(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))[%])\\s*,\\s*(?:(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))[%])(?:\\s*,\\s*(?:(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))))?\\)",hex3:"\\#[0-9a-fA-F]{3}",hex6:"\\#[0-9a-fA-F]{6}"}}},function(e,t,n){"use strict";function r(e,t){return e<t?-1:e>t?1:0}function i(e,t){return-1*r(e,t)}e.exports={sort:{ascending:r,descending:i}}},function(e,t,n){"use strict";var r=n(19),i=n(0);e.exports={camel2dash:r(function(e){return e.replace(/([A-Z])/g,function(e){return"-"+e.toLowerCase()})}),dash2camel:r(function(e){return e.replace(/(-\w)/g,function(e){return e[1].toUpperCase()})}),prependCamel:r(function(e,t){return e+t[0].toUpperCase()+t.substring(1)},function(e,t){return e+"$"+t}),capitalize:function(e){return i.emptyString(e)?e:e.charAt(0).toUpperCase()+e.substring(1)}}},function(e,t,n){"use strict";var r=n(4),i=r?r.performance:null,a={},o=i&&i.now?function(){return i.now()}:function(){return Date.now()},s=function(){if(r){if(r.requestAnimationFrame)return function(e){r.requestAnimationFrame(e)};if(r.mozRequestAnimationFrame)return function(e){r.mozRequestAnimationFrame(e)};if(r.webkitRequestAnimationFrame)return function(e){r.webkitRequestAnimationFrame(e)};if(r.msRequestAnimationFrame)return function(e){r.msRequestAnimationFrame(e)}}return function(e){e&&setTimeout(function(){e(o())},1e3/60)}}();a.requestAnimationFrame=function(e){s(e)},a.performanceNow=o,a.debounce=n(139),a.now=function(){return Date.now()},e.exports=a},function(e,t,n){e.exports=n(138)},function(e,t,n){var r,i,a;(function(){var n,o,s,u,l,c,d,h,f,p,v,g,y,m,b;s=Math.floor,p=Math.min,o=function(e,t){return e<t?-1:e>t?1:0},f=function(e,t,n,r,i){var a;if(null==n&&(n=0),null==i&&(i=o),n<0)throw new Error("lo must be non-negative");for(null==r&&(r=e.length);n<r;)a=s((n+r)/2),i(t,e[a])<0?r=a:n=a+1;return[].splice.apply(e,[n,n-n].concat(t)),t},c=function(e,t,n){return null==n&&(n=o),e.push(t),m(e,0,e.length-1,n)},l=function(e,t){var n,r;return null==t&&(t=o),n=e.pop(),e.length?(r=e[0],e[0]=n,b(e,0,t)):r=n,r},h=function(e,t,n){var r;return null==n&&(n=o),r=e[0],e[0]=t,b(e,0,n),r},d=function(e,t,n){var r;return null==n&&(n=o),e.length&&n(e[0],t)<0&&(r=[e[0],t],t=r[0],e[0]=r[1],b(e,0,n)),t},u=function(e,t){var n,r,i,a,u,l;for(null==t&&(t=o),a=function(){l=[];for(var t=0,n=s(e.length/2);0<=n?t<n:t>n;0<=n?t++:t--)l.push(t);return l}.apply(this).reverse(),u=[],r=0,i=a.length;r<i;r++)n=a[r],u.push(b(e,n,t));return u},y=function(e,t,n){var r;if(null==n&&(n=o),-1!==(r=e.indexOf(t)))return m(e,0,r,n),b(e,r,n)},v=function(e,t,n){var r,i,a,s,l;if(null==n&&(n=o),i=e.slice(0,t),!i.length)return i;for(u(i,n),l=e.slice(t),a=0,s=l.length;a<s;a++)r=l[a],d(i,r,n);return i.sort(n).reverse()},g=function(e,t,n){var r,i,a,s,c,d,h,v,g;if(null==n&&(n=o),10*t<=e.length){if(a=e.slice(0,t).sort(n),!a.length)return a;for(i=a[a.length-1],h=e.slice(t),s=0,d=h.length;s<d;s++)r=h[s],n(r,i)<0&&(f(a,r,0,null,n),a.pop(),i=a[a.length-1]);return a}for(u(e,n),g=[],c=0,v=p(t,e.length);0<=v?c<v:c>v;0<=v?++c:--c)g.push(l(e,n));return g},m=function(e,t,n,r){var i,a,s;for(null==r&&(r=o),i=e[n];n>t&&(s=n-1>>1,a=e[s],r(i,a)<0);)e[n]=a,n=s;return e[n]=i},b=function(e,t,n){var r,i,a,s,u;for(null==n&&(n=o),i=e.length,u=t,a=e[t],r=2*t+1;r<i;)s=r+1,s<i&&!(n(e[r],e[s])<0)&&(r=s),e[t]=e[r],t=r,r=2*t+1;return e[t]=a,m(e,u,t,n)},n=function(){function e(e){this.cmp=null!=e?e:o,this.nodes=[]}return e.push=c,e.pop=l,e.replace=h,e.pushpop=d,e.heapify=u,e.updateItem=y,e.nlargest=v,e.nsmallest=g,e.prototype.push=function(e){return c(this.nodes,e,this.cmp)},e.prototype.pop=function(){return l(this.nodes,this.cmp)},e.prototype.peek=function(){return this.nodes[0]},e.prototype.contains=function(e){return-1!==this.nodes.indexOf(e)},e.prototype.replace=function(e){return h(this.nodes,e,this.cmp)},e.prototype.pushpop=function(e){return d(this.nodes,e,this.cmp)},e.prototype.heapify=function(){return u(this.nodes,this.cmp)},e.prototype.updateItem=function(e){return y(this.nodes,e,this.cmp)},e.prototype.clear=function(){return this.nodes=[]},e.prototype.empty=function(){return 0===this.nodes.length},e.prototype.size=function(){return this.nodes.length},e.prototype.clone=function(){var t;return t=new e,t.nodes=this.nodes.slice(0),t},e.prototype.toArray=function(){return this.nodes.slice(0)},e.prototype.insert=e.prototype.push,e.prototype.top=e.prototype.peek,e.prototype.front=e.prototype.peek,e.prototype.has=e.prototype.contains,e.prototype.copy=e.prototype.clone,e}(),function(n,o){i=[],r=o,void 0!==(a="function"==typeof r?r.apply(t,i):r)&&(e.exports=a)}(0,function(){return n})}).call(this)},function(e,t,n){(function(t){function n(e,t,n){function i(t){var n=v,r=g;return v=g=void 0,k=t,m=e.apply(r,n)}function a(e){return k=e,b=setTimeout(c,t),C?i(e):m}function u(e){var n=e-E,r=e-k,i=t-n;return P?w(i,y-r):i}function l(e){var n=e-E,r=e-k;return void 0===E||n>=t||n<0||P&&r>=y}function c(){var e=_();return l(e)?d(e):void(b=setTimeout(c,u(e)))}function d(e){return b=void 0,T&&v?i(e):(v=g=void 0,m)}function h(){void 0!==b&&clearTimeout(b),k=0,v=E=g=b=void 0}function f(){return void 0===b?m:d(_())}function p(){var e=_(),n=l(e);if(v=arguments,g=this,E=e,n){if(void 0===b)return a(E);if(P)return b=setTimeout(c,t),i(E)}return void 0===b&&(b=setTimeout(c,t)),m}var v,g,y,m,b,E,k=0,C=!1,P=!1,T=!0;if("function"!=typeof e)throw new TypeError(s);return t=o(t)||0,r(n)&&(C=!!n.leading,P="maxWait"in n,y=P?x(o(n.maxWait)||0,t):y,T="trailing"in n?!!n.trailing:T),p.cancel=h,p.flush=f,p}function r(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function i(e){return!!e&&"object"==typeof e}function a(e){return"symbol"==typeof e||i(e)&&b.call(e)==l}function o(e){if("number"==typeof e)return e;if(a(e))return u;if(r(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=r(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(c,"");var n=h.test(e);return n||f.test(e)?p(e.slice(2),n?2:8):d.test(e)?u:+e}var s="Expected a function",u=NaN,l="[object Symbol]",c=/^\s+|\s+$/g,d=/^[-+]0x[0-9a-f]+$/i,h=/^0b[01]+$/i,f=/^0o[0-7]+$/i,p=parseInt,v="object"==typeof t&&t&&t.Object===Object&&t,g="object"==typeof self&&self&&self.Object===Object&&self,y=v||g||Function("return this")(),m=Object.prototype,b=m.toString,x=Math.max,w=Math.min,_=function(){return y.Date.now()};e.exports=n}).call(t,n(20))},function(e,t){function n(){throw new Error("setTimeout has not been defined")}function r(){throw new Error("clearTimeout has not been defined")}function i(e){if(c===setTimeout)return setTimeout(e,0);if((c===n||!c)&&setTimeout)return c=setTimeout,setTimeout(e,0);try{return c(e,0)}catch(t){try{return c.call(null,e,0)}catch(t){return c.call(this,e,0)}}}function a(e){if(d===clearTimeout)return clearTimeout(e);if((d===r||!d)&&clearTimeout)return d=clearTimeout,clearTimeout(e);try{return d(e)}catch(t){try{return d.call(null,e)}catch(t){return d.call(this,e)}}}function o(){v&&f&&(v=!1,f.length?p=f.concat(p):g=-1,p.length&&s())}function s(){if(!v){var e=i(o);v=!0;for(var t=p.length;t;){for(f=p,p=[];++g<t;)f&&f[g].run();g=-1,t=p.length}f=null,v=!1,a(e)}}function u(e,t){this.fun=e,this.array=t}function l(){}var c,d,h=e.exports={};!function(){try{c="function"==typeof setTimeout?setTimeout:n}catch(e){c=n}try{d="function"==typeof clearTimeout?clearTimeout:r}catch(e){d=r}}();var f,p=[],v=!1,g=-1;h.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];p.push(new u(e,t)),1!==p.length||v||i(s)},u.prototype.run=function(){this.fun.apply(null,this.array)},h.title="browser",h.browser=!0,h.env={},h.argv=[],h.version="",h.versions={},h.on=l,h.addListener=l,h.once=l,h.off=l,h.removeListener=l,h.removeAllListeners=l,h.emit=l,h.prependListener=l,h.prependOnceListener=l,h.listeners=function(e){return[]},h.binding=function(e){throw new Error("process.binding is not supported")},h.cwd=function(){return"/"},h.chdir=function(e){throw new Error("process.chdir is not supported")},h.umask=function(){return 0}},function(e,t,n){(function(e,t){!function(e,n){"use strict";function r(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),n=0;n<t.length;n++)t[n]=arguments[n+1];var r={callback:e,args:t};return l[u]=r,s(u),u++}function i(e){delete l[e]}function a(e){var t=e.callback,r=e.args;switch(r.length){case 0:t();break;case 1:t(r[0]);break;case 2:t(r[0],r[1]);break;case 3:t(r[0],r[1],r[2]);break;default:t.apply(n,r)}}function o(e){if(c)setTimeout(o,0,e);else{var t=l[e];if(t){c=!0;try{a(t)}finally{i(e),c=!1}}}}if(!e.setImmediate){var s,u=1,l={},c=!1,d=e.document,h=Object.getPrototypeOf&&Object.getPrototypeOf(e);h=h&&h.setTimeout?h:e,"[object process]"==={}.toString.call(e.process)?function(){s=function(e){t.nextTick(function(){o(e)})}}():function(){if(e.postMessage&&!e.importScripts){var t=!0,n=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=n,t}}()?function(){var t="setImmediate$"+Math.random()+"$",n=function(n){n.source===e&&"string"==typeof n.data&&0===n.data.indexOf(t)&&o(+n.data.slice(t.length))};e.addEventListener?e.addEventListener("message",n,!1):e.attachEvent("onmessage",n),s=function(n){e.postMessage(t+n,"*")}}():e.MessageChannel?function(){var e=new MessageChannel;e.port1.onmessage=function(e){o(e.data)},s=function(t){e.port2.postMessage(t)}}():d&&"onreadystatechange"in d.createElement("script")?function(){var e=d.documentElement;s=function(t){var n=d.createElement("script");n.onreadystatechange=function(){o(t),n.onreadystatechange=null,e.removeChild(n),n=null},e.appendChild(n)}}():function(){s=function(e){setTimeout(o,0,e)}}(),h.setImmediate=r,h.clearImmediate=i}}("undefined"==typeof self?void 0===e?this:e:self)}).call(t,n(20),n(140))},function(e,t,n){function r(e,t){this._id=e,this._clearFn=t}var i=Function.prototype.apply;t.setTimeout=function(){return new r(i.call(setTimeout,window,arguments),clearTimeout)},t.setInterval=function(){return new r(i.call(setInterval,window,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},r.prototype.unref=r.prototype.ref=function(){},r.prototype.close=function(){this._clearFn.call(window,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},n(141),t.setImmediate=setImmediate,t.clearImmediate=clearImmediate}])}),!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t;"undefined"!=typeof window?t=window:"undefined"!=typeof global?t=global:"undefined"!=typeof self&&(t=self),t.dagre=e()}}(function(){var e;return function e(t,n,r){function i(o,s){if(!n[o]){if(!t[o]){var u="function"==typeof require&&require;if(!s&&u)return u(o,!0);if(a)return a(o,!0);var l=new Error("Cannot find module '"+o+"'");throw l.code="MODULE_NOT_FOUND",l}var c=n[o]={exports:{}};t[o][0].call(c.exports,function(e){var n=t[o][1][e];return i(n?n:e)},c,c.exports,e,t,n,r)}return n[o].exports}for(var a="function"==typeof require&&require,o=0;o<r.length;o++)i(r[o]);return i}({1:[function(e,t,n){t.exports={graphlib:e("./lib/graphlib"),layout:e("./lib/layout"),debug:e("./lib/debug"),util:{time:e("./lib/util").time,notime:e("./lib/util").notime},version:e("./lib/version")}},{"./lib/debug":6,"./lib/graphlib":7,"./lib/layout":9,"./lib/util":29,"./lib/version":30}],2:[function(e,t,n){"use strict";function r(e){function t(e){return function(t){return e.edge(t).weight;
}}var n="greedy"===e.graph().acyclicer?s(e,t(e)):i(e);o.each(n,function(t){var n=e.edge(t);e.removeEdge(t),n.forwardName=t.name,n.reversed=!0,e.setEdge(t.w,t.v,n,o.uniqueId("rev"))})}function i(e){function t(a){o.has(i,a)||(i[a]=!0,r[a]=!0,o.each(e.outEdges(a),function(e){o.has(r,e.w)?n.push(e):t(e.w)}),delete r[a])}var n=[],r={},i={};return o.each(e.nodes(),t),n}function a(e){o.each(e.edges(),function(t){var n=e.edge(t);if(n.reversed){e.removeEdge(t);var r=n.forwardName;delete n.reversed,delete n.forwardName,e.setEdge(t.w,t.v,n,r)}})}var o=e("./lodash"),s=e("./greedy-fas");t.exports={run:r,undo:a}},{"./greedy-fas":8,"./lodash":10}],3:[function(e,t,n){function r(e){function t(n){var r=e.children(n),o=e.node(n);if(r.length&&a.each(r,t),a.has(o,"minRank")){o.borderLeft=[],o.borderRight=[];for(var s=o.minRank,u=o.maxRank+1;s<u;++s)i(e,"borderLeft","_bl",n,o,s),i(e,"borderRight","_br",n,o,s)}}a.each(e.children(),t)}function i(e,t,n,r,i,a){var s={width:0,height:0,rank:a,borderType:t},u=i[t][a-1],l=o.addDummyNode(e,"border",s,n);i[t][a]=l,e.setParent(l,r),u&&e.setEdge(u,l,{weight:1})}var a=e("./lodash"),o=e("./util");t.exports=r},{"./lodash":10,"./util":29}],4:[function(e,t,n){"use strict";function r(e){var t=e.graph().rankdir.toLowerCase();"lr"!==t&&"rl"!==t||a(e)}function i(e){var t=e.graph().rankdir.toLowerCase();"bt"!==t&&"rl"!==t||s(e),"lr"!==t&&"rl"!==t||(l(e),a(e))}function a(e){d.each(e.nodes(),function(t){o(e.node(t))}),d.each(e.edges(),function(t){o(e.edge(t))})}function o(e){var t=e.width;e.width=e.height,e.height=t}function s(e){d.each(e.nodes(),function(t){u(e.node(t))}),d.each(e.edges(),function(t){var n=e.edge(t);d.each(n.points,u),d.has(n,"y")&&u(n)})}function u(e){e.y=-e.y}function l(e){d.each(e.nodes(),function(t){c(e.node(t))}),d.each(e.edges(),function(t){var n=e.edge(t);d.each(n.points,c),d.has(n,"x")&&c(n)})}function c(e){var t=e.x;e.x=e.y,e.y=t}var d=e("./lodash");t.exports={adjust:r,undo:i}},{"./lodash":10}],5:[function(e,t,n){function r(){var e={};e._next=e._prev=e,this._sentinel=e}function i(e){e._prev._next=e._next,e._next._prev=e._prev,delete e._next,delete e._prev}function a(e,t){if("_next"!==e&&"_prev"!==e)return t}t.exports=r,r.prototype.dequeue=function(){var e=this._sentinel,t=e._prev;if(t!==e)return i(t),t},r.prototype.enqueue=function(e){var t=this._sentinel;e._prev&&e._next&&i(e),e._next=t._next,t._next._prev=e,t._next=e,e._prev=t},r.prototype.toString=function(){for(var e=[],t=this._sentinel,n=t._prev;n!==t;)e.push(JSON.stringify(n,a)),n=n._prev;return"["+e.join(", ")+"]"}},{}],6:[function(e,t,n){function r(e){var t=a.buildLayerMatrix(e),n=new o({compound:!0,multigraph:!0}).setGraph({});return i.each(e.nodes(),function(t){n.setNode(t,{label:t}),n.setParent(t,"layer"+e.node(t).rank)}),i.each(e.edges(),function(e){n.setEdge(e.v,e.w,{},e.name)}),i.each(t,function(e,t){var r="layer"+t;n.setNode(r,{rank:"same"}),i.reduce(e,function(e,t){return n.setEdge(e,t,{style:"invis"}),t})}),n}var i=e("./lodash"),a=e("./util"),o=e("./graphlib").Graph;t.exports={debugOrdering:r}},{"./graphlib":7,"./lodash":10,"./util":29}],7:[function(e,t,n){var r;if("function"==typeof e)try{r=e("graphlib")}catch(e){}r||(r=window.graphlib),t.exports=r},{graphlib:31}],8:[function(e,t,n){function r(e,t){if(e.nodeCount()<=1)return[];var n=o(e,t||d),r=i(n.graph,n.buckets,n.zeroIdx);return u.flatten(u.map(r,function(t){return e.outEdges(t.v,t.w)}),!0)}function i(e,t,n){for(var r,i=[],o=t[t.length-1],s=t[0];e.nodeCount();){for(;r=s.dequeue();)a(e,t,n,r);for(;r=o.dequeue();)a(e,t,n,r);if(e.nodeCount())for(var u=t.length-2;u>0;--u)if(r=t[u].dequeue()){i=i.concat(a(e,t,n,r,!0));break}}return i}function a(e,t,n,r,i){var a=i?[]:void 0;return u.each(e.inEdges(r.v),function(r){var o=e.edge(r),u=e.node(r.v);i&&a.push({v:r.v,w:r.w}),u.out-=o,s(t,n,u)}),u.each(e.outEdges(r.v),function(r){var i=e.edge(r),a=r.w,o=e.node(a);o.in-=i,s(t,n,o)}),e.removeNode(r.v),a}function o(e,t){var n=new l,r=0,i=0;u.each(e.nodes(),function(e){n.setNode(e,{v:e,in:0,out:0})}),u.each(e.edges(),function(e){var a=n.edge(e.v,e.w)||0,o=t(e),s=a+o;n.setEdge(e.v,e.w,s),i=Math.max(i,n.node(e.v).out+=o),r=Math.max(r,n.node(e.w).in+=o)});var a=u.range(i+r+3).map(function(){return new c}),o=r+1;return u.each(n.nodes(),function(e){s(a,o,n.node(e))}),{graph:n,buckets:a,zeroIdx:o}}function s(e,t,n){n.out?n.in?e[n.out-n.in+t].enqueue(n):e[e.length-1].enqueue(n):e[0].enqueue(n)}var u=e("./lodash"),l=e("./graphlib").Graph,c=e("./data/list");t.exports=r;var d=u.constant(1)},{"./data/list":5,"./graphlib":7,"./lodash":10}],9:[function(e,t,n){"use strict";function r(e,t){var n=t&&t.debugTiming?L.time:L.notime;n("layout",function(){var t=n("  buildLayoutGraph",function(){return o(e)});n("  runLayout",function(){i(t,n)}),n("  updateInputGraph",function(){a(e,t)})})}function i(e,t){t("    makeSpaceForEdgeLabels",function(){s(e)}),t("    removeSelfEdges",function(){g(e)}),t("    acyclic",function(){_.run(e)}),t("    nestingGraph.run",function(){S.run(e)}),t("    rank",function(){k(L.asNonCompoundGraph(e))}),t("    injectEdgeLabelProxies",function(){u(e)}),t("    removeEmptyRanks",function(){T(e)}),t("    nestingGraph.cleanup",function(){S.cleanup(e)}),t("    normalizeRanks",function(){C(e)}),t("    assignRankMinMax",function(){l(e)}),t("    removeEdgeLabelProxies",function(){c(e)}),t("    normalize.run",function(){E.run(e)}),t("    parentDummyChains",function(){P(e)}),t("    addBorderSegments",function(){D(e)}),t("    order",function(){N(e)}),t("    insertSelfEdges",function(){y(e)}),t("    adjustCoordinateSystem",function(){I.adjust(e)}),t("    position",function(){M(e)}),t("    positionSelfEdges",function(){m(e)}),t("    removeBorderNodes",function(){v(e)}),t("    normalize.undo",function(){E.undo(e)}),t("    fixupEdgeLabelCoords",function(){f(e)}),t("    undoCoordinateSystem",function(){I.undo(e)}),t("    translateGraph",function(){d(e)}),t("    assignNodeIntersects",function(){h(e)}),t("    reversePoints",function(){p(e)}),t("    acyclic.undo",function(){_.undo(e)})}function a(e,t){w.each(e.nodes(),function(n){var r=e.node(n),i=t.node(n);r&&(r.x=i.x,r.y=i.y,t.children(n).length&&(r.width=i.width,r.height=i.height))}),w.each(e.edges(),function(n){var r=e.edge(n),i=t.edge(n);r.points=i.points,w.has(i,"x")&&(r.x=i.x,r.y=i.y)}),e.graph().width=t.graph().width,e.graph().height=t.graph().height}function o(e){var t=new B({multigraph:!0,compound:!0}),n=x(e.graph());return t.setGraph(w.merge({},A,b(n,O),w.pick(n,z))),w.each(e.nodes(),function(n){var r=x(e.node(n));t.setNode(n,w.defaults(b(r,R),j)),t.setParent(n,e.parent(n))}),w.each(e.edges(),function(n){var r=x(e.edge(n));t.setEdge(n,w.merge({},V,b(r,F),w.pick(r,q)))}),t}function s(e){var t=e.graph();t.ranksep/=2,w.each(e.edges(),function(n){var r=e.edge(n);r.minlen*=2,"c"!==r.labelpos.toLowerCase()&&("TB"===t.rankdir||"BT"===t.rankdir?r.width+=r.labeloffset:r.height+=r.labeloffset)})}function u(e){w.each(e.edges(),function(t){var n=e.edge(t);if(n.width&&n.height){var r=e.node(t.v),i=e.node(t.w),a={rank:(i.rank-r.rank)/2+r.rank,e:t};L.addDummyNode(e,"edge-proxy",a,"_ep")}})}function l(e){var t=0;w.each(e.nodes(),function(n){var r=e.node(n);r.borderTop&&(r.minRank=e.node(r.borderTop).rank,r.maxRank=e.node(r.borderBottom).rank,t=w.max(t,r.maxRank))}),e.graph().maxRank=t}function c(e){w.each(e.nodes(),function(t){var n=e.node(t);"edge-proxy"===n.dummy&&(e.edge(n.e).labelRank=n.rank,e.removeNode(t))})}function d(e){function t(e){var t=e.x,o=e.y,s=e.width,u=e.height;n=Math.min(n,t-s/2),r=Math.max(r,t+s/2),i=Math.min(i,o-u/2),a=Math.max(a,o+u/2)}var n=Number.POSITIVE_INFINITY,r=0,i=Number.POSITIVE_INFINITY,a=0,o=e.graph(),s=o.marginx||0,u=o.marginy||0;w.each(e.nodes(),function(n){t(e.node(n))}),w.each(e.edges(),function(n){var r=e.edge(n);w.has(r,"x")&&t(r)}),n-=s,i-=u,w.each(e.nodes(),function(t){var r=e.node(t);r.x-=n,r.y-=i}),w.each(e.edges(),function(t){var r=e.edge(t);w.each(r.points,function(e){e.x-=n,e.y-=i}),w.has(r,"x")&&(r.x-=n),w.has(r,"y")&&(r.y-=i)}),o.width=r-n+s,o.height=a-i+u}function h(e){w.each(e.edges(),function(t){var n,r,i=e.edge(t),a=e.node(t.v),o=e.node(t.w);i.points?(n=i.points[0],r=i.points[i.points.length-1]):(i.points=[],n=o,r=a),i.points.unshift(L.intersectRect(a,n)),i.points.push(L.intersectRect(o,r))})}function f(e){w.each(e.edges(),function(t){var n=e.edge(t);if(w.has(n,"x"))switch("l"!==n.labelpos&&"r"!==n.labelpos||(n.width-=n.labeloffset),n.labelpos){case"l":n.x-=n.width/2+n.labeloffset;break;case"r":n.x+=n.width/2+n.labeloffset}})}function p(e){w.each(e.edges(),function(t){var n=e.edge(t);n.reversed&&n.points.reverse()})}function v(e){w.each(e.nodes(),function(t){if(e.children(t).length){var n=e.node(t),r=e.node(n.borderTop),i=e.node(n.borderBottom),a=e.node(w.last(n.borderLeft)),o=e.node(w.last(n.borderRight));n.width=Math.abs(o.x-a.x),n.height=Math.abs(i.y-r.y),n.x=a.x+n.width/2,n.y=r.y+n.height/2}}),w.each(e.nodes(),function(t){"border"===e.node(t).dummy&&e.removeNode(t)})}function g(e){w.each(e.edges(),function(t){if(t.v===t.w){var n=e.node(t.v);n.selfEdges||(n.selfEdges=[]),n.selfEdges.push({e:t,label:e.edge(t)}),e.removeEdge(t)}})}function y(e){var t=L.buildLayerMatrix(e);w.each(t,function(t){var n=0;w.each(t,function(t,r){var i=e.node(t);i.order=r+n,w.each(i.selfEdges,function(t){L.addDummyNode(e,"selfedge",{width:t.label.width,height:t.label.height,rank:i.rank,order:r+ ++n,e:t.e,label:t.label},"_se")}),delete i.selfEdges})})}function m(e){w.each(e.nodes(),function(t){var n=e.node(t);if("selfedge"===n.dummy){var r=e.node(n.e.v),i=r.x+r.width/2,a=r.y,o=n.x-i,s=r.height/2;e.setEdge(n.e,n.label),e.removeNode(t),n.label.points=[{x:i+2*o/3,y:a-s},{x:i+5*o/6,y:a-s},{x:i+o,y:a},{x:i+5*o/6,y:a+s},{x:i+2*o/3,y:a+s}],n.label.x=n.x,n.label.y=n.y}})}function b(e,t){return w.mapValues(w.pick(e,t),Number)}function x(e){var t={};return w.each(e,function(e,n){t[n.toLowerCase()]=e}),t}var w=e("./lodash"),_=e("./acyclic"),E=e("./normalize"),k=e("./rank"),C=e("./util").normalizeRanks,P=e("./parent-dummy-chains"),T=e("./util").removeEmptyRanks,S=e("./nesting-graph"),D=e("./add-border-segments"),I=e("./coordinate-system"),N=e("./order"),M=e("./position"),L=e("./util"),B=e("./graphlib").Graph;t.exports=r;var O=["nodesep","edgesep","ranksep","marginx","marginy"],A={ranksep:50,edgesep:20,nodesep:50,rankdir:"tb"},z=["acyclicer","ranker","rankdir","align"],R=["width","height"],j={width:0,height:0},F=["minlen","weight","width","height","labeloffset"],V={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"},q=["labelpos"]},{"./acyclic":2,"./add-border-segments":3,"./coordinate-system":4,"./graphlib":7,"./lodash":10,"./nesting-graph":11,"./normalize":12,"./order":17,"./parent-dummy-chains":22,"./position":24,"./rank":26,"./util":29}],10:[function(e,t,n){var r;if("function"==typeof e)try{r=e("lodash")}catch(e){}r||(r=window._),t.exports=r},{lodash:51}],11:[function(e,t,n){function r(e){var t=l.addDummyNode(e,"root",{},"_root"),n=a(e),r=u.max(n)-1,s=2*r+1;e.graph().nestingRoot=t,u.each(e.edges(),function(t){e.edge(t).minlen*=s});var c=o(e)+1;u.each(e.children(),function(a){i(e,t,s,c,r,n,a)}),e.graph().nodeRankFactor=s}function i(e,t,n,r,a,o,s){var c=e.children(s);if(!c.length)return void(s!==t&&e.setEdge(t,s,{weight:0,minlen:n}));var d=l.addBorderNode(e,"_bt"),h=l.addBorderNode(e,"_bb"),f=e.node(s);e.setParent(d,s),f.borderTop=d,e.setParent(h,s),f.borderBottom=h,u.each(c,function(u){i(e,t,n,r,a,o,u);var l=e.node(u),c=l.borderTop?l.borderTop:u,f=l.borderBottom?l.borderBottom:u,p=l.borderTop?r:2*r,v=c!==f?1:a-o[s]+1;e.setEdge(d,c,{weight:p,minlen:v,nestingEdge:!0}),e.setEdge(f,h,{weight:p,minlen:v,nestingEdge:!0})}),e.parent(s)||e.setEdge(t,d,{weight:0,minlen:a+o[s]})}function a(e){function t(r,i){var a=e.children(r);a&&a.length&&u.each(a,function(e){t(e,i+1)}),n[r]=i}var n={};return u.each(e.children(),function(e){t(e,1)}),n}function o(e){return u.reduce(e.edges(),function(t,n){return t+e.edge(n).weight},0)}function s(e){var t=e.graph();e.removeNode(t.nestingRoot),delete t.nestingRoot,u.each(e.edges(),function(t){var n=e.edge(t);n.nestingEdge&&e.removeEdge(t)})}var u=e("./lodash"),l=e("./util");t.exports={run:r,cleanup:s}},{"./lodash":10,"./util":29}],12:[function(e,t,n){"use strict";function r(e){e.graph().dummyChains=[],o.each(e.edges(),function(t){i(e,t)})}function i(e,t){var n=t.v,r=e.node(n).rank,i=t.w,a=e.node(i).rank,o=t.name,u=e.edge(t),l=u.labelRank;if(a!==r+1){e.removeEdge(t);var c,d,h;for(h=0,++r;r<a;++h,++r)u.points=[],d={width:0,height:0,edgeLabel:u,edgeObj:t,rank:r},c=s.addDummyNode(e,"edge",d,"_d"),r===l&&(d.width=u.width,d.height=u.height,d.dummy="edge-label",d.labelpos=u.labelpos),e.setEdge(n,c,{weight:u.weight},o),0===h&&e.graph().dummyChains.push(c),n=c;e.setEdge(n,i,{weight:u.weight},o)}}function a(e){o.each(e.graph().dummyChains,function(t){var n,r=e.node(t),i=r.edgeLabel;for(e.setEdge(r.edgeObj,i);r.dummy;)n=e.successors(t)[0],e.removeNode(t),i.points.push({x:r.x,y:r.y}),"edge-label"===r.dummy&&(i.x=r.x,i.y=r.y,i.width=r.width,i.height=r.height),t=n,r=e.node(t)})}var o=e("./lodash"),s=e("./util");t.exports={run:r,undo:a}},{"./lodash":10,"./util":29}],13:[function(e,t,n){function r(e,t,n){var r,a={};i.each(n,function(n){for(var i,o,s=e.parent(n);s;){if(i=e.parent(s),i?(o=a[i],a[i]=s):(o=r,r=s),o&&o!==s)return void t.setEdge(o,s);s=i}})}var i=e("../lodash");t.exports=r},{"../lodash":10}],14:[function(e,t,n){function r(e,t){return i.map(t,function(t){var n=e.inEdges(t);if(n.length){var r=i.reduce(n,function(t,n){var r=e.edge(n),i=e.node(n.v);return{sum:t.sum+r.weight*i.order,weight:t.weight+r.weight}},{sum:0,weight:0});return{v:t,barycenter:r.sum/r.weight,weight:r.weight}}return{v:t}})}var i=e("../lodash");t.exports=r},{"../lodash":10}],15:[function(e,t,n){function r(e,t,n){var r=i(e),s=new o({compound:!0}).setGraph({root:r}).setDefaultNodeLabel(function(t){return e.node(t)});return a.each(e.nodes(),function(i){var o=e.node(i),u=e.parent(i);(o.rank===t||o.minRank<=t&&t<=o.maxRank)&&(s.setNode(i),s.setParent(i,u||r),a.each(e[n](i),function(t){var n=t.v===i?t.w:t.v,r=s.edge(n,i),o=a.isUndefined(r)?0:r.weight;s.setEdge(n,i,{weight:e.edge(t).weight+o})}),a.has(o,"minRank")&&s.setNode(i,{borderLeft:o.borderLeft[t],borderRight:o.borderRight[t]}))}),s}function i(e){for(var t;e.hasNode(t=a.uniqueId("_root")););return t}var a=e("../lodash"),o=e("../graphlib").Graph;t.exports=r},{"../graphlib":7,"../lodash":10}],16:[function(e,t,n){"use strict";function r(e,t){for(var n=0,r=1;r<t.length;++r)n+=i(e,t[r-1],t[r]);return n}function i(e,t,n){for(var r=a.zipObject(n,a.map(n,function(e,t){return t})),i=a.flatten(a.map(t,function(t){return a.chain(e.outEdges(t)).map(function(t){return{pos:r[t.w],weight:e.edge(t).weight}}).sortBy("pos").value()}),!0),o=1;o<n.length;)o<<=1;var s=2*o-1;o-=1;var u=a.map(new Array(s),function(){return 0}),l=0;return a.each(i.forEach(function(e){var t=e.pos+o;u[t]+=e.weight;for(var n=0;t>0;)t%2&&(n+=u[t+1]),t=t-1>>1,u[t]+=e.weight;l+=e.weight*n})),l}var a=e("../lodash");t.exports=r},{"../lodash":10}],17:[function(e,t,n){"use strict";function r(e){var t=p.maxRank(e),n=i(e,s.range(1,t+1),"inEdges"),r=i(e,s.range(t-1,-1,-1),"outEdges"),c=u(e);o(e,c);for(var d,h=Number.POSITIVE_INFINITY,f=0,v=0;v<4;++f,++v){a(f%2?n:r,f%4>=2),c=p.buildLayerMatrix(e);var g=l(e,c);g<h&&(v=0,d=s.cloneDeep(c),h=g)}o(e,d)}function i(e,t,n){return s.map(t,function(t){return d(e,t,n)})}function a(e,t){var n=new f;s.each(e,function(e){var r=e.graph().root,i=c(e,r,n,t);s.each(i.vs,function(t,n){e.node(t).order=n}),h(e,n,i.vs)})}function o(e,t){s.each(t,function(t){s.each(t,function(t,n){e.node(t).order=n})})}var s=e("../lodash"),u=e("./init-order"),l=e("./cross-count"),c=e("./sort-subgraph"),d=e("./build-layer-graph"),h=e("./add-subgraph-constraints"),f=e("../graphlib").Graph,p=e("../util");t.exports=r},{"../graphlib":7,"../lodash":10,"../util":29,"./add-subgraph-constraints":13,"./build-layer-graph":15,"./cross-count":16,"./init-order":18,"./sort-subgraph":20}],18:[function(e,t,n){"use strict";function r(e){function t(r){if(!i.has(n,r)){n[r]=!0;var a=e.node(r);o[a.rank].push(r),i.each(e.successors(r),t)}}var n={},r=i.filter(e.nodes(),function(t){return!e.children(t).length}),a=i.max(i.map(r,function(t){return e.node(t).rank})),o=i.map(i.range(a+1),function(){return[]}),s=i.sortBy(r,function(t){return e.node(t).rank});return i.each(s,t),o}var i=e("../lodash");t.exports=r},{"../lodash":10}],19:[function(e,t,n){"use strict";function r(e,t){var n={};o.each(e,function(e,t){var r=n[e.v]={indegree:0,in:[],out:[],vs:[e.v],i:t};o.isUndefined(e.barycenter)||(r.barycenter=e.barycenter,r.weight=e.weight)}),o.each(t.edges(),function(e){var t=n[e.v],r=n[e.w];o.isUndefined(t)||o.isUndefined(r)||(r.indegree++,t.out.push(n[e.w]))});var r=o.filter(n,function(e){return!e.indegree});return i(r)}function i(e){function t(e){return function(t){t.merged||(o.isUndefined(t.barycenter)||o.isUndefined(e.barycenter)||t.barycenter>=e.barycenter)&&a(e,t)}}function n(t){return function(n){n.in.push(t),0===--n.indegree&&e.push(n)}}for(var r=[];e.length;){var i=e.pop();r.push(i),o.each(i.in.reverse(),t(i)),o.each(i.out,n(i))}return o.chain(r).filter(function(e){return!e.merged}).map(function(e){return o.pick(e,["vs","i","barycenter","weight"])}).value()}function a(e,t){var n=0,r=0;e.weight&&(n+=e.barycenter*e.weight,r+=e.weight),t.weight&&(n+=t.barycenter*t.weight,r+=t.weight),e.vs=t.vs.concat(e.vs),e.barycenter=n/r,e.weight=r,e.i=Math.min(t.i,e.i),t.merged=!0}var o=e("../lodash");t.exports=r},{"../lodash":10}],20:[function(e,t,n){function r(e,t,n,c){var d=e.children(t),h=e.node(t),f=h?h.borderLeft:void 0,p=h?h.borderRight:void 0,v={};f&&(d=o.filter(d,function(e){return e!==f&&e!==p}));var g=s(e,d);o.each(g,function(t){if(e.children(t.v).length){var i=r(e,t.v,n,c);v[t.v]=i,o.has(i,"barycenter")&&a(t,i)}});var y=u(g,n);i(y,v);var m=l(y,c);if(f&&(m.vs=o.flatten([f,m.vs,p],!0),e.predecessors(f).length)){var b=e.node(e.predecessors(f)[0]),x=e.node(e.predecessors(p)[0]);o.has(m,"barycenter")||(m.barycenter=0,m.weight=0),m.barycenter=(m.barycenter*m.weight+b.order+x.order)/(m.weight+2),m.weight+=2}return m}function i(e,t){o.each(e,function(e){e.vs=o.flatten(e.vs.map(function(e){return t[e]?t[e].vs:e}),!0)})}function a(e,t){o.isUndefined(e.barycenter)?(e.barycenter=t.barycenter,e.weight=t.weight):(e.barycenter=(e.barycenter*e.weight+t.barycenter*t.weight)/(e.weight+t.weight),e.weight+=t.weight)}var o=e("../lodash"),s=e("./barycenter"),u=e("./resolve-conflicts"),l=e("./sort");t.exports=r},{"../lodash":10,"./barycenter":14,"./resolve-conflicts":19,"./sort":21}],21:[function(e,t,n){function r(e,t){var n=s.partition(e,function(e){return o.has(e,"barycenter")}),r=n.lhs,u=o.sortBy(n.rhs,function(e){return-e.i}),l=[],c=0,d=0,h=0;r.sort(a(!!t)),h=i(l,u,h),o.each(r,function(e){h+=e.vs.length,l.push(e.vs),c+=e.barycenter*e.weight,d+=e.weight,h=i(l,u,h)});var f={vs:o.flatten(l,!0)};return d&&(f.barycenter=c/d,f.weight=d),f}function i(e,t,n){for(var r;t.length&&(r=o.last(t)).i<=n;)t.pop(),e.push(r.vs),n++;return n}function a(e){return function(t,n){return t.barycenter<n.barycenter?-1:t.barycenter>n.barycenter?1:e?n.i-t.i:t.i-n.i}}var o=e("../lodash"),s=e("../util");t.exports=r},{"../lodash":10,"../util":29}],22:[function(e,t,n){function r(e){var t=a(e);o.each(e.graph().dummyChains,function(n){for(var r=e.node(n),a=r.edgeObj,o=i(e,t,a.v,a.w),s=o.path,u=o.lca,l=0,c=s[l],d=!0;n!==a.w;){if(r=e.node(n),d){for(;(c=s[l])!==u&&e.node(c).maxRank<r.rank;)l++;c===u&&(d=!1)}if(!d){for(;l<s.length-1&&e.node(c=s[l+1]).minRank<=r.rank;)l++;c=s[l]}e.setParent(n,c),n=e.successors(n)[0]}})}function i(e,t,n,r){var i,a,o=[],s=[],u=Math.min(t[n].low,t[r].low),l=Math.max(t[n].lim,t[r].lim);i=n;do i=e.parent(i),o.push(i);while(i&&(t[i].low>u||l>t[i].lim));for(a=i,i=r;(i=e.parent(i))!==a;)s.push(i);return{path:o.concat(s.reverse()),lca:a}}function a(e){function t(i){var a=r;o.each(e.children(i),t),n[i]={low:a,lim:r++}}var n={},r=0;return o.each(e.children(),t),n}var o=e("./lodash");t.exports=r},{"./lodash":10}],23:[function(e,t,n){"use strict";function r(e,t){function n(t,n){var i=0,s=0,u=t.length,l=y.last(n);return y.each(n,function(t,c){var d=a(e,t),h=d?e.node(d).order:u;(d||t===l)&&(y.each(n.slice(s,c+1),function(t){y.each(e.predecessors(t),function(n){var a=e.node(n),s=a.order;!(s<i||h<s)||a.dummy&&e.node(t).dummy||o(r,n,t)})}),s=c+1,i=h)}),n}var r={};return y.reduce(t,n),r}function i(e,t){function n(t,n,r,a,s){var u;y.each(y.range(n,r),function(n){u=t[n],e.node(u).dummy&&y.each(e.predecessors(u),function(t){var n=e.node(t);n.dummy&&(n.order<a||n.order>s)&&o(i,t,u)})})}function r(t,r){var i,a=-1,o=0;return y.each(r,function(s,u){if("border"===e.node(s).dummy){var l=e.predecessors(s);l.length&&(i=e.node(l[0]).order,n(r,o,u,a,i),o=u,a=i)}n(r,o,r.length,i,t.length)}),r}var i={};return y.reduce(t,r),i}function a(e,t){if(e.node(t).dummy)return y.find(e.predecessors(t),function(t){return e.node(t).dummy})}function o(e,t,n){if(t>n){var r=t;t=n,n=r}var i=e[t];i||(e[t]=i={}),i[n]=!0}function s(e,t,n){if(t>n){var r=t;t=n,n=r}return y.has(e[t],n)}function u(e,t,n,r){var i={},a={},o={};return y.each(t,function(e){y.each(e,function(e,t){i[e]=e,a[e]=e,o[e]=t})}),y.each(t,function(e){var t=-1;y.each(e,function(e){var u=r(e);if(u.length){u=y.sortBy(u,function(e){return o[e]});for(var l=(u.length-1)/2,c=Math.floor(l),d=Math.ceil(l);c<=d;++c){var h=u[c];a[e]===e&&t<o[h]&&!s(n,e,h)&&(a[h]=e,a[e]=i[e]=i[h],t=o[h])}}})}),{root:i,align:a}}function l(e,t,n,r,i){function a(e){y.has(l,e)||(l[e]=!0,s[e]=y.reduce(u.inEdges(e),function(e,t){return a(t.v),Math.max(e,s[t.v]+u.edge(t))},0))}function o(t){if(2!==l[t]){l[t]++;var n=e.node(t),r=y.reduce(u.outEdges(t),function(e,t){return o(t.w),Math.min(e,s[t.w]-u.edge(t))},Number.POSITIVE_INFINITY);r!==Number.POSITIVE_INFINITY&&n.borderType!==d&&(s[t]=Math.max(s[t],r))}}var s={},u=c(e,t,n,i),l={};y.each(u.nodes(),a);var d=i?"borderLeft":"borderRight";return y.each(u.nodes(),o),y.each(r,function(e){s[e]=s[n[e]]}),s}function c(e,t,n,r){var i=new m,a=e.graph(),o=v(a.nodesep,a.edgesep,r);return y.each(t,function(t){var r;y.each(t,function(t){var a=n[t];if(i.setNode(a),r){var s=n[r],u=i.edge(s,a);i.setEdge(s,a,Math.max(o(e,t,r),u||0))}r=t})}),i}function d(e,t){return y.min(t,function(t){var n=y.min(t,function(t,n){return t-g(e,n)/2}),r=y.max(t,function(t,n){return t+g(e,n)/2});return r-n})}function h(e,t){var n=y.min(t),r=y.max(t);y.each(["u","d"],function(i){y.each(["l","r"],function(a){var o,s=i+a,u=e[s];u!==t&&(o="l"===a?n-y.min(u):r-y.max(u),o&&(e[s]=y.mapValues(u,function(e){return e+o})))})})}function f(e,t){return y.mapValues(e.ul,function(n,r){if(t)return e[t.toLowerCase()][r];var i=y.sortBy(y.pluck(e,r));return(i[1]+i[2])/2})}function p(e){var t,n=b.buildLayerMatrix(e),a=y.merge(r(e,n),i(e,n)),o={};y.each(["u","d"],function(r){t="u"===r?n:y.values(n).reverse(),y.each(["l","r"],function(n){"r"===n&&(t=y.map(t,function(e){return y.values(e).reverse()}));var i=y.bind("u"===r?e.predecessors:e.successors,e),s=u(e,t,a,i),c=l(e,t,s.root,s.align,"r"===n);"r"===n&&(c=y.mapValues(c,function(e){return-e})),o[r+n]=c})});var s=d(e,o);return h(o,s),f(o,e.graph().align)}function v(e,t,n){return function(r,i,a){var o,s=r.node(i),u=r.node(a),l=0;if(l+=s.width/2,y.has(s,"labelpos"))switch(s.labelpos.toLowerCase()){case"l":o=-s.width/2;break;case"r":o=s.width/2}if(o&&(l+=n?o:-o),o=0,l+=(s.dummy?t:e)/2,l+=(u.dummy?t:e)/2,l+=u.width/2,y.has(u,"labelpos"))switch(u.labelpos.toLowerCase()){case"l":o=u.width/2;break;case"r":o=-u.width/2}return o&&(l+=n?o:-o),o=0,l}}function g(e,t){return e.node(t).width}var y=e("../lodash"),m=e("../graphlib").Graph,b=e("../util");t.exports={positionX:p,findType1Conflicts:r,findType2Conflicts:i,addConflict:o,hasConflict:s,verticalAlignment:u,horizontalCompaction:l,alignCoordinates:h,findSmallestWidthAlignment:d,balance:f}},{"../graphlib":7,"../lodash":10,"../util":29}],24:[function(e,t,n){"use strict";function r(e){e=o.asNonCompoundGraph(e),i(e),a.each(s(e),function(t,n){e.node(n).x=t})}function i(e){var t=o.buildLayerMatrix(e),n=e.graph().ranksep,r=0;a.each(t,function(t){var i=a.max(a.map(t,function(t){return e.node(t).height}));a.each(t,function(t){e.node(t).y=r+i/2}),r+=i+n})}var a=e("../lodash"),o=e("../util"),s=e("./bk").positionX;t.exports=r},{"../lodash":10,"../util":29,"./bk":23}],25:[function(e,t,n){"use strict";function r(e){var t=new u({directed:!1}),n=e.nodes()[0],r=e.nodeCount();t.setNode(n,{});for(var s,c;i(t,e)<r;)s=a(t,e),c=t.hasNode(s.v)?l(e,s):-l(e,s),o(t,e,c);return t}function i(e,t){function n(r){s.each(t.nodeEdges(r),function(i){var a=i.v,o=r===a?i.w:a;e.hasNode(o)||l(t,i)||(e.setNode(o,{}),e.setEdge(r,o,{}),n(o))})}return s.each(e.nodes(),n),e.nodeCount()}function a(e,t){return s.min(t.edges(),function(n){if(e.hasNode(n.v)!==e.hasNode(n.w))return l(t,n)})}function o(e,t,n){s.each(e.nodes(),function(e){t.node(e).rank+=n})}var s=e("../lodash"),u=e("../graphlib").Graph,l=e("./util").slack;t.exports=r},{"../graphlib":7,"../lodash":10,"./util":28}],26:[function(e,t,n){"use strict";function r(e){switch(e.graph().ranker){case"network-simplex":a(e);break;case"tight-tree":i(e);break;case"longest-path":c(e);break;default:a(e)}}function i(e){s(e),u(e)}function a(e){l(e)}var o=e("./util"),s=o.longestPath,u=e("./feasible-tree"),l=e("./network-simplex");t.exports=r;var c=s},{"./feasible-tree":25,"./network-simplex":27,"./util":28}],27:[function(e,t,n){"use strict";function r(e){e=w(e),m(e);var t=g(e);s(t),i(t,e);for(var n,r;n=l(t);)r=c(t,e,n),d(t,e,n,r)}function i(e,t){var n=x(e,e.nodes());n=n.slice(0,n.length-1),v.each(n,function(n){a(e,t,n)})}function a(e,t,n){var r=e.node(n),i=r.parent;e.edge(n,i).cutvalue=o(e,t,n)}function o(e,t,n){var r=e.node(n),i=r.parent,a=!0,o=t.edge(n,i),s=0;return o||(a=!1,o=t.edge(i,n)),s=o.weight,v.each(t.nodeEdges(n),function(r){var o=r.v===n,u=o?r.w:r.v;if(u!==i){var l=o===a,c=t.edge(r).weight;if(s+=l?c:-c,f(e,n,u)){var d=e.edge(n,u).cutvalue;s+=l?-d:d}}}),s}function s(e,t){arguments.length<2&&(t=e.nodes()[0]),u(e,{},1,t)}function u(e,t,n,r,i){var a=n,o=e.node(r);return t[r]=!0,v.each(e.neighbors(r),function(i){v.has(t,i)||(n=u(e,t,n,i,r))}),o.low=a,o.lim=n++,i?o.parent=i:delete o.parent,n}function l(e){return v.find(e.edges(),function(t){return e.edge(t).cutvalue<0})}function c(e,t,n){var r=n.v,i=n.w;t.hasEdge(r,i)||(r=n.w,i=n.v);var a=e.node(r),o=e.node(i),s=a,u=!1;a.lim>o.lim&&(s=o,u=!0);var l=v.filter(t.edges(),function(t){return u===p(e,e.node(t.v),s)&&u!==p(e,e.node(t.w),s)});return v.min(l,function(e){return y(t,e)})}function d(e,t,n,r){var a=n.v,o=n.w;e.removeEdge(a,o),e.setEdge(r.v,r.w,{}),s(e),i(e,t),h(e,t)}function h(e,t){var n=v.find(e.nodes(),function(e){return!t.node(e).parent}),r=b(e,n);r=r.slice(1),v.each(r,function(n){var r=e.node(n).parent,i=t.edge(n,r),a=!1;i||(i=t.edge(r,n),a=!0),t.node(n).rank=t.node(r).rank+(a?i.minlen:-i.minlen)})}function f(e,t,n){return e.hasEdge(t,n)}function p(e,t,n){return n.low<=t.lim&&t.lim<=n.lim}var v=e("../lodash"),g=e("./feasible-tree"),y=e("./util").slack,m=e("./util").longestPath,b=e("../graphlib").alg.preorder,x=e("../graphlib").alg.postorder,w=e("../util").simplify;t.exports=r,r.initLowLimValues=s,r.initCutValues=i,r.calcCutValue=o,r.leaveEdge=l,r.enterEdge=c,r.exchangeEdges=d},{"../graphlib":7,"../lodash":10,"../util":29,"./feasible-tree":25,"./util":28}],28:[function(e,t,n){"use strict";function r(e){function t(r){var i=e.node(r);if(a.has(n,r))return i.rank;n[r]=!0;var o=a.min(a.map(e.outEdges(r),function(n){return t(n.w)-e.edge(n).minlen}));return o===Number.POSITIVE_INFINITY&&(o=0),i.rank=o}var n={};a.each(e.sources(),t)}function i(e,t){return e.node(t.w).rank-e.node(t.v).rank-e.edge(t).minlen}var a=e("../lodash");t.exports={longestPath:r,slack:i}},{"../lodash":10}],29:[function(e,t,n){"use strict";function r(e,t,n,r){var i;do i=y.uniqueId(r);while(e.hasNode(i));return n.dummy=t,e.setNode(i,n),i}function i(e){var t=(new m).setGraph(e.graph());return y.each(e.nodes(),function(n){t.setNode(n,e.node(n))}),y.each(e.edges(),function(n){var r=t.edge(n.v,n.w)||{weight:0,minlen:1},i=e.edge(n);t.setEdge(n.v,n.w,{weight:r.weight+i.weight,minlen:Math.max(r.minlen,i.minlen)})}),t}function a(e){var t=new m({multigraph:e.isMultigraph()}).setGraph(e.graph());return y.each(e.nodes(),function(n){e.children(n).length||t.setNode(n,e.node(n))}),y.each(e.edges(),function(n){t.setEdge(n,e.edge(n))}),t}function o(e){var t=y.map(e.nodes(),function(t){var n={};return y.each(e.outEdges(t),function(t){n[t.w]=(n[t.w]||0)+e.edge(t).weight}),n});return y.zipObject(e.nodes(),t)}function s(e){var t=y.map(e.nodes(),function(t){var n={};return y.each(e.inEdges(t),function(t){n[t.v]=(n[t.v]||0)+e.edge(t).weight}),n});return y.zipObject(e.nodes(),t)}function u(e,t){var n=e.x,r=e.y,i=t.x-n,a=t.y-r,o=e.width/2,s=e.height/2;if(!i&&!a)throw new Error("Not possible to find intersection inside of the rectangle");var u,l;return Math.abs(a)*o>Math.abs(i)*s?(a<0&&(s=-s),u=s*i/a,l=s):(i<0&&(o=-o),u=o,l=o*a/i),{x:n+u,y:r+l}}function l(e){var t=y.map(y.range(f(e)+1),function(){return[]});return y.each(e.nodes(),function(n){var r=e.node(n),i=r.rank;y.isUndefined(i)||(t[i][r.order]=n)}),t}function c(e){var t=y.min(y.map(e.nodes(),function(t){return e.node(t).rank}));y.each(e.nodes(),function(n){var r=e.node(n);y.has(r,"rank")&&(r.rank-=t)})}function d(e){var t=y.min(y.map(e.nodes(),function(t){return e.node(t).rank})),n=[];y.each(e.nodes(),function(r){var i=e.node(r).rank-t;n[i]||(n[i]=[]),n[i].push(r)});var r=0,i=e.graph().nodeRankFactor;y.each(n,function(t,n){y.isUndefined(t)&&n%i!==0?--r:r&&y.each(t,function(t){e.node(t).rank+=r})})}function h(e,t,n,i){var a={width:0,height:0};return arguments.length>=4&&(a.rank=n,a.order=i),r(e,"border",a,t)}function f(e){return y.max(y.map(e.nodes(),function(t){var n=e.node(t).rank;if(!y.isUndefined(n))return n}))}function p(e,t){var n={lhs:[],rhs:[]};return y.each(e,function(e){t(e)?n.lhs.push(e):n.rhs.push(e)}),n}function v(e,t){var n=y.now();try{return t()}finally{console.log(e+" time: "+(y.now()-n)+"ms")}}function g(e,t){return t()}var y=e("./lodash"),m=e("./graphlib").Graph;t.exports={addDummyNode:r,simplify:i,asNonCompoundGraph:a,successorWeights:o,predecessorWeights:s,intersectRect:u,buildLayerMatrix:l,normalizeRanks:c,removeEmptyRanks:d,addBorderNode:h,maxRank:f,partition:p,time:v,notime:g}},{"./graphlib":7,"./lodash":10}],30:[function(e,t,n){t.exports="0.7.4"},{}],31:[function(e,t,n){var r=e("./lib");t.exports={Graph:r.Graph,json:e("./lib/json"),alg:e("./lib/alg"),version:r.version}},{"./lib":47,"./lib/alg":38,"./lib/json":48}],32:[function(e,t,n){function r(e){function t(a){i.has(r,a)||(r[a]=!0,n.push(a),i.each(e.successors(a),t),i.each(e.predecessors(a),t))}var n,r={},a=[];return i.each(e.nodes(),function(e){n=[],t(e),n.length&&a.push(n)}),a}var i=e("../lodash");t.exports=r},{"../lodash":49}],33:[function(e,t,n){function r(e,t,n){a.isArray(t)||(t=[t]);var r=[],o={};return a.each(t,function(t){if(!e.hasNode(t))throw new Error("Graph does not have node: "+t);i(e,t,"post"===n,o,r)}),r}function i(e,t,n,r,o){a.has(r,t)||(r[t]=!0,n||o.push(t),a.each(e.neighbors(t),function(t){i(e,t,n,r,o)}),n&&o.push(t))}var a=e("../lodash");t.exports=r},{"../lodash":49}],34:[function(e,t,n){function r(e,t,n){return a.transform(e.nodes(),function(r,a){r[a]=i(e,a,t,n)},{})}var i=e("./dijkstra"),a=e("../lodash");t.exports=r},{"../lodash":49,"./dijkstra":35}],35:[function(e,t,n){function r(e,t,n,r){return i(e,String(t),n||s,r||function(t){return e.outEdges(t)})}function i(e,t,n,r){var i,a,s={},u=new o,l=function(e){var t=e.v!==i?e.v:e.w,r=s[t],o=n(e),l=a.distance+o;if(o<0)throw new Error("dijkstra does not allow negative edge weights. Bad edge: "+e+" Weight: "+o);l<r.distance&&(r.distance=l,r.predecessor=i,u.decrease(t,l))};for(e.nodes().forEach(function(e){var n=e===t?0:Number.POSITIVE_INFINITY;s[e]={distance:n},u.add(e,n)});u.size()>0&&(i=u.removeMin(),a=s[i],a.distance!==Number.POSITIVE_INFINITY);)r(i).forEach(l);return s}var a=e("../lodash"),o=e("../data/priority-queue");
t.exports=r;var s=a.constant(1)},{"../data/priority-queue":45,"../lodash":49}],36:[function(e,t,n){function r(e){return i.filter(a(e),function(t){return t.length>1||1===t.length&&e.hasEdge(t[0],t[0])})}var i=e("../lodash"),a=e("./tarjan");t.exports=r},{"../lodash":49,"./tarjan":43}],37:[function(e,t,n){function r(e,t,n){return i(e,t||o,n||function(t){return e.outEdges(t)})}function i(e,t,n){var r={},i=e.nodes();return i.forEach(function(e){r[e]={},r[e][e]={distance:0},i.forEach(function(t){e!==t&&(r[e][t]={distance:Number.POSITIVE_INFINITY})}),n(e).forEach(function(n){var i=n.v===e?n.w:n.v,a=t(n);r[e][i]={distance:a,predecessor:e}})}),i.forEach(function(e){var t=r[e];i.forEach(function(n){var a=r[n];i.forEach(function(n){var r=a[e],i=t[n],o=a[n],s=r.distance+i.distance;s<o.distance&&(o.distance=s,o.predecessor=i.predecessor)})})}),r}var a=e("../lodash");t.exports=r;var o=a.constant(1)},{"../lodash":49}],38:[function(e,t,n){t.exports={components:e("./components"),dijkstra:e("./dijkstra"),dijkstraAll:e("./dijkstra-all"),findCycles:e("./find-cycles"),floydWarshall:e("./floyd-warshall"),isAcyclic:e("./is-acyclic"),postorder:e("./postorder"),preorder:e("./preorder"),prim:e("./prim"),tarjan:e("./tarjan"),topsort:e("./topsort")}},{"./components":32,"./dijkstra":35,"./dijkstra-all":34,"./find-cycles":36,"./floyd-warshall":37,"./is-acyclic":39,"./postorder":40,"./preorder":41,"./prim":42,"./tarjan":43,"./topsort":44}],39:[function(e,t,n){function r(e){try{i(e)}catch(e){if(e instanceof i.CycleException)return!1;throw e}return!0}var i=e("./topsort");t.exports=r},{"./topsort":44}],40:[function(e,t,n){function r(e,t){return i(e,t,"post")}var i=e("./dfs");t.exports=r},{"./dfs":33}],41:[function(e,t,n){function r(e,t){return i(e,t,"pre")}var i=e("./dfs");t.exports=r},{"./dfs":33}],42:[function(e,t,n){function r(e,t){function n(e){var n=e.v===r?e.w:e.v,i=l.priority(n);if(void 0!==i){var a=t(e);a<i&&(u[n]=r,l.decrease(n,a))}}var r,s=new a,u={},l=new o;if(0===e.nodeCount())return s;i.each(e.nodes(),function(e){l.add(e,Number.POSITIVE_INFINITY),s.setNode(e)}),l.decrease(e.nodes()[0],0);for(var c=!1;l.size()>0;){if(r=l.removeMin(),i.has(u,r))s.setEdge(r,u[r]);else{if(c)throw new Error("Input graph is not connected: "+e);c=!0}e.nodeEdges(r).forEach(n)}return s}var i=e("../lodash"),a=e("../graph"),o=e("../data/priority-queue");t.exports=r},{"../data/priority-queue":45,"../graph":46,"../lodash":49}],43:[function(e,t,n){function r(e){function t(s){var u=a[s]={onStack:!0,lowlink:n,index:n++};if(r.push(s),e.successors(s).forEach(function(e){i.has(a,e)?a[e].onStack&&(u.lowlink=Math.min(u.lowlink,a[e].index)):(t(e),u.lowlink=Math.min(u.lowlink,a[e].lowlink))}),u.lowlink===u.index){var l,c=[];do l=r.pop(),a[l].onStack=!1,c.push(l);while(s!==l);o.push(c)}}var n=0,r=[],a={},o=[];return e.nodes().forEach(function(e){i.has(a,e)||t(e)}),o}var i=e("../lodash");t.exports=r},{"../lodash":49}],44:[function(e,t,n){function r(e){function t(s){if(a.has(r,s))throw new i;a.has(n,s)||(r[s]=!0,n[s]=!0,a.each(e.predecessors(s),t),delete r[s],o.push(s))}var n={},r={},o=[];if(a.each(e.sinks(),t),a.size(n)!==e.nodeCount())throw new i;return o}function i(){}var a=e("../lodash");t.exports=r,r.CycleException=i},{"../lodash":49}],45:[function(e,t,n){function r(){this._arr=[],this._keyIndices={}}var i=e("../lodash");t.exports=r,r.prototype.size=function(){return this._arr.length},r.prototype.keys=function(){return this._arr.map(function(e){return e.key})},r.prototype.has=function(e){return i.has(this._keyIndices,e)},r.prototype.priority=function(e){var t=this._keyIndices[e];if(void 0!==t)return this._arr[t].priority},r.prototype.min=function(){if(0===this.size())throw new Error("Queue underflow");return this._arr[0].key},r.prototype.add=function(e,t){var n=this._keyIndices;if(e=String(e),!i.has(n,e)){var r=this._arr,a=r.length;return n[e]=a,r.push({key:e,priority:t}),this._decrease(a),!0}return!1},r.prototype.removeMin=function(){this._swap(0,this._arr.length-1);var e=this._arr.pop();return delete this._keyIndices[e.key],this._heapify(0),e.key},r.prototype.decrease=function(e,t){var n=this._keyIndices[e];if(t>this._arr[n].priority)throw new Error("New priority is greater than current priority. Key: "+e+" Old: "+this._arr[n].priority+" New: "+t);this._arr[n].priority=t,this._decrease(n)},r.prototype._heapify=function(e){var t=this._arr,n=2*e,r=n+1,i=e;n<t.length&&(i=t[n].priority<t[i].priority?n:i,r<t.length&&(i=t[r].priority<t[i].priority?r:i),i!==e&&(this._swap(e,i),this._heapify(i)))},r.prototype._decrease=function(e){for(var t,n=this._arr,r=n[e].priority;0!==e&&(t=e>>1,!(n[t].priority<r));)this._swap(e,t),e=t},r.prototype._swap=function(e,t){var n=this._arr,r=this._keyIndices,i=n[e],a=n[t];n[e]=a,n[t]=i,r[a.key]=e,r[i.key]=t}},{"../lodash":49}],46:[function(e,t,n){"use strict";function r(e){this._isDirected=!l.has(e,"directed")||e.directed,this._isMultigraph=!!l.has(e,"multigraph")&&e.multigraph,this._isCompound=!!l.has(e,"compound")&&e.compound,this._label=void 0,this._defaultNodeLabelFn=l.constant(void 0),this._defaultEdgeLabelFn=l.constant(void 0),this._nodes={},this._isCompound&&(this._parent={},this._children={},this._children[d]={}),this._in={},this._preds={},this._out={},this._sucs={},this._edgeObjs={},this._edgeLabels={}}function i(e,t){l.has(e,t)?e[t]++:e[t]=1}function a(e,t){--e[t]||delete e[t]}function o(e,t,n,r){if(!e&&t>n){var i=t;t=n,n=i}return t+h+n+h+(l.isUndefined(r)?c:r)}function s(e,t,n,r){if(!e&&t>n){var i=t;t=n,n=i}var a={v:t,w:n};return r&&(a.name=r),a}function u(e,t){return o(e,t.v,t.w,t.name)}var l=e("./lodash");t.exports=r;var c="\0",d="\0",h="";r.prototype._nodeCount=0,r.prototype._edgeCount=0,r.prototype.isDirected=function(){return this._isDirected},r.prototype.isMultigraph=function(){return this._isMultigraph},r.prototype.isCompound=function(){return this._isCompound},r.prototype.setGraph=function(e){return this._label=e,this},r.prototype.graph=function(){return this._label},r.prototype.setDefaultNodeLabel=function(e){return l.isFunction(e)||(e=l.constant(e)),this._defaultNodeLabelFn=e,this},r.prototype.nodeCount=function(){return this._nodeCount},r.prototype.nodes=function(){return l.keys(this._nodes)},r.prototype.sources=function(){return l.filter(this.nodes(),function(e){return l.isEmpty(this._in[e])},this)},r.prototype.sinks=function(){return l.filter(this.nodes(),function(e){return l.isEmpty(this._out[e])},this)},r.prototype.setNodes=function(e,t){var n=arguments;return l.each(e,function(e){n.length>1?this.setNode(e,t):this.setNode(e)},this),this},r.prototype.setNode=function(e,t){return l.has(this._nodes,e)?(arguments.length>1&&(this._nodes[e]=t),this):(this._nodes[e]=arguments.length>1?t:this._defaultNodeLabelFn(e),this._isCompound&&(this._parent[e]=d,this._children[e]={},this._children[d][e]=!0),this._in[e]={},this._preds[e]={},this._out[e]={},this._sucs[e]={},++this._nodeCount,this)},r.prototype.node=function(e){return this._nodes[e]},r.prototype.hasNode=function(e){return l.has(this._nodes,e)},r.prototype.removeNode=function(e){var t=this;if(l.has(this._nodes,e)){var n=function(e){t.removeEdge(t._edgeObjs[e])};delete this._nodes[e],this._isCompound&&(this._removeFromParentsChildList(e),delete this._parent[e],l.each(this.children(e),function(e){this.setParent(e)},this),delete this._children[e]),l.each(l.keys(this._in[e]),n),delete this._in[e],delete this._preds[e],l.each(l.keys(this._out[e]),n),delete this._out[e],delete this._sucs[e],--this._nodeCount}return this},r.prototype.setParent=function(e,t){if(!this._isCompound)throw new Error("Cannot set parent in a non-compound graph");if(l.isUndefined(t))t=d;else{t+="";for(var n=t;!l.isUndefined(n);n=this.parent(n))if(n===e)throw new Error("Setting "+t+" as parent of "+e+" would create create a cycle");this.setNode(t)}return this.setNode(e),this._removeFromParentsChildList(e),this._parent[e]=t,this._children[t][e]=!0,this},r.prototype._removeFromParentsChildList=function(e){delete this._children[this._parent[e]][e]},r.prototype.parent=function(e){if(this._isCompound){var t=this._parent[e];if(t!==d)return t}},r.prototype.children=function(e){if(l.isUndefined(e)&&(e=d),this._isCompound){var t=this._children[e];if(t)return l.keys(t)}else{if(e===d)return this.nodes();if(this.hasNode(e))return[]}},r.prototype.predecessors=function(e){var t=this._preds[e];if(t)return l.keys(t)},r.prototype.successors=function(e){var t=this._sucs[e];if(t)return l.keys(t)},r.prototype.neighbors=function(e){var t=this.predecessors(e);if(t)return l.union(t,this.successors(e))},r.prototype.setDefaultEdgeLabel=function(e){return l.isFunction(e)||(e=l.constant(e)),this._defaultEdgeLabelFn=e,this},r.prototype.edgeCount=function(){return this._edgeCount},r.prototype.edges=function(){return l.values(this._edgeObjs)},r.prototype.setPath=function(e,t){var n=this,r=arguments;return l.reduce(e,function(e,i){return r.length>1?n.setEdge(e,i,t):n.setEdge(e,i),i}),this},r.prototype.setEdge=function(){var e,t,n,r,a=!1;l.isPlainObject(arguments[0])?(e=arguments[0].v,t=arguments[0].w,n=arguments[0].name,2===arguments.length&&(r=arguments[1],a=!0)):(e=arguments[0],t=arguments[1],n=arguments[3],arguments.length>2&&(r=arguments[2],a=!0)),e=""+e,t=""+t,l.isUndefined(n)||(n=""+n);var u=o(this._isDirected,e,t,n);if(l.has(this._edgeLabels,u))return a&&(this._edgeLabels[u]=r),this;if(!l.isUndefined(n)&&!this._isMultigraph)throw new Error("Cannot set a named edge when isMultigraph = false");this.setNode(e),this.setNode(t),this._edgeLabels[u]=a?r:this._defaultEdgeLabelFn(e,t,n);var c=s(this._isDirected,e,t,n);return e=c.v,t=c.w,Object.freeze(c),this._edgeObjs[u]=c,i(this._preds[t],e),i(this._sucs[e],t),this._in[t][u]=c,this._out[e][u]=c,this._edgeCount++,this},r.prototype.edge=function(e,t,n){var r=1===arguments.length?u(this._isDirected,arguments[0]):o(this._isDirected,e,t,n);return this._edgeLabels[r]},r.prototype.hasEdge=function(e,t,n){var r=1===arguments.length?u(this._isDirected,arguments[0]):o(this._isDirected,e,t,n);return l.has(this._edgeLabels,r)},r.prototype.removeEdge=function(e,t,n){var r=1===arguments.length?u(this._isDirected,arguments[0]):o(this._isDirected,e,t,n),i=this._edgeObjs[r];return i&&(e=i.v,t=i.w,delete this._edgeLabels[r],delete this._edgeObjs[r],a(this._preds[t],e),a(this._sucs[e],t),delete this._in[t][r],delete this._out[e][r],this._edgeCount--),this},r.prototype.inEdges=function(e,t){var n=this._in[e];if(n){var r=l.values(n);return t?l.filter(r,function(e){return e.v===t}):r}},r.prototype.outEdges=function(e,t){var n=this._out[e];if(n){var r=l.values(n);return t?l.filter(r,function(e){return e.w===t}):r}},r.prototype.nodeEdges=function(e,t){var n=this.inEdges(e,t);if(n)return n.concat(this.outEdges(e,t))}},{"./lodash":49}],47:[function(e,t,n){t.exports={Graph:e("./graph"),version:e("./version")}},{"./graph":46,"./version":50}],48:[function(e,t,n){function r(e){var t={options:{directed:e.isDirected(),multigraph:e.isMultigraph(),compound:e.isCompound()},nodes:i(e),edges:a(e)};return s.isUndefined(e.graph())||(t.value=s.clone(e.graph())),t}function i(e){return s.map(e.nodes(),function(t){var n=e.node(t),r=e.parent(t),i={v:t};return s.isUndefined(n)||(i.value=n),s.isUndefined(r)||(i.parent=r),i})}function a(e){return s.map(e.edges(),function(t){var n=e.edge(t),r={v:t.v,w:t.w};return s.isUndefined(t.name)||(r.name=t.name),s.isUndefined(n)||(r.value=n),r})}function o(e){var t=new u(e.options).setGraph(e.value);return s.each(e.nodes,function(e){t.setNode(e.v,e.value),e.parent&&t.setParent(e.v,e.parent)}),s.each(e.edges,function(e){t.setEdge({v:e.v,w:e.w,name:e.name},e.value)}),t}var s=e("./lodash"),u=e("./graph");t.exports={write:r,read:o}},{"./graph":46,"./lodash":49}],49:[function(e,t,n){t.exports=e(10)},{"/Users/<USER>/projects/dagre/lib/lodash.js":10,lodash:51}],50:[function(e,t,n){t.exports="1.0.5"},{}],51:[function(t,n,r){(function(t){(function(){function i(e,t){if(e!==t){var n=null===e,r=e===P,i=e===e,a=null===t,o=t===P,s=t===t;if(e>t&&!a||!i||n&&!o&&s||r&&s)return 1;if(e<t&&!n||!s||a&&!r&&i||o&&i)return-1}return 0}function a(e,t,n){for(var r=e.length,i=n?r:-1;n?i--:++i<r;)if(t(e[i],i,e))return i;return-1}function o(e,t,n){if(t!==t)return y(e,n);for(var r=n-1,i=e.length;++r<i;)if(e[r]===t)return r;return-1}function s(e){return"function"==typeof e||!1}function u(e){return null==e?"":e+""}function l(e,t){for(var n=-1,r=e.length;++n<r&&t.indexOf(e.charAt(n))>-1;);return n}function c(e,t){for(var n=e.length;n--&&t.indexOf(e.charAt(n))>-1;);return n}function d(e,t){return i(e.criteria,t.criteria)||e.index-t.index}function h(e,t,n){for(var r=-1,a=e.criteria,o=t.criteria,s=a.length,u=n.length;++r<s;){var l=i(a[r],o[r]);if(l){if(r>=u)return l;var c=n[r];return l*("asc"===c||c===!0?1:-1)}}return e.index-t.index}function f(e){return We[e]}function p(e){return Ue[e]}function v(e,t,n){return t?e=Ge[e]:n&&(e=Ze[e]),"\\"+e}function g(e){return"\\"+Ze[e]}function y(e,t,n){for(var r=e.length,i=t+(n?0:-1);n?i--:++i<r;){var a=e[i];if(a!==a)return i}return-1}function m(e){return!!e&&"object"==typeof e}function b(e){return e<=160&&e>=9&&e<=13||32==e||160==e||5760==e||6158==e||e>=8192&&(e<=8202||8232==e||8233==e||8239==e||8287==e||12288==e||65279==e)}function x(e,t){for(var n=-1,r=e.length,i=-1,a=[];++n<r;)e[n]===t&&(e[n]=W,a[++i]=n);return a}function w(e,t){for(var n,r=-1,i=e.length,a=-1,o=[];++r<i;){var s=e[r],u=t?t(s,r,e):s;r&&n===u||(n=u,o[++a]=s)}return o}function _(e){for(var t=-1,n=e.length;++t<n&&b(e.charCodeAt(t)););return t}function E(e){for(var t=e.length;t--&&b(e.charCodeAt(t)););return t}function k(e){return He[e]}function C(e){function t(e){if(m(e)&&!Ds(e)&&!(e instanceof b)){if(e instanceof r)return e;if(eo.call(e,"__chain__")&&eo.call(e,"__wrapped__"))return fr(e)}return new r(e)}function n(){}function r(e,t,n){this.__wrapped__=e,this.__actions__=n||[],this.__chain__=!!t}function b(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=So,this.__views__=[]}function K(){var e=new b(this.__wrapped__);return e.__actions__=et(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=et(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=et(this.__views__),e}function ne(){if(this.__filtered__){var e=new b(this);e.__dir__=-1,e.__filtered__=!0}else e=this.clone(),e.__dir__*=-1;return e}function ie(){var e=this.__wrapped__.value(),t=this.__dir__,n=Ds(e),r=t<0,i=n?e.length:0,a=Un(0,i,this.__views__),o=a.start,s=a.end,u=s-o,l=r?s:o-1,c=this.__iteratees__,d=c.length,h=0,f=Eo(u,this.__takeCount__);if(!n||i<V||i==u&&f==u)return nn(r&&n?e.reverse():e,this.__actions__);var p=[];e:for(;u--&&h<f;){l+=t;for(var v=-1,g=e[l];++v<d;){var y=c[v],m=y.iteratee,b=y.type,x=m(g);if(b==Y)g=x;else if(!x){if(b==q)continue e;break e}}p[h++]=g}return p}function We(){this.__data__={}}function Ue(e){return this.has(e)&&delete this.__data__[e]}function He(e){return"__proto__"==e?P:this.__data__[e]}function $e(e){return"__proto__"!=e&&eo.call(this.__data__,e)}function Ge(e,t){return"__proto__"!=e&&(this.__data__[e]=t),this}function Ze(e){var t=e?e.length:0;for(this.data={hash:yo(null),set:new co};t--;)this.push(e[t])}function Qe(e,t){var n=e.data,r="string"==typeof t||Bi(t)?n.set.has(t):n.hash[t];return r?0:-1}function Ke(e){var t=this.data;"string"==typeof e||Bi(e)?t.set.add(e):t.hash[e]=!0}function Je(e,t){for(var n=-1,r=e.length,i=-1,a=t.length,o=Fa(r+a);++n<r;)o[n]=e[n];for(;++i<a;)o[n++]=t[i];return o}function et(e,t){var n=-1,r=e.length;for(t||(t=Fa(r));++n<r;)t[n]=e[n];return t}function tt(e,t){for(var n=-1,r=e.length;++n<r&&t(e[n],n,e)!==!1;);return e}function nt(e,t){for(var n=e.length;n--&&t(e[n],n,e)!==!1;);return e}function at(e,t){for(var n=-1,r=e.length;++n<r;)if(!t(e[n],n,e))return!1;return!0}function ot(e,t,n,r){for(var i=-1,a=e.length,o=r,s=o;++i<a;){var u=e[i],l=+t(u);n(l,o)&&(o=l,s=u)}return s}function st(e,t){for(var n=-1,r=e.length,i=-1,a=[];++n<r;){var o=e[n];t(o,n,e)&&(a[++i]=o)}return a}function ut(e,t){for(var n=-1,r=e.length,i=Fa(r);++n<r;)i[n]=t(e[n],n,e);return i}function lt(e,t){for(var n=-1,r=t.length,i=e.length;++n<r;)e[i+n]=t[n];return e}function ct(e,t,n,r){var i=-1,a=e.length;for(r&&a&&(n=e[++i]);++i<a;)n=t(n,e[i],i,e);return n}function dt(e,t,n,r){var i=e.length;for(r&&i&&(n=e[--i]);i--;)n=t(n,e[i],i,e);return n}function ht(e,t){for(var n=-1,r=e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}function ft(e,t){for(var n=e.length,r=0;n--;)r+=+t(e[n])||0;return r}function pt(e,t){return e===P?t:e}function vt(e,t,n,r){return e!==P&&eo.call(r,n)?e:t}function gt(e,t,n){for(var r=-1,i=Fs(t),a=i.length;++r<a;){var o=i[r],s=e[o],u=n(s,t[o],o,e,t);(u===u?u===s:s!==s)&&(s!==P||o in e)||(e[o]=u)}return e}function yt(e,t){return null==t?e:bt(t,Fs(t),e)}function mt(e,t){for(var n=-1,r=null==e,i=!r&&Qn(e),a=i?e.length:0,o=t.length,s=Fa(o);++n<o;){var u=t[n];i?s[n]=Kn(u,a)?e[u]:P:s[n]=r?P:e[u]}return s}function bt(e,t,n){n||(n={});for(var r=-1,i=t.length;++r<i;){var a=t[r];n[a]=e[a]}return n}function xt(e,t,n){var r=typeof e;return"function"==r?t===P?e:on(e,t,n):null==e?Ta:"object"==r?jt(e):t===P?La(e):Ft(e,t)}function wt(e,t,n,r,i,a,o){var s;if(n&&(s=i?n(e,r,i):n(e)),s!==P)return s;if(!Bi(e))return e;var u=Ds(e);if(u){if(s=Hn(e),!t)return et(e,s)}else{var l=no.call(e),c=l==Q;if(l!=ee&&l!=U&&(!c||i))return Xe[l]?Gn(e,l,t):i?e:{};if(s=$n(c?{}:e),!t)return yt(s,e)}a||(a=[]),o||(o=[]);for(var d=a.length;d--;)if(a[d]==e)return o[d];return a.push(e),o.push(s),(u?tt:Nt)(e,function(r,i){s[i]=wt(r,t,n,i,e,a,o)}),s}function _t(e,t,n){if("function"!=typeof e)throw new Ga(X);return ho(function(){e.apply(P,n)},t)}function Et(e,t){var n=e?e.length:0,r=[];if(!n)return r;var i=-1,a=Yn(),s=a==o,u=s&&t.length>=V?vn(t):null,l=t.length;u&&(a=Qe,s=!1,t=u);e:for(;++i<n;){var c=e[i];if(s&&c===c){for(var d=l;d--;)if(t[d]===c)continue e;r.push(c)}else a(t,c,0)<0&&r.push(c)}return r}function kt(e,t){var n=!0;return Ao(e,function(e,r,i){return n=!!t(e,r,i)}),n}function Ct(e,t,n,r){var i=r,a=i;return Ao(e,function(e,o,s){var u=+t(e,o,s);(n(u,i)||u===r&&u===a)&&(i=u,a=e)}),a}function Pt(e,t,n,r){var i=e.length;for(n=null==n?0:+n||0,n<0&&(n=-n>i?0:i+n),r=r===P||r>i?i:+r||0,r<0&&(r+=i),i=n>r?0:r>>>0,n>>>=0;n<i;)e[n++]=t;return e}function Tt(e,t){var n=[];return Ao(e,function(e,r,i){t(e,r,i)&&n.push(e)}),n}function St(e,t,n,r){var i;return n(e,function(e,n,a){if(t(e,n,a))return i=r?n:e,!1}),i}function Dt(e,t,n,r){r||(r=[]);for(var i=-1,a=e.length;++i<a;){var o=e[i];m(o)&&Qn(o)&&(n||Ds(o)||Ci(o))?t?Dt(o,t,n,r):lt(r,o):n||(r[r.length]=o)}return r}function It(e,t){return Ro(e,t,ea)}function Nt(e,t){return Ro(e,t,Fs)}function Mt(e,t){return jo(e,t,Fs)}function Lt(e,t){for(var n=-1,r=t.length,i=-1,a=[];++n<r;){var o=t[n];Li(e[o])&&(a[++i]=o)}return a}function Bt(e,t,n){if(null!=e){n!==P&&n in dr(e)&&(t=[n]);for(var r=0,i=t.length;null!=e&&r<i;)e=e[t[r++]];return r&&r==i?e:P}}function Ot(e,t,n,r,i,a){return e===t||(null==e||null==t||!Bi(e)&&!m(t)?e!==e&&t!==t:At(e,t,Ot,n,r,i,a))}function At(e,t,n,r,i,a,o){var s=Ds(e),u=Ds(t),l=H,c=H;s||(l=no.call(e),l==U?l=ee:l!=ee&&(s=Yi(e))),u||(c=no.call(t),c==U?c=ee:c!=ee&&(u=Yi(t)));var d=l==ee,h=c==ee,f=l==c;if(f&&!s&&!d)return jn(e,t,l);if(!i){var p=d&&eo.call(e,"__wrapped__"),v=h&&eo.call(t,"__wrapped__");if(p||v)return n(p?e.value():e,v?t.value():t,r,i,a,o)}if(!f)return!1;a||(a=[]),o||(o=[]);for(var g=a.length;g--;)if(a[g]==e)return o[g]==t;a.push(e),o.push(t);var y=(s?Rn:Fn)(e,t,n,r,i,a,o);return a.pop(),o.pop(),y}function zt(e,t,n){var r=t.length,i=r,a=!n;if(null==e)return!i;for(e=dr(e);r--;){var o=t[r];if(a&&o[2]?o[1]!==e[o[0]]:!(o[0]in e))return!1}for(;++r<i;){o=t[r];var s=o[0],u=e[s],l=o[1];if(a&&o[2]){if(u===P&&!(s in e))return!1}else{var c=n?n(u,l,s):P;if(!(c===P?Ot(l,u,n,!0):c))return!1}}return!0}function Rt(e,t){var n=-1,r=Qn(e)?Fa(e.length):[];return Ao(e,function(e,i,a){r[++n]=t(e,i,a)}),r}function jt(e){var t=Xn(e);if(1==t.length&&t[0][2]){var n=t[0][0],r=t[0][1];return function(e){return null!=e&&(e[n]===r&&(r!==P||n in dr(e)))}}return function(e){return zt(e,t)}}function Ft(e,t){var n=Ds(e),r=er(e)&&rr(t),i=e+"";return e=hr(e),function(a){if(null==a)return!1;var o=i;if(a=dr(a),(n||!r)&&!(o in a)){if(a=1==e.length?a:Bt(a,$t(e,0,-1)),null==a)return!1;o=Pr(e),a=dr(a)}return a[o]===t?t!==P||o in a:Ot(t,a[o],P,!0)}}function Vt(e,t,n,r,i){if(!Bi(e))return e;var a=Qn(t)&&(Ds(t)||Yi(t)),o=a?P:Fs(t);return tt(o||t,function(s,u){if(o&&(u=s,s=t[u]),m(s))r||(r=[]),i||(i=[]),qt(e,t,u,Vt,n,r,i);else{var l=e[u],c=n?n(l,s,u,e,t):P,d=c===P;d&&(c=s),c===P&&(!a||u in e)||!d&&(c===c?c===l:l!==l)||(e[u]=c)}}),e}function qt(e,t,n,r,i,a,o){for(var s=a.length,u=t[n];s--;)if(a[s]==u)return void(e[n]=o[s]);var l=e[n],c=i?i(l,u,n,e,t):P,d=c===P;d&&(c=u,Qn(u)&&(Ds(u)||Yi(u))?c=Ds(l)?l:Qn(l)?et(l):[]:Fi(u)||Ci(u)?c=Ci(l)?$i(l):Fi(l)?l:{}:d=!1),a.push(u),o.push(c),d?e[n]=r(c,u,i,a,o):(c===c?c!==l:l===l)&&(e[n]=c)}function Yt(e){return function(t){return null==t?P:t[e]}}function Xt(e){var t=e+"";return e=hr(e),function(n){return Bt(n,e,t)}}function Wt(e,t){for(var n=e?t.length:0;n--;){var r=t[n];if(r!=i&&Kn(r)){var i=r;fo.call(e,r,1)}}return e}function Ut(e,t){return e+mo(Po()*(t-e+1))}function Ht(e,t,n,r,i){return i(e,function(e,i,a){n=r?(r=!1,e):t(n,e,i,a)}),n}function $t(e,t,n){var r=-1,i=e.length;t=null==t?0:+t||0,t<0&&(t=-t>i?0:i+t),n=n===P||n>i?i:+n||0,n<0&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var a=Fa(i);++r<i;)a[r]=e[r+t];return a}function Gt(e,t){var n;return Ao(e,function(e,r,i){return n=t(e,r,i),!n}),!!n}function Zt(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}function Qt(e,t,n){var r=Vn(),i=-1;t=ut(t,function(e){return r(e)});var a=Rt(e,function(e){var n=ut(t,function(t){return t(e)});return{criteria:n,index:++i,value:e}});return Zt(a,function(e,t){return h(e,t,n)})}function Kt(e,t){var n=0;return Ao(e,function(e,r,i){n+=+t(e,r,i)||0}),n}function Jt(e,t){var n=-1,r=Yn(),i=e.length,a=r==o,s=a&&i>=V,u=s?vn():null,l=[];u?(r=Qe,a=!1):(s=!1,u=t?[]:l);e:for(;++n<i;){var c=e[n],d=t?t(c,n,e):c;if(a&&c===c){for(var h=u.length;h--;)if(u[h]===d)continue e;t&&u.push(d),l.push(c)}else r(u,d,0)<0&&((t||s)&&u.push(d),l.push(c))}return l}function en(e,t){for(var n=-1,r=t.length,i=Fa(r);++n<r;)i[n]=e[t[n]];return i}function tn(e,t,n,r){for(var i=e.length,a=r?i:-1;(r?a--:++a<i)&&t(e[a],a,e););return n?$t(e,r?0:a,r?a+1:i):$t(e,r?a+1:0,r?i:a)}function nn(e,t){var n=e;n instanceof b&&(n=n.value());for(var r=-1,i=t.length;++r<i;){var a=t[r];n=a.func.apply(a.thisArg,lt([n],a.args))}return n}function rn(e,t,n){var r=0,i=e?e.length:r;if("number"==typeof t&&t===t&&i<=No){for(;r<i;){var a=r+i>>>1,o=e[a];(n?o<=t:o<t)&&null!==o?r=a+1:i=a}return i}return an(e,t,Ta,n)}function an(e,t,n,r){t=n(t);for(var i=0,a=e?e.length:0,o=t!==t,s=null===t,u=t===P;i<a;){var l=mo((i+a)/2),c=n(e[l]),d=c!==P,h=c===c;if(o)var f=h||r;else f=s?h&&d&&(r||null!=c):u?h&&(r||d):null!=c&&(r?c<=t:c<t);f?i=l+1:a=l}return Eo(a,Io)}function on(e,t,n){if("function"!=typeof e)return Ta;if(t===P)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 3:return function(n,r,i){return e.call(t,n,r,i)};case 4:return function(n,r,i,a){return e.call(t,n,r,i,a)};case 5:return function(n,r,i,a,o){return e.call(t,n,r,i,a,o)}}return function(){return e.apply(t,arguments)}}function sn(e){var t=new ao(e.byteLength),n=new po(t);return n.set(new po(e)),t}function un(e,t,n){for(var r=n.length,i=-1,a=_o(e.length-r,0),o=-1,s=t.length,u=Fa(s+a);++o<s;)u[o]=t[o];for(;++i<r;)u[n[i]]=e[i];for(;a--;)u[o++]=e[i++];return u}function ln(e,t,n){for(var r=-1,i=n.length,a=-1,o=_o(e.length-i,0),s=-1,u=t.length,l=Fa(o+u);++a<o;)l[a]=e[a];for(var c=a;++s<u;)l[c+s]=t[s];for(;++r<i;)l[c+n[r]]=e[a++];return l}function cn(e,t){return function(n,r,i){var a=t?t():{};if(r=Vn(r,i,3),Ds(n))for(var o=-1,s=n.length;++o<s;){var u=n[o];e(a,u,r(u,o,n),n)}else Ao(n,function(t,n,i){e(a,t,r(t,n,i),i)});return a}}function dn(e){return yi(function(t,n){var r=-1,i=null==t?0:n.length,a=i>2?n[i-2]:P,o=i>2?n[2]:P,s=i>1?n[i-1]:P;for("function"==typeof a?(a=on(a,s,5),i-=2):(a="function"==typeof s?s:P,i-=a?1:0),o&&Jn(n[0],n[1],o)&&(a=i<3?P:a,i=1);++r<i;){var u=n[r];u&&e(t,u,a)}return t})}function hn(e,t){return function(n,r){var i=n?qo(n):0;if(!nr(i))return e(n,r);for(var a=t?i:-1,o=dr(n);(t?a--:++a<i)&&r(o[a],a,o)!==!1;);return n}}function fn(e){return function(t,n,r){for(var i=dr(t),a=r(t),o=a.length,s=e?o:-1;e?s--:++s<o;){var u=a[s];if(n(i[u],u,i)===!1)break}return t}}function pn(e,t){function n(){var i=this&&this!==rt&&this instanceof n?r:e;return i.apply(t,arguments)}var r=yn(e);return n}function vn(e){return yo&&co?new Ze(e):null}function gn(e){return function(t){for(var n=-1,r=ka(ca(t)),i=r.length,a="";++n<i;)a=e(a,r[n],n);return a}}function yn(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=Oo(e.prototype),r=e.apply(n,t);return Bi(r)?r:n}}function mn(e){function t(n,r,i){i&&Jn(n,r,i)&&(r=P);var a=zn(n,e,P,P,P,P,P,r);return a.placeholder=t.placeholder,a}return t}function bn(e,t){return yi(function(n){var r=n[0];return null==r?r:(n.push(t),e.apply(P,n))})}function xn(e,t){return function(n,r,i){if(i&&Jn(n,r,i)&&(r=P),r=Vn(r,i,3),1==r.length){n=Ds(n)?n:cr(n);var a=ot(n,r,e,t);if(!n.length||a!==t)return a}return Ct(n,r,e,t)}}function wn(e,t){return function(n,r,i){if(r=Vn(r,i,3),Ds(n)){var o=a(n,r,t);return o>-1?n[o]:P}return St(n,r,e)}}function _n(e){return function(t,n,r){return t&&t.length?(n=Vn(n,r,3),a(t,n,e)):-1}}function En(e){return function(t,n,r){return n=Vn(n,r,3),St(t,n,e,!0)}}function kn(e){return function(){for(var t,n=arguments.length,i=e?n:-1,a=0,o=Fa(n);e?i--:++i<n;){var s=o[a++]=arguments[i];if("function"!=typeof s)throw new Ga(X);!t&&r.prototype.thru&&"wrapper"==qn(s)&&(t=new r([],!0))}for(i=t?-1:n;++i<n;){s=o[i];var u=qn(s),l="wrapper"==u?Vo(s):P;t=l&&tr(l[0])&&l[1]==(O|N|L|A)&&!l[4].length&&1==l[9]?t[qn(l[0])].apply(t,l[3]):1==s.length&&tr(s)?t[u]():t.thru(s)}return function(){var e=arguments,r=e[0];if(t&&1==e.length&&Ds(r)&&r.length>=V)return t.plant(r).value();for(var i=0,a=n?o[i].apply(this,e):r;++i<n;)a=o[i].call(this,a);return a}}}function Cn(e,t){return function(n,r,i){return"function"==typeof r&&i===P&&Ds(n)?e(n,r):t(n,on(r,i,3))}}function Pn(e){return function(t,n,r){return"function"==typeof n&&r===P||(n=on(n,r,3)),e(t,n,ea)}}function Tn(e){return function(t,n,r){return"function"==typeof n&&r===P||(n=on(n,r,3)),e(t,n)}}function Sn(e){return function(t,n,r){var i={};return n=Vn(n,r,3),Nt(t,function(t,r,a){var o=n(t,r,a);r=e?o:r,t=e?t:o,i[r]=t}),i}}function Dn(e){return function(t,n,r){return t=u(t),(e?t:"")+Ln(t,n,r)+(e?"":t)}}function In(e){var t=yi(function(n,r){var i=x(r,t.placeholder);return zn(n,e,P,r,i)});return t}function Nn(e,t){return function(n,r,i,a){var o=arguments.length<3;return"function"==typeof r&&a===P&&Ds(n)?e(n,r,i,o):Ht(n,Vn(r,a,4),i,o,t)}}function Mn(e,t,n,r,i,a,o,s,u,l){function c(){for(var m=arguments.length,b=m,w=Fa(m);b--;)w[b]=arguments[b];if(r&&(w=un(w,r,i)),a&&(w=ln(w,a,o)),p||g){var _=c.placeholder,E=x(w,_);if(m-=E.length,m<l){var k=s?et(s):P,C=_o(l-m,0),T=p?E:P,I=p?P:E,N=p?w:P,M=p?P:w;t|=p?L:B,t&=~(p?B:L),v||(t&=~(S|D));var O=[e,t,n,N,T,M,I,k,u,C],A=Mn.apply(P,O);return tr(e)&&Yo(A,O),A.placeholder=_,A}}var z=h?n:this,R=f?z[e]:e;return s&&(w=ur(w,s)),d&&u<w.length&&(w.length=u),this&&this!==rt&&this instanceof c&&(R=y||yn(e)),R.apply(z,w)}var d=t&O,h=t&S,f=t&D,p=t&N,v=t&I,g=t&M,y=f?P:yn(e);return c}function Ln(e,t,n){var r=e.length;if(t=+t,r>=t||!xo(t))return"";var i=t-r;return n=null==n?" ":n+"",ga(n,go(i/n.length)).slice(0,i)}function Bn(e,t,n,r){function i(){for(var t=-1,s=arguments.length,u=-1,l=r.length,c=Fa(l+s);++u<l;)c[u]=r[u];for(;s--;)c[u++]=arguments[++t];var d=this&&this!==rt&&this instanceof i?o:e;return d.apply(a?n:this,c)}var a=t&S,o=yn(e);return i}function On(e){var t=Xa[e];return function(e,n){return n=n===P?0:+n||0,n?(n=uo(10,n),t(e*n)/n):t(e)}}function An(e){return function(t,n,r,i){var a=Vn(r);return null==r&&a===xt?rn(t,n,e):an(t,n,a(r,i,1),e)}}function zn(e,t,n,r,i,a,o,s){var u=t&D;if(!u&&"function"!=typeof e)throw new Ga(X);var l=r?r.length:0;if(l||(t&=~(L|B),r=i=P),l-=i?i.length:0,t&B){var c=r,d=i;r=i=P}var h=u?P:Vo(e),f=[e,t,n,r,i,c,d,a,o,s];if(h&&(ir(f,h),t=f[1],s=f[9]),f[9]=null==s?u?0:e.length:_o(s-l,0)||0,t==S)var p=pn(f[0],f[2]);else p=t!=L&&t!=(S|L)||f[4].length?Mn.apply(P,f):Bn.apply(P,f);var v=h?Fo:Yo;return v(p,f)}function Rn(e,t,n,r,i,a,o){var s=-1,u=e.length,l=t.length;if(u!=l&&!(i&&l>u))return!1;for(;++s<u;){var c=e[s],d=t[s],h=r?r(i?d:c,i?c:d,s):P;if(h!==P){if(h)continue;return!1}if(i){if(!ht(t,function(e){return c===e||n(c,e,r,i,a,o)}))return!1}else if(c!==d&&!n(c,d,r,i,a,o))return!1}return!0}function jn(e,t,n){switch(n){case $:case G:return+e==+t;case Z:return e.name==t.name&&e.message==t.message;case J:return e!=+e?t!=+t:e==+t;case te:case re:return e==t+""}return!1}function Fn(e,t,n,r,i,a,o){var s=Fs(e),u=s.length,l=Fs(t),c=l.length;if(u!=c&&!i)return!1;for(var d=u;d--;){var h=s[d];if(!(i?h in t:eo.call(t,h)))return!1}for(var f=i;++d<u;){h=s[d];var p=e[h],v=t[h],g=r?r(i?v:p,i?p:v,h):P;if(!(g===P?n(p,v,r,i,a,o):g))return!1;f||(f="constructor"==h)}if(!f){var y=e.constructor,m=t.constructor;if(y!=m&&"constructor"in e&&"constructor"in t&&!("function"==typeof y&&y instanceof y&&"function"==typeof m&&m instanceof m))return!1}return!0}function Vn(e,n,r){var i=t.callback||Ca;return i=i===Ca?xt:i,r?i(e,n,r):i}function qn(e){for(var t=e.name,n=Bo[t],r=n?n.length:0;r--;){var i=n[r],a=i.func;if(null==a||a==e)return i.name}return t}function Yn(e,n,r){var i=t.indexOf||kr;return i=i===kr?o:i,e?i(e,n,r):i}function Xn(e){for(var t=ta(e),n=t.length;n--;)t[n][2]=rr(t[n][1]);return t}function Wn(e,t){var n=null==e?P:e[t];return zi(n)?n:P}function Un(e,t,n){for(var r=-1,i=n.length;++r<i;){var a=n[r],o=a.size;switch(a.type){case"drop":e+=o;break;case"dropRight":t-=o;break;case"take":t=Eo(t,e+o);break;case"takeRight":e=_o(e,t-o)}}return{start:e,end:t}}function Hn(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&eo.call(e,"index")&&(n.index=e.index,n.input=e.input),n}function $n(e){var t=e.constructor;return"function"==typeof t&&t instanceof t||(t=Ua),new t}function Gn(e,t,n){var r=e.constructor;switch(t){case ae:return sn(e);case $:case G:return new r(+e);case oe:case se:case ue:case le:case ce:case de:case he:case fe:case pe:var i=e.buffer;return new r(n?sn(i):i,e.byteOffset,e.length);case J:case re:return new r(e);case te:var a=new r(e.source,Le.exec(e));a.lastIndex=e.lastIndex}return a}function Zn(e,t,n){null==e||er(t,e)||(t=hr(t),e=1==t.length?e:Bt(e,$t(t,0,-1)),t=Pr(t));var r=null==e?e:e[t];return null==r?P:r.apply(e,n)}function Qn(e){return null!=e&&nr(qo(e))}function Kn(e,t){return e="number"==typeof e||Ae.test(e)?+e:-1,t=null==t?Mo:t,e>-1&&e%1==0&&e<t}function Jn(e,t,n){if(!Bi(n))return!1;var r=typeof t;if("number"==r?Qn(n)&&Kn(t,n.length):"string"==r&&t in n){var i=n[t];return e===e?e===i:i!==i}return!1}function er(e,t){var n=typeof e;if("string"==n&&Pe.test(e)||"number"==n)return!0;if(Ds(e))return!1;var r=!Ce.test(e);return r||null!=t&&e in dr(t)}function tr(e){var n=qn(e);if(!(n in b.prototype))return!1;var r=t[n];if(e===r)return!0;var i=Vo(r);return!!i&&e===i[0]}function nr(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=Mo}function rr(e){return e===e&&!Bi(e)}function ir(e,t){var n=e[1],r=t[1],i=n|r,a=i<O,o=r==O&&n==N||r==O&&n==A&&e[7].length<=t[8]||r==(O|A)&&n==N;if(!a&&!o)return e;r&S&&(e[2]=t[2],i|=n&S?0:I);var s=t[3];if(s){var u=e[3];e[3]=u?un(u,s,t[4]):et(s),e[4]=u?x(e[3],W):et(t[4])}return s=t[5],s&&(u=e[5],e[5]=u?ln(u,s,t[6]):et(s),e[6]=u?x(e[5],W):et(t[6])),s=t[7],s&&(e[7]=et(s)),r&O&&(e[8]=null==e[8]?t[8]:Eo(e[8],t[8])),null==e[9]&&(e[9]=t[9]),
e[0]=t[0],e[1]=i,e}function ar(e,t){return e===P?t:Is(e,t,ar)}function or(e,t){e=dr(e);for(var n=-1,r=t.length,i={};++n<r;){var a=t[n];a in e&&(i[a]=e[a])}return i}function sr(e,t){var n={};return It(e,function(e,r,i){t(e,r,i)&&(n[r]=e)}),n}function ur(e,t){for(var n=e.length,r=Eo(t.length,n),i=et(e);r--;){var a=t[r];e[r]=Kn(a,n)?i[a]:P}return e}function lr(e){for(var t=ea(e),n=t.length,r=n&&e.length,i=!!r&&nr(r)&&(Ds(e)||Ci(e)),a=-1,o=[];++a<n;){var s=t[a];(i&&Kn(s,r)||eo.call(e,s))&&o.push(s)}return o}function cr(e){return null==e?[]:Qn(e)?Bi(e)?e:Ua(e):aa(e)}function dr(e){return Bi(e)?e:Ua(e)}function hr(e){if(Ds(e))return e;var t=[];return u(e).replace(Te,function(e,n,r,i){t.push(r?i.replace(Ne,"$1"):n||e)}),t}function fr(e){return e instanceof b?e.clone():new r(e.__wrapped__,e.__chain__,et(e.__actions__))}function pr(e,t,n){t=(n?Jn(e,t,n):null==t)?1:_o(mo(t)||1,1);for(var r=0,i=e?e.length:0,a=-1,o=Fa(go(i/t));r<i;)o[++a]=$t(e,r,r+=t);return o}function vr(e){for(var t=-1,n=e?e.length:0,r=-1,i=[];++t<n;){var a=e[t];a&&(i[++r]=a)}return i}function gr(e,t,n){var r=e?e.length:0;return r?((n?Jn(e,t,n):null==t)&&(t=1),$t(e,t<0?0:t)):[]}function yr(e,t,n){var r=e?e.length:0;return r?((n?Jn(e,t,n):null==t)&&(t=1),t=r-(+t||0),$t(e,0,t<0?0:t)):[]}function mr(e,t,n){return e&&e.length?tn(e,Vn(t,n,3),!0,!0):[]}function br(e,t,n){return e&&e.length?tn(e,Vn(t,n,3),!0):[]}function xr(e,t,n,r){var i=e?e.length:0;return i?(n&&"number"!=typeof n&&Jn(e,t,n)&&(n=0,r=i),Pt(e,t,n,r)):[]}function wr(e){return e?e[0]:P}function _r(e,t,n){var r=e?e.length:0;return n&&Jn(e,t,n)&&(t=!1),r?Dt(e,t):[]}function Er(e){var t=e?e.length:0;return t?Dt(e,!0):[]}function kr(e,t,n){var r=e?e.length:0;if(!r)return-1;if("number"==typeof n)n=n<0?_o(r+n,0):n;else if(n){var i=rn(e,t);return i<r&&(t===t?t===e[i]:e[i]!==e[i])?i:-1}return o(e,t,n||0)}function Cr(e){return yr(e,1)}function Pr(e){var t=e?e.length:0;return t?e[t-1]:P}function Tr(e,t,n){var r=e?e.length:0;if(!r)return-1;var i=r;if("number"==typeof n)i=(n<0?_o(r+n,0):Eo(n||0,r-1))+1;else if(n){i=rn(e,t,!0)-1;var a=e[i];return(t===t?t===a:a!==a)?i:-1}if(t!==t)return y(e,i,!0);for(;i--;)if(e[i]===t)return i;return-1}function Sr(){var e=arguments,t=e[0];if(!t||!t.length)return t;for(var n=0,r=Yn(),i=e.length;++n<i;)for(var a=0,o=e[n];(a=r(t,o,a))>-1;)fo.call(t,a,1);return t}function Dr(e,t,n){var r=[];if(!e||!e.length)return r;var i=-1,a=[],o=e.length;for(t=Vn(t,n,3);++i<o;){var s=e[i];t(s,i,e)&&(r.push(s),a.push(i))}return Wt(e,a),r}function Ir(e){return gr(e,1)}function Nr(e,t,n){var r=e?e.length:0;return r?(n&&"number"!=typeof n&&Jn(e,t,n)&&(t=0,n=r),$t(e,t,n)):[]}function Mr(e,t,n){var r=e?e.length:0;return r?((n?Jn(e,t,n):null==t)&&(t=1),$t(e,0,t<0?0:t)):[]}function Lr(e,t,n){var r=e?e.length:0;return r?((n?Jn(e,t,n):null==t)&&(t=1),t=r-(+t||0),$t(e,t<0?0:t)):[]}function Br(e,t,n){return e&&e.length?tn(e,Vn(t,n,3),!1,!0):[]}function Or(e,t,n){return e&&e.length?tn(e,Vn(t,n,3)):[]}function Ar(e,t,n,r){var i=e?e.length:0;if(!i)return[];null!=t&&"boolean"!=typeof t&&(r=n,n=Jn(e,t,r)?P:t,t=!1);var a=Vn();return null==n&&a===xt||(n=a(n,r,3)),t&&Yn()==o?w(e,n):Jt(e,n)}function zr(e){if(!e||!e.length)return[];var t=-1,n=0;e=st(e,function(e){if(Qn(e))return n=_o(e.length,n),!0});for(var r=Fa(n);++t<n;)r[t]=ut(e,Yt(t));return r}function Rr(e,t,n){var r=e?e.length:0;if(!r)return[];var i=zr(e);return null==t?i:(t=on(t,n,4),ut(i,function(e){return ct(e,t,P,!0)}))}function jr(){for(var e=-1,t=arguments.length;++e<t;){var n=arguments[e];if(Qn(n))var r=r?lt(Et(r,n),Et(n,r)):n}return r?Jt(r):[]}function Fr(e,t){var n=-1,r=e?e.length:0,i={};for(!r||t||Ds(e[0])||(t=[]);++n<r;){var a=e[n];t?i[a]=t[n]:a&&(i[a[0]]=a[1])}return i}function Vr(e){var n=t(e);return n.__chain__=!0,n}function qr(e,t,n){return t.call(n,e),e}function Yr(e,t,n){return t.call(n,e)}function Xr(){return Vr(this)}function Wr(){return new r(this.value(),this.__chain__)}function Ur(e){for(var t,r=this;r instanceof n;){var i=fr(r);t?a.__wrapped__=i:t=i;var a=i;r=r.__wrapped__}return a.__wrapped__=e,t}function Hr(){var e=this.__wrapped__,t=function(e){return n&&n.__dir__<0?e:e.reverse()};if(e instanceof b){var n=e;return this.__actions__.length&&(n=new b(this)),n=n.reverse(),n.__actions__.push({func:Yr,args:[t],thisArg:P}),new r(n,this.__chain__)}return this.thru(t)}function $r(){return this.value()+""}function Gr(){return nn(this.__wrapped__,this.__actions__)}function Zr(e,t,n){var r=Ds(e)?at:kt;return n&&Jn(e,t,n)&&(t=P),"function"==typeof t&&n===P||(t=Vn(t,n,3)),r(e,t)}function Qr(e,t,n){var r=Ds(e)?st:Tt;return t=Vn(t,n,3),r(e,t)}function Kr(e,t){return is(e,jt(t))}function Jr(e,t,n,r){var i=e?qo(e):0;return nr(i)||(e=aa(e),i=e.length),n="number"!=typeof n||r&&Jn(t,n,r)?0:n<0?_o(i+n,0):n||0,"string"==typeof e||!Ds(e)&&qi(e)?n<=i&&e.indexOf(t,n)>-1:!!i&&Yn(e,t,n)>-1}function ei(e,t,n){var r=Ds(e)?ut:Rt;return t=Vn(t,n,3),r(e,t)}function ti(e,t){return ei(e,La(t))}function ni(e,t,n){var r=Ds(e)?st:Tt;return t=Vn(t,n,3),r(e,function(e,n,r){return!t(e,n,r)})}function ri(e,t,n){if(n?Jn(e,t,n):null==t){e=cr(e);var r=e.length;return r>0?e[Ut(0,r-1)]:P}var i=-1,a=Hi(e),r=a.length,o=r-1;for(t=Eo(t<0?0:+t||0,r);++i<t;){var s=Ut(i,o),u=a[s];a[s]=a[i],a[i]=u}return a.length=t,a}function ii(e){return ri(e,So)}function ai(e){var t=e?qo(e):0;return nr(t)?t:Fs(e).length}function oi(e,t,n){var r=Ds(e)?ht:Gt;return n&&Jn(e,t,n)&&(t=P),"function"==typeof t&&n===P||(t=Vn(t,n,3)),r(e,t)}function si(e,t,n){if(null==e)return[];n&&Jn(e,t,n)&&(t=P);var r=-1;t=Vn(t,n,3);var i=Rt(e,function(e,n,i){return{criteria:t(e,n,i),index:++r,value:e}});return Zt(i,d)}function ui(e,t,n,r){return null==e?[]:(r&&Jn(t,n,r)&&(n=P),Ds(t)||(t=null==t?[]:[t]),Ds(n)||(n=null==n?[]:[n]),Qt(e,t,n))}function li(e,t){return Qr(e,jt(t))}function ci(e,t){if("function"!=typeof t){if("function"!=typeof e)throw new Ga(X);var n=e;e=t,t=n}return e=xo(e=+e)?e:0,function(){if(--e<1)return t.apply(this,arguments)}}function di(e,t,n){return n&&Jn(e,t,n)&&(t=P),t=e&&null==t?e.length:_o(+t||0,0),zn(e,O,P,P,P,P,t)}function hi(e,t){var n;if("function"!=typeof t){if("function"!=typeof e)throw new Ga(X);var r=e;e=t,t=r}return function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=P),n}}function fi(e,t,n){function r(){f&&oo(f),l&&oo(l),v=0,l=f=p=P}function i(t,n){n&&oo(n),l=f=p=P,t&&(v=vs(),c=e.apply(h,u),f||l||(u=h=P))}function a(){var e=t-(vs()-d);e<=0||e>t?i(p,l):f=ho(a,e)}function o(){i(y,f)}function s(){if(u=arguments,d=vs(),h=this,p=y&&(f||!m),g===!1)var n=m&&!f;else{l||m||(v=d);var r=g-(d-v),i=r<=0||r>g;i?(l&&(l=oo(l)),v=d,c=e.apply(h,u)):l||(l=ho(o,r))}return i&&f?f=oo(f):f||t===g||(f=ho(a,t)),n&&(i=!0,c=e.apply(h,u)),!i||f||l||(u=h=P),c}var u,l,c,d,h,f,p,v=0,g=!1,y=!0;if("function"!=typeof e)throw new Ga(X);if(t=t<0?0:+t||0,n===!0){var m=!0;y=!1}else Bi(n)&&(m=!!n.leading,g="maxWait"in n&&_o(+n.maxWait||0,t),y="trailing"in n?!!n.trailing:y);return s.cancel=r,s}function pi(e,t){if("function"!=typeof e||t&&"function"!=typeof t)throw new Ga(X);var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],a=n.cache;if(a.has(i))return a.get(i);var o=e.apply(this,r);return n.cache=a.set(i,o),o};return n.cache=new pi.Cache,n}function vi(e){if("function"!=typeof e)throw new Ga(X);return function(){return!e.apply(this,arguments)}}function gi(e){return hi(2,e)}function yi(e,t){if("function"!=typeof e)throw new Ga(X);return t=_o(t===P?e.length-1:+t||0,0),function(){for(var n=arguments,r=-1,i=_o(n.length-t,0),a=Fa(i);++r<i;)a[r]=n[t+r];switch(t){case 0:return e.call(this,a);case 1:return e.call(this,n[0],a);case 2:return e.call(this,n[0],n[1],a)}var o=Fa(t+1);for(r=-1;++r<t;)o[r]=n[r];return o[t]=a,e.apply(this,o)}}function mi(e){if("function"!=typeof e)throw new Ga(X);return function(t){return e.apply(this,t)}}function bi(e,t,n){var r=!0,i=!0;if("function"!=typeof e)throw new Ga(X);return n===!1?r=!1:Bi(n)&&(r="leading"in n?!!n.leading:r,i="trailing"in n?!!n.trailing:i),fi(e,t,{leading:r,maxWait:+t,trailing:i})}function xi(e,t){return t=null==t?Ta:t,zn(t,L,P,[e],[])}function wi(e,t,n,r){return t&&"boolean"!=typeof t&&Jn(e,t,n)?t=!1:"function"==typeof t&&(r=n,n=t,t=!1),"function"==typeof n?wt(e,t,on(n,r,1)):wt(e,t)}function _i(e,t,n){return"function"==typeof t?wt(e,!0,on(t,n,1)):wt(e,!0)}function Ei(e,t){return e>t}function ki(e,t){return e>=t}function Ci(e){return m(e)&&Qn(e)&&eo.call(e,"callee")&&!lo.call(e,"callee")}function Pi(e){return e===!0||e===!1||m(e)&&no.call(e)==$}function Ti(e){return m(e)&&no.call(e)==G}function Si(e){return!!e&&1===e.nodeType&&m(e)&&!Fi(e)}function Di(e){return null==e||(Qn(e)&&(Ds(e)||qi(e)||Ci(e)||m(e)&&Li(e.splice))?!e.length:!Fs(e).length)}function Ii(e,t,n,r){n="function"==typeof n?on(n,r,3):P;var i=n?n(e,t):P;return i===P?Ot(e,t,n):!!i}function Ni(e){return m(e)&&"string"==typeof e.message&&no.call(e)==Z}function Mi(e){return"number"==typeof e&&xo(e)}function Li(e){return Bi(e)&&no.call(e)==Q}function Bi(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function Oi(e,t,n,r){return n="function"==typeof n?on(n,r,3):P,zt(e,Xn(t),n)}function Ai(e){return ji(e)&&e!=+e}function zi(e){return null!=e&&(Li(e)?io.test(Ja.call(e)):m(e)&&Oe.test(e))}function Ri(e){return null===e}function ji(e){return"number"==typeof e||m(e)&&no.call(e)==J}function Fi(e){var t;if(!m(e)||no.call(e)!=ee||Ci(e)||!eo.call(e,"constructor")&&(t=e.constructor,"function"==typeof t&&!(t instanceof t)))return!1;var n;return It(e,function(e,t){n=t}),n===P||eo.call(e,n)}function Vi(e){return Bi(e)&&no.call(e)==te}function qi(e){return"string"==typeof e||m(e)&&no.call(e)==re}function Yi(e){return m(e)&&nr(e.length)&&!!Ye[no.call(e)]}function Xi(e){return e===P}function Wi(e,t){return e<t}function Ui(e,t){return e<=t}function Hi(e){var t=e?qo(e):0;return nr(t)?t?et(e):[]:aa(e)}function $i(e){return bt(e,ea(e))}function Gi(e,t,n){var r=Oo(e);return n&&Jn(e,t,n)&&(t=P),t?yt(r,t):r}function Zi(e){return Lt(e,ea(e))}function Qi(e,t,n){var r=null==e?P:Bt(e,hr(t),t+"");return r===P?n:r}function Ki(e,t){if(null==e)return!1;var n=eo.call(e,t);if(!n&&!er(t)){if(t=hr(t),e=1==t.length?e:Bt(e,$t(t,0,-1)),null==e)return!1;t=Pr(t),n=eo.call(e,t)}return n||nr(e.length)&&Kn(t,e.length)&&(Ds(e)||Ci(e))}function Ji(e,t,n){n&&Jn(e,t,n)&&(t=P);for(var r=-1,i=Fs(e),a=i.length,o={};++r<a;){var s=i[r],u=e[s];t?eo.call(o,u)?o[u].push(s):o[u]=[s]:o[u]=s}return o}function ea(e){if(null==e)return[];Bi(e)||(e=Ua(e));var t=e.length;t=t&&nr(t)&&(Ds(e)||Ci(e))&&t||0;for(var n=e.constructor,r=-1,i="function"==typeof n&&n.prototype===e,a=Fa(t),o=t>0;++r<t;)a[r]=r+"";for(var s in e)o&&Kn(s,t)||"constructor"==s&&(i||!eo.call(e,s))||a.push(s);return a}function ta(e){e=dr(e);for(var t=-1,n=Fs(e),r=n.length,i=Fa(r);++t<r;){var a=n[t];i[t]=[a,e[a]]}return i}function na(e,t,n){var r=null==e?P:e[t];return r===P&&(null==e||er(t,e)||(t=hr(t),e=1==t.length?e:Bt(e,$t(t,0,-1)),r=null==e?P:e[Pr(t)]),r=r===P?n:r),Li(r)?r.call(e):r}function ra(e,t,n){if(null==e)return e;var r=t+"";t=null!=e[r]||er(t,e)?[r]:hr(t);for(var i=-1,a=t.length,o=a-1,s=e;null!=s&&++i<a;){var u=t[i];Bi(s)&&(i==o?s[u]=n:null==s[u]&&(s[u]=Kn(t[i+1])?[]:{})),s=s[u]}return e}function ia(e,t,n,r){var i=Ds(e)||Yi(e);if(t=Vn(t,r,4),null==n)if(i||Bi(e)){var a=e.constructor;n=i?Ds(e)?new a:[]:Oo(Li(a)?a.prototype:P)}else n={};return(i?tt:Nt)(e,function(e,r,i){return t(n,e,r,i)}),n}function aa(e){return en(e,Fs(e))}function oa(e){return en(e,ea(e))}function sa(e,t,n){return t=+t||0,n===P?(n=t,t=0):n=+n||0,e>=Eo(t,n)&&e<_o(t,n)}function ua(e,t,n){n&&Jn(e,t,n)&&(t=n=P);var r=null==e,i=null==t;if(null==n&&(i&&"boolean"==typeof e?(n=e,e=1):"boolean"==typeof t&&(n=t,i=!0)),r&&i&&(t=1,i=!1),e=+e||0,i?(t=e,e=0):t=+t||0,n||e%1||t%1){var a=Po();return Eo(e+a*(t-e+so("1e-"+((a+"").length-1))),t)}return Ut(e,t)}function la(e){return e=u(e),e&&e.charAt(0).toUpperCase()+e.slice(1)}function ca(e){return e=u(e),e&&e.replace(ze,f).replace(Ie,"")}function da(e,t,n){e=u(e),t+="";var r=e.length;return n=n===P?r:Eo(n<0?0:+n||0,r),n-=t.length,n>=0&&e.indexOf(t,n)==n}function ha(e){return e=u(e),e&&we.test(e)?e.replace(be,p):e}function fa(e){return e=u(e),e&&De.test(e)?e.replace(Se,v):e||"(?:)"}function pa(e,t,n){e=u(e),t=+t;var r=e.length;if(r>=t||!xo(t))return e;var i=(t-r)/2,a=mo(i),o=go(i);return n=Ln("",o,n),n.slice(0,a)+e+n}function va(e,t,n){return(n?Jn(e,t,n):null==t)?t=0:t&&(t=+t),e=ba(e),Co(e,t||(Be.test(e)?16:10))}function ga(e,t){var n="";if(e=u(e),t=+t,t<1||!e||!xo(t))return n;do t%2&&(n+=e),t=mo(t/2),e+=e;while(t);return n}function ya(e,t,n){return e=u(e),n=null==n?0:Eo(n<0?0:+n||0,e.length),e.lastIndexOf(t,n)==n}function ma(e,n,r){var i=t.templateSettings;r&&Jn(e,n,r)&&(n=r=P),e=u(e),n=gt(yt({},r||n),i,vt);var a,o,s=gt(yt({},n.imports),i.imports,vt),l=Fs(s),c=en(s,l),d=0,h=n.interpolate||Re,f="__p += '",p=Ha((n.escape||Re).source+"|"+h.source+"|"+(h===ke?Me:Re).source+"|"+(n.evaluate||Re).source+"|$","g"),v="//# sourceURL="+("sourceURL"in n?n.sourceURL:"lodash.templateSources["+ ++qe+"]")+"\n";e.replace(p,function(t,n,r,i,s,u){return r||(r=i),f+=e.slice(d,u).replace(je,g),n&&(a=!0,f+="' +\n__e("+n+") +\n'"),s&&(o=!0,f+="';\n"+s+";\n__p += '"),r&&(f+="' +\n((__t = ("+r+")) == null ? '' : __t) +\n'"),d=u+t.length,t}),f+="';\n";var y=n.variable;y||(f="with (obj) {\n"+f+"\n}\n"),f=(o?f.replace(ve,""):f).replace(ge,"$1").replace(ye,"$1;"),f="function("+(y||"obj")+") {\n"+(y?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(a?", __e = _.escape":"")+(o?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+f+"return __p\n}";var m=Qs(function(){return Ya(l,v+"return "+f).apply(P,c)});if(m.source=f,Ni(m))throw m;return m}function ba(e,t,n){var r=e;return(e=u(e))?(n?Jn(r,t,n):null==t)?e.slice(_(e),E(e)+1):(t+="",e.slice(l(e,t),c(e,t)+1)):e}function xa(e,t,n){var r=e;return e=u(e),e?(n?Jn(r,t,n):null==t)?e.slice(_(e)):e.slice(l(e,t+"")):e}function wa(e,t,n){var r=e;return e=u(e),e?(n?Jn(r,t,n):null==t)?e.slice(0,E(e)+1):e.slice(0,c(e,t+"")+1):e}function _a(e,t,n){n&&Jn(e,t,n)&&(t=P);var r=z,i=R;if(null!=t)if(Bi(t)){var a="separator"in t?t.separator:a;r="length"in t?+t.length||0:r,i="omission"in t?u(t.omission):i}else r=+t||0;if(e=u(e),r>=e.length)return e;var o=r-i.length;if(o<1)return i;var s=e.slice(0,o);if(null==a)return s+i;if(Vi(a)){if(e.slice(o).search(a)){var l,c,d=e.slice(0,o);for(a.global||(a=Ha(a.source,(Le.exec(a)||"")+"g")),a.lastIndex=0;l=a.exec(d);)c=l.index;s=s.slice(0,null==c?o:c)}}else if(e.indexOf(a,o)!=o){var h=s.lastIndexOf(a);h>-1&&(s=s.slice(0,h))}return s+i}function Ea(e){return e=u(e),e&&xe.test(e)?e.replace(me,k):e}function ka(e,t,n){return n&&Jn(e,t,n)&&(t=P),e=u(e),e.match(t||Fe)||[]}function Ca(e,t,n){return n&&Jn(e,t,n)&&(t=P),m(e)?Sa(e):xt(e,t)}function Pa(e){return function(){return e}}function Ta(e){return e}function Sa(e){return jt(wt(e,!0))}function Da(e,t){return Ft(e,wt(t,!0))}function Ia(e,t,n){if(null==n){var r=Bi(t),i=r?Fs(t):P,a=i&&i.length?Lt(t,i):P;(a?a.length:r)||(a=!1,n=t,t=e,e=this)}a||(a=Lt(t,Fs(t)));var o=!0,s=-1,u=Li(e),l=a.length;n===!1?o=!1:Bi(n)&&"chain"in n&&(o=n.chain);for(;++s<l;){var c=a[s],d=t[c];e[c]=d,u&&(e.prototype[c]=function(t){return function(){var n=this.__chain__;if(o||n){var r=e(this.__wrapped__),i=r.__actions__=et(this.__actions__);return i.push({func:t,args:arguments,thisArg:e}),r.__chain__=n,r}return t.apply(e,lt([this.value()],arguments))}}(d))}return e}function Na(){return rt._=ro,this}function Ma(){}function La(e){return er(e)?Yt(e):Xt(e)}function Ba(e){return function(t){return Bt(e,hr(t),t+"")}}function Oa(e,t,n){n&&Jn(e,t,n)&&(t=n=P),e=+e||0,n=null==n?1:+n||0,null==t?(t=e,e=0):t=+t||0;for(var r=-1,i=_o(go((t-e)/(n||1)),0),a=Fa(i);++r<i;)a[r]=e,e+=n;return a}function Aa(e,t,n){if(e=mo(e),e<1||!xo(e))return[];var r=-1,i=Fa(Eo(e,Do));for(t=on(t,n,1);++r<e;)r<Do?i[r]=t(r):t(r);return i}function za(e){var t=++to;return u(e)+t}function Ra(e,t){return(+e||0)+(+t||0)}function ja(e,t,n){return n&&Jn(e,t,n)&&(t=P),t=Vn(t,n,3),1==t.length?ft(Ds(e)?e:cr(e),t):Kt(e,t)}e=e?it.defaults(rt.Object(),e,it.pick(rt,Ve)):rt;var Fa=e.Array,Va=e.Date,qa=e.Error,Ya=e.Function,Xa=e.Math,Wa=e.Number,Ua=e.Object,Ha=e.RegExp,$a=e.String,Ga=e.TypeError,Za=Fa.prototype,Qa=Ua.prototype,Ka=$a.prototype,Ja=Ya.prototype.toString,eo=Qa.hasOwnProperty,to=0,no=Qa.toString,ro=rt._,io=Ha("^"+Ja.call(eo).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ao=e.ArrayBuffer,oo=e.clearTimeout,so=e.parseFloat,uo=Xa.pow,lo=Qa.propertyIsEnumerable,co=Wn(e,"Set"),ho=e.setTimeout,fo=Za.splice,po=e.Uint8Array,vo=Wn(e,"WeakMap"),go=Xa.ceil,yo=Wn(Ua,"create"),mo=Xa.floor,bo=Wn(Fa,"isArray"),xo=e.isFinite,wo=Wn(Ua,"keys"),_o=Xa.max,Eo=Xa.min,ko=Wn(Va,"now"),Co=e.parseInt,Po=Xa.random,To=Wa.NEGATIVE_INFINITY,So=Wa.POSITIVE_INFINITY,Do=4294967295,Io=Do-1,No=Do>>>1,Mo=9007199254740991,Lo=vo&&new vo,Bo={};t.support={};t.templateSettings={escape:_e,evaluate:Ee,interpolate:ke,variable:"",imports:{_:t}};var Oo=function(){function e(){}return function(t){if(Bi(t)){e.prototype=t;var n=new e;e.prototype=P}return n||{}}}(),Ao=hn(Nt),zo=hn(Mt,!0),Ro=fn(),jo=fn(!0),Fo=Lo?function(e,t){return Lo.set(e,t),e}:Ta,Vo=Lo?function(e){return Lo.get(e)}:Ma,qo=Yt("length"),Yo=function(){var e=0,t=0;return function(n,r){var i=vs(),a=F-(i-t);if(t=i,a>0){if(++e>=j)return n}else e=0;return Fo(n,r)}}(),Xo=yi(function(e,t){return m(e)&&Qn(e)?Et(e,Dt(t,!1,!0)):[]}),Wo=_n(),Uo=_n(!0),Ho=yi(function(e){for(var t=e.length,n=t,r=Fa(d),i=Yn(),a=i==o,s=[];n--;){var u=e[n]=Qn(u=e[n])?u:[];r[n]=a&&u.length>=120?vn(n&&u):null}var l=e[0],c=-1,d=l?l.length:0,h=r[0];e:for(;++c<d;)if(u=l[c],(h?Qe(h,u):i(s,u,0))<0){for(var n=t;--n;){var f=r[n];if((f?Qe(f,u):i(e[n],u,0))<0)continue e}h&&h.push(u),s.push(u)}return s}),$o=yi(function(e,t){t=Dt(t);var n=mt(e,t);return Wt(e,t.sort(i)),n}),Go=An(),Zo=An(!0),Qo=yi(function(e){return Jt(Dt(e,!1,!0))}),Ko=yi(function(e,t){return Qn(e)?Et(e,t):[]}),Jo=yi(zr),es=yi(function(e){var t=e.length,n=t>2?e[t-2]:P,r=t>1?e[t-1]:P;return t>2&&"function"==typeof n?t-=2:(n=t>1&&"function"==typeof r?(--t,r):P,r=P),e.length=t,Rr(e,n,r)}),ts=yi(function(e){return e=Dt(e),this.thru(function(t){return Je(Ds(t)?t:[dr(t)],e)})}),ns=yi(function(e,t){return mt(e,Dt(t))}),rs=cn(function(e,t,n){eo.call(e,n)?++e[n]:e[n]=1}),is=wn(Ao),as=wn(zo,!0),os=Cn(tt,Ao),ss=Cn(nt,zo),us=cn(function(e,t,n){eo.call(e,n)?e[n].push(t):e[n]=[t]}),ls=cn(function(e,t,n){e[n]=t}),cs=yi(function(e,t,n){var r=-1,i="function"==typeof t,a=er(t),o=Qn(e)?Fa(e.length):[];return Ao(e,function(e){var s=i?t:a&&null!=e?e[t]:P;o[++r]=s?s.apply(e,n):Zn(e,t,n)}),o}),ds=cn(function(e,t,n){e[n?0:1].push(t)},function(){return[[],[]]}),hs=Nn(ct,Ao),fs=Nn(dt,zo),ps=yi(function(e,t){if(null==e)return[];var n=t[2];return n&&Jn(t[0],t[1],n)&&(t.length=1),Qt(e,Dt(t),[])}),vs=ko||function(){return(new Va).getTime()},gs=yi(function(e,t,n){var r=S;if(n.length){var i=x(n,gs.placeholder);r|=L}return zn(e,r,t,n,i)}),ys=yi(function(e,t){t=t.length?Dt(t):Zi(e);for(var n=-1,r=t.length;++n<r;){var i=t[n];e[i]=zn(e[i],S,e)}return e}),ms=yi(function(e,t,n){var r=S|D;if(n.length){var i=x(n,ms.placeholder);r|=L}return zn(t,r,e,n,i)}),bs=mn(N),xs=mn(M),ws=yi(function(e,t){return _t(e,1,t)}),_s=yi(function(e,t,n){return _t(e,t,n)}),Es=kn(),ks=kn(!0),Cs=yi(function(e,t){if(t=Dt(t),"function"!=typeof e||!at(t,s))throw new Ga(X);var n=t.length;return yi(function(r){for(var i=Eo(r.length,n);i--;)r[i]=t[i](r[i]);return e.apply(this,r)})}),Ps=In(L),Ts=In(B),Ss=yi(function(e,t){return zn(e,A,P,P,P,Dt(t))}),Ds=bo||function(e){return m(e)&&nr(e.length)&&no.call(e)==H},Is=dn(Vt),Ns=dn(function(e,t,n){return n?gt(e,t,n):yt(e,t)}),Ms=bn(Ns,pt),Ls=bn(Is,ar),Bs=En(Nt),Os=En(Mt),As=Pn(Ro),zs=Pn(jo),Rs=Tn(Nt),js=Tn(Mt),Fs=wo?function(e){var t=null==e?P:e.constructor;return"function"==typeof t&&t.prototype===e||"function"!=typeof e&&Qn(e)?lr(e):Bi(e)?wo(e):[]}:lr,Vs=Sn(!0),qs=Sn(),Ys=yi(function(e,t){if(null==e)return{};if("function"!=typeof t[0]){var t=ut(Dt(t),$a);return or(e,Et(ea(e),t))}var n=on(t[0],t[1],3);return sr(e,function(e,t,r){return!n(e,t,r)})}),Xs=yi(function(e,t){return null==e?{}:"function"==typeof t[0]?sr(e,on(t[0],t[1],3)):or(e,Dt(t))}),Ws=gn(function(e,t,n){return t=t.toLowerCase(),e+(n?t.charAt(0).toUpperCase()+t.slice(1):t)}),Us=gn(function(e,t,n){return e+(n?"-":"")+t.toLowerCase()}),Hs=Dn(),$s=Dn(!0),Gs=gn(function(e,t,n){return e+(n?"_":"")+t.toLowerCase()}),Zs=gn(function(e,t,n){return e+(n?" ":"")+(t.charAt(0).toUpperCase()+t.slice(1))}),Qs=yi(function(e,t){try{return e.apply(P,t)}catch(e){return Ni(e)?e:new qa(e)}}),Ks=yi(function(e,t){return function(n){return Zn(n,e,t)}}),Js=yi(function(e,t){return function(n){return Zn(e,n,t)}}),eu=On("ceil"),tu=On("floor"),nu=xn(Ei,To),ru=xn(Wi,So),iu=On("round");return t.prototype=n.prototype,r.prototype=Oo(n.prototype),r.prototype.constructor=r,b.prototype=Oo(n.prototype),b.prototype.constructor=b,We.prototype.delete=Ue,We.prototype.get=He,We.prototype.has=$e,We.prototype.set=Ge,Ze.prototype.push=Ke,pi.Cache=We,t.after=ci,t.ary=di,t.assign=Ns,t.at=ns,t.before=hi,t.bind=gs,t.bindAll=ys,t.bindKey=ms,t.callback=Ca,t.chain=Vr,t.chunk=pr,t.compact=vr,t.constant=Pa,t.countBy=rs,t.create=Gi,t.curry=bs,t.curryRight=xs,t.debounce=fi,t.defaults=Ms,t.defaultsDeep=Ls,t.defer=ws,t.delay=_s,t.difference=Xo,t.drop=gr,t.dropRight=yr,t.dropRightWhile=mr,t.dropWhile=br,t.fill=xr,t.filter=Qr,t.flatten=_r,t.flattenDeep=Er,t.flow=Es,t.flowRight=ks,t.forEach=os,t.forEachRight=ss,t.forIn=As,t.forInRight=zs,t.forOwn=Rs,t.forOwnRight=js,t.functions=Zi,t.groupBy=us,t.indexBy=ls,t.initial=Cr,t.intersection=Ho,t.invert=Ji,t.invoke=cs,t.keys=Fs,t.keysIn=ea,t.map=ei,t.mapKeys=Vs,t.mapValues=qs,t.matches=Sa,t.matchesProperty=Da,t.memoize=pi,t.merge=Is,t.method=Ks,t.methodOf=Js,t.mixin=Ia,t.modArgs=Cs,t.negate=vi,t.omit=Ys,t.once=gi,t.pairs=ta,t.partial=Ps,t.partialRight=Ts,t.partition=ds,t.pick=Xs,t.pluck=ti,t.property=La,t.propertyOf=Ba,t.pull=Sr,t.pullAt=$o,t.range=Oa,t.rearg=Ss,t.reject=ni,t.remove=Dr,t.rest=Ir,t.restParam=yi,t.set=ra,t.shuffle=ii,t.slice=Nr,t.sortBy=si,t.sortByAll=ps,t.sortByOrder=ui,t.spread=mi,t.take=Mr,t.takeRight=Lr,t.takeRightWhile=Br,t.takeWhile=Or,t.tap=qr,t.throttle=bi,t.thru=Yr,t.times=Aa,t.toArray=Hi,t.toPlainObject=$i,t.transform=ia,t.union=Qo,t.uniq=Ar,t.unzip=zr,t.unzipWith=Rr,t.values=aa,t.valuesIn=oa,t.where=li,t.without=Ko,t.wrap=xi,t.xor=jr,t.zip=Jo,t.zipObject=Fr,t.zipWith=es,t.backflow=ks,t.collect=ei,t.compose=ks,t.each=os,t.eachRight=ss,t.extend=Ns,t.iteratee=Ca,t.methods=Zi,t.object=Fr,t.select=Qr,t.tail=Ir,t.unique=Ar,Ia(t,t),t.add=Ra,t.attempt=Qs,t.camelCase=Ws,t.capitalize=la,t.ceil=eu,t.clone=wi,t.cloneDeep=_i,t.deburr=ca,t.endsWith=da,t.escape=ha,t.escapeRegExp=fa,t.every=Zr,t.find=is,t.findIndex=Wo,t.findKey=Bs,t.findLast=as,t.findLastIndex=Uo,t.findLastKey=Os,t.findWhere=Kr,t.first=wr,t.floor=tu,t.get=Qi,t.gt=Ei,t.gte=ki,t.has=Ki,t.identity=Ta,t.includes=Jr,t.indexOf=kr,t.inRange=sa,t.isArguments=Ci,t.isArray=Ds,t.isBoolean=Pi,t.isDate=Ti,t.isElement=Si,t.isEmpty=Di,t.isEqual=Ii,t.isError=Ni,t.isFinite=Mi,t.isFunction=Li,t.isMatch=Oi,t.isNaN=Ai,t.isNative=zi,t.isNull=Ri,t.isNumber=ji,t.isObject=Bi,t.isPlainObject=Fi,t.isRegExp=Vi,t.isString=qi,t.isTypedArray=Yi,t.isUndefined=Xi,t.kebabCase=Us,t.last=Pr,t.lastIndexOf=Tr,t.lt=Wi,t.lte=Ui,t.max=nu,t.min=ru,t.noConflict=Na,t.noop=Ma,t.now=vs,t.pad=pa,t.padLeft=Hs,t.padRight=$s,t.parseInt=va,t.random=ua,t.reduce=hs,t.reduceRight=fs,t.repeat=ga,t.result=na,t.round=iu,t.runInContext=C,t.size=ai,t.snakeCase=Gs,t.some=oi,t.sortedIndex=Go,t.sortedLastIndex=Zo,t.startCase=Zs,t.startsWith=ya,t.sum=ja,t.template=ma,t.trim=ba,t.trimLeft=xa,t.trimRight=wa,t.trunc=_a,t.unescape=Ea,t.uniqueId=za,t.words=ka,t.all=Zr,t.any=oi,t.contains=Jr,t.eq=Ii,t.detect=is,t.foldl=hs,t.foldr=fs,t.head=wr,t.include=Jr,t.inject=hs,Ia(t,function(){var e={};return Nt(t,function(n,r){t.prototype[r]||(e[r]=n)}),e}(),!1),t.sample=ri,t.prototype.sample=function(e){return this.__chain__||null!=e?this.thru(function(t){return ri(t,e)}):ri(this.value())},t.VERSION=T,tt(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){t[e].placeholder=t}),tt(["drop","take"],function(e,t){b.prototype[e]=function(n){var r=this.__filtered__;if(r&&!t)return new b(this);n=null==n?1:_o(mo(n)||0,0);var i=this.clone();return r?i.__takeCount__=Eo(i.__takeCount__,n):i.__views__.push({size:n,type:e+(i.__dir__<0?"Right":"")}),i},b.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}}),tt(["filter","map","takeWhile"],function(e,t){var n=t+1,r=n!=Y;b.prototype[e]=function(e,t){var i=this.clone();return i.__iteratees__.push({iteratee:Vn(e,t,1),type:n}),i.__filtered__=i.__filtered__||r,i}}),tt(["first","last"],function(e,t){var n="take"+(t?"Right":"");b.prototype[e]=function(){return this[n](1).value()[0]}}),tt(["initial","rest"],function(e,t){var n="drop"+(t?"":"Right");b.prototype[e]=function(){return this.__filtered__?new b(this):this[n](1)}}),tt(["pluck","where"],function(e,t){var n=t?"filter":"map",r=t?jt:La;b.prototype[e]=function(e){return this[n](r(e))}}),b.prototype.compact=function(){return this.filter(Ta)},b.prototype.reject=function(e,t){return e=Vn(e,t,1),this.filter(function(t){return!e(t)})},b.prototype.slice=function(e,t){e=null==e?0:+e||0;var n=this;return n.__filtered__&&(e>0||t<0)?new b(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==P&&(t=+t||0,n=t<0?n.dropRight(-t):n.take(t-e)),n)},b.prototype.takeRightWhile=function(e,t){return this.reverse().takeWhile(e,t).reverse()},b.prototype.toArray=function(){return this.take(So)},Nt(b.prototype,function(e,n){var i=/^(?:filter|map|reject)|While$/.test(n),a=/^(?:first|last)$/.test(n),o=t[a?"take"+("last"==n?"Right":""):n];o&&(t.prototype[n]=function(){var t=a?[1]:arguments,n=this.__chain__,s=this.__wrapped__,u=!!this.__actions__.length,l=s instanceof b,c=t[0],d=l||Ds(s);d&&i&&"function"==typeof c&&1!=c.length&&(l=d=!1);var h=function(e){return a&&n?o(e,1)[0]:o.apply(P,lt([e],t))},f={func:Yr,args:[h],thisArg:P},p=l&&!u;if(a&&!n)return p?(s=s.clone(),s.__actions__.push(f),e.call(s)):o.call(P,this.value())[0];if(!a&&d){s=p?s:new b(this);var v=e.apply(s,t);return v.__actions__.push(f),new r(v,n)}return this.thru(h)})}),tt(["join","pop","push","replace","shift","sort","splice","split","unshift"],function(e){var n=(/^(?:replace|split)$/.test(e)?Ka:Za)[e],r=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",i=/^(?:join|pop|replace|shift)$/.test(e);t.prototype[e]=function(){var e=arguments;return i&&!this.__chain__?n.apply(this.value(),e):this[r](function(t){return n.apply(t,e)})}}),Nt(b.prototype,function(e,n){var r=t[n];if(r){var i=r.name,a=Bo[i]||(Bo[i]=[]);a.push({name:n,func:r})}}),Bo[Mn(P,D).name]=[{name:"wrapper",func:P}],b.prototype.clone=K,b.prototype.reverse=ne,b.prototype.value=ie,t.prototype.chain=Xr,t.prototype.commit=Wr,t.prototype.concat=ts,t.prototype.plant=Ur,t.prototype.reverse=Hr,t.prototype.toString=$r,t.prototype.run=t.prototype.toJSON=t.prototype.valueOf=t.prototype.value=Gr,t.prototype.collect=t.prototype.map,t.prototype.head=t.prototype.first,t.prototype.select=t.prototype.filter,t.prototype.tail=t.prototype.rest,t}var P,T="3.10.0",S=1,D=2,I=4,N=8,M=16,L=32,B=64,O=128,A=256,z=30,R="...",j=150,F=16,V=200,q=1,Y=2,X="Expected a function",W="__lodash_placeholder__",U="[object Arguments]",H="[object Array]",$="[object Boolean]",G="[object Date]",Z="[object Error]",Q="[object Function]",K="[object Map]",J="[object Number]",ee="[object Object]",te="[object RegExp]",ne="[object Set]",re="[object String]",ie="[object WeakMap]",ae="[object ArrayBuffer]",oe="[object Float32Array]",se="[object Float64Array]",ue="[object Int8Array]",le="[object Int16Array]",ce="[object Int32Array]",de="[object Uint8Array]",he="[object Uint8ClampedArray]",fe="[object Uint16Array]",pe="[object Uint32Array]",ve=/\b__p \+= '';/g,ge=/\b(__p \+=) '' \+/g,ye=/(__e\(.*?\)|\b__t\)) \+\n'';/g,me=/&(?:amp|lt|gt|quot|#39|#96);/g,be=/[&<>"'`]/g,xe=RegExp(me.source),we=RegExp(be.source),_e=/<%-([\s\S]+?)%>/g,Ee=/<%([\s\S]+?)%>/g,ke=/<%=([\s\S]+?)%>/g,Ce=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\n\\]|\\.)*?\1)\]/,Pe=/^\w*$/,Te=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\n\\]|\\.)*?)\2)\]/g,Se=/^[:!,]|[\\^$.*+?()[\]{}|\/]|(^[0-9a-fA-Fnrtuvx])|([\n\r\u2028\u2029])/g,De=RegExp(Se.source),Ie=/[\u0300-\u036f\ufe20-\ufe23]/g,Ne=/\\(\\)?/g,Me=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,Le=/\w*$/,Be=/^0[xX]/,Oe=/^\[object .+?Constructor\]$/,Ae=/^\d+$/,ze=/[\xc0-\xd6\xd8-\xde\xdf-\xf6\xf8-\xff]/g,Re=/($^)/,je=/['\n\r\u2028\u2029\\]/g,Fe=function(){var e="[A-Z\\xc0-\\xd6\\xd8-\\xde]",t="[a-z\\xdf-\\xf6\\xf8-\\xff]+";return RegExp(e+"+(?="+e+t+")|"+e+"?"+t+"|"+e+"+|[0-9]+","g")}(),Ve=["Array","ArrayBuffer","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Math","Number","Object","RegExp","Set","String","_","clearTimeout","isFinite","parseFloat","parseInt","setTimeout","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap"],qe=-1,Ye={};Ye[oe]=Ye[se]=Ye[ue]=Ye[le]=Ye[ce]=Ye[de]=Ye[he]=Ye[fe]=Ye[pe]=!0,Ye[U]=Ye[H]=Ye[ae]=Ye[$]=Ye[G]=Ye[Z]=Ye[Q]=Ye[K]=Ye[J]=Ye[ee]=Ye[te]=Ye[ne]=Ye[re]=Ye[ie]=!1;var Xe={};Xe[U]=Xe[H]=Xe[ae]=Xe[$]=Xe[G]=Xe[oe]=Xe[se]=Xe[ue]=Xe[le]=Xe[ce]=Xe[J]=Xe[ee]=Xe[te]=Xe[re]=Xe[de]=Xe[he]=Xe[fe]=Xe[pe]=!0,Xe[Z]=Xe[Q]=Xe[K]=Xe[ne]=Xe[ie]=!1;var We={"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss"},Ue={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","`":"&#96;"},He={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'","&#96;":"`"},$e={function:!0,object:!0},Ge={0:"x30",1:"x31",2:"x32",3:"x33",4:"x34",5:"x35",6:"x36",7:"x37",8:"x38",9:"x39",A:"x41",B:"x42",C:"x43",D:"x44",E:"x45",F:"x46",a:"x61",b:"x62",c:"x63",d:"x64",e:"x65",f:"x66",n:"x6e",r:"x72",t:"x74",u:"x75",v:"x76",x:"x78"},Ze={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Qe=$e[typeof r]&&r&&!r.nodeType&&r,Ke=$e[typeof n]&&n&&!n.nodeType&&n,Je=Qe&&Ke&&"object"==typeof t&&t&&t.Object&&t,et=$e[typeof self]&&self&&self.Object&&self,tt=$e[typeof window]&&window&&window.Object&&window,nt=Ke&&Ke.exports===Qe&&Qe,rt=Je||tt!==(this&&this.window)&&tt||et||this,it=C();"function"==typeof e&&"object"==typeof e.amd&&e.amd?(rt._=it,e(function(){return it})):Qe&&Ke?nt?(Ke.exports=it)._=it:Qe._=it:rt._=it}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[1])(1)}),function(){"use strict";var e=function(e,t){function n(e){var t=this.options={};for(var n in i)t[n]=i[n];for(var n in e)t[n]=e[n]}if(e&&t){var r=function(e){return"function"==typeof e},i={nodeSep:void 0,edgeSep:void 0,rankSep:void 0,rankDir:void 0,minLen:function(e){return 1},edgeWeight:function(e){return 1},fit:!0,padding:30,animate:!1,animationDuration:500,animationEasing:void 0,boundingBox:void 0,ready:function(){},stop:function(){}};n.prototype.run=function(){var e=this.options,n=this,i=e.cy,a=e.eles,o=function(e,t){return r(t)?t.apply(e,[e]):t},s=e.boundingBox||{x1:0,y1:0,w:i.width(),h:i.height()};void 0===s.x2&&(s.x2=s.x1+s.w),void 0===s.w&&(s.w=s.x2-s.x1),void 0===s.y2&&(s.y2=s.y1+s.h),void 0===s.h&&(s.h=s.y2-s.y1);var u=new t.graphlib.Graph({multigraph:!0,compound:!0}),l={},c=function(e,t){null!=t&&(l[e]=t)};c("nodesep",e.nodeSep),c("edgesep",e.edgeSep),c("ranksep",e.rankSep),c("rankdir",e.rankDir),u.setGraph(l),u.setDefaultEdgeLabel(function(){return{}}),u.setDefaultNodeLabel(function(){return{}});for(var d=a.nodes(),h=0;h<d.length;h++){var f=d[h],p=f.boundingBox();u.setNode(f.id(),{
width:p.w,height:p.h,name:f.id()})}for(var h=0;h<d.length;h++){var f=d[h];f.isChild()&&u.setParent(f.id(),f.parent().id())}for(var v=a.edges().stdFilter(function(e){return!e.source().isParent()&&!e.target().isParent()}),h=0;h<v.length;h++){var g=v[h];u.setEdge(g.source().id(),g.target().id(),{minlen:o(g,e.minLen),weight:o(g,e.edgeWeight),name:g.id()},g.id())}t.layout(u);for(var y=u.nodes(),h=0;h<y.length;h++){var m=y[h],b=u.node(m);i.getElementById(m).scratch().dagre=b}var x;e.boundingBox?(x={x1:1/0,x2:-(1/0),y1:1/0,y2:-(1/0)},d.forEach(function(e){var t=e.scratch().dagre;x.x1=Math.min(x.x1,t.x),x.x2=Math.max(x.x2,t.x),x.y1=Math.min(x.y1,t.y),x.y2=Math.max(x.y2,t.y)}),x.w=x.x2-x.x1,x.h=x.y2-x.y1):x=s;var w=function(t){if(e.boundingBox){var n=0===x.w?0:(t.x-x.x1)/x.w,r=0===x.h?0:(t.y-x.y1)/x.h;return{x:s.x1+n*s.w,y:s.y1+r*s.h}}return t};return d.layoutPositions(n,e,function(e){e="object"==typeof e?e:this;var t=e.scratch().dagre;return w({x:t.x,y:t.y})}),this},e("layout","dagre",n)}};"undefined"!=typeof module&&module.exports?module.exports=function(t,n){e(t,n||require("dagre"))}:"undefined"!=typeof define&&define.amd&&define("cytoscape-dagre",function(){return e}),"undefined"!=typeof cytoscape&&"undefined"!=typeof dagre&&e(cytoscape,dagre)}();