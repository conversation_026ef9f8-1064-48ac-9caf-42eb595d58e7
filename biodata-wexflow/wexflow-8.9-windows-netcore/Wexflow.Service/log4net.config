﻿<?xml version="1.0" encoding="utf-8" ?>
<log4net>
  <appender name="RollingFile" type="log4net.Appender.RollingFileAppender">
    <file value="Wexflow.log"/>
    <encoding value="utf-8"/>
    <appendToFile value="true"/>
    <rollingStyle value="Date"/>
    <datePattern value="yyyyMMdd"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %5level [%thread] - %message%newline"/>
    </layout>
  </appender>
  <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %5level [%thread] - %message%newline"/>
    </layout>
  </appender>
  <root>
    <level value="INFO"/>
    <appender-ref ref="ConsoleAppender"/>
    <appender-ref ref="RollingFile"/>
  </root>
</log4net>
