#Requires -RunAsAdministrator

<#
.SYNOPSIS
    Script de gestion du service Windows Wexflow

.DESCRIPTION
    Ce script permet de gérer le service Windows Wexflow (démarrer, arrêter, redémarrer, statut).
    Il doit être exécuté avec des privilèges administrateur.

.PARAMETER Action
    Action à effectuer (Start, Stop, Restart, Status, Logs)

.PARAMETER ServiceName
    Nom du service (par défaut: WexflowService)

.PARAMETER LogCount
    Nombre d'entrées de log à afficher (par défaut: 10)

.EXAMPLE
    .\Manage-WexflowService.ps1 -Action Start
    
.EXAMPLE
    .\Manage-WexflowService.ps1 -Action Status
    
.EXAMPLE
    .\Manage-WexflowService.ps1 -Action Logs -LogCount 20
#>

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("Start", "Stop", "Restart", "Status", "Logs")]
    [string]$Action,
    
    [string]$ServiceName = "WexflowService",
    [int]$LogCount = 10
)

# Configuration
$ErrorActionPreference = "Stop"

function Write-ColoredOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Get-ServiceStatus {
    param([string]$Name)
    
    try {
        $service = Get-Service -Name $Name -ErrorAction Stop
        return @{
            Exists = $true
            Status = $service.Status
            StartType = (Get-WmiObject -Class Win32_Service -Filter "Name='$Name'").StartMode
            Service = $service
        }
    }
    catch {
        return @{
            Exists = $false
            Status = "NotFound"
            StartType = "Unknown"
            Service = $null
        }
    }
}

function Start-WexflowService {
    Write-ColoredOutput "Démarrage du service '$ServiceName'..." "Yellow"
    
    $serviceInfo = Get-ServiceStatus -Name $ServiceName
    if (-not $serviceInfo.Exists) {
        throw "Le service '$ServiceName' n'existe pas"
    }
    
    if ($serviceInfo.Status -eq "Running") {
        Write-ColoredOutput "Le service est déjà en cours d'exécution" "Green"
        return
    }
    
    try {
        Start-Service -Name $ServiceName
        
        # Attendre le démarrage
        $timeout = 30
        $elapsed = 0
        while ((Get-Service -Name $ServiceName).Status -ne "Running" -and $elapsed -lt $timeout) {
            Write-Host "." -NoNewline
            Start-Sleep -Seconds 1
            $elapsed++
        }
        Write-Host ""
        
        $finalStatus = (Get-Service -Name $ServiceName).Status
        if ($finalStatus -eq "Running") {
            Write-ColoredOutput "Service démarré avec succès !" "Green"
        } else {
            Write-ColoredOutput "Attention: Le service n'a pas démarré dans les temps (Statut: $finalStatus)" "Yellow"
        }
    }
    catch {
        throw "Erreur lors du démarrage du service: $($_.Exception.Message)"
    }
}

function Stop-WexflowService {
    Write-ColoredOutput "Arrêt du service '$ServiceName'..." "Yellow"
    
    $serviceInfo = Get-ServiceStatus -Name $ServiceName
    if (-not $serviceInfo.Exists) {
        throw "Le service '$ServiceName' n'existe pas"
    }
    
    if ($serviceInfo.Status -eq "Stopped") {
        Write-ColoredOutput "Le service est déjà arrêté" "Green"
        return
    }
    
    try {
        Stop-Service -Name $ServiceName -Force
        
        # Attendre l'arrêt
        $timeout = 30
        $elapsed = 0
        while ((Get-Service -Name $ServiceName).Status -ne "Stopped" -and $elapsed -lt $timeout) {
            Write-Host "." -NoNewline
            Start-Sleep -Seconds 1
            $elapsed++
        }
        Write-Host ""
        
        $finalStatus = (Get-Service -Name $ServiceName).Status
        if ($finalStatus -eq "Stopped") {
            Write-ColoredOutput "Service arrêté avec succès !" "Green"
        } else {
            Write-ColoredOutput "Attention: Le service n'a pas pu être arrêté dans les temps (Statut: $finalStatus)" "Yellow"
        }
    }
    catch {
        throw "Erreur lors de l'arrêt du service: $($_.Exception.Message)"
    }
}

function Restart-WexflowService {
    Write-ColoredOutput "Redémarrage du service '$ServiceName'..." "Yellow"
    
    # Arrêt
    Stop-WexflowService
    Start-Sleep -Seconds 2
    
    # Démarrage
    Start-WexflowService
}

function Show-ServiceStatus {
    Write-ColoredOutput "========================================" "Cyan"
    Write-ColoredOutput "  Statut du Service Wexflow" "Cyan"
    Write-ColoredOutput "========================================" "Cyan"
    Write-Host ""
    
    $serviceInfo = Get-ServiceStatus -Name $ServiceName
    
    if (-not $serviceInfo.Exists) {
        Write-ColoredOutput "❌ Le service '$ServiceName' n'existe pas" "Red"
        Write-Host ""
        Write-ColoredOutput "Pour installer le service, utilisez:" "Yellow"
        Write-ColoredOutput ".\Install-WexflowService.ps1" "Gray"
        return
    }
    
    # Informations de base
    Write-ColoredOutput "Informations générales:" "White"
    Write-ColoredOutput "- Nom du service: $ServiceName" "Gray"
    Write-ColoredOutput "- Nom d'affichage: $($serviceInfo.Service.DisplayName)" "Gray"
    
    # Statut avec couleur
    $statusColor = switch ($serviceInfo.Status) {
        "Running" { "Green" }
        "Stopped" { "Red" }
        "Paused" { "Yellow" }
        default { "Yellow" }
    }
    Write-ColoredOutput "- Statut: $($serviceInfo.Status)" $statusColor
    Write-ColoredOutput "- Type de démarrage: $($serviceInfo.StartType)" "Gray"
    Write-Host ""
    
    # Informations détaillées si le service fonctionne
    if ($serviceInfo.Status -eq "Running") {
        try {
            # Recherche du processus
            $processes = Get-Process | Where-Object { $_.ProcessName -like "*Wexflow*" }
            if ($processes) {
                Write-ColoredOutput "Processus associés:" "White"
                foreach ($process in $processes) {
                    $memoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
                    Write-ColoredOutput "- $($process.ProcessName) (PID: $($process.Id), Mémoire: ${memoryMB} MB)" "Gray"
                }
                Write-Host ""
            }
            
            # Vérification de la connectivité (port 8000 par défaut)
            try {
                $connection = Test-NetConnection -ComputerName "localhost" -Port 8000 -WarningAction SilentlyContinue
                if ($connection.TcpTestSucceeded) {
                    Write-ColoredOutput "✅ Service web accessible sur le port 8000" "Green"
                } else {
                    Write-ColoredOutput "⚠️  Service web non accessible sur le port 8000" "Yellow"
                }
            }
            catch {
                Write-ColoredOutput "⚠️  Impossible de vérifier la connectivité web" "Yellow"
            }
        }
        catch {
            Write-ColoredOutput "Impossible d'obtenir les informations détaillées: $($_.Exception.Message)" "Yellow"
        }
    }
    
    Write-Host ""
    Write-ColoredOutput "Commandes disponibles:" "White"
    Write-ColoredOutput "- Démarrer: .\Manage-WexflowService.ps1 -Action Start" "Gray"
    Write-ColoredOutput "- Arrêter: .\Manage-WexflowService.ps1 -Action Stop" "Gray"
    Write-ColoredOutput "- Redémarrer: .\Manage-WexflowService.ps1 -Action Restart" "Gray"
    Write-ColoredOutput "- Logs: .\Manage-WexflowService.ps1 -Action Logs" "Gray"
}

function Show-ServiceLogs {
    Write-ColoredOutput "========================================" "Cyan"
    Write-ColoredOutput "  Logs du Service Wexflow ($LogCount dernières entrées)" "Cyan"
    Write-ColoredOutput "========================================" "Cyan"
    Write-Host ""
    
    # Logs du journal d'événements Windows
    try {
        Write-ColoredOutput "📋 Journal d'événements Windows:" "White"
        $events = Get-EventLog -LogName Application -Source "Wexflow Service" -Newest $LogCount -ErrorAction SilentlyContinue
        
        if ($events) {
            foreach ($event in $events) {
                $levelColor = switch ($event.EntryType) {
                    "Error" { "Red" }
                    "Warning" { "Yellow" }
                    "Information" { "Green" }
                    default { "Gray" }
                }
                
                Write-ColoredOutput "[$($event.TimeGenerated)] [$($event.EntryType)] $($event.Message)" $levelColor
            }
        } else {
            Write-ColoredOutput "Aucune entrée trouvée dans le journal d'événements" "Gray"
        }
    }
    catch {
        Write-ColoredOutput "Impossible de lire le journal d'événements: $($_.Exception.Message)" "Yellow"
    }
    
    Write-Host ""
    
    # Logs de fichier
    try {
        Write-ColoredOutput "📄 Fichiers de logs:" "White"
        $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
        $parentDir = Split-Path -Parent $scriptDir
        $logsDir = Join-Path $parentDir "Logs"
        
        if (Test-Path $logsDir) {
            $logFiles = Get-ChildItem -Path $logsDir -Filter "*.log" | Sort-Object LastWriteTime -Descending
            
            if ($logFiles) {
                $latestLog = $logFiles[0]
                Write-ColoredOutput "Fichier le plus récent: $($latestLog.Name) (modifié le $($latestLog.LastWriteTime))" "Gray"
                
                # Affichage des dernières lignes
                $content = Get-Content -Path $latestLog.FullName -Tail $LogCount -ErrorAction SilentlyContinue
                if ($content) {
                    foreach ($line in $content) {
                        Write-ColoredOutput $line "Gray"
                    }
                } else {
                    Write-ColoredOutput "Le fichier de log est vide" "Gray"
                }
            } else {
                Write-ColoredOutput "Aucun fichier de log trouvé dans: $logsDir" "Gray"
            }
        } else {
            Write-ColoredOutput "Dossier de logs non trouvé: $logsDir" "Gray"
        }
    }
    catch {
        Write-ColoredOutput "Impossible de lire les fichiers de logs: $($_.Exception.Message)" "Yellow"
    }
}

# Fonction principale
function Manage-WexflowService {
    try {
        # Vérification des privilèges administrateur pour certaines actions
        if ($Action -in @("Start", "Stop", "Restart") -and -not (Test-Administrator)) {
            throw "Les actions Start, Stop et Restart nécessitent des privilèges administrateur"
        }
        
        switch ($Action) {
            "Start" { Start-WexflowService }
            "Stop" { Stop-WexflowService }
            "Restart" { Restart-WexflowService }
            "Status" { Show-ServiceStatus }
            "Logs" { Show-ServiceLogs }
        }
    }
    catch {
        Write-ColoredOutput "ERREUR: $($_.Exception.Message)" "Red"
        exit 1
    }
}

# Exécution
Manage-WexflowService
