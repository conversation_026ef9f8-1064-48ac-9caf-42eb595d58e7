﻿2025-07-02 09:31:46,760  INFO [46] - Workflow.PollingFileSystemWatcher.OnCreated
2025-07-02 09:31:46,771  INFO [46] - [Biodata Watcher / 1] [BiodataFileWatcher] Date de démarrage récupérée depuis l'état: 2025-07-01 16:19:07
2025-07-02 09:31:46,773  INFO [46] - A change in the workflow Biodata Watcher has been detected. The workflow will be reloaded.
2025-07-02 09:31:46,776  INFO [46] - [Biodata Watcher / 1] [BiodataFileWatcher] Date de démarrage récupérée depuis l'état: 2025-07-01 16:19:07
2025-07-02 09:31:46,776  INFO [46] - Workflow loaded: {id: 19198, name: Biodata Watcher, enabled: True, launchType: Trigger}
2025-07-02 09:32:01,865  INFO [87] - Workflow.PollingFileSystemWatcher.OnCreated
2025-07-02 09:32:01,872  INFO [87] - [Biodata Watcher / 1] [BiodataFileWatcher] Date de démarrage récupérée depuis l'état: 2025-07-01 16:19:07
2025-07-02 09:32:01,876  INFO [87] - A change in the workflow Biodata Watcher has been detected. The workflow will be reloaded.
2025-07-02 09:32:01,879  INFO [87] - [Biodata Watcher / 1] [BiodataFileWatcher] Date de démarrage récupérée depuis l'état: 2025-07-01 16:19:07
2025-07-02 09:32:01,880  INFO [87] - Workflow loaded: {id: 19198, name: Biodata Watcher, enabled: True, launchType: Trigger}
2025-07-02 09:32:01,881  INFO [87] - Workflow.PollingFileSystemWatcher.OnDeleted
2025-07-02 09:32:22,943  INFO [87] - Workflow.PollingFileSystemWatcher.OnChanged
2025-07-02 09:32:22,947 ERROR [87] - An error occured while saving the workflow <?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="19198" name="Biodata Watcher" description="Workflow de surveillance et traitement des fichiers biodata 2">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
    <Setting name="approval" value="false" />
    <Setting name="enableParallelJobs" value="false" />
  </Settings>
  <LocalVariables>
    <Variable name="scanInterval" value="*/2 * * * *" />
    <Variable name="filePattern" value="*.pdf" />
    <Variable name="basePath" value="\\**********\" />
    <Variable name="labPath" value="/mnt/remote_share_processed" />
  </LocalVariables>
  <Tasks>
    <!-- 1. BiodataFileWatcher : surveille le dossier avec filtrage par date -->
    <Task id="2" name="BiodataJobStatusUpdater" description="Surveillance du dossier avec filtrage par date d'activation" enabled="true">
      <Setting name="watchPath" value="Z:\" />
      <Setting name="filePattern" value="*.pdf" />
      <Setting name="includeSubfolders" value="true" />
      <Setting name="onFileCreated" value="3" />
    </Task>

   
  </Tasks>
  <ExecutionGraph>
    <Task id="2"><Parent id="-1" /></Task>

  </ExecutionGraph>
</Workflow>
:
System.Exception: The type of the task BiodataJobStatusUpdater could not be loaded.
   at Wexflow.Core.Workflow.Load(String xml) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 617
   at Wexflow.Core.Workflow..ctor(WexflowEngine wexflowEngine, Int32 jobId, Dictionary`2 jobs, String dbId, String xml, String wexflowTempFolder, String tasksFolder, String approvalFolder, String xsdPath, Db database, Variable[] globalVariables) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 299
   at Wexflow.Core.WexflowEngine.SaveWorkflow(String userId, UserProfile userProfile, String xml, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 438
2025-07-02 09:32:22,965 ERROR [87] - Error while saving the workflow C:\Wexflow-netcore\Workflows\Workflow_Biodata_JobStatusUpdater.xml
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at Wexflow.Core.WexflowEngine.SaveWorkflowFromFile(String userId, UserProfile userProfile, String filePath, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 532
2025-07-02 09:32:23,972  INFO [87] - Workflow.PollingFileSystemWatcher.OnChanged
2025-07-02 09:32:23,975 ERROR [87] - An error occured while saving the workflow <?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="19198" name="Biodata Watcher" description="Workflow de surveillance et traitement des fichiers biodata 2">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
    <Setting name="approval" value="false" />
    <Setting name="enableParallelJobs" value="false" />
  </Settings>
  <LocalVariables>
    <Variable name="scanInterval" value="*/2 * * * *" />
    <Variable name="filePattern" value="*.pdf" />
    <Variable name="basePath" value="\\**********\" />
    <Variable name="labPath" value="/mnt/remote_share_processed" />
  </LocalVariables>
  <Tasks>
    <!-- 1. BiodataJobStatusUpdater : surveille le dossier avec filtrage par date -->
    <Task id="2" name="BiodataJobStatusUpdater" description="Surveillance du dossier avec filtrage par date d'activation" enabled="true">
      <Setting name="watchPath" value="Z:\" />
      <Setting name="filePattern" value="*.pdf" />
      <Setting name="includeSubfolders" value="true" />
      <Setting name="onFileCreated" value="3" />
    </Task>

   
  </Tasks>
  <ExecutionGraph>
    <Task id="2"><Parent id="-1" /></Task>

  </ExecutionGraph>
</Workflow>
:
System.Exception: The type of the task BiodataJobStatusUpdater could not be loaded.
   at Wexflow.Core.Workflow.Load(String xml) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 617
   at Wexflow.Core.Workflow..ctor(WexflowEngine wexflowEngine, Int32 jobId, Dictionary`2 jobs, String dbId, String xml, String wexflowTempFolder, String tasksFolder, String approvalFolder, String xsdPath, Db database, Variable[] globalVariables) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 299
   at Wexflow.Core.WexflowEngine.SaveWorkflow(String userId, UserProfile userProfile, String xml, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 438
2025-07-02 09:32:23,978 ERROR [87] - Error while saving the workflow C:\Wexflow-netcore\Workflows\Workflow_Biodata_JobStatusUpdater.xml
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at Wexflow.Core.WexflowEngine.SaveWorkflowFromFile(String userId, UserProfile userProfile, String filePath, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 532
2025-07-02 09:32:37,021  INFO [46] - Workflow.PollingFileSystemWatcher.OnChanged
2025-07-02 09:32:37,024 ERROR [46] - An error occured while saving the workflow <?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="19198" name="Biodata Watcher" description="Workflow de surveillance et traitement des fichiers biodata 2">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
    <Setting name="approval" value="false" />
    <Setting name="enableParallelJobs" value="false" />
  </Settings>
  <LocalVariables>
    <Variable name="scanInterval" value="*/2 * * * *" />
    <Variable name="filePattern" value="*.pdf" />
    <Variable name="basePath" value="\\**********\" />
    <Variable name="labPath" value="/mnt/remote_share_processed" />
  </LocalVariables>
  <Tasks>
    <!-- 1. BiodataJobStatusUpdater : mise à jour des status des jobs-->
    <Task id="2" name="BiodataJobStatusUpdater" description="Surveillance du dossier avec filtrage par date d'activation" enabled="true">
      <Setting name="watchPath" value="Z:\" />
      <Setting name="filePattern" value="*.pdf" />
      <Setting name="includeSubfolders" value="true" />
      <Setting name="onFileCreated" value="3" />
    </Task>

   
  </Tasks>
  <ExecutionGraph>
    <Task id="2"><Parent id="-1" /></Task>

  </ExecutionGraph>
</Workflow>
:
System.Exception: The type of the task BiodataJobStatusUpdater could not be loaded.
   at Wexflow.Core.Workflow.Load(String xml) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 617
   at Wexflow.Core.Workflow..ctor(WexflowEngine wexflowEngine, Int32 jobId, Dictionary`2 jobs, String dbId, String xml, String wexflowTempFolder, String tasksFolder, String approvalFolder, String xsdPath, Db database, Variable[] globalVariables) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 299
   at Wexflow.Core.WexflowEngine.SaveWorkflow(String userId, UserProfile userProfile, String xml, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 438
2025-07-02 09:32:37,027 ERROR [46] - Error while saving the workflow C:\Wexflow-netcore\Workflows\Workflow_Biodata_JobStatusUpdater.xml
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at Wexflow.Core.WexflowEngine.SaveWorkflowFromFile(String userId, UserProfile userProfile, String filePath, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 532
2025-07-02 09:34:43,409  INFO [45] - Workflow.PollingFileSystemWatcher.OnChanged
2025-07-02 09:34:43,413 ERROR [45] - An error occured while saving the workflow <?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="19198" name="Biodata Watcher" description="Workflow de surveillance et traitement des fichiers biodata 2">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
    <Setting name="approval" value="false" />
    <Setting name="enableParallelJobs" value="false" />
  </Settings>
  <LocalVariables>
    <Variable name="scanInterval" value="*/2 * * * *" />
    <Variable name="filePattern" value="*.pdf" />
    <Variable name="basePath" value="\\**********\" />
    <Variable name="labPath" value="/mnt/remote_share_processed" />
  </LocalVariables>
  <Tasks>
    <!-- 1. BiodataJobStatusUpdater : mise à jour des status des jobs-->
    <Task id="2" name="BiodataJobStatusUpdater" description="Surveillance du dossier avec filtrage par date d'activation" enabled="true">
      <Setting name="watchPath" value="Z:\" />
      <Setting name="filePattern" value="*.pdf" />
      <Setting name="includeSubfolders" value="true" />
      <Setting name="onFileCreated" value="3" />
    </Task>

   
  </Tasks>
  <ExecutionGraph>
    <Task id="2"><Parent id="-1" /></Task>

  </ExecutionGraph>
</Workflow>
:
System.Exception: The type of the task BiodataJobStatusUpdater could not be loaded.
   at Wexflow.Core.Workflow.Load(String xml) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 617
   at Wexflow.Core.Workflow..ctor(WexflowEngine wexflowEngine, Int32 jobId, Dictionary`2 jobs, String dbId, String xml, String wexflowTempFolder, String tasksFolder, String approvalFolder, String xsdPath, Db database, Variable[] globalVariables) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 299
   at Wexflow.Core.WexflowEngine.SaveWorkflow(String userId, UserProfile userProfile, String xml, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 438
2025-07-02 09:34:43,417 ERROR [45] - Error while saving the workflow C:\Wexflow-netcore\Workflows\Workflow_Biodata_JobStatusUpdater.xml
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at Wexflow.Core.WexflowEngine.SaveWorkflowFromFile(String userId, UserProfile userProfile, String filePath, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 532
2025-07-02 09:34:44,418  INFO [45] - Workflow.PollingFileSystemWatcher.OnChanged
2025-07-02 09:34:44,420 ERROR [45] - An error occured while saving the workflow <?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="1919" name="Biodata Watcher" description="Workflow de surveillance et traitement des fichiers biodata 2">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
    <Setting name="approval" value="false" />
    <Setting name="enableParallelJobs" value="false" />
  </Settings>
  <LocalVariables>
    <Variable name="scanInterval" value="*/2 * * * *" />
    <Variable name="filePattern" value="*.pdf" />
    <Variable name="basePath" value="\\**********\" />
    <Variable name="labPath" value="/mnt/remote_share_processed" />
  </LocalVariables>
  <Tasks>
    <!-- 1. BiodataJobStatusUpdater : mise à jour des status des jobs-->
    <Task id="2" name="BiodataJobStatusUpdater" description="Surveillance du dossier avec filtrage par date d'activation" enabled="true">
      <Setting name="watchPath" value="Z:\" />
      <Setting name="filePattern" value="*.pdf" />
      <Setting name="includeSubfolders" value="true" />
      <Setting name="onFileCreated" value="3" />
    </Task>

   
  </Tasks>
  <ExecutionGraph>
    <Task id="2"><Parent id="-1" /></Task>

  </ExecutionGraph>
</Workflow>
:
System.Exception: The type of the task BiodataJobStatusUpdater could not be loaded.
   at Wexflow.Core.Workflow.Load(String xml) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 617
   at Wexflow.Core.Workflow..ctor(WexflowEngine wexflowEngine, Int32 jobId, Dictionary`2 jobs, String dbId, String xml, String wexflowTempFolder, String tasksFolder, String approvalFolder, String xsdPath, Db database, Variable[] globalVariables) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 299
   at Wexflow.Core.WexflowEngine.SaveWorkflow(String userId, UserProfile userProfile, String xml, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 396
2025-07-02 09:34:44,423 ERROR [45] - Error while saving the workflow C:\Wexflow-netcore\Workflows\Workflow_Biodata_JobStatusUpdater.xml
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at Wexflow.Core.WexflowEngine.SaveWorkflowFromFile(String userId, UserProfile userProfile, String filePath, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 532
2025-07-02 09:34:45,425  INFO [45] - Workflow.PollingFileSystemWatcher.OnChanged
2025-07-02 09:34:45,428 ERROR [45] - An error occured while saving the workflow <?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="19199" name="Biodata Watcher" description="Workflow de surveillance et traitement des fichiers biodata 2">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
    <Setting name="approval" value="false" />
    <Setting name="enableParallelJobs" value="false" />
  </Settings>
  <LocalVariables>
    <Variable name="scanInterval" value="*/2 * * * *" />
    <Variable name="filePattern" value="*.pdf" />
    <Variable name="basePath" value="\\**********\" />
    <Variable name="labPath" value="/mnt/remote_share_processed" />
  </LocalVariables>
  <Tasks>
    <!-- 1. BiodataJobStatusUpdater : mise à jour des status des jobs-->
    <Task id="2" name="BiodataJobStatusUpdater" description="Surveillance du dossier avec filtrage par date d'activation" enabled="true">
      <Setting name="watchPath" value="Z:\" />
      <Setting name="filePattern" value="*.pdf" />
      <Setting name="includeSubfolders" value="true" />
      <Setting name="onFileCreated" value="3" />
    </Task>

   
  </Tasks>
  <ExecutionGraph>
    <Task id="2"><Parent id="-1" /></Task>

  </ExecutionGraph>
</Workflow>
:
System.Exception: The type of the task BiodataJobStatusUpdater could not be loaded.
   at Wexflow.Core.Workflow.Load(String xml) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 617
   at Wexflow.Core.Workflow..ctor(WexflowEngine wexflowEngine, Int32 jobId, Dictionary`2 jobs, String dbId, String xml, String wexflowTempFolder, String tasksFolder, String approvalFolder, String xsdPath, Db database, Variable[] globalVariables) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 299
   at Wexflow.Core.WexflowEngine.SaveWorkflow(String userId, UserProfile userProfile, String xml, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 438
2025-07-02 09:34:45,433 ERROR [45] - Error while saving the workflow C:\Wexflow-netcore\Workflows\Workflow_Biodata_JobStatusUpdater.xml
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at Wexflow.Core.WexflowEngine.SaveWorkflowFromFile(String userId, UserProfile userProfile, String filePath, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 532
2025-07-02 09:34:55,477  INFO [45] - Workflow.PollingFileSystemWatcher.OnChanged
2025-07-02 09:34:55,482 ERROR [45] - An error occured while saving the workflow <?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="19199" name="Biodata Jobs Stats" description="Workflow de surveillance et traitement des fichiers biodata 2">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
    <Setting name="approval" value="false" />
    <Setting name="enableParallelJobs" value="false" />
  </Settings>
  <LocalVariables>
    <Variable name="scanInterval" value="*/2 * * * *" />
    <Variable name="filePattern" value="*.pdf" />
    <Variable name="basePath" value="\\**********\" />
    <Variable name="labPath" value="/mnt/remote_share_processed" />
  </LocalVariables>
  <Tasks>
    <!-- 1. BiodataJobStatusUpdater : mise à jour des status des jobs-->
    <Task id="2" name="BiodataJobStatusUpdater" description="Surveillance du dossier avec filtrage par date d'activation" enabled="true">
      <Setting name="watchPath" value="Z:\" />
      <Setting name="filePattern" value="*.pdf" />
      <Setting name="includeSubfolders" value="true" />
      <Setting name="onFileCreated" value="3" />
    </Task>

   
  </Tasks>
  <ExecutionGraph>
    <Task id="2"><Parent id="-1" /></Task>

  </ExecutionGraph>
</Workflow>
:
System.Exception: The type of the task BiodataJobStatusUpdater could not be loaded.
   at Wexflow.Core.Workflow.Load(String xml) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 617
   at Wexflow.Core.Workflow..ctor(WexflowEngine wexflowEngine, Int32 jobId, Dictionary`2 jobs, String dbId, String xml, String wexflowTempFolder, String tasksFolder, String approvalFolder, String xsdPath, Db database, Variable[] globalVariables) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 299
   at Wexflow.Core.WexflowEngine.SaveWorkflow(String userId, UserProfile userProfile, String xml, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 438
2025-07-02 09:34:55,485 ERROR [45] - Error while saving the workflow C:\Wexflow-netcore\Workflows\Workflow_Biodata_JobStatusUpdater.xml
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at Wexflow.Core.WexflowEngine.SaveWorkflowFromFile(String userId, UserProfile userProfile, String filePath, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 532
2025-07-02 09:34:59,503  INFO [45] - Workflow.PollingFileSystemWatcher.OnChanged
2025-07-02 09:34:59,506 ERROR [45] - An error occured while saving the workflow <?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="19199" name="Biodata Jobs Status Updater" description="Workflow de surveillance et traitement des fichiers biodata 2">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
    <Setting name="approval" value="false" />
    <Setting name="enableParallelJobs" value="false" />
  </Settings>
  <LocalVariables>
    <Variable name="scanInterval" value="*/2 * * * *" />
    <Variable name="filePattern" value="*.pdf" />
    <Variable name="basePath" value="\\**********\" />
    <Variable name="labPath" value="/mnt/remote_share_processed" />
  </LocalVariables>
  <Tasks>
    <!-- 1. BiodataJobStatusUpdater : mise à jour des status des jobs-->
    <Task id="2" name="BiodataJobStatusUpdater" description="Surveillance du dossier avec filtrage par date d'activation" enabled="true">
      <Setting name="watchPath" value="Z:\" />
      <Setting name="filePattern" value="*.pdf" />
      <Setting name="includeSubfolders" value="true" />
      <Setting name="onFileCreated" value="3" />
    </Task>

   
  </Tasks>
  <ExecutionGraph>
    <Task id="2"><Parent id="-1" /></Task>

  </ExecutionGraph>
</Workflow>
:
System.Exception: The type of the task BiodataJobStatusUpdater could not be loaded.
   at Wexflow.Core.Workflow.Load(String xml) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 617
   at Wexflow.Core.Workflow..ctor(WexflowEngine wexflowEngine, Int32 jobId, Dictionary`2 jobs, String dbId, String xml, String wexflowTempFolder, String tasksFolder, String approvalFolder, String xsdPath, Db database, Variable[] globalVariables) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 299
   at Wexflow.Core.WexflowEngine.SaveWorkflow(String userId, UserProfile userProfile, String xml, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 438
2025-07-02 09:34:59,509 ERROR [45] - Error while saving the workflow C:\Wexflow-netcore\Workflows\Workflow_Biodata_JobStatusUpdater.xml
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at Wexflow.Core.WexflowEngine.SaveWorkflowFromFile(String userId, UserProfile userProfile, String filePath, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 532
2025-07-02 09:35:19,564  INFO [45] - Workflow.PollingFileSystemWatcher.OnChanged
2025-07-02 09:35:19,567 ERROR [45] - An error occured while saving the workflow <?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="19199" name="Biodata Jobs Status Updater" description="Workflow de lmise à jour des status des jobs">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
    <Setting name="approval" value="false" />
    <Setting name="enableParallelJobs" value="false" />
  </Settings>
  <LocalVariables>
    <Variable name="scanInterval" value="*/2 * * * *" />
    <Variable name="filePattern" value="*.pdf" />
    <Variable name="basePath" value="\\**********\" />
    <Variable name="labPath" value="/mnt/remote_share_processed" />
  </LocalVariables>
  <Tasks>
    <!-- 1. BiodataJobStatusUpdater : mise à jour des status des jobs-->
    <Task id="2" name="BiodataJobStatusUpdater" description="Surveillance du dossier avec filtrage par date d'activation" enabled="true">
      <Setting name="watchPath" value="Z:\" />
      <Setting name="filePattern" value="*.pdf" />
      <Setting name="includeSubfolders" value="true" />
      <Setting name="onFileCreated" value="3" />
    </Task>

   
  </Tasks>
  <ExecutionGraph>
    <Task id="2"><Parent id="-1" /></Task>

  </ExecutionGraph>
</Workflow>
:
System.Exception: The type of the task BiodataJobStatusUpdater could not be loaded.
   at Wexflow.Core.Workflow.Load(String xml) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 617
   at Wexflow.Core.Workflow..ctor(WexflowEngine wexflowEngine, Int32 jobId, Dictionary`2 jobs, String dbId, String xml, String wexflowTempFolder, String tasksFolder, String approvalFolder, String xsdPath, Db database, Variable[] globalVariables) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 299
   at Wexflow.Core.WexflowEngine.SaveWorkflow(String userId, UserProfile userProfile, String xml, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 438
2025-07-02 09:35:19,570 ERROR [45] - Error while saving the workflow C:\Wexflow-netcore\Workflows\Workflow_Biodata_JobStatusUpdater.xml
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at Wexflow.Core.WexflowEngine.SaveWorkflowFromFile(String userId, UserProfile userProfile, String filePath, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 532
2025-07-02 09:35:20,573  INFO [45] - Workflow.PollingFileSystemWatcher.OnChanged
2025-07-02 09:35:20,576 ERROR [45] - An error occured while saving the workflow <?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="19199" name="Biodata Jobs Status Updater" description="Workflow de lmise à jour des status des jobs">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
    <Setting name="approval" value="false" />
    <Setting name="enableParallelJobs" value="false" />
  </Settings>
  <LocalVariables>
    <Variable name="scanInterval" value="*/2 * * * *" />
    <Variable name="filePattern" value="*.pdf" />
    <Variable name="basePath" value="\\**********\" />
    <Variable name="labPath" value="/mnt/remote_share_processed" />
  </LocalVariables>
  <Tasks>
    <!-- 1. BiodataJobStatusUpdater : mise à jour des status des jobs-->
    <Task id="2" name="BiodataJobStatusUpdater" description="Surveillance du dossier avec filtrage par date d'activation" enabled="true">
      <Setting name="watchPath" value="Z:\" />
      <Setting name="filePattern" value="*.pdf" />
      <Setting name="includeSubfolders" value="true" />
      <Setting name="onFileCreated" value="3" />
    </Task>

   
  </Tasks>
  <ExecutionGraph>
    <Task id="2"><Parent id="-1" /></Task>

  </ExecutionGraph>
</Workflow>
:
System.Exception: The type of the task BiodataJobStatusUpdater could not be loaded.
   at Wexflow.Core.Workflow.Load(String xml) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 617
   at Wexflow.Core.Workflow..ctor(WexflowEngine wexflowEngine, Int32 jobId, Dictionary`2 jobs, String dbId, String xml, String wexflowTempFolder, String tasksFolder, String approvalFolder, String xsdPath, Db database, Variable[] globalVariables) in /_/src/netcore/Wexflow.Core/Workflow.cs:line 299
   at Wexflow.Core.WexflowEngine.SaveWorkflow(String userId, UserProfile userProfile, String xml, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 438
2025-07-02 09:35:20,579 ERROR [45] - Error while saving the workflow C:\Wexflow-netcore\Workflows\Workflow_Biodata_JobStatusUpdater.xml
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at Wexflow.Core.WexflowEngine.SaveWorkflowFromFile(String userId, UserProfile userProfile, String filePath, Boolean schedule) in /_/src/netcore/Wexflow.Core/WexflowEngine.cs:line 532
2025-07-02 09:43:56,941  INFO [1] - Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-02 09:43:56,942  INFO [1] - Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-02 09:43:56,945  INFO [1] - Scheduler QuartzScheduler_$_NON_CLUSTERED Shutdown complete.
2025-07-02 09:43:56,946  INFO [1] - Workflows stopped.
2025-07-02 09:43:56,957  INFO [1] - Status count and dashboard entries cleared.
2025-07-02 09:43:56,958  INFO [1] - Database disposed.
