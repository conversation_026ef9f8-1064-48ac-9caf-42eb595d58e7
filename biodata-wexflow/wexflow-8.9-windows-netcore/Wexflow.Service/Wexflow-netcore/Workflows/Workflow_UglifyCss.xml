﻿<?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="164" name="Workflow_UglifyCss" description="Workflow_UglifyCss">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
  </Settings>
  <Tasks>
    <Task id="1" name="FilesLoader" description="Loading CSS files" enabled="true">
	  <Setting name="file" value="C:\WexflowTesting\UglifyCss_src\wexflow-designer.css" />
	  <Setting name="file" value="C:\WexflowTesting\UglifyCss_src\wexflow-manager.css" />
    </Task>
    <Task id="2" name="UglifyCss" description="Uglifying CSS files" enabled="true">
	  <Setting name="selectFiles" value="1" />
    </Task>
    <Task id="3" name="FilesMover" description="Moving uglified files to UglifyCss_dest folder" enabled="true">
      <Setting name="selectFiles" value="2" />
      <Setting name="destFolder" value="C:\WexflowTesting\UglifyCss_dest\" />
	  <Setting name="overwrite" value="true" />
    </Task>
  </Tasks>
</Workflow>