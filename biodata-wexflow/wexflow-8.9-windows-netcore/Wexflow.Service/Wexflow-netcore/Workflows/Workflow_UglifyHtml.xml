﻿<?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="165" name="Workflow_UglifyHtml" description="Workflow_UglifyHtml">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
  </Settings>
  <Tasks>
    <Task id="1" name="FilesLoader" description="Loading HTML files" enabled="true">
	  <Setting name="file" value="C:\WexflowTesting\UglifyHtml_src\wexflow-designer.html" />
	  <Setting name="file" value="C:\WexflowTesting\UglifyHtml_src\wexflow-manager.html" />
    </Task>
    <Task id="2" name="UglifyHtml" description="Uglifying HTML files" enabled="true">
	  <Setting name="selectFiles" value="1" />
    </Task>
    <Task id="3" name="FilesMover" description="Moving uglified files to UglifyHtml_dest folder" enabled="true">
      <Setting name="selectFiles" value="2" />
      <Setting name="destFolder" value="C:\WexflowTesting\UglifyHtml_dest\" />
	  <Setting name="overwrite" value="true" />
    </Task>
  </Tasks>
</Workflow>