﻿<?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="173" name="Workflow_ApproveVacations" description="Workflow_ApproveVacations">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
    <Setting name="approval" value="true" />
    <Setting name="enableParallelJobs" value="true" />
  </Settings>
  <LocalVariables />
  <Tasks>
    <Task id="1" name="ApproveRecord" description="Approve vacations" enabled="true">
      <Setting name="record" value="4" />
      <Setting name="assignedTo" value="wexflow" />
    </Task>
  </Tasks>
</Workflow>