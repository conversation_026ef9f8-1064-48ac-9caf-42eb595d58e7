<Workflow xmlns="urn:wexflow-schema" id="145" name="Workflow_FilesLoaderEx_RemoveMaxModifyDate" description="Workflow_FilesLoaderEx_RemoveMaxModifyDate">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
  </Settings>
  <LocalVariables />
  <Tasks>
    <Task id="1" name="FilesLoaderEx" description="Loading files_RemoveMaxModifyDate" enabled="true">
      <Setting name="folder" value="C:\WexflowTesting\FilesLoaderEx" />
      <Setting name="removeMaxModifyDate" value="2" />
    </Task>
    <Task id="2" name="ListFiles" description="Listing files" enabled="true" />
  </Tasks>
</Workflow>