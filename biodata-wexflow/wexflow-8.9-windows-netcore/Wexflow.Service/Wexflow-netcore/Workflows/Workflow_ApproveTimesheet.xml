﻿<?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="172" name="Workflow_ApproveTimesheet" description="Workflow_ApproveTimesheet">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
    <Setting name="approval" value="true" />
    <Setting name="enableParallelJobs" value="true" />
  </Settings>
  <LocalVariables />
  <Tasks>
    <Task id="1" name="ApproveRecord" description="Approve timesheet" enabled="true">
      <Setting name="record" value="3" />
      <Setting name="assignedTo" value="wexflow" />
    </Task>
  </Tasks>
</Workflow>