<Workflow xmlns="urn:wexflow-schema" id="73" name="Workflow_ImagesResizer" description="Workflow_ImagesResizer">
	<Settings>
		<Setting name="launchType" value="trigger" />
		<Setting name="enabled" value="true" />
	</Settings>
	<Tasks>
		<Task id="1" name="FilesLoader" description="Loading JPG images" enabled="true">
			<Setting name="file" value="C:\WexflowTesting\ImagesResizerSrc\image1.jpg" />
			<Setting name="file" value="C:\WexflowTesting\ImagesResizerSrc\image2.jpg" />
			<Setting name="file" value="C:\WexflowTesting\ImagesResizerSrc\image3.jpg" />
		</Task>
		<Task id="2" name="ImagesResizer" description="Resizing images" enabled="true">
			<Setting name="selectFiles" value="1" />
			<Setting name="width" value="512" />
			<Setting name="height" value="384" />
		</Task>
		<Task id="3" name="FilesMover" description="Moving the resized images to ImagesResizerDest folder" enabled="true">
			<Setting name="selectFiles" value="2" />
			<Setting name="destFolder" value="C:\WexflowTesting\ImagesResizerDest\" />
			<Setting name="overwrite" value="true" />
		</Task>
	</Tasks>
</Workflow>
