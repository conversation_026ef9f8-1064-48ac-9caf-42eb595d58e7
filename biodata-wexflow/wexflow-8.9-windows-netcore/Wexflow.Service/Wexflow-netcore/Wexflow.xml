<?xml version="1.0" encoding="UTF-8" ?>
<Wexflow>
  <Setting name="workflowsFolder" value="install_folder\Wexflow-netcore\Workflows" />
  <Setting name="recordsFolder" value="install_folder\Wexflow-netcore\Records" />
  <Setting name="recordsHotFolder" value="install_folder\Wexflow-netcore\Records\_HotFolder" />
  <Setting name="tempFolder" value="install_folder\Wexflow-netcore\Temp" />
  <Setting name="tasksFolder" value="install_folder\Wexflow-netcore\Tasks" />
  <Setting name="approvalFolder" value="install_folder\Wexflow-netcore\Approval" />
  <Setting name="xsd" value="install_folder\Wexflow-netcore\Workflow.xsd" />
  <Setting name="tasksNamesFile" value="install_folder\Wexflow-netcore\TasksNames.json" />
  <Setting name="tasksSettingsFile" value="install_folder\Wexflow-netcore\TasksSettings.json" />
  <Setting name="globalVariablesFile" value="install_folder\Wexflow-netcore\GlobalVariables.xml" />
  <!-- SQLite or MongoDB or SQLServer or PostgreSQL or MySQL or LiteDB -->
  <Setting name="dbType" value="SQLite" />
  <!-- SQLite -->
  <Setting name="connectionString" value="Data Source=install_folder\Wexflow-netcore\Database\Wexflow.sqlite;Version=3;" />
  <!-- MongoDB -->
  <!--<Setting name="connectionString" value="Database=wexflow_netcore;MongoUrl=mongodb://localhost:27017;EnabledSslProtocols=false;SslProtocols=None" />-->
  <!-- SQLServer -->
  <!--<Setting name="connectionString" value="Server=localhost;Trusted_Connection=True;Database=wexflow_netcore;" />-->
  <!-- PostgreSQL -->
  <!--<Setting name="connectionString" value="Server=127.0.0.1;User Id=postgres;Password=000000;Database=wexflow_netcore;Port=5432" />-->
  <!-- MySQL -->
  <!--<Setting name="connectionString" value="Server=localhost;Database=wexflow_netcore;Uid=root;Pwd=000000;Port=3306" />-->
  <!-- LiteDB -->
  <!--<Setting name="connectionString" value="Filename=install_folder\Wexflow-netcore\Database\Wexflow.db; Connection=direct" />-->
</Wexflow>
