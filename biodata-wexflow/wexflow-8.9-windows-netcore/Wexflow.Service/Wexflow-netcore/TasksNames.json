[{"Name": "Approval", "Description": "Marks the current workflow as needing approval."}, {"Name": "ApprovalRecordsCreator", "Description": "Creates records from files."}, {"Name": "ApprovalWorkflowsCreator", "Description": "Creates approval workflows for records from shared memory and starts them."}, {"Name": "ApproveRecord", "Description": "Assigns a record to a user and launches the approval process on that record."}, {"Name": "CsvToJson", "Description": "Converts CSV files to JSON files."}, {"Name": "CsvToSql", "Description": "Converts CSV files to SQL scripts."}, {"Name": "CsvToXml", "Description": "Converts CSV files to XML files."}, {"Name": "CsvToYaml", "Description": "Converts CSV files to YAML files."}, {"Name": "EnvironmentVariable", "Description": "A flowchart task that retrieves the value of an environment variable."}, {"Name": "ExecPython", "Description": "Executes Python scripts."}, {"Name": "FileContentMatch", "Description": "A flowchart task that checks whether the content of a file matches a regex pattern."}, {"Name": "FileExists", "Description": "A flowchart task that checks whether a file exists or not."}, {"Name": "FileMatch", "Description": "A flowchart task that checks whether a file exists or not in a directory by using a regex pattern."}, {"Name": "FileNotExist", "Description": "A flowchart task that checks whether a file does not exist."}, {"Name": "FileNotMatch", "Description": "A flowchart task that checks whether a file does not exist in a directory by using a regex pattern."}, {"Name": "FilesConcat", "Description": "Concatenates a collection of files."}, {"Name": "FilesCopier", "Description": "Copies a collection of files to a destination folder."}, {"Name": "FilesDecryptor", "Description": "Decrypts a collection of files encrypted by the task FilesEncryptor."}, {"Name": "FilesDiff", "Description": "Calculates the diff of two files."}, {"Name": "FilesEncryptor", "Description": "Encrypts a collection of files."}, {"Name": "FilesEqual", "Description": "Checks whether two files are the same."}, {"Name": "FilesExist", "Description": "Checks whether a collection of files and/or directories exists."}, {"Name": "FilesInfo", "Description": "Generates files information of a collection of files. "}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Description": "Concatenates a collection of files."}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Description": "Loads a collection of files located in folders or through the file option."}, {"Name": "FilesLoaderEx", "Description": "Loads a collection of files located in folders or through the file option."}, {"Name": "FilesMover", "Description": "Moves a collection of files to a destination folder."}, {"Name": "FilesRemover", "Description": "Deletes a collection of files."}, {"Name": "FilesRenamer", "Description": "Renames a collection of files on the file system."}, {"Name": "FilesSplitter", "Description": "Splits files into chunks."}, {"Name": "FileSystemWatcher", "Description": "Watches a hot folder and triggers tasks on file created, changed or deleted."}, {"Name": "FolderExists", "Description": "A flowchart task that checks whether a folder exists or not."}, {"Name": "Ftp", "Description": "Allows to list, upload, download or delete files over FTP, FTPS or SFTP."}, {"Name": "Guid", "Description": "Generates Guids."}, {"Name": "Http", "Description": "Downbloads a collection of files over HTTP or HTTPS. "}, {"Name": "HttpDelete", "Description": "Executes a DELETE request."}, {"Name": "HttpGet", "Description": "Executes a GET request."}, {"Name": "HttpPatch", "Description": "Executes a PATCH request."}, {"Name": "HttpPost", "Description": "Executes a POST request."}, {"Name": "HttpPut", "Description": "Executes a PUT request."}, {"Name": "ImagesConcat", "Description": "Concatenates a collection of image files."}, {"Name": "ImagesCropper", "Description": "Crops a collection of image files."}, {"Name": "ImagesOverlay", "Description": "Overlays a collection of image files."}, {"Name": "ImagesResizer", "Description": "Resizes a collection of image files."}, {"Name": "ImagesTransformer", "Description": "Transforms a collection of image files to a specified format."}, {"Name": "InstagramUploadImage", "Description": "Uploads images to Instagram."}, {"Name": "InstagramUploadVideo", "Description": "Uploads videos to Instagram."}, {"Name": "JsonToYaml", "Description": "Converts JSON files to YAML files."}, {"Name": "ListEntities", "Description": "Lists all the entities loaded by the workflow tasks in the logs."}, {"Name": "ListFiles", "Description": "Lists all the files loaded by the workflow tasks in the logs."}, {"Name": "MailsReceiver", "Description": "Fetches a collection of emails."}, {"Name": "MailsSender", "Description": "Sends emails."}, {"Name": "Md5", "Description": "Generates MD5 sums of a collection of files."}, {"Name": "MessageCorrect", "Description": "A flowchart task that checks whether a message is in the memory of the task having as key message."}, {"Name": "Mkdir", "Description": "Creates a collection of folders."}, {"Name": "Movedir", "Description": "Moves a folder."}, {"Name": "Now", "Description": "A flowchart task that retrieves the current date in the specified format."}, {"Name": "<PERSON>", "Description": "Checks whether a server responds to a ping request or not."}, {"Name": "ProcessInfo", "Description": "Shows information about a process."}, {"Name": "ProcessLauncher", "Description": "Launches a process."}, {"Name": "Reddit", "Description": "Sends posts and links to Reddit."}, {"Name": "RedditListComments", "Description": "Retrieves Reddit comment history."}, {"Name": "RedditListPosts", "Description": "Retrieves Reddit post history."}, {"Name": "Rmdir", "Description": "Deletes a collection of folders."}, {"Name": "ScssToCss", "Description": "Converts SCSS files to CSS files."}, {"Name": "Sha1", "Description": "Generates SHA-1 hashes of a collection of files."}, {"Name": "Sha256", "Description": "Generates SHA-256 hashes of a collection of files."}, {"Name": "Sha512", "Description": "Generates SHA-512 hashes of a collection of files."}, {"Name": "<PERSON><PERSON>ck", "Description": "Sends slack messages."}, {"Name": "Sql", "Description": "Executes a collection of SQL script files."}, {"Name": "SqlToCsv", "Description": "Executes a collection of SQL scripts and outputs the results in CSV files."}, {"Name": "SqlToXml", "Description": "Executes a collection of SQL scripts and outputs the results in XML files."}, {"Name": "SshCmd", "Description": "Executes an SSH command."}, {"Name": "SubWorkflow", "Description": "Kicks off a sub workflow."}, {"Name": "Tar", "Description": "Creates a .tar from a collection of files."}, {"Name": "TextsDecryptor", "Description": "Decrypts a collection of files encrypted by the task TextsEncryptor."}, {"Name": "TextsEncryptor", "Description": "Encrypts a collection of text based files."}, {"Name": "Tgz", "Description": "Creates a tar.gz from a collection of files."}, {"Name": "<PERSON><PERSON>", "Description": "Downloads torrent files."}, {"Name": "Touch", "Description": "Creates a collection of empty files."}, {"Name": "<PERSON><PERSON><PERSON>", "Description": "Sends an SMS."}, {"Name": "Twitter", "Description": "Sends tweets."}, {"Name": "UglifyCss", "Description": "Compresses and minifies CSS files."}, {"Name": "UglifyHtml", "Description": "Compresses and minifies HTML files."}, {"Name": "UglifyJs", "Description": "Uglifies JavaScript files."}, {"Name": "Untar", "Description": "Extracts tar archives."}, {"Name": "Untgz", "Description": "Extracts tar.gz archives."}, {"Name": "Unzip", "Description": "Extracts zip archives."}, {"Name": "Vimeo", "Description": "Uploads videos to Vimeo."}, {"Name": "VimeoListUploads", "Description": "Retrieves a list of videos uploaded to a Vimeo channel."}, {"Name": "Wait", "Description": "Waits for a specified duration of time."}, {"Name": "XmlToCsv", "Description": "Converts an XML file to a CSV file."}, {"Name": "Xslt", "Description": "Transforms a list of XML files."}, {"Name": "YouTube", "Description": "Uploads videos to YouTube."}, {"Name": "YouTubeListUploads", "Description": "Retrieves a list of videos uploaded to a YouTube channel."}, {"Name": "YouTubeSearch", "Description": "Searches for content on YouTube."}, {"Name": "YamlToJson", "Description": "Converts YAML files to JSON files."}, {"Name": "Zip", "Description": "Creates a .zip from a collection of files."}]