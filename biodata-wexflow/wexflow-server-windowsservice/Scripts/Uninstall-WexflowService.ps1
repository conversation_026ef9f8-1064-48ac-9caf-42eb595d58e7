#Requires -RunAsAdministrator

<#
.SYNOPSIS
    Désinstalle le service Windows Wexflow

.DESCRIPTION
    Ce script désinstalle complètement le service Windows Wexflow.
    Il doit être exécuté avec des privilèges administrateur.

.PARAMETER ServiceName
    Nom du service à désinstaller (par défaut: WexflowService)

.PARAMETER RemoveLogs
    Supprime également les fichiers de logs (par défaut: false)

.EXAMPLE
    .\Uninstall-WexflowService.ps1
    
.EXAMPLE
    .\Uninstall-WexflowService.ps1 -RemoveLogs
#>

param(
    [string]$ServiceName = "WexflowService",
    [switch]$RemoveLogs = $false
)

# Configuration
$ErrorActionPreference = "Stop"

function Write-ColoredOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Uninstall-WexflowWindowsService {
    try {
        Write-ColoredOutput "========================================" "Cyan"
        Write-ColoredOutput "  Désinstallation du Service Wexflow" "Cyan"
        Write-ColoredOutput "========================================" "Cyan"
        Write-Host ""

        # Vérification des privilèges administrateur
        if (-not (Test-Administrator)) {
            throw "Ce script doit être exécuté avec des privilèges administrateur"
        }

        Write-ColoredOutput "Configuration:" "Yellow"
        Write-ColoredOutput "- Nom du service: $ServiceName" "White"
        Write-ColoredOutput "- Supprimer les logs: $RemoveLogs" "White"
        Write-Host ""

        # Vérification si le service existe
        $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if (-not $service) {
            Write-ColoredOutput "Le service '$ServiceName' n'existe pas ou est déjà désinstallé." "Yellow"
            Write-Host ""
            return
        }

        Write-ColoredOutput "Service trouvé - Statut: $($service.Status)" "Green"

        # Arrêt du service s'il est en cours d'exécution
        if ($service.Status -eq "Running") {
            Write-ColoredOutput "Arrêt du service..." "Yellow"
            
            try {
                Stop-Service -Name $ServiceName -Force -NoWait
                
                # Attendre l'arrêt complet avec timeout
                $timeout = 30
                $elapsed = 0
                while ((Get-Service -Name $ServiceName).Status -ne "Stopped" -and $elapsed -lt $timeout) {
                    Write-Host "." -NoNewline
                    Start-Sleep -Seconds 1
                    $elapsed++
                }
                Write-Host ""
                
                $currentStatus = (Get-Service -Name $ServiceName).Status
                if ($currentStatus -eq "Stopped") {
                    Write-ColoredOutput "Service arrêté avec succès" "Green"
                } else {
                    Write-ColoredOutput "Attention: Le service n'a pas pu être arrêté proprement (Statut: $currentStatus)" "Yellow"
                    Write-ColoredOutput "Tentative d'arrêt forcé..." "Yellow"
                    
                    # Tentative d'arrêt forcé via taskkill
                    $processes = Get-Process | Where-Object { $_.ProcessName -like "*Wexflow*" }
                    foreach ($process in $processes) {
                        try {
                            Write-ColoredOutput "Arrêt forcé du processus: $($process.ProcessName) (PID: $($process.Id))" "Yellow"
                            Stop-Process -Id $process.Id -Force
                        }
                        catch {
                            Write-ColoredOutput "Impossible d'arrêter le processus $($process.ProcessName): $($_.Exception.Message)" "Yellow"
                        }
                    }
                    
                    Start-Sleep -Seconds 2
                }
            }
            catch {
                Write-ColoredOutput "Erreur lors de l'arrêt du service: $($_.Exception.Message)" "Yellow"
                Write-ColoredOutput "Continuation de la désinstallation..." "Yellow"
            }
        }

        # Suppression du service
        Write-ColoredOutput "Suppression du service..." "Yellow"
        
        $deleteResult = & sc.exe delete $ServiceName 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColoredOutput "Service supprimé avec succès" "Green"
        } else {
            # Vérifier si l'erreur est due au fait que le service n'existe plus
            if ($deleteResult -like "*Le service spécifié n'existe pas*" -or 
                $deleteResult -like "*The specified service does not exist*") {
                Write-ColoredOutput "Le service était déjà supprimé" "Green"
            } else {
                Write-ColoredOutput "Attention lors de la suppression: $deleteResult" "Yellow"
            }
        }

        # Attendre que la suppression soit effective
        Write-ColoredOutput "Vérification de la suppression..." "Yellow"
        Start-Sleep -Seconds 3

        # Vérification finale
        $serviceCheck = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if ($serviceCheck) {
            Write-ColoredOutput "Attention: Le service semble toujours exister" "Yellow"
        } else {
            Write-ColoredOutput "Service complètement supprimé" "Green"
        }

        # Suppression des logs si demandé
        if ($RemoveLogs) {
            Write-ColoredOutput "Suppression des fichiers de logs..." "Yellow"
            
            $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
            $parentDir = Split-Path -Parent $scriptDir
            $logsDir = Join-Path $parentDir "Logs"
            
            if (Test-Path $logsDir) {
                try {
                    Remove-Item -Path $logsDir -Recurse -Force
                    Write-ColoredOutput "Dossier de logs supprimé: $logsDir" "Green"
                }
                catch {
                    Write-ColoredOutput "Impossible de supprimer le dossier de logs: $($_.Exception.Message)" "Yellow"
                }
            } else {
                Write-ColoredOutput "Dossier de logs non trouvé: $logsDir" "Gray"
            }

            # Nettoyage des logs du journal d'événements Windows
            try {
                Write-ColoredOutput "Nettoyage des entrées du journal d'événements..." "Yellow"
                $events = Get-EventLog -LogName Application -Source "Wexflow Service" -ErrorAction SilentlyContinue
                if ($events) {
                    Write-ColoredOutput "Trouvé $($events.Count) entrée(s) dans le journal d'événements" "Gray"
                    Write-ColoredOutput "Note: Les entrées du journal d'événements ne peuvent pas être supprimées individuellement" "Gray"
                }
            }
            catch {
                Write-ColoredOutput "Aucune entrée trouvée dans le journal d'événements" "Gray"
            }
        }

        Write-Host ""
        Write-ColoredOutput "========================================" "Cyan"
        Write-ColoredOutput "  Désinstallation terminée !" "Cyan"
        Write-ColoredOutput "========================================" "Cyan"
        Write-Host ""
        
        Write-ColoredOutput "Résumé:" "White"
        Write-ColoredOutput "- Service '$ServiceName' désinstallé" "Green"
        if ($RemoveLogs) {
            Write-ColoredOutput "- Fichiers de logs supprimés" "Green"
        } else {
            Write-ColoredOutput "- Fichiers de logs conservés (utilisez -RemoveLogs pour les supprimer)" "Gray"
        }
        Write-Host ""
        
        Write-ColoredOutput "Pour une désinstallation complète, vous pouvez également:" "White"
        Write-ColoredOutput "- Supprimer manuellement le dossier d'installation" "Gray"
        Write-ColoredOutput "- Nettoyer le registre Windows si nécessaire" "Gray"
        Write-Host ""

    }
    catch {
        Write-ColoredOutput "ERREUR: $($_.Exception.Message)" "Red"
        Write-Host ""
        Write-ColoredOutput "La désinstallation a échoué. Vérifiez les points suivants:" "Yellow"
        Write-ColoredOutput "1. Vous exécutez ce script en tant qu'administrateur" "Yellow"
        Write-ColoredOutput "2. Aucun processus n'utilise encore le service" "Yellow"
        Write-ColoredOutput "3. Le service n'est pas en cours d'utilisation par d'autres applications" "Yellow"
        exit 1
    }
}

# Confirmation avant désinstallation
Write-ColoredOutput "Êtes-vous sûr de vouloir désinstaller le service Wexflow '$ServiceName' ?" "Yellow"
if ($RemoveLogs) {
    Write-ColoredOutput "ATTENTION: Les fichiers de logs seront également supprimés !" "Red"
}
$confirmation = Read-Host "Tapez 'OUI' pour confirmer"

if ($confirmation -eq "OUI") {
    Uninstall-WexflowWindowsService
} else {
    Write-ColoredOutput "Désinstallation annulée par l'utilisateur" "Yellow"
}
