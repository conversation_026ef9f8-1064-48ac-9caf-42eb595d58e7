using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading;
using System.Threading.Tasks;
using log4net;
using log4net.Config;
using System.IO;
using System.Reflection;

namespace Wexflow.Server.WindowsService
{
    /// <summary>
    /// Service Windows principal qui encapsule le moteur Wexflow
    /// </summary>
    public class WexflowWindowsService : BackgroundService
    {
        private readonly ILogger<WexflowWindowsService> _logger;
        private readonly WexflowConfiguration _config;
        private readonly IWexflowEngineWrapper _wexflowEngine;
        private static readonly ILog _log4netLogger = LogManager.GetLogger(typeof(WexflowWindowsService));

        public WexflowWindowsService(
            ILogger<WexflowWindowsService> logger,
            IOptions<WexflowConfiguration> config,
            IWexflowEngineWrapper wexflowEngine)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _config = config?.Value ?? throw new ArgumentNullException(nameof(config));
            _wexflowEngine = wexflowEngine ?? throw new ArgumentNullException(nameof(wexflowEngine));
        }

        /// <summary>
        /// Démarrage du service
        /// </summary>
        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Démarrage du service Wexflow...");

                // Configuration de log4net
                ConfigureLog4Net();

                _log4netLogger.Info("=== DÉMARRAGE DU SERVICE WEXFLOW ===");
                _log4netLogger.Info($"Version: {Assembly.GetExecutingAssembly().GetName().Version}");
                _log4netLogger.Info($"Répertoire de base: {AppContext.BaseDirectory}");

                // Initialisation du moteur Wexflow
                await _wexflowEngine.InitializeAsync(_config, cancellationToken);

                _logger.LogInformation("Service Wexflow démarré avec succès");
                _log4netLogger.Info("Service Wexflow démarré avec succès");

                await base.StartAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors du démarrage du service Wexflow");
                _log4netLogger.Error("Erreur lors du démarrage du service Wexflow", ex);
                throw;
            }
        }

        /// <summary>
        /// Exécution principale du service
        /// </summary>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                _logger.LogInformation("Exécution du service Wexflow en cours...");
                _log4netLogger.Info("Exécution du service Wexflow en cours...");

                // Démarrage du moteur Wexflow
                await _wexflowEngine.StartAsync(stoppingToken);

                // Boucle principale du service
                while (!stoppingToken.IsCancellationRequested)
                {
                    try
                    {
                        // Vérification périodique de l'état du moteur
                        await _wexflowEngine.HealthCheckAsync();

                        // Attente avant la prochaine vérification (30 secondes)
                        await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                    }
                    catch (OperationCanceledException)
                    {
                        // Arrêt demandé
                        break;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Erreur lors de l'exécution du service");
                        _log4netLogger.Error("Erreur lors de l'exécution du service", ex);

                        // Attente avant de continuer en cas d'erreur
                        await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Arrêt du service demandé");
                _log4netLogger.Info("Arrêt du service demandé");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur critique dans l'exécution du service");
                _log4netLogger.Error("Erreur critique dans l'exécution du service", ex);
                throw;
            }
        }

        /// <summary>
        /// Arrêt du service
        /// </summary>
        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Arrêt du service Wexflow...");
                _log4netLogger.Info("=== ARRÊT DU SERVICE WEXFLOW ===");

                // Arrêt du moteur Wexflow
                await _wexflowEngine.StopAsync(cancellationToken);

                _logger.LogInformation("Service Wexflow arrêté avec succès");
                _log4netLogger.Info("Service Wexflow arrêté avec succès");

                await base.StopAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'arrêt du service Wexflow");
                _log4netLogger.Error("Erreur lors de l'arrêt du service Wexflow", ex);
                throw;
            }
        }

        /// <summary>
        /// Configuration de log4net
        /// </summary>
        private void ConfigureLog4Net()
        {
            try
            {
                var log4netConfigFile = Path.Combine(AppContext.BaseDirectory, "log4net.config");
                if (File.Exists(log4netConfigFile))
                {
                    XmlConfigurator.Configure(new FileInfo(log4netConfigFile));
                    _logger.LogInformation($"Configuration log4net chargée depuis: {log4netConfigFile}");
                }
                else
                {
                    // Configuration par défaut si le fichier n'existe pas
                    BasicConfigurator.Configure();
                    _logger.LogWarning($"Fichier log4net.config non trouvé, configuration par défaut utilisée");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la configuration de log4net");
            }
        }
    }
}
