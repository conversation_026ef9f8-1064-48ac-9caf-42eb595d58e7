using System.Threading;
using System.Threading.Tasks;

namespace Wexflow.Server.WindowsService
{
    /// <summary>
    /// Interface pour l'encapsulation du moteur Wexflow
    /// </summary>
    public interface IWexflowEngineWrapper
    {
        /// <summary>
        /// Initialise le moteur Wexflow
        /// </summary>
        /// <param name="config">Configuration Wexflow</param>
        /// <param name="cancellationToken">Token d'annulation</param>
        Task InitializeAsync(WexflowConfiguration config, CancellationToken cancellationToken = default);

        /// <summary>
        /// Démarre le moteur Wexflow
        /// </summary>
        /// <param name="cancellationToken">Token d'annulation</param>
        Task StartAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Arrête le moteur Wexflow
        /// </summary>
        /// <param name="cancellationToken">Token d'annulation</param>
        Task StopAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Vérifie l'état de santé du moteur
        /// </summary>
        Task HealthCheckAsync();

        /// <summary>
        /// Indique si le moteur est en cours d'exécution
        /// </summary>
        bool IsRunning { get; }

        /// <summary>
        /// Indique si le moteur est initialisé
        /// </summary>
        bool IsInitialized { get; }
    }
}
