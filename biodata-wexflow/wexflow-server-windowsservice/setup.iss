; Script Inno Setup pour BioData OCR Solution
; Créé automatiquement pour déployer l'API BioData et Wexflow

[Setup]
AppName=BioData OCR Solution
AppVersion=1.0.0
AppPublisher=BioData Solutions
AppPublisherURL=https://biodata.hemadialyse.com
AppSupportURL=https://biodata.hemadialyse.com
AppUpdatesURL=https://biodata.hemadialyse.com
DefaultDirName={autopf}\BioData-Wexflow
DefaultGroupName=BioData-Wexflow Suite
AllowNoIcons=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir=Output
OutputBaseFilename=BioData-Install-Setup
SetupIconFile=
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "french"; MessagesFile: "compiler:Languages\French.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Types]
Name: "full"; Description: "Installation complète (API + Wexflow Service)"
Name: "apionly"; Description: "API BioData seulement"
Name: "wexflowservice"; Description: "Wexflow Service Windows seulement"
Name: "wexflowlegacy"; Description: "Wexflow traditionnel (mode console)"
Name: "custom"; Description: "Installation personnalisée"; Flags: iscustom

[Components]
Name: "api"; Description: "API BioData"; Types: full apionly; Flags: fixed
Name: "wexflowservice"; Description: "Wexflow Service Windows"; Types: full wexflowservice
Name: "wexflowlegacy"; Description: "Wexflow Engine (mode console)"; Types: wexflowlegacy
Name: "scripts"; Description: "Scripts de lancement"; Types: full apionly wexflowlegacy

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1
Name: "startmenu"; Description: "Créer des raccourcis dans le menu démarrer"; GroupDescription: "{cm:AdditionalIcons}"; Flags: checkedonce
Name: "configpaths"; Description: "Configurer automatiquement les chemins Wexflow"; GroupDescription: "Configuration Wexflow"; Flags: checkedonce; Components: wexflowlegacy wexflowservice
Name: "installservice"; Description: "Installer et démarrer le service Windows automatiquement"; GroupDescription: "Service Windows"; Flags: checkedonce; Components: wexflowservice
Name: "startservice"; Description: "Démarrer le service après installation"; GroupDescription: "Service Windows"; Flags: checkedonce; Components: wexflowservice

[Files]
; API BioData Files
Source: "api\*"; DestDir: "{app}\API"; Components: api; Flags: ignoreversion recursesubdirs createallsubdirs
; Wexflow Service Windows Files
Source: "wexflow-service\*"; DestDir: "{app}\WexflowService"; Components: wexflowservice; Flags: ignoreversion recursesubdirs createallsubdirs
; Wexflow Legacy Files (mode console)
Source: "wexflow\*"; DestDir: "{app}\Wexflow"; Components: wexflowlegacy; Flags: ignoreversion recursesubdirs createallsubdirs
; Scripts de lancement
Source: "scripts\*"; DestDir: "{app}\Scripts"; Components: scripts; Flags: ignoreversion

[Dirs]
; Dossiers Wexflow Service avec permissions complètes
Name: "{app}\WexflowService\Wexflow-netcore\Database"; Components: wexflowservice; Permissions: everyone-full
Name: "{app}\WexflowService\Wexflow-netcore\Records"; Components: wexflowservice; Permissions: everyone-full
Name: "{app}\WexflowService\Wexflow-netcore\Records\_HotFolder"; Components: wexflowservice; Permissions: everyone-full
Name: "{app}\WexflowService\Wexflow-netcore\Temp"; Components: wexflowservice; Permissions: everyone-full
Name: "{app}\WexflowService\Wexflow-netcore\Workflows"; Components: wexflowservice; Permissions: everyone-full
Name: "{app}\WexflowService\Wexflow-netcore\Tasks"; Components: wexflowservice; Permissions: everyone-full
Name: "{app}\WexflowService\Wexflow-netcore\Approval"; Components: wexflowservice; Permissions: everyone-full
Name: "{app}\WexflowService\Logs"; Components: wexflowservice; Permissions: everyone-full
; Dossiers Wexflow Legacy avec permissions complètes
Name: "{app}\Wexflow\Wexflow-netcore\Database"; Components: wexflowlegacy; Permissions: everyone-full
Name: "{app}\Wexflow\Wexflow-netcore\Records"; Components: wexflowlegacy; Permissions: everyone-full
Name: "{app}\Wexflow\Wexflow-netcore\Records\_HotFolder"; Components: wexflowlegacy; Permissions: everyone-full
Name: "{app}\Wexflow\Wexflow-netcore\Temp"; Components: wexflowlegacy; Permissions: everyone-full
Name: "{app}\Wexflow\Wexflow-netcore\Workflows"; Components: wexflowlegacy; Permissions: everyone-full
Name: "{app}\Wexflow\Wexflow-netcore\Tasks"; Components: wexflowlegacy; Permissions: everyone-full
Name: "{app}\Wexflow\Wexflow-netcore\Approval"; Components: wexflowlegacy; Permissions: everyone-full

[Icons]
Name: "{group}\Démarrer API BioData"; Filename: "{app}\Scripts\start-api.bat"; Components: api; Tasks: startmenu
Name: "{group}\Démarrer Wexflow (Legacy)"; Filename: "{app}\Scripts\start-wexflow.bat"; Components: wexflowlegacy; Tasks: startmenu
Name: "{group}\Gérer Service Wexflow"; Filename: "powershell.exe"; Parameters: "-ExecutionPolicy Bypass -File ""{app}\WexflowService\Scripts\Manage-WexflowService.ps1"" -Action Status"; Components: wexflowservice; Tasks: startmenu
Name: "{group}\Démarrer Service Wexflow"; Filename: "powershell.exe"; Parameters: "-ExecutionPolicy Bypass -File ""{app}\WexflowService\Scripts\Manage-WexflowService.ps1"" -Action Start"; Components: wexflowservice; Tasks: startmenu
Name: "{group}\Arrêter Service Wexflow"; Filename: "powershell.exe"; Parameters: "-ExecutionPolicy Bypass -File ""{app}\WexflowService\Scripts\Manage-WexflowService.ps1"" -Action Stop"; Components: wexflowservice; Tasks: startmenu
Name: "{group}\Logs Service Wexflow"; Filename: "powershell.exe"; Parameters: "-ExecutionPolicy Bypass -File ""{app}\WexflowService\Scripts\Manage-WexflowService.ps1"" -Action Logs"; Components: wexflowservice; Tasks: startmenu
Name: "{group}\Démarrer Tout"; Filename: "{app}\Scripts\start-all.bat"; Components: api wexflowlegacy; Tasks: startmenu
Name: "{group}\Arrêter Tout"; Filename: "{app}\Scripts\stop-all.bat"; Components: api wexflowlegacy; Tasks: startmenu
Name: "{group}\{cm:UninstallProgram,BioData-Wexflow Suite}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\BioData-Wexflow Suite"; Filename: "{app}\Scripts\start-all.bat"; Tasks: desktopicon; Components: api wexflowlegacy
Name: "{autodesktop}\API BioData"; Filename: "{app}\Scripts\start-api.bat"; Tasks: desktopicon; Components: api; Check: not IsComponentSelected('wexflowlegacy') and not IsComponentSelected('wexflowservice')
Name: "{autodesktop}\Wexflow Legacy"; Filename: "{app}\Scripts\start-wexflow.bat"; Tasks: desktopicon; Components: wexflowlegacy; Check: not IsComponentSelected('api')
Name: "{autodesktop}\Gérer Service Wexflow"; Filename: "powershell.exe"; Parameters: "-ExecutionPolicy Bypass -File ""{app}\WexflowService\Scripts\Manage-WexflowService.ps1"" -Action Status"; Tasks: desktopicon; Components: wexflowservice; Check: not IsComponentSelected('api')

[Run]
; Installation du service Windows Wexflow
Filename: "powershell.exe"; Parameters: "-ExecutionPolicy Bypass -File ""{app}\WexflowService\Scripts\Install-WexflowService.ps1"""; WorkingDir: "{app}\WexflowService"; Flags: runhidden waituntilterminated; Components: wexflowservice; Tasks: installservice
; Démarrage du service après installation
Filename: "powershell.exe"; Parameters: "-ExecutionPolicy Bypass -File ""{app}\WexflowService\Scripts\Manage-WexflowService.ps1"" -Action Start"; WorkingDir: "{app}\WexflowService"; Flags: runhidden waituntilterminated; Components: wexflowservice; Tasks: startservice
; Lancement traditionnel
Filename: "{app}\Scripts\start-all.bat"; Description: "{cm:LaunchProgram,BioData-Wexflow Suite}"; Flags: nowait postinstall skipifsilent; Components: api wexflowlegacy
Filename: "{app}\Scripts\start-api.bat"; Description: "{cm:LaunchProgram,API BioData}"; Flags: nowait postinstall skipifsilent; Components: api; Check: not IsComponentSelected('wexflowlegacy') and not IsComponentSelected('wexflowservice')
Filename: "{app}\Scripts\start-wexflow.bat"; Description: "{cm:LaunchProgram,Wexflow}"; Flags: nowait postinstall skipifsilent; Components: wexflowlegacy; Check: not IsComponentSelected('api')
; Ouverture du gestionnaire de service pour vérification
Filename: "powershell.exe"; Parameters: "-ExecutionPolicy Bypass -File ""{app}\WexflowService\Scripts\Manage-WexflowService.ps1"" -Action Status"; Description: "Vérifier le statut du service Wexflow"; Flags: nowait postinstall skipifsilent; Components: wexflowservice

[Registry]
; Variable d'environnement pour le dossier d'installation Wexflow
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; ValueType: string; ValueName: "WEXFLOW_INSTALL_DIR"; ValueData: "{app}\Wexflow"; Flags: preservestringtype; Components: wexflow

[UninstallRun]
; Désinstallation du service Windows Wexflow
Filename: "powershell.exe"; Parameters: "-ExecutionPolicy Bypass -File ""{app}\WexflowService\Scripts\Uninstall-WexflowService.ps1"""; WorkingDir: "{app}\WexflowService"; Flags: runhidden waituntilterminated; Components: wexflowservice
; Arrêt des processus traditionnels
Filename: "{app}\Scripts\stop-all.bat"; Flags: runhidden; Components: wexflowlegacy

[UninstallDelete]
; Sous-dossiers spécifiques
Type: filesandordirs; Name: "{app}\Wexflow\Wexflow-netcore\Database"
Type: filesandordirs; Name: "{app}\Wexflow\Wexflow-netcore\Records"
Type: filesandordirs; Name: "{app}\Wexflow\Wexflow-netcore\Temp"
Type: filesandordirs; Name: "{app}\Wexflow\Wexflow-netcore\Workflows"
Type: filesandordirs; Name: "{app}\Wexflow\Wexflow-netcore\Tasks"
Type: filesandordirs; Name: "{app}\Wexflow\Wexflow-netcore\Approval"
Type: filesandordirs; Name: "{app}\Wexflow\Logs"
Type: filesandordirs; Name: "{app}\API\logs"
Type: filesandordirs; Name: "{app}\API\test"

; Dossiers principaux
Type: filesandordirs; Name: "{app}\API"
Type: filesandordirs; Name: "{app}\Wexflow"
Type: filesandordirs; Name: "{app}\Wexflow\Wexflow-netcore"
Type: filesandordirs; Name: "{app}\Wexflow\WexflowTesting"

[Messages]
WelcomeLabel2=Cet assistant va installer [name/ver] sur votre ordinateur.%n%nCette suite comprend l'API BioData et le moteur de workflow Wexflow pour la gestion des données biomédicales.

[Code]
var
  ResultCode: Integer;

function IsDotNetInstalled(): Boolean;
begin
  Result := Exec('dotnet', '--version', '', SW_HIDE, ewWaitUntilTerminated, ResultCode) and (ResultCode = 0);
end;

function LoadTextFromFile(const FileName: String): String;
var
  Lines: TArrayOfString;
  I: Integer;
begin
  Result := '';
  if LoadStringsFromFile(FileName, Lines) then
  begin
    for I := 0 to GetArrayLength(Lines) - 1 do
    begin
      Result := Result + Lines[I];
      if I < GetArrayLength(Lines) - 1 then
        Result := Result + #13#10;
    end;
  end;
end;

function SaveTextToFile(const FileName, Content: String): Boolean;
var
  Lines: TArrayOfString;
  I, Start, LineCount: Integer;
  Line: String;
begin
  Result := False;
  SetArrayLength(Lines, 0);
  LineCount := 0;
  
  Start := 1;
  for I := 1 to Length(Content) do
  begin
    if (Content[I] = #13) and (I < Length(Content)) and (Content[I + 1] = #10) then
    begin
      Line := Copy(Content, Start, I - Start);
      Inc(LineCount);
      SetArrayLength(Lines, LineCount);
      Lines[LineCount - 1] := Line;
      Start := I + 2;
      I := I + 1; // Skip the #10
    end
    else if I = Length(Content) then
    begin
      Line := Copy(Content, Start, I - Start + 1);
      Inc(LineCount);
      SetArrayLength(Lines, LineCount);
      Lines[LineCount - 1] := Line;
    end;
  end;
  
  Result := SaveStringsToFile(FileName, Lines, False);
end;

function StringReplace(const S, OldPattern, NewPattern: String): String;
var
  SearchStr, TempStr: String;
  I, StartPos: Integer;
begin
  Result := '';
  TempStr := S;
  SearchStr := OldPattern;
  
  StartPos := Pos(SearchStr, TempStr);
  while StartPos > 0 do
  begin
    Result := Result + Copy(TempStr, 1, StartPos - 1) + NewPattern;
    TempStr := Copy(TempStr, StartPos + Length(SearchStr), Length(TempStr));
    StartPos := Pos(SearchStr, TempStr);
  end;
  
  Result := Result + TempStr;
end;

function EscapeJsonPath(const Path: String): String;
begin
  Result := StringReplace(Path, '\', '\\');
end;

procedure UpdateWexflowPaths(const InstallDir: String);
var
  WexflowXmlPath: String;
  Content: String;
  Lines: TArrayOfString;
  I: Integer;
begin
  WexflowXmlPath := ExpandConstant('{app}\Wexflow\Wexflow-netcore\Wexflow.xml');
  
  // Charger le contenu du fichier
  Content := LoadTextFromFile(WexflowXmlPath);
  if Content <> '' then
  begin
    Log('Mise à jour des chemins dans Wexflow.xml...');
    
    // Remplacer tous les "install_folder" par le chemin d'installation réel
    Content := StringReplace(Content, 'install_folder', ExpandConstant('{app}'));
    
    // Sauvegarder le fichier modifié
    if SaveTextToFile(WexflowXmlPath, Content) then
      Log('Wexflow.xml sauvegardé avec succès')
    else
      Log('Erreur lors de la sauvegarde de Wexflow.xml');
  end
  else
    Log('Erreur: Impossible de charger Wexflow.xml');
end;

procedure UpdateWexflowConfiguration();
var
  AppSettingsPath: String;
  AppSettingsContent: String;
  InstallDir: String;
  EscapedPath: String;
begin
  if (not IsComponentSelected('wexflowlegacy') and not IsComponentSelected('wexflowservice')) or not IsTaskSelected('configpaths') then
    Exit;

  InstallDir := ExpandConstant('{app}');
  Log('Configuration des chemins Wexflow pour : ' + InstallDir);

  // Mise à jour de Wexflow.xml pour legacy
  if IsComponentSelected('wexflowlegacy') then
    UpdateWexflowPaths(InstallDir);

  // Mise à jour d'appsettings.json pour legacy
  if IsComponentSelected('wexflowlegacy') then
  begin
    AppSettingsPath := ExpandConstant('{app}\Wexflow\Wexflow.Server\appsettings.json');
    AppSettingsContent := LoadTextFromFile(AppSettingsPath);
    if AppSettingsContent <> '' then
    begin
      Log('Mise à jour appsettings.json legacy...');

      // Préparer le chemin pour JSON (doubler les backslashes)
      EscapedPath := EscapeJsonPath(InstallDir);

      // Remplacer le placeholder "install_folder" par le chemin d'installation réel
      AppSettingsContent := StringReplace(AppSettingsContent, 'install_folder', EscapedPath);

      // Sauvegarder le fichier modifié
      if SaveTextToFile(AppSettingsPath, AppSettingsContent) then
        Log('appsettings.json legacy configuré avec les chemins absolus')
      else
        Log('Erreur: Impossible de sauvegarder appsettings.json legacy');
    end
    else
      Log('Erreur: Impossible de charger appsettings.json legacy');
  end;

  Log('Configuration Wexflow terminée');
end;

procedure UpdateWexflowServiceConfiguration();
var
  AppSettingsPath: String;
  AppSettingsContent: String;
  InstallDir: String;
  EscapedPath: String;
begin
  if not IsComponentSelected('wexflowservice') or not IsTaskSelected('configpaths') then
    Exit;

  InstallDir := ExpandConstant('{app}');
  Log('Configuration des chemins Wexflow Service pour : ' + InstallDir);

  // Mise à jour d'appsettings.json pour le service
  AppSettingsPath := ExpandConstant('{app}\WexflowService\appsettings.json');
  AppSettingsContent := LoadTextFromFile(AppSettingsPath);
  if AppSettingsContent <> '' then
  begin
    Log('Mise à jour appsettings.json service...');

    // Préparer le chemin pour JSON (doubler les backslashes)
    EscapedPath := EscapeJsonPath(InstallDir);

    // Remplacer le placeholder "install_folder" par le chemin d'installation réel
    AppSettingsContent := StringReplace(AppSettingsContent, 'install_folder', EscapedPath);

    // Sauvegarder le fichier modifié
    if SaveTextToFile(AppSettingsPath, AppSettingsContent) then
      Log('appsettings.json service configuré avec les chemins absolus')
    else
      Log('Erreur: Impossible de sauvegarder appsettings.json service');
  end
  else
    Log('Erreur: Impossible de charger appsettings.json service');

  // Mise à jour de Wexflow.xml pour le service
  AppSettingsPath := ExpandConstant('{app}\WexflowService\Wexflow-netcore\Wexflow.xml');
  AppSettingsContent := LoadTextFromFile(AppSettingsPath);
  if AppSettingsContent <> '' then
  begin
    Log('Mise à jour Wexflow.xml service...');

    // Remplacer tous les "install_folder" par le chemin d'installation réel
    AppSettingsContent := StringReplace(AppSettingsContent, 'install_folder', InstallDir);

    // Sauvegarder le fichier modifié
    if SaveTextToFile(AppSettingsPath, AppSettingsContent) then
      Log('Wexflow.xml service configuré avec les chemins absolus')
    else
      Log('Erreur: Impossible de sauvegarder Wexflow.xml service');
  end
  else
    Log('Erreur: Impossible de charger Wexflow.xml service');

  Log('Configuration Wexflow Service terminée');
end;

function InitializeSetup(): Boolean;
begin
  Result := True;
  if not IsDotNetInstalled() then
  begin
    if MsgBox('Microsoft .NET Runtime n''est pas installé sur ce système.' + #13#10 + 
              'Il est requis pour faire fonctionner cette application.' + #13#10 + #13#10 + 
              'Voulez-vous continuer l''installation ? Vous devrez installer .NET manuellement après.', 
              mbConfirmation, MB_YESNO) = IDNO then
      Result := False;
  end;
end;

procedure UpdateInstallBat(const InstallDir: String);
var
  InstallBatPath: String;
  Content: String;
begin
  InstallBatPath := ExpandConstant('{app}\Wexflow\install.bat');
  
  // Charger le contenu du fichier
  Content := LoadTextFromFile(InstallBatPath);
  if Content <> '' then
  begin
    Log('Mise à jour des chemins dans install.bat...');
    
    // Remplacer tous les "install_folder" par le chemin d'installation réel
    Content := StringReplace(Content, 'install_folder', ExpandConstant('{app}'));
    
    // Sauvegarder le fichier modifié
    if SaveTextToFile(InstallBatPath, Content) then
      Log('install.bat sauvegardé avec succès')
    else
      Log('Erreur lors de la sauvegarde de install.bat');
  end
  else
    Log('Erreur: Impossible de charger install.bat');
end;

function PrepareToInstall(var NeedsRestart: Boolean): String;
begin
  Result := '';
  
  // Configurer les fichiers avant l'exécution des commandes [Run]
  if IsComponentSelected('wexflow') then
  begin
    // S'assurer que la tâche de configuration des chemins est sélectionnée
    if not IsTaskSelected('configpaths') then
    begin
      Log('Activation forcée de la tâche de configuration des chemins Wexflow');
      // On ne peut pas activer la tâche programmatiquement, mais on peut avertir l'utilisateur
      Result := 'Pour une installation correcte, la tâche "Configurer automatiquement les chemins Wexflow" ' +
                'doit être sélectionnée. Veuillez revenir en arrière et la sélectionner.';
      Exit;
    end;
    
    // Configurer automatiquement les chemins Wexflow
    UpdateWexflowConfiguration();
    
    // Mettre à jour le fichier install.bat
    UpdateInstallBat('');  // Le chemin est maintenant défini directement dans la fonction
  end;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  // Actions post-installation
  if CurStep = ssPostInstall then
  begin
    // Initialiser la variable
    ResultCode := 0;
    // Créer les répertoires de logs si nécessaire
    if IsComponentSelected('api') then
    begin
      CreateDir(ExpandConstant('{app}\API\logs'));
      CreateDir(ExpandConstant('{app}\API\test'));
    end;
    
    if IsComponentSelected('wexflow') then
    begin
      CreateDir(ExpandConstant('{app}\Wexflow\Logs'));
      CreateDir(ExpandConstant('{app}\Wexflow\Temp'));
      
      // Vérifier une dernière fois si les remplacements ont été effectués
      if IsTaskSelected('configpaths') then
      begin
        // Vérifier Wexflow.xml
        if Pos('install_folder', LoadTextFromFile(ExpandConstant('{app}\Wexflow\Wexflow-netcore\Wexflow.xml'))) > 0 then
        begin
          // Réessayer le remplacement
          UpdateWexflowPaths(ExpandConstant('{app}'));
        end;
        
        // Vérifier appsettings.json
        if Pos('install_folder', LoadTextFromFile(ExpandConstant('{app}\Wexflow\Wexflow.Server\appsettings.json'))) > 0 then
        begin
          // Réessayer le remplacement
          UpdateWexflowConfiguration();
        end;
        
        // Vérifier install.bat
        if Pos('install_folder', LoadTextFromFile(ExpandConstant('{app}\Wexflow\install.bat'))) > 0 then
        begin
          // Réessayer le remplacement
          UpdateInstallBat('');
        end;
      end;
    end;
  end;
  
  // Exécuter install.bat juste avant la finalisation de l'installation
  if CurStep = ssDone then
  begin
    if IsComponentSelected('wexflow') then
    begin
      Log('Exécution de install.bat juste avant la finalisation...');
      
      // Vérifier une dernière fois si les remplacements ont été effectués
      if IsTaskSelected('configpaths') then
      begin
        // Vérifier et mettre à jour les fichiers si nécessaire
        if Pos('install_folder', LoadTextFromFile(ExpandConstant('{app}\Wexflow\Wexflow-netcore\Wexflow.xml'))) > 0 then
          UpdateWexflowPaths(ExpandConstant('{app}'));
          
        if Pos('install_folder', LoadTextFromFile(ExpandConstant('{app}\Wexflow\Wexflow.Server\appsettings.json'))) > 0 then
          UpdateWexflowConfiguration();
          
        if Pos('install_folder', LoadTextFromFile(ExpandConstant('{app}\Wexflow\install.bat'))) > 0 then
          UpdateInstallBat('');
      end;
      
      // Exécuter install.bat
      Log('Exécution de install.bat...');
      if Exec(ExpandConstant('{app}\Wexflow\install.bat'), '', ExpandConstant('{app}\Wexflow'), SW_SHOW, ewWaitUntilTerminated, ResultCode) then
        Log('install.bat exécuté avec succès. Code de retour: ' + IntToStr(ResultCode))
      else
        Log('Erreur lors de l''exécution de install.bat');
    end;
  end;
end;
