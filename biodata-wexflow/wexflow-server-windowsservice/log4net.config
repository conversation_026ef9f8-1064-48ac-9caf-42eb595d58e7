<?xml version="1.0" encoding="utf-8" ?>
<log4net>
  <!-- Appender pour fichier de log avec rotation -->
  <appender name="RollingFile" type="log4net.Appender.RollingFileAppender">
    <file value="Logs\WexflowService.log"/>
    <encoding value="utf-8"/>
    <appendToFile value="true"/>
    <rollingStyle value="Composite"/>
    <datePattern value="yyyyMMdd"/>
    <maxSizeRollBackups value="10"/>
    <maximumFileSize value="10MB"/>
    <staticLogFileName value="true"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date{yyyy-MM-dd HH:mm:ss,fff} [%thread] %5level %logger{1} - %message%newline"/>
    </layout>
  </appender>

  <!-- Appender pour le journal d'événements Windows -->
  <appender name="EventLogAppender" type="log4net.Appender.EventLogAppender">
    <applicationName value="Wexflow Service"/>
    <logName value="Application"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date{yyyy-MM-dd HH:mm:ss} [%thread] %5level %logger - %message"/>
    </layout>
    <filter type="log4net.Filter.LevelRangeFilter">
      <levelMin value="WARN"/>
      <levelMax value="FATAL"/>
    </filter>
  </appender>

  <!-- Appender pour la console (mode debug uniquement) -->
  <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date{HH:mm:ss,fff} [%thread] %5level %logger{1} - %message%newline"/>
    </layout>
  </appender>

  <!-- Logger pour les erreurs critiques uniquement dans le journal d'événements -->
  <logger name="Wexflow.Server.WindowsService.Critical" additivity="false">
    <level value="ERROR"/>
    <appender-ref ref="EventLogAppender"/>
    <appender-ref ref="RollingFile"/>
  </logger>

  <!-- Logger principal pour le service -->
  <logger name="Wexflow.Server.WindowsService" additivity="false">
    <level value="INFO"/>
    <appender-ref ref="RollingFile"/>
    <appender-ref ref="EventLogAppender"/>
  </logger>

  <!-- Configuration racine -->
  <root>
    <level value="INFO"/>
    <appender-ref ref="RollingFile"/>
    <!-- Décommentez la ligne suivante pour activer les logs console en mode debug -->
    <!-- <appender-ref ref="ConsoleAppender"/> -->
  </root>
</log4net>
